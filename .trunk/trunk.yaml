# This file controls the behavior of Trunk: https://docs.trunk.io/cli
# To learn more about the format of this file, see https://docs.trunk.io/reference/trunk-yaml
version: 0.1
cli:
  version: 1.22.12
# Trunk provides extensibility via plugins. (https://docs.trunk.io/plugins)
plugins:
  sources:
    - id: trunk
      ref: v1.6.7
      uri: https://github.com/trunk-io/plugins
# Many linters and tools depend on runtimes - configure them here. (https://docs.trunk.io/runtimes)
runtimes:
  enabled:
    - go@1.21.0
    - node@18.20.5
    - python@3.10.8
# This is the section where you manage your linters. (https://docs.trunk.io/check/configuration)
lint:
  enabled:
    - bandit@1.8.3
    - black@25.1.0
    - checkov@3.2.398
    - flake8@7.2.0
    - git-diff-check
    - isort@6.0.1
    - markdownlint@0.44.0
    - osv-scanner@2.0.1
    - oxipng@9.1.4
    - prettier@3.5.3
    - ruff@0.11.4
    - shellcheck@0.10.0
    - shfmt@3.6.0
    - svgo@3.3.2
    - taplo@0.9.3
    - trufflehog@3.88.23
    - yamllint@1.37.0
actions:
  disabled:
    - trunk-announce
    - trunk-check-pre-push
    - trunk-fmt-pre-commit
  enabled:
    - trunk-upgrade-available

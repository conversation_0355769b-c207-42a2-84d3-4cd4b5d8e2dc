"""
UPS Configuration Module
This module provides configuration settings for UPS integration.
"""

import os
from typing import Dict, Any

# UPS API Configuration
UPS_CONFIG = {
    "client_id": os.environ.get("UPS_CLIENT_ID"),
    "client_secret": os.environ.get("UPS_CLIENT_SECRET"),
    "account_number": os.environ.get("UPS_ACCOUNT_NUMBER"),
    "use_sandbox": os.environ.get("UPS_USE_SANDBOX", "True").lower() == "true",
    # Default shipper information
    "default_shipper": {
        "name": os.environ.get("UPS_DEFAULT_SHIPPER_NAME", "Ligentec SA"),
        "attention_name": os.environ.get(
            "UPS_DEFAULT_ATTENTION_NAME", "Shipping Department"
        ),
        "phone": os.environ.get("UPS_DEFAULT_SHIPPER_PHONE", ""),
        "address_line1": os.environ.get("UPS_DEFAULT_ADDRESS_LINE1", ""),
        "address_line2": os.environ.get("UPS_DEFAULT_ADDRESS_LINE2", ""),
        "city": os.environ.get("UPS_DEFAULT_CITY", ""),
        "state_province": os.environ.get("UPS_DEFAULT_STATE", ""),
        "postal_code": os.environ.get("UPS_DEFAULT_POSTAL_CODE", ""),
        "country_code": os.environ.get("UPS_DEFAULT_COUNTRY_CODE", ""),
        "email": os.environ.get("UPS_DEFAULT_EMAIL", ""),
    },
    # Package types
    "package_types": {
        "small_box": "02",  # UPS Package/Small Box
        "medium_box": "03",  # UPS Package/Medium Box
        "large_box": "04",  # UPS Package/Large Box
        "express_box": "21",  # UPS Express Box
        "tube": "01",  # UPS Tube
        "pak": "04",  # UPS Pak
        "pallet": "30",  # UPS Pallet
    },
    # Service types
    "service_types": {
        "ground": "03",  # UPS Ground
        "next_day_air": "01",  # UPS Next Day Air
        "next_day_air_saver": "13",  # UPS Next Day Air Saver
        "next_day_air_early": "14",  # UPS Next Day Air Early
        "2nd_day_air": "02",  # UPS 2nd Day Air
        "2nd_day_air_am": "59",  # UPS 2nd Day Air A.M.
        "3_day_select": "12",  # UPS 3 Day Select
        "standard": "11",  # UPS Standard
        "worldwide_express": "07",  # UPS Worldwide Express
        "worldwide_express_plus": "54",  # UPS Worldwide Express Plus
        "worldwide_expedited": "08",  # UPS Worldwide Expedited
        "worldwide_saver": "65",  # UPS Worldwide Saver
    },
}


def get_ups_config() -> Dict[str, Any]:
    """Get UPS configuration settings

    Returns:
        Dictionary containing UPS configuration
    """
    return UPS_CONFIG

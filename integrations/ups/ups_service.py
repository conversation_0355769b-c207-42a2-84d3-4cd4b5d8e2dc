"""
UPS Service Module
This module provides high-level services for UPS shipping operations.
"""

import os  # noqa: F401
import logging
from typing import Dict, Any, Optional, List, Tuple  # noqa: F401
from flask import current_app
from datetime import datetime  # noqa: F401

from integrations.ups.ups_client import UPSClient
from integrations.ups.ups_config import get_ups_config
from integrations.ups.ups_utils import (
    save_shipping_label,
    generate_commercial_invoice,
    format_tracking_info,
    upload_to_asana,
)
from integrations.asana.asana_client import get_asana_client

# Configure logging
logger = logging.getLogger(__name__)


class UPSService:
    """Service for UPS shipping operations"""

    def __init__(self):
        """Initialize the UPS service"""
        self.config = get_ups_config()
        self.client = UPSClient(use_sandbox=self.config.get("use_sandbox", True))

    def create_shipment(
        self, shipment_data: Dict[str, Any], asana_task_gid: Optional[str] = None
    ) -> Dict[str, Any]:
        """Create a UPS shipment with label

        Args:
            shipment_data: Dictionary containing shipment details
            asana_task_gid: Optional Asana task GID for uploading documents

        Returns:
            Dictionary containing shipment results
        """
        try:
            # Create shipment using UPS API
            response = self.client.create_shipping_label(shipment_data)

            # Extract tracking number
            tracking_number = ""
            if "ShipmentResponse" in response:
                shipment_results = response["ShipmentResponse"].get(
                    "ShipmentResults", {}
                )
                if "PackageResults" in shipment_results:
                    package_results = shipment_results["PackageResults"]
                    if isinstance(package_results, list):
                        # Multiple packages
                        tracking_number = package_results[0].get("TrackingNumber", "")
                    else:
                        # Single package
                        tracking_number = package_results.get("TrackingNumber", "")

            if not tracking_number:
                logger.error("No tracking number found in UPS response")
                return {
                    "success": False,
                    "message": "Failed to create shipment: No tracking number returned",
                    "data": response,
                }

            # Save shipping label
            label_path, label_name = save_shipping_label(
                response,
                tracking_number,
                output_dir=current_app.config.get("UPLOAD_FOLDER"),
            )

            # Generate commercial invoice if international
            is_international = self._is_international_shipment(shipment_data)
            invoice_path = ""
            invoice_name = ""

            if is_international:
                invoice_path, invoice_name = generate_commercial_invoice(
                    shipment_data,
                    tracking_number,
                    output_dir=current_app.config.get("UPLOAD_FOLDER"),
                )

            # Upload documents to Asana if task GID provided
            asana_uploads = []
            if asana_task_gid:
                asana_client = get_asana_client()

                # Upload label
                if label_path:
                    label_uploaded = upload_to_asana(
                        label_path, asana_task_gid, asana_client
                    )
                    asana_uploads.append(
                        {"file_name": label_name, "uploaded": label_uploaded}
                    )

                # Upload invoice if available
                if invoice_path:
                    invoice_uploaded = upload_to_asana(
                        invoice_path, asana_task_gid, asana_client
                    )
                    asana_uploads.append(
                        {"file_name": invoice_name, "uploaded": invoice_uploaded}
                    )

            # Return success response
            return {
                "success": True,
                "message": "Shipment created successfully",
                "tracking_number": tracking_number,
                "label_path": label_path,
                "label_name": label_name,
                "invoice_path": invoice_path,
                "invoice_name": invoice_name,
                "is_international": is_international,
                "asana_uploads": asana_uploads,
                "raw_response": response,
            }

        except Exception as e:
            logger.error(f"Error creating UPS shipment: {str(e)}")
            return {
                "success": False,
                "message": f"Failed to create shipment: {str(e)}",
                "data": {},
            }

    def track_shipment(self, tracking_number: str) -> Dict[str, Any]:
        """Track a UPS shipment

        Args:
            tracking_number: UPS tracking number

        Returns:
            Dictionary containing tracking information
        """
        try:
            # Log the tracking request
            logger.info(
                f"Tracking UPS shipment with tracking number: {tracking_number}"
            )

            # Get tracking data from UPS API
            response = self.client.track_package(tracking_number)

            # Check for error response
            if "error" in response:
                logger.error(f"UPS tracking API error: {response['error']}")
                return {
                    "success": False,
                    "message": f"Failed to track shipment: {response.get('error')}",
                    "tracking_info": {},
                    "raw_response": response,
                    "status": response.get("status", 400),
                }

            # Format tracking information
            tracking_info = format_tracking_info(response)

            return {
                "success": True,
                "message": "Tracking information retrieved successfully",
                "tracking_info": tracking_info,
                "raw_response": response,
            }

        except Exception as e:
            logger.error(f"Error tracking UPS shipment: {str(e)}")
            return {
                "success": False,
                "message": f"Failed to track shipment: {str(e)}",
                "tracking_info": {},
                "raw_response": {},
            }

    def get_shipping_rates(self, rate_request: Dict[str, Any]) -> Dict[str, Any]:
        """Get UPS shipping rates

        Args:
            rate_request: Dictionary containing rate request details

        Returns:
            Dictionary containing available rates
        """
        try:
            # Get rates from UPS API
            response = self.client.get_shipping_rates(rate_request)

            # Format rate information
            formatted_rates = []

            if "RateResponse" in response:
                rate_response = response["RateResponse"]
                if "RatedShipment" in rate_response:
                    rated_shipments = rate_response["RatedShipment"]

                    # Handle single or multiple rate results
                    if not isinstance(rated_shipments, list):
                        rated_shipments = [rated_shipments]

                    for shipment in rated_shipments:
                        service_code = shipment.get("Service", {}).get("Code")
                        service_name = self._get_service_name(service_code)

                        total_charges = shipment.get("TotalCharges", {})
                        currency_code = total_charges.get("CurrencyCode", "USD")
                        monetary_value = total_charges.get("MonetaryValue", "0.00")

                        guaranteed_delivery = shipment.get("GuaranteedDelivery", {})
                        is_guaranteed = (
                            guaranteed_delivery.get("Guaranteed", "N") == "Y"
                        )
                        business_days = guaranteed_delivery.get(
                            "BusinessDaysInTransit", ""
                        )

                        rate_info = {
                            "service_code": service_code,
                            "service_name": service_name,
                            "total_charge": f"{monetary_value} {currency_code}",
                            "currency_code": currency_code,
                            "monetary_value": monetary_value,
                            "is_guaranteed": is_guaranteed,
                            "business_days": business_days,
                        }

                        formatted_rates.append(rate_info)

            return {
                "success": True,
                "message": "Shipping rates retrieved successfully",
                "rates": formatted_rates,
                "raw_response": response,
            }

        except Exception as e:
            logger.error(f"Error getting UPS shipping rates: {str(e)}")
            return {
                "success": False,
                "message": f"Failed to get shipping rates: {str(e)}",
                "rates": [],
                "raw_response": {},
            }

    def validate_address(self, address: Dict[str, Any]) -> Dict[str, Any]:
        """Validate an address using UPS Address Validation API

        Args:
            address: Dictionary containing address details

        Returns:
            Dictionary containing validation results
        """
        try:
            # Log the address validation request
            logger.info(f"Validating address: {address}")

            # Validate address using UPS API
            response = self.client.validate_address(address)

            # Check for error response
            if "error" in response:
                logger.error(f"UPS address validation API error: {response['error']}")
                return {
                    "success": False,
                    "message": f"Failed to validate address: {response.get('error')}",
                    "is_valid": False,
                    "validation_result": "Error",
                    "suggested_addresses": [],
                    "raw_response": response,
                    "status": response.get("status", 400),
                }

            # Process validation results
            is_valid = False
            validation_result = ""
            suggested_addresses = []

            if "XAVResponse" in response:
                xav_response = response["XAVResponse"]
                if "ValidAddressIndicator" in xav_response:
                    is_valid = True
                    validation_result = "Valid address"
                elif "AmbiguousAddressIndicator" in xav_response:
                    is_valid = False
                    validation_result = "Ambiguous address"

                    # Get candidate addresses
                    if "Candidate" in xav_response:
                        candidates = xav_response["Candidate"]
                        if not isinstance(candidates, list):
                            candidates = [candidates]

                        for candidate in candidates:
                            if "AddressKeyFormat" in candidate:
                                address_format = candidate["AddressKeyFormat"]

                                suggested_address = {
                                    "address_line1": address_format.get(
                                        "AddressLine", ""
                                    ),
                                    "address_line2": address_format.get(
                                        "AddressLine2", ""
                                    ),
                                    "city": address_format.get(
                                        "PoliticalDivision2", ""
                                    ),
                                    "state": address_format.get(
                                        "PoliticalDivision1", ""
                                    ),
                                    "postal_code": address_format.get(
                                        "PostcodePrimaryLow", ""
                                    ),
                                    "country_code": address_format.get(
                                        "CountryCode", ""
                                    ),
                                }

                                suggested_addresses.append(suggested_address)
                elif "NoCandidatesIndicator" in xav_response:
                    is_valid = False
                    validation_result = "Invalid address - no candidates found"

            return {
                "success": True,
                "message": "Address validation completed",
                "is_valid": is_valid,
                "validation_result": validation_result,
                "suggested_addresses": suggested_addresses,
                "raw_response": response,
            }

        except Exception as e:
            logger.error(f"Error validating address with UPS: {str(e)}")
            return {
                "success": False,
                "message": f"Failed to validate address: {str(e)}",
                "is_valid": False,
                "validation_result": f"Error: {str(e)}",
                "suggested_addresses": [],
                "raw_response": {},
            }

    def _is_international_shipment(self, shipment_data: Dict[str, Any]) -> bool:
        """Determine if a shipment is international

        Args:
            shipment_data: Dictionary containing shipment details

        Returns:
            Boolean indicating if shipment is international
        """
        try:
            if "ShipmentRequest" in shipment_data:
                request = shipment_data["ShipmentRequest"]

                # Get shipper and ship to country codes
                shipper_country = (
                    request.get("Shipper", {}).get("Address", {}).get("CountryCode")
                )
                ship_to_country = (
                    request.get("ShipTo", {}).get("Address", {}).get("CountryCode")
                )

                # If country codes are different, it's international
                return shipper_country != ship_to_country

            return False

        except Exception as e:
            logger.error(f"Error determining if shipment is international: {str(e)}")
            return False

    def _get_service_name(self, service_code: str) -> str:
        """Get service name from service code

        Args:
            service_code: UPS service code

        Returns:
            Service name
        """
        service_types = self.config.get("service_types", {})

        # Invert the service types dictionary to map codes to names
        service_names = {v: k for k, v in service_types.items()}

        # Get service name or return code if not found
        return service_names.get(service_code, f"Service {service_code}")

    def build_shipment_request(
        self,
        ship_to_address: Dict[str, Any],
        package_dimensions: Dict[str, Any],
        service_code: str,
        package_type: str,
        description: str = "Wafer Shipment",
        reference_number: str = "",
        is_return: bool = False,
    ) -> Dict[str, Any]:
        """Build a UPS shipment request

        Args:
            ship_to_address: Dictionary containing recipient address
            package_dimensions: Dictionary containing package dimensions and weight
            service_code: UPS service code
            package_type: UPS package type code
            description: Shipment description
            reference_number: Reference number for the shipment
            is_return: Whether this is a return shipment

        Returns:
            Dictionary containing formatted shipment request
        """
        # Get default shipper information
        default_shipper = self.config.get("default_shipper", {})

        # Build shipment request
        shipment_request = {
            "ShipmentRequest": {
                "Request": {
                    "RequestOption": "validate",
                    "TransactionReference": {
                        "CustomerContext": "Talaria Dashboard Shipping"
                    },
                },
                "Shipment": {
                    "Description": description,
                    "Shipper": {
                        "Name": default_shipper.get("name", ""),
                        "AttentionName": default_shipper.get("attention_name", ""),
                        "Phone": {"Number": default_shipper.get("phone", "")},
                        "ShipperNumber": self.config.get("account_number", ""),
                        "Address": {
                            "AddressLine": default_shipper.get("address_line1", ""),
                            "AddressLine2": default_shipper.get("address_line2", ""),
                            "City": default_shipper.get("city", ""),
                            "StateProvinceCode": default_shipper.get(
                                "state_province", ""
                            ),
                            "PostalCode": default_shipper.get("postal_code", ""),
                            "CountryCode": default_shipper.get("country_code", ""),
                        },
                    },
                    "ShipTo": {
                        "Name": ship_to_address.get("name", ""),
                        "AttentionName": ship_to_address.get("attention_name", ""),
                        "Phone": {"Number": ship_to_address.get("phone", "")},
                        "Address": {
                            "AddressLine": ship_to_address.get("address_line1", ""),
                            "AddressLine2": ship_to_address.get("address_line2", ""),
                            "City": ship_to_address.get("city", ""),
                            "StateProvinceCode": ship_to_address.get(
                                "state_province", ""
                            ),
                            "PostalCode": ship_to_address.get("postal_code", ""),
                            "CountryCode": ship_to_address.get("country_code", ""),
                        },
                    },
                    "Service": {
                        "Code": service_code,
                        "Description": self._get_service_name(service_code),
                    },
                    "Package": {
                        "Description": description,
                        "Packaging": {"Code": package_type, "Description": "Package"},
                        "Dimensions": {
                            "UnitOfMeasurement": {
                                "Code": package_dimensions.get(
                                    "unit_of_measurement", "CM"
                                ),
                                "Description": "Centimeters",
                            },
                            "Length": str(package_dimensions.get("length", "")),
                            "Width": str(package_dimensions.get("width", "")),
                            "Height": str(package_dimensions.get("height", "")),
                        },
                        "PackageWeight": {
                            "UnitOfMeasurement": {
                                "Code": package_dimensions.get("weight_unit", "KGS"),
                                "Description": "Kilograms",
                            },
                            "Weight": str(package_dimensions.get("weight", "")),
                        },
                    },
                    "PaymentInformation": {
                        "ShipmentCharge": {
                            "Type": "01",  # Transportation
                            "BillShipper": {
                                "AccountNumber": self.config.get("account_number", "")
                            },
                        }
                    },
                },
                "LabelSpecification": {
                    "LabelImageFormat": {"Code": "GIF", "Description": "GIF"},
                    "HTTPUserAgent": "Mozilla/5.0",
                },
            }
        }

        # Add reference number if provided
        if reference_number:
            shipment_request["ShipmentRequest"]["Shipment"]["ReferenceNumber"] = {
                "Value": reference_number
            }

        # If this is a return shipment, swap shipper and ship to
        if is_return:
            temp = shipment_request["ShipmentRequest"]["Shipment"]["Shipper"]
            shipment_request["ShipmentRequest"]["Shipment"]["Shipper"] = (
                shipment_request["ShipmentRequest"]["Shipment"]["ShipTo"]
            )
            shipment_request["ShipmentRequest"]["Shipment"]["ShipTo"] = temp

            # Make sure the account number is still with the shipper
            shipment_request["ShipmentRequest"]["Shipment"]["Shipper"][
                "ShipperNumber"
            ] = self.config.get("account_number", "")

        return shipment_request

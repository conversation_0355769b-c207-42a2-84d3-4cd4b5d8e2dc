"""
UPS Utilities Module
This module provides utility functions for working with UPS shipping labels,
tracking information, and other UPS-related functionality.
"""

import os
import base64
import logging
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
from flask import current_app
import requests
from io import BytesIO
from PIL import Image

# Configure logging
logger = logging.getLogger(__name__)

def save_shipping_label(
    label_data: Dict[str, Any], 
    tracking_number: str,
    output_dir: Optional[str] = None
) -> Tuple[str, str]:
    """Save a UPS shipping label to file
    
    Args:
        label_data: Dictionary containing label image data
        tracking_number: UPS tracking number
        output_dir: Directory to save the label (defaults to app's upload folder)
        
    Returns:
        Tuple containing (file_path, file_name)
    """
    try:
        # Get the label image data
        if "ShipmentResponse" in label_data:
            response = label_data["ShipmentResponse"]
            if "ShipmentResults" in response:
                results = response["ShipmentResults"]
                if "PackageResults" in results:
                    package_results = results["PackageResults"]
                    if isinstance(package_results, list):
                        # Multiple packages
                        image_data = package_results[0].get("ShippingLabel", {}).get("GraphicImage")
                    else:
                        # Single package
                        image_data = package_results.get("ShippingLabel", {}).get("GraphicImage")
                else:
                    image_data = None
            else:
                image_data = None
        else:
            image_data = None
            
        if not image_data:
            logger.error("No label image found in UPS response")
            return "", ""
            
        # Decode base64 image data
        image_bytes = base64.b64decode(image_data)
        
        # Determine output directory
        if not output_dir:
            output_dir = current_app.config.get("UPLOAD_FOLDER", "uploads")
            
        # Create directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_name = f"UPS_Label_{tracking_number}_{timestamp}.gif"
        file_path = os.path.join(output_dir, file_name)
        
        # Save the image
        with open(file_path, "wb") as f:
            f.write(image_bytes)
            
        return file_path, file_name
        
    except Exception as e:
        logger.error(f"Error saving UPS shipping label: {str(e)}")
        return "", ""
        
def generate_commercial_invoice(
    shipment_data: Dict[str, Any],
    tracking_number: str,
    output_dir: Optional[str] = None
) -> Tuple[str, str]:
    """Generate a commercial invoice for international shipments
    
    Args:
        shipment_data: Dictionary containing shipment details
        tracking_number: UPS tracking number
        output_dir: Directory to save the invoice (defaults to app's upload folder)
        
    Returns:
        Tuple containing (file_path, file_name)
    """
    try:
        # This is a placeholder for actual invoice generation
        # In a real implementation, you would generate a PDF invoice
        # based on the shipment data
        
        # Determine output directory
        if not output_dir:
            output_dir = current_app.config.get("UPLOAD_FOLDER", "uploads")
            
        # Create directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_name = f"UPS_Invoice_{tracking_number}_{timestamp}.pdf"
        file_path = os.path.join(output_dir, file_name)
        
        # TODO: Implement actual invoice generation
        # For now, we'll just create a placeholder file
        with open(file_path, "w") as f:
            f.write(f"Commercial Invoice for Shipment {tracking_number}")
            
        return file_path, file_name
        
    except Exception as e:
        logger.error(f"Error generating commercial invoice: {str(e)}")
        return "", ""
        
def format_tracking_info(tracking_data: Dict[str, Any]) -> Dict[str, Any]:
    """Format UPS tracking information for display
    
    Args:
        tracking_data: Raw tracking data from UPS API
        
    Returns:
        Dictionary containing formatted tracking information
    """
    try:
        formatted_info = {
            "tracking_number": "",
            "status": "",
            "status_description": "",
            "estimated_delivery": "",
            "service_type": "",
            "weight": "",
            "activities": []
        }
        
        # Extract tracking information
        if "TrackResponse" in tracking_data:
            response = tracking_data["TrackResponse"]
            if "Shipment" in response:
                shipment = response["Shipment"]
                
                # Get tracking number
                if "InquiryNumber" in shipment:
                    formatted_info["tracking_number"] = shipment["InquiryNumber"]
                    
                # Get package info
                if "Package" in shipment:
                    package = shipment["Package"]
                    
                    # Get status
                    if "Activity" in package:
                        activities = package["Activity"]
                        if isinstance(activities, list) and activities:
                            latest = activities[0]
                            if "Status" in latest:
                                status = latest["Status"]
                                formatted_info["status"] = status.get("Type", "")
                                formatted_info["status_description"] = status.get("Description", "")
                                
                            # Format activities
                            for activity in activities:
                                if "Status" in activity and "Date" in activity and "Time" in activity:
                                    status = activity["Status"]
                                    date = activity["Date"]
                                    time = activity["Time"]
                                    
                                    # Format date and time
                                    try:
                                        date_str = f"{date[0:4]}-{date[4:6]}-{date[6:8]}"
                                        time_str = f"{time[0:2]}:{time[2:4]}:{time[4:6]}"
                                        datetime_str = f"{date_str} {time_str}"
                                    except:
                                        datetime_str = "Unknown"
                                        
                                    activity_entry = {
                                        "date": datetime_str,
                                        "status": status.get("Description", ""),
                                        "location": activity.get("ActivityLocation", {}).get("Description", "")
                                    }
                                    
                                    formatted_info["activities"].append(activity_entry)
                    
                    # Get weight
                    if "PackageWeight" in package:
                        weight = package["PackageWeight"]
                        weight_value = weight.get("Weight", "")
                        weight_unit = weight.get("UnitOfMeasurement", {}).get("Code", "")
                        formatted_info["weight"] = f"{weight_value} {weight_unit}"
                
                # Get service type
                if "Service" in shipment:
                    service = shipment["Service"]
                    formatted_info["service_type"] = service.get("Description", "")
                    
                # Get estimated delivery
                if "DeliveryDetail" in shipment:
                    delivery = shipment["DeliveryDetail"]
                    if "Date" in delivery and "Time" in delivery:
                        date = delivery["Date"]
                        time = delivery["Time"]
                        
                        # Format date and time
                        try:
                            date_str = f"{date[0:4]}-{date[4:6]}-{date[6:8]}"
                            time_str = f"{time[0:2]}:{time[2:4]}"
                            formatted_info["estimated_delivery"] = f"{date_str} {time_str}"
                        except:
                            formatted_info["estimated_delivery"] = "Unknown"
        
        return formatted_info
        
    except Exception as e:
        logger.error(f"Error formatting tracking information: {str(e)}")
        return {
            "tracking_number": "",
            "status": "Error",
            "status_description": f"Error processing tracking data: {str(e)}",
            "estimated_delivery": "",
            "service_type": "",
            "weight": "",
            "activities": []
        }
        
def upload_to_asana(
    file_path: str, 
    task_gid: str, 
    asana_client: Any
) -> bool:
    """Upload a file to an Asana task
    
    Args:
        file_path: Path to the file to upload
        task_gid: Asana task GID
        asana_client: Initialized Asana client
        
    Returns:
        Boolean indicating success or failure
    """
    try:
        # Check if file exists
        if not os.path.exists(file_path):
            logger.error(f"File not found: {file_path}")
            return False
            
        # Upload file to Asana
        with open(file_path, "rb") as f:
            file_content = f.read()
            
        # Use Asana client to upload file
        attachment_api = asana_client.attachments_api
        response = attachment_api.create_attachment_for_task(
            task_gid=task_gid,
            file=file_content,
            file_name=os.path.basename(file_path),
            file_content_type="application/octet-stream"
        )
        
        return True
        
    except Exception as e:
        logger.error(f"Error uploading file to Asana: {str(e)}")
        return False

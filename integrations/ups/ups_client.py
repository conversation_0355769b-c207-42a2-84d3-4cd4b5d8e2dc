"""
UPS API Client for Talaria Dashboard
This module provides integration with UPS APIs for shipping label creation,
tracking, and other shipping-related functionality.
"""

import os
import json
import base64
import logging
import time
import requests
import dotenv
import pathlib
from typing import Dict, Any, Optional, List, Union
from flask import current_app
from datetime import datetime, timedelta

# Configure logging
logger = logging.getLogger(__name__)


# Load environment variables from database/.env file
def load_db_env_vars():
    """Load environment variables from database/.env file"""
    project_root = pathlib.Path(
        os.path.dirname(os.path.abspath(__file__))
    ).parent.parent
    db_env_path = project_root / "database" / ".env"

    if db_env_path.exists():
        logger.info(f"Loading environment variables from {db_env_path}")
        dotenv.load_dotenv(db_env_path)
    else:
        logger.warning(f"Database .env file not found at {db_env_path}")


# Load environment variables from database/.env file
load_db_env_vars()

# UPS API URLs
UPS_OAUTH_URL = "https://onlinetools.ups.com/security/v1/oauth/token"
UPS_SANDBOX_OAUTH_URL = "https://wwwcie.ups.com/security/v1/oauth/token"
UPS_BASE_URL = "https://onlinetools.ups.com"
UPS_SANDBOX_BASE_URL = "https://wwwcie.ups.com"  # Sandbox environment

# UPS API Version
UPS_API_VERSION = "v1"

# API Endpoints
SHIPPING_API_ENDPOINT = "/api/shipments/v1/ship"
TRACKING_API_ENDPOINT = "/api/track/v1/details"
RATE_API_ENDPOINT = "/api/rating/v1/Rate"
ADDRESS_VALIDATION_ENDPOINT = "/api/addressvalidation/v1/1"


class UPSClient:
    """UPS API Client for interacting with UPS shipping services using OAuth"""

    def __init__(self, use_sandbox: bool = False):
        """Initialize the UPS client with credentials from environment variables"""
        # Log the environment variables for debugging
        logger.info(f"UPS_CLIENT_ID: {os.environ.get('UPS_CLIENT_ID')}")
        logger.info(
            f"UPS_CLIENT_SECRET: {os.environ.get('UPS_CLIENT_SECRET', '[REDACTED]') != '' and '[SET]' or '[NOT SET]'}"
        )
        logger.info(f"UPS_ACCOUNT_NUMBER: {os.environ.get('UPS_ACCOUNT_NUMBER')}")

        self.client_id = os.environ.get("UPS_CLIENT_ID")
        self.client_secret = os.environ.get("UPS_CLIENT_SECRET")
        self.account_number = os.environ.get("UPS_ACCOUNT_NUMBER")
        self.redirect_uri = os.environ.get(
            "UPS_REDIRECT_URI", "http://localhost:8000/ups/auth/callback"
        )

        self.use_sandbox = use_sandbox
        self.base_url = UPS_SANDBOX_BASE_URL if use_sandbox else UPS_BASE_URL
        self.oauth_url = UPS_SANDBOX_OAUTH_URL if use_sandbox else UPS_OAUTH_URL

        # OAuth tokens
        self.access_token = None
        self.refresh_token = None
        self.token_expiry = None

    def _get_auth_token(self) -> str:
        """Get OAuth token for UPS API authentication using client credentials flow

        Returns:
            str: Access token for API requests
        """
        from flask import session

        # Check if we have a valid token in session
        if (
            session.get("ups_access_token")
            and session.get("ups_token_expires", 0) > time.time()
        ):
            logger.info("Using existing UPS access token from session")
            return session.get("ups_access_token")

        # No valid token, get a new one using client credentials
        try:
            logger.info("Getting new UPS access token using client credentials")

            # Create auth header
            auth_str = f"{self.client_id}:{self.client_secret}"
            auth_bytes = auth_str.encode("ascii")
            auth_b64 = base64.b64encode(auth_bytes).decode("ascii")

            headers = {
                "Authorization": f"Basic {auth_b64}",
                "Content-Type": "application/x-www-form-urlencoded",
                "Accept": "application/json",
            }

            # Add merchant ID if available
            if self.account_number:
                headers["x-merchant-id"] = self.account_number

            data = {"grant_type": "client_credentials"}

            # Log request details for debugging
            logger.info(f"Token request URL: {self.oauth_url}")
            logger.info(f"Token request headers: {headers}")
            logger.info(f"Token request data: {data}")

            response = requests.post(self.oauth_url, headers=headers, data=data)

            # Log response for debugging
            logger.info(f"Token response status: {response.status_code}")
            logger.info(f"Token response headers: {response.headers}")

            try:
                response_data = response.json()
                logger.info(f"Token response data: {json.dumps(response_data)}")
            except Exception:
                logger.info(f"Token response text: {response.text}")

            response.raise_for_status()
            token_data = response.json()

            # Save token in session
            session["ups_access_token"] = token_data.get("access_token")
            session["ups_token_expires"] = time.time() + int(
                token_data.get("expires_in", 3600)
            )

            logger.info("Successfully obtained new UPS access token")
            return session.get("ups_access_token")

        except Exception as e:
            logger.error(f"Error getting UPS access token: {str(e)}")
            if hasattr(e, "response") and e.response:
                logger.error(f"Response status: {e.response.status_code}")
                logger.error(f"Response headers: {e.response.headers}")
                logger.error(f"Response text: {e.response.text}")

            # Clear invalid tokens
            session.pop("ups_access_token", None)
            session.pop("ups_token_expires", None)

            # Return None to indicate authentication failure
            return None

    def _make_api_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        """Make an authenticated request to the UPS API

        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint to call
            data: Request payload
            params: Query parameters
            headers: Additional headers

        Returns:
            Dict containing the API response
        """
        if headers is None:
            headers = {}

        # Get authentication token
        token = self._get_auth_token()

        if not token:
            logger.error("No valid authentication token available")
            return {"error": "Authentication required", "status": 401}

        # Set up headers
        request_headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json",
            "Accept": "application/json",
            "transId": f"talaria_{int(time.time())}",
            "transactionSrc": "Talaria Dashboard",
            **headers,
        }

        # Make the request
        try:
            url = f"{self.base_url}{endpoint}"

            # Log the request details for debugging
            logger.info(f"Making UPS API request to: {url}")
            logger.info(f"Method: {method}")
            logger.info(f"Headers: {request_headers}")
            if data:
                logger.info(f"Data: {json.dumps(data)}")
            if params:
                logger.info(f"Params: {params}")

            if method.upper() == "GET":
                response = requests.get(url, headers=request_headers, params=params)
            elif method.upper() == "POST":
                response = requests.post(
                    url, headers=request_headers, json=data, params=params
                )
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            # Log the response for debugging
            logger.info(f"UPS API response status: {response.status_code}")
            logger.info(f"UPS API response headers: {response.headers}")

            try:
                response_data = response.json()
                logger.info(f"UPS API response data: {json.dumps(response_data)}")
            except Exception:
                logger.info(f"UPS API response text: {response.text}")
                response_data = {"text": response.text}

            response.raise_for_status()
            return response_data

        except requests.RequestException as e:
            logger.error(f"UPS API request failed: {str(e)}")
            if hasattr(e, "response") and e.response:
                logger.error(f"Response status: {e.response.status_code}")
                logger.error(f"Response headers: {e.response.headers}")
                logger.error(f"Response text: {e.response.text}")

                try:
                    error_data = e.response.json()
                    logger.error(f"Error data: {json.dumps(error_data)}")
                    return {"error": error_data, "status": e.response.status_code}
                except Exception:
                    return {"error": e.response.text, "status": e.response.status_code}

            return {"error": str(e), "status": 500}

    def create_shipping_label(self, shipment_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a shipping label using UPS Shipping API

        Args:
            shipment_data: Dictionary containing shipment details

        Returns:
            Dictionary containing label information and tracking number
        """
        # Add account number to the request if not provided
        if self.account_number and "ShipmentRequest" in shipment_data:
            if "Shipper" in shipment_data["ShipmentRequest"]:
                if "ShipperNumber" not in shipment_data["ShipmentRequest"]["Shipper"]:
                    shipment_data["ShipmentRequest"]["Shipper"][
                        "ShipperNumber"
                    ] = self.account_number

        # Make API request
        return self._make_api_request("POST", SHIPPING_API_ENDPOINT, data=shipment_data)

    def track_package(self, tracking_number: str) -> Dict[str, Any]:
        """Track a package using UPS Tracking API

        Args:
            tracking_number: UPS tracking number

        Returns:
            Dictionary containing tracking information
        """
        # Prepare tracking request
        tracking_request = {
            "TrackRequest": {
                "Request": {
                    "RequestOption": "1",
                    "TransactionReference": {
                        "CustomerContext": "Talaria Dashboard Tracking"
                    },
                },
                "InquiryNumber": tracking_number,
            }
        }

        # Make API request
        return self._make_api_request(
            "POST", TRACKING_API_ENDPOINT, data=tracking_request
        )

    def get_shipping_rates(self, rate_request: Dict[str, Any]) -> Dict[str, Any]:
        """Get shipping rates using UPS Rating API

        Args:
            rate_request: Dictionary containing rate request details

        Returns:
            Dictionary containing available rates
        """
        # Add account number to the request if not provided
        if self.account_number and "RateRequest" in rate_request:
            if "Shipper" in rate_request["RateRequest"]:
                if "ShipperNumber" not in rate_request["RateRequest"]["Shipper"]:
                    rate_request["RateRequest"]["Shipper"][
                        "ShipperNumber"
                    ] = self.account_number

        # Make API request
        return self._make_api_request("POST", RATE_API_ENDPOINT, data=rate_request)

    def validate_address(self, address: Dict[str, Any]) -> Dict[str, Any]:
        """Validate an address using UPS Address Validation API

        Args:
            address: Dictionary containing address details

        Returns:
            Dictionary containing validation results
        """
        # Prepare address validation request
        validation_request = {
            "XAVRequest": {
                "AddressValidationRequest": {
                    "Request": {
                        "RequestOption": "1",
                        "TransactionReference": {
                            "CustomerContext": "Talaria Dashboard Address Validation"
                        },
                    },
                    "Address": address,
                }
            }
        }

        # Make API request
        return self._make_api_request(
            "POST", ADDRESS_VALIDATION_ENDPOINT, data=validation_request
        )

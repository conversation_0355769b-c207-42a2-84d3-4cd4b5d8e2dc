from flask import current_app
import asana
from typing import Dict, Any

from integrations.asana.asana_client import WORKSPACE_GID


def create_custom_fields(api_client: asana.ApiClient) -> Dict[str, Dict[str, Any]]:
    """Create or get custom fields in Asana workspace"""
    custom_fields_api = asana.CustomFieldsApi(api_client)

    field_definitions = [
        {
            "name": "Need Reviewing?",
            "type": "enum",
            "enum_options": [
                {"name": "Yes", "color": "green"},
                {"name": "No", "color": "red"},
            ],
        },
        {
            "name": "Priority",
            "type": "enum",
            "enum_options": [
                {"name": "Low", "color": "blue"},
                {"name": "Medium", "color": "yellow"},
                {"name": "High", "color": "red"},
            ],
        },
        {
            "name": "Label-free shipment ?",
            "type": "enum",
            "enum_options": [
                {"name": "Yes", "color": "green"},
                {"name": "No", "color": "red"},
            ],
        },
        {
            "name": "Keep Cassette closed",
            "type": "enum",
            "enum_options": [
                {"name": "Yes", "color": "green"},
                {"name": "No", "color": "red"},
            ],
        },
        {"name": "Wafer choice type", "type": "text"},
        {"name": "Wafers IDs", "type": "text"},
        {"name": "Number of wafers", "type": "text"},
        {"name": "Shipping address", "type": "text"},
        {"name": "Contact person", "type": "text"},
        {"name": "Email (delivery contact)", "type": "text"},
        {"name": "Telephone number", "type": "text"},
        {"name": "Ligentec label title", "type": "text"},
        # Metadata fields
        {"name": "XFAB purchase order", "type": "text"},
        {"name": "XFAB Device ID", "type": "text"},
        {"name": "Project ID", "type": "text"},
        {"name": "Eiger number", "type": "text"},
        {"name": "Tapeout (Eiger)", "type": "text"},
        {"name": "Vendor lot", "type": "text"},
        {"name": "Customer lot (Eiger)", "type": "text"},
        {
            "name": "Rib ?",
            "type": "enum",
            "enum_options": [
                {"name": "Yes", "color": "green"},
                {"name": "No", "color": "red"},
            ],
        },
        {"name": "TOX target nm", "type": "text"},
        {
            "name": "Heaters?",
            "type": "enum",
            "enum_options": [
                {"name": "Yes", "color": "green"},
                {"name": "No", "color": "red"},
            ],
        },
        {
            "name": "Undercuts?",
            "type": "enum",
            "enum_options": [
                {"name": "Yes", "color": "green"},
                {"name": "No", "color": "red"},
            ],
        },
        {"name": "SiN tube position (1-6)", "type": "text"},
        {"name": "Mask", "type": "text"},
    ]

    created_fields = {}
    for field_def in field_definitions:
        try:
            body = {
                "data": {
                    "workspace": WORKSPACE_GID,
                    "name": field_def["name"],
                    "type": field_def["type"],
                    "enum_options": field_def.get("enum_options", []),
                }
            }

            try:
                response = custom_fields_api.create_custom_field(
                    body=body,
                    opts={
                        "opt_fields": (
                            "name,gid,enum_options,enum_options.name,enum_options.gid"
                        )
                    },
                ).to_dict()
            except asana.rest.ApiException as e:
                if "Name has already been taken" in str(e):
                    # Field exists, get existing field
                    fields = custom_fields_api.get_custom_fields_for_workspace(
                        WORKSPACE_GID,
                        opts={
                            "opt_fields": (
                                "name,gid,enum_options,enum_options.name,"
                                "enum_options.gid"
                            )
                        },
                    ).to_dict()

                    for field in fields.get("data", []):
                        if field.get("name") == field_def["name"]:
                            response = field
                            break
                else:
                    raise e

            # Store the field data
            created_fields[field_def["name"]] = {
                "gid": response["gid"],
                "enum_options": (
                    {
                        opt["name"]: opt["gid"]
                        for opt in response.get("enum_options", [])
                    }
                    if field_def["type"] == "enum"
                    else None
                ),
            }

        except Exception as e:
            current_app.logger.error(f"Error with field {field_def['name']}: {str(e)}")

    return created_fields


def add_custom_field(field_map: Dict[str, Any], field_name: str, value: Any) -> bool:
    """Safely add a custom field to the custom_fields dictionary"""
    field = field_map.get(field_name)
    # Only add if value exists
    if field and isinstance(field, dict) and "gid" in field and value:
        field_map[field["gid"]] = value
        current_app.logger.info(f"Setting field {field_name}: {value}")
        return True
    return False

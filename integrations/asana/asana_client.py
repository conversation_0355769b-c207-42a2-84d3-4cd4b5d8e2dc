import asana
from flask import current_app
from typing import Dict, Optional, List

# Asana API configuration
OPTS = {
    "opt_fields": (
        "actual_time_minutes,approval_status,assignee,assignee_section,assignee_status,"
        "completed,completed_at,completed_by,created_at,created_by,custom_fields,"
        "dependencies,dependents,due_at,due_on,external,followers,gid,hearted,hearts,"
        "html_notes,is_rendered_as_separator,liked,likes,memberships,modified_at,name,"
        "notes,num_hearts,num_likes,num_subtasks,parent,permalink_url,projects,"
        "resource_subtype,resource_type,start_at,start_on,tags,workspace"
    )
}

OPT_PORTFOLIO = {
    "opt_fields": (
        "archived,color,completed,completed_at,completed_by,completed_by.name,"
        "created_at,created_from_template,created_from_template.name,current_status,"
        "current_status.author,current_status.author.name,current_status.color,"
        "current_status.created_at,current_status.created_by,"
        "current_status.created_by.name,current_status.html_text,"
        "current_status.modified_at,current_status.text,current_status.title,"
        "current_status_update,current_status_update.resource_subtype,"
        "current_status_update.title,custom_field_settings,"
        "custom_field_settings.custom_field,"
        "custom_field_settings.custom_field.asana_created_field,"
        "custom_field_settings.custom_field.created_by,"
        "custom_field_settings.custom_field.created_by.name,"
        "custom_field_settings.custom_field.currency_code,"
        "custom_field_settings.custom_field.custom_label,"
        "custom_field_settings.custom_field.custom_label_position,"
        "custom_field_settings.custom_field.date_value,"
        "custom_field_settings.custom_field.date_value.date,"
        "custom_field_settings.custom_field.date_value.date_time,"
        "custom_field_settings.custom_field.default_access_level,"
        "custom_field_settings.custom_field.description,"
        "custom_field_settings.custom_field['display_value'],"
        "custom_field_settings.custom_field.enabled,"
        "custom_field_settings.custom_field.enum_options,"
        "custom_field_settings.custom_field.enum_options.color,"
        "custom_field_settings.custom_field.enum_options.enabled,"
        "custom_field_settings.custom_field.enum_options.name,"
        "custom_field_settings.custom_field['enum_value'],"
        "custom_field_settings.custom_field['enum_value'].color,"
        "custom_field_settings.custom_field['enum_value'].enabled,"
        "custom_field_settings.custom_field['enum_value'].name,"
        "custom_field_settings.custom_field.format,"
        "custom_field_settings.custom_field.has_notifications_enabled,"
        "custom_field_settings.custom_field.id_prefix,"
        "custom_field_settings.custom_field.is_formula_field,"
        "custom_field_settings.custom_field.is_global_to_workspace,"
        "custom_field_settings.custom_field.is_value_read_only,"
        "custom_field_settings.custom_field.multi_enum_values,"
        "custom_field_settings.custom_field.multi_enum_values.color,"
        "custom_field_settings.custom_field.multi_enum_values.enabled,"
        "custom_field_settings.custom_field.multi_enum_values.name,"
        "custom_field_settings.custom_field.name,"
        "custom_field_settings.custom_field['number_value'],"
        "custom_field_settings.custom_field['people_value'],"
        "custom_field_settings.custom_field['people_value'].name,"
        "custom_field_settings.custom_field.precision,"
        "custom_field_settings.custom_field.privacy_setting,"
        "custom_field_settings.custom_field.representation_type,"
        "custom_field_settings.custom_field.resource_subtype,"
        "custom_field_settings.custom_field.text_value,"
        "custom_field_settings.custom_field.type,custom_field_settings.is_important,"
        "custom_field_settings.parent,custom_field_settings.parent.name,"
        "custom_field_settings.project,custom_field_settings.project.name,"
        "custom_fields,custom_fields.date_value,custom_fields.date_value.date,"
        "custom_fields.date_value.date_time,custom_fields['display_value'],"
        "custom_fields.enabled,custom_fields.enum_options,"
        "custom_fields.enum_options.color,custom_fields.enum_options.enabled,"
        "custom_fields.enum_options.name,custom_fields['enum_value'],"
        "custom_fields['enum_value'].color,custom_fields['enum_value'].enabled,"
        "custom_fields['enum_value'].name,custom_fields.id_prefix,"
        "custom_fields.is_formula_field,custom_fields.multi_enum_values,"
        "custom_fields.multi_enum_values.color,custom_fields.multi_enum_values.enabled,"
        "custom_fields.multi_enum_values.name,custom_fields.name,"
        "custom_fields['number_value'],custom_fields.representation_type,"
        "custom_fields.text_value,custom_fields.type,default_access_level,"
        "default_view,due_date,due_on,followers,followers.name,html_notes,icon,"
        "members,members.name,minimum_access_level_for_customization,"
        "minimum_access_level_for_sharing,modified_at,name,notes,offset,owner,path,"
        "permalink_url,privacy_setting,project_brief,public,start_on,team,team.name,"
        "uri,workspace,workspace.name"
    )
}

# Portfolio GIDs from your code
PORTFOLIOS = [
    "1206397258164834",  # proto
    "1206397258164837",  # ENG
    "1207525923277412",  # PhX
]

TEAM_GID = "464428578363726"
WORKSPACE_GID = "1204968822616977"
SHIPMENT_PROJECT_GID = "1206397258493005"


def get_asana_client() -> asana.ApiClient:
    """Create and return configured Asana API client"""
    configuration = asana.Configuration()
    # TODO: Move this to environment variable or secure configuration
    configuration.access_token = "1/1204968822616977:ed9cf542d701d8a0809f0155bb7bd306"
    return asana.ApiClient(configuration)


def search_project_gid(
    api_client: asana.ApiClient, lot_ids: List[str]
) -> List[Optional[str]]:
    """Search for project GID across portfolios"""
    projects_gid = [None] * len(lot_ids)
    try:
        portfolio_api = asana.PortfoliosApi(api_client)

        # Tant qu'il reste des lots à trouver et des portfolios à explorer
        i = 0
        while i < len(PORTFOLIOS) and any(gid is None for gid in projects_gid):
            portfolio_gid = PORTFOLIOS[i]
            i += 1  # Passer au portefeuille suivant après traitement
            try:
                items = portfolio_api.get_items_for_portfolio(
                    portfolio_gid, OPT_PORTFOLIO
                )
                for e in items:
                    for idx, lot_id in enumerate(lot_ids):
                        if projects_gid[idx] is None and lot_id in e["name"]:
                            projects_gid[idx] = e["gid"]

                    # Si tous les GIDs ont été trouvés, on arrête la recherche
                    if all(gid is not None for gid in projects_gid):
                        break

            except Exception as e:
                current_app.logger.error(
                    f"Error searching portfolio {portfolio_gid}: {str(e)}"
                )
                continue

        return projects_gid

    except Exception as e:
        current_app.logger.error(f"Error in search_project_gid: {str(e)}")
        return projects_gid


def search_single_project_gid(
    api_client: asana.ApiClient, lot_id: str
) -> Optional[str]:
    """Search for a single project GID for a lot ID"""
    results = search_project_gid(api_client, [lot_id])
    return results[0] if results and results[0] else None


def get_requester_gid_by_name(
    api_client: asana.ApiClient, requester_name: str
) -> Optional[str]:
    """Get requester GID by name"""
    try:
        mem = asana.TeamMembershipsApi(api_client)
        # Convert generator to list
        requesters_list = list(
            mem.get_team_memberships_for_team(
                team_gid=TEAM_GID, opts={"opt_fields": "user.name,user.gid"}
            )
        )

        for e in requesters_list:
            # Handle both object and dict formats
            if isinstance(e, dict) and "user" in e:
                user_info = e["user"]
                if (
                    isinstance(user_info, dict)
                    and "name" in user_info
                    and requester_name in user_info["name"]
                ):
                    return user_info.get("gid")
            elif hasattr(e, "user"):
                user_info = e.user
                if hasattr(user_info, "name") and requester_name in user_info.name:
                    if hasattr(user_info, "gid"):
                        return user_info.gid
                    elif isinstance(user_info, dict):
                        return user_info.get("gid")

        return None
    except Exception as e:
        current_app.logger.error(f"Error in get_requester_gid_by_name: {str(e)}")
        return None


def get_pi_task_gid(
    api_client: asana.ApiClient, project_gid: str, opt_fields: Dict[str, str]
) -> Optional[str]:
    """Get PI task GID from project"""
    try:
        tasks_api_instance = asana.TasksApi(api_client)
        tasks = tasks_api_instance.get_tasks_for_project(project_gid, opt_fields)
        for task in tasks:
            if "PI : " in task["name"]:
                return task["gid"]
        return None

    except Exception as e:
        current_app.logger.error(f"Error in get_pi_task_gid: {str(e)}")
        return None


def get_pi_task(
    api_client: asana.ApiClient, project_gid: str, opt_fields: Dict[str, str]
):
    """
    Recherche dans le projet une tâche dont le nom contient 'PI : '
    et retourne cette tâche

    Args:
        api_client: Client Asana API
        project_gid: ID du projet à consulter
        opt_fields: Options pour la requête API

    Returns:
        La tâche PI trouvée ou None si aucune tâche correspondante n'est trouvée
    """
    try:
        tasks_api_instance = asana.TasksApi(api_client)

        # Récupérer toutes les tâches du projet et convertir en liste
        tasks = list(
            tasks_api_instance.get_tasks_for_project(project_gid, opts=opt_fields)
        )

        # Rechercher une tâche avec "PI : " dans le nom
        for task in tasks:
            task_name = ""
            if isinstance(task, dict) and "name" in task:
                task_name = task["name"]
            elif hasattr(task, "name"):
                task_name = task.name

            if "PI : " in task_name:
                # On a trouvé une tâche PI, récupérer les détails complets
                task_gid = (
                    task.get("gid", task.gid) if isinstance(task, dict) else task.gid
                )

                try:
                    # Récupérer les détails complets de la tâche
                    pi_task = tasks_api_instance.get_task(task_gid, opts=opt_fields)
                    return pi_task
                except Exception as inner_e:
                    current_app.logger.error(
                        (
                            f"Erreur lors de la récupération des détails de la tâche : "
                            f"{str(inner_e)}"
                        )
                    )
                    return None

        # Aucune tâche PI trouvée dans ce projet
        current_app.logger.debug(
            f"Aucune tâche contenant 'PI : ' trouvée dans le projet {project_gid}"
        )
        return None

    except Exception as e:
        current_app.logger.error(f"Erreur dans get_pi_task: {str(e)}")
        return None

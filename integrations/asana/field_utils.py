from flask import current_app
from typing import Dict, Optional, Any, List, Union


def map_package_size(size: str) -> Optional[str]:
    """Map package size values to Asana enum options"""
    if not size:
        return None

    size_lower = str(size).lower()

    if "40x40x35" in size_lower:
        return "40x40x35 cm 5 Kg (1 cassette)"
    elif "40x80x35" in size_lower or "40x20x35" in size_lower:
        return "40x80x35 cm 8 Kg (2 cassettes)"

    return None


def map_package_size_reverse(size: str) -> Optional[str]:
    """Map package size value to Asana enum option"""
    if not size:
        return None

    # Convert to string and lowercase for case-insensitive comparison
    size_lower = str(size).lower()

    if "40x40x35" in size_lower:
        return "40x40x35 cm 5 Kg (1 cassette)"
    elif "40x80x35" in size_lower or "40x20x35" in size_lower:
        return "40x80x35 cm 8 Kg (2 cassettes)"

    # Default to the most common size if not matching
    return "40x40x35 cm 5 Kg (1 cassette)"


def map_mask_type(mask: str) -> str:
    """Map mask type to form values"""
    mask_lower = str(mask).lower()
    if "e-beam" in mask_lower or "ebeam" in mask_lower:
        return "e-beam"
    elif "laser" in mask_lower:
        return "laser"
    return "e-beam"  # Default value


def get_package_size_option(size: str) -> str:
    """Map parcel size to Asana enum option"""
    size_mapping = {
        "40x40x35": "40x40x35 cm 5 Kg (1 cassette)",
        "40x80x35": "40x80x35 cm 8 Kg (2 cassettes)",
    }
    return size_mapping.get(size, "")


def get_field_by_name(
    name: str, field_list: List[Dict[str, Any]]
) -> Optional[Dict[str, Any]]:
    """Get field from field list by name with improved error handling"""
    try:
        # Check if field_list is None or not a list
        if field_list is None:
            current_app.logger.debug(f"field_list is None when searching for {name}")
            return None

        # Handle case when field_list is unexpectedly not a list
        if not isinstance(field_list, list):
            current_app.logger.warning(
                f"field_list is not a list when searching for {name}, "
                f"type: {type(field_list)}"
            )
            return None

        # For empty field list
        if len(field_list) == 0:
            current_app.logger.debug(f"Empty field_list when searching for {name}")
            return None

        # Extract names safely, checking each item is a dict with a 'name' key
        names = []
        for i, field in enumerate(field_list):
            if isinstance(field, dict) and "name" in field:
                names.append(field["name"])
                # Direct match - return immediately
                if field["name"] == name:
                    return field
            else:
                current_app.logger.warning(
                    f"Field at index {i} is not a proper field dict: {field}"
                )

        # If we get here, we didn't find an exact match - try case-insensitive
        name_lower = name.lower()
        for field in field_list:
            if (
                isinstance(field, dict)
                and "name" in field
                and field["name"].lower() == name_lower
            ):
                return field

        # No match found - log diagnostic info
        current_app.logger.debug(f"Field '{name}' not found. Available fields: {names}")
        return None
    except Exception as e:
        current_app.logger.error(f"Error in get_field_by_name for '{name}': {str(e)}")
        return None


def extract_pi_fields(pi_task: Dict[str, Any]) -> Dict[str, Any]:
    """Extract fields from PI task for use in shipment task creation"""
    if not pi_task:
        return {}

    pi_fields = {}
    custom_fields = pi_task.get("custom_fields", [])

    # Helper function to find field value by name with proper type checking
    def get_field_value(name):
        try:
            field = next(
                (
                    f
                    for f in custom_fields
                    if isinstance(f, dict) and f.get("name") == name
                ),
                None,
            )
            if not field:
                return None

            field_type = field.get("type")
            if field_type == "text":
                return field.get("text_value") or field.get("display_value")
            elif field_type == "number":
                return field.get("number_value")
            elif field_type == "enum":
                enum_value = field.get("enum_value")
                if enum_value and isinstance(enum_value, dict):
                    return enum_value.get("name")
                return field.get("display_value")
            elif field_type == "people":
                people_value = field.get("people_value")
                if people_value and isinstance(people_value, dict):
                    return people_value.get("name")
                return field.get("display_value")
            elif field_type == "date":
                date_value = field.get("date_value")
                if date_value and isinstance(date_value, dict):
                    return date_value.get("date")
                return field.get("display_value")

            return field.get("display_value")
        except Exception as e:
            current_app.logger.error(f"Error extracting field {name}: {str(e)}")
            return None

    # Extract relevant fields with safer error handling
    try:
        pi_fields["lot_reservation"] = get_field_value("Lot reservation")
        pi_fields["account_manager"] = get_field_value("Account/Project manager")
        pi_fields["sales_order"] = get_field_value("Sales order")
        pi_fields["customer_id"] = get_field_value("Customer ID")
        pi_fields["corridor"] = get_field_value("Corridor")
        pi_fields["lot_project"] = get_field_value("Lot project")

        # Filter out None values
        return {k: v for k, v in pi_fields.items() if v is not None}
    except Exception as e:
        current_app.logger.error(f"Error in extract_pi_fields: {str(e)}")
        return {}


def get_gid_from_list_options(value: str, data: Dict[str, Any]) -> Optional[str]:
    """Get GID from list of options based on value"""
    try:
        if not value or not data or not hasattr(data, "enum_options"):
            return None

        option_list = data["enum_options"]
        name_list = [e["name"] for e in option_list]
        gid_list = [e["gid"] for e in option_list]

        try:
            return gid_list[name_list.index(value)]
        except ValueError:
            # If exact match not found, try case-insensitive match
            value_lower = value.lower()
            for i, name in enumerate(name_list):
                if name.lower() == value_lower:
                    return gid_list[i]
            return None

    except Exception as e:
        current_app.logger.error(f"Error in get_gid_from_list_options: {str(e)}")
        return None


def handle_boolean_enum(value: Union[bool, str], field_name: str) -> Optional[str]:
    """Handle boolean fields that use TRUE/FALSE enum values in Asana"""
    if value is None or value == "":
        return None

    is_true = False
    if isinstance(value, bool):
        is_true = value
    elif isinstance(value, str):
        is_true = value.lower() in ["true", "yes", "1", "on"]

    # Return the appropriate string based on the field
    if field_name in ["RIB ?", "HEATERS?", "Undercuts?"]:
        return "TRUE" if is_true else "FALSE"
    else:
        return "Yes" if is_true else "No"


def handle_mask_type(value: str) -> Optional[str]:
    """Handle the Mask field formatting"""
    if not value:
        return None

    if isinstance(value, str) and "laser" in value.lower():
        return "Laser"

    return "E-beam"  # Default to E-beam if not laser

from flask import Flask, render_template
import requests
from datetime import datetime, timedelta


@app.route("/shipment_statistics")
def shipment_statistics():
    project_name = "200mm"  # Replace with your actual project name
    project_gid = get_asana_project_gid_with_requests(project_name)

    if project_gid:
        stats = get_shipment_stats_from_asana(project_gid)
        return render_template("shipment_statistics.html", stats=stats)
    else:
        return "Failed to retrieve project GID", 400


def get_asana_project_gid_with_requests(project_name):
    headers = {
        "Authorization": "Bearer 1/1204968822616977:ed9cf542d701d8a0809f0155bb7bd306"
    }

    try:
        response = requests.get(
            "https://app.asana.com/api/1.0/workspaces", headers=headers
        )
        response.raise_for_status()
        workspaces = response.json()["data"]

        for workspace in workspaces:
            response = requests.get(
                f"https://app.asana.com/api/1.0/projects?workspace={
                    workspace['gid']
                }&opt_fields=name,gid",
                headers=headers,
            )
            response.raise_for_status()
            projects = response.json()["data"]

            for project in projects:
                if project["name"] == project_name:
                    return project["gid"]

        print(f"Project '{project_name}' not found.")
        return None

    except requests.RequestException as e:
        print(f"An error occurred: {str(e)}")
        return None


def get_shipment_stats_from_asana(project_gid):
    headers = {
        "Authorization": "Bearer 1/1204968822616977:ed9cf542d701d8a0809f0155bb7bd306"
    }

    sections = [
        "Incoming",
        "Planned",
        "In preparation",
        "packaged",
        "Waiting for review",
        "Sent",
        "Delivered",
    ]
    stats = {section: {"weekly": 0, "monthly": 0, "yearly": 0} for section in sections}

    now = datetime.now()
    week_ago = now - timedelta(days=7)
    month_ago = now - timedelta(days=30)
    year_ago = now - timedelta(days=365)

    try:
        response = requests.get(
            f"https://app.asana.com/api/1.0/projects/{
                project_gid
            }/tasks?opt_fields=name,completed_at,memberships.section.name",
            headers=headers,
        )
        response.raise_for_status()
        tasks = response.json()["data"]

        for task in tasks:
            if task["completed_at"]:
                completed_at = datetime.fromisoformat(task["completed_at"].rstrip("Z"))
                section = next(
                    (
                        membership["section"]["name"]
                        for membership in task["memberships"]
                        if "section" in membership
                    ),
                    None,
                )

                if section in sections:
                    if completed_at >= week_ago:
                        stats[section]["weekly"] += 1
                    if completed_at >= month_ago:
                        stats[section]["monthly"] += 1
                    if completed_at >= year_ago:
                        stats[section]["yearly"] += 1

        return {
            "weekly": {section: stats[section]["weekly"] for section in sections},
            "monthly": {section: stats[section]["monthly"] for section in sections},
            "yearly": {section: stats[section]["yearly"] for section in sections},
        }

    except requests.RequestException as e:
        print(f"An error occurred: {str(e)}")
        return None


# Usage
project_name = "200mm"
project_gid = get_asana_project_gid_with_requests(project_name)
if project_gid:
    print(f"The GID for project '{project_name}' is: {project_gid}")
else:
    print("Failed to retrieve the project GID.")

services:
  talaria-dashboard:
    image: ${IMAGE_TAG} # Do not change
    ports:
      - "${HOST_PORT}:${APP_PORT}" # Do not change
    secrets:
      - source: db_host
        target: /run/secrets/db_host
      - source: db_name
        target: /run/secrets/db_name
      - source: db_user
        target: /run/secrets/db_user
      - source: db_password
        target: /run/secrets/db_password
      - source: auth_db_host
        target: /run/secrets/auth_db_host
      - source: auth_db_name
        target: /run/secrets/auth_db_name
      - source: auth_db_user
        target: /run/secrets/auth_db_user
      - source: auth_db_password
        target: /run/secrets/auth_db_password
      - source: secret_key
        target: /run/secrets/secret_key
      - source: google_drive_file_id
        target: /run/secrets/google_drive_file_id
      - source: mail_server
        target: /run/secrets/mail_server
      - source: mail_port
        target: /run/secrets/mail_port
      - source: mail_default_sender
        target: /run/secrets/mail_default_sender
      - source: default_shipping_recipients
        target: /run/secrets/default_shipping_recipients
      - source: ups_client_id
        target: /run/secrets/ups_client_id
      - source: ups_client_secret
        target: /run/secrets/ups_client_secret
      - source: ups_account_number
        target: /run/secrets/ups_account_number
      - source: ups_use_sandbox
        target: /run/secrets/ups_use_sandbox
      - source: ups_default_shipper_name
        target: /run/secrets/ups_default_shipper_name
      - source: ups_default_attention_name
        target: /run/secrets/ups_default_attention_name
      - source: ups_default_shipper_phone
        target: /run/secrets/ups_default_shipper_phone
      - source: ups_default_address_line1
        target: /run/secrets/ups_default_address_line1
      - source: ups_default_address_line2
        target: /run/secrets/ups_default_address_line2
      - source: ups_default_city
        target: /run/secrets/ups_default_city
      - source: ups_default_state
        target: /run/secrets/ups_default_state
      - source: ups_default_postal_code
        target: /run/secrets/ups_default_postal_code
      - source: ups_default_country_code
        target: /run/secrets/ups_default_country_code
      - source: ups_default_email
        target: /run/secrets/ups_default_email
      - source: flask_app
        target: /run/secrets/flask_app
      - source: flask_env
        target: /run/secrets/flask_env
    configs:
      - source: app_config # Do not change
        target: /app/config.yml # Do not change
    environment:
      - CONFIG_FILE=/app/config.yml # Do not change
      - FLASK_APP=app.py
      - FLASK_ENV=production
      - FORCE_FONT=DejaVuSans
      - DEJAVU_FONT_PATH=/usr/share/fonts/ttf-dejavu/DejaVuSans.ttf
      - DOWNLOAD_FOLDER=/app/downloads
      - ASANA_TOKEN=${ASANA_TOKEN}
      - AUTH_DB_PASSWORD_FILE=/run/secrets/auth_db_password
      - AUTH_DB_HOST_FILE=/run/secrets/auth_db_host
      - AUTH_DB_NAME_FILE=/run/secrets/auth_db_name
      - AUTH_DB_USER_FILE=/run/secrets/auth_db_user
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${APP_PORT}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      replicas: 1
      resources:
        limits:
          memory: 1G
          cpus: "1"
      update_config:
        parallelism: 1
        delay: 10s
        order: start-first
        failure_action: rollback
      restart_policy:
        condition: any # Changed from on-failure to any - restart for any reason
        delay: 5s # Short delay between restart attempts
        max_attempts: 0 # 0 means unlimited attempts
      placement:
        constraints:
          - node.role == manager # Place on manager nodes for reliability
    stop_grace_period: 30s

secrets:
  db_host:
    file: ./temps_db_host.txt
    name: ${DB_HOST}
  db_name:
    file: ./temps_db_name.txt
    name: ${DB_NAME}
  db_user:
    file: ./temps_db_user.txt
    name: ${DB_USER}
  db_password:
    file: ./temps_db_password.txt
    name: ${DB_PASSWORD}
  auth_db_host:
    file: ./temps_auth_db_host.txt
    name: ${AUTH_DB_HOST}
  auth_db_name:
    file: ./temps_auth_db_name.txt
    name: ${AUTH_DB_NAME}
  auth_db_user:
    file: ./temps_auth_db_user.txt
    name: ${AUTH_DB_USER}
  auth_db_password:
    file: ./temps_auth_db_password.txt
    name: ${AUTH_DB_PASSWORD}
  secret_key:
    file: ./temps_secret_key.txt
    name: ${SECRET_KEY}
  google_drive_file_id:
    file: ./temps_google_drive_file_id.txt
    name: ${GOOGLE_DRIVE_FILE_ID}
  mail_server:
    file: ./temps_mail_server.txt
    name: ${MAIL_SERVER}
  mail_port:
    file: ./temps_mail_port.txt
    name: ${MAIL_PORT}
  mail_default_sender:
    file: ./temps_mail_default_sender.txt
    name: ${MAIL_DEFAULT_SENDER}
  default_shipping_recipients:
    file: ./temps_default_shipping_recipients.txt
    name: ${DEFAULT_SHIPPING_RECIPIENTS}
  ups_client_id:
    file: ./temps_ups_client_id.txt
    name: ${UPS_CLIENT_ID}
  ups_client_secret:
    file: ./temps_ups_client_secret.txt
    name: ${UPS_CLIENT_SECRET}
  ups_account_number:
    file: ./temps_ups_account_number.txt
    name: ${UPS_ACCOUNT_NUMBER}
  ups_use_sandbox:
    file: ./temps_ups_use_sandbox.txt
    name: ${UPS_USE_SANDBOX}
  ups_default_shipper_name:
    file: ./temps_ups_default_shipper_name.txt
    name: ${UPS_DEFAULT_SHIPPER_NAME}
  ups_default_attention_name:
    file: ./temps_ups_default_attention_name.txt
    name: ${UPS_DEFAULT_ATTENTION_NAME}
  ups_default_shipper_phone:
    file: ./temps_ups_default_shipper_phone.txt
    name: ${UPS_DEFAULT_SHIPPER_PHONE}
  ups_default_address_line1:
    file: ./temps_ups_default_address_line1.txt
    name: ${UPS_DEFAULT_ADDRESS_LINE1}
  ups_default_address_line2:
    file: ./temps_ups_default_address_line2.txt
    name: ${UPS_DEFAULT_ADDRESS_LINE2}
  ups_default_city:
    file: ./temps_ups_default_city.txt
    name: ${UPS_DEFAULT_CITY}
  ups_default_state:
    file: ./temps_ups_default_state.txt
    name: ${UPS_DEFAULT_STATE}
  ups_default_postal_code:
    file: ./temps_ups_default_postal_code.txt
    name: ${UPS_DEFAULT_POSTAL_CODE}
  ups_default_country_code:
    file: ./temps_ups_default_country_code.txt
    name: ${UPS_DEFAULT_COUNTRY_CODE}
  ups_default_email:
    file: ./temps_ups_default_email.txt
    name: ${UPS_DEFAULT_EMAIL}
  flask_app:
    file: ./temps_flask_app.txt
    name: ${FLASK_APP}
  flask_env:
    file: ./temps_flask_env.txt
    name: ${FLASK_ENV}

configs:
  app_config:
    file: ./configs/app_config.yml

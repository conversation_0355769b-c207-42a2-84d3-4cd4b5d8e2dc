"""
Smart Workflow Engine for Talaria Dashboard
Provides automated workflow management, rule-based processing, and intelligent task orchestration
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import asyncio
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)


class WorkflowStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TriggerType(Enum):
    INVENTORY_CHANGE = "inventory_change"
    SHIPMENT_UPDATE = "shipment_update"
    THRESHOLD_BREACH = "threshold_breach"
    SCHEDULE = "schedule"
    MANUAL = "manual"
    API_WEBHOOK = "api_webhook"


@dataclass
class WorkflowRule:
    """Defines a workflow rule with conditions and actions"""
    id: str
    name: str
    description: str
    trigger_type: TriggerType
    conditions: Dict[str, Any]
    actions: List[Dict[str, Any]]
    enabled: bool = True
    priority: int = 1
    created_at: datetime = None
    updated_at: datetime = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()


@dataclass
class WorkflowExecution:
    """Represents a workflow execution instance"""
    id: str
    rule_id: str
    status: WorkflowStatus
    trigger_data: Dict[str, Any]
    started_at: datetime
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    results: Dict[str, Any] = None

    def __post_init__(self):
        if self.results is None:
            self.results = {}


class WorkflowEngine:
    """
    Smart Workflow Engine that processes business rules and automates tasks
    """

    def __init__(self):
        self.rules: Dict[str, WorkflowRule] = {}
        self.executions: Dict[str, WorkflowExecution] = {}
        self.action_handlers: Dict[str, Callable] = {}
        self.condition_evaluators: Dict[str, Callable] = {}
        self.executor = ThreadPoolExecutor(max_workers=5)
        self.is_running = False
        
        # Register built-in action handlers
        self._register_builtin_handlers()

    def _register_builtin_handlers(self):
        """Register built-in action handlers"""
        self.register_action_handler('send_notification', self._send_notification)
        self.register_action_handler('update_inventory', self._update_inventory)
        self.register_action_handler('create_shipment', self._create_shipment)
        self.register_action_handler('send_email', self._send_email)
        self.register_action_handler('log_event', self._log_event)
        self.register_action_handler('call_api', self._call_api)
        self.register_action_handler('generate_report', self._generate_report)

        # Register condition evaluators
        self.register_condition_evaluator('threshold', self._evaluate_threshold)
        self.register_condition_evaluator('time_based', self._evaluate_time_based)
        self.register_condition_evaluator('inventory_level', self._evaluate_inventory_level)
        self.register_condition_evaluator('custom_script', self._evaluate_custom_script)

    def register_action_handler(self, action_type: str, handler: Callable):
        """Register a custom action handler"""
        self.action_handlers[action_type] = handler
        logger.info(f"Registered action handler: {action_type}")

    def register_condition_evaluator(self, condition_type: str, evaluator: Callable):
        """Register a custom condition evaluator"""
        self.condition_evaluators[condition_type] = evaluator
        logger.info(f"Registered condition evaluator: {condition_type}")

    def add_rule(self, rule: WorkflowRule) -> bool:
        """Add a new workflow rule"""
        try:
            self.rules[rule.id] = rule
            logger.info(f"Added workflow rule: {rule.name} ({rule.id})")
            return True
        except Exception as e:
            logger.error(f"Failed to add workflow rule {rule.id}: {str(e)}")
            return False

    def remove_rule(self, rule_id: str) -> bool:
        """Remove a workflow rule"""
        if rule_id in self.rules:
            del self.rules[rule_id]
            logger.info(f"Removed workflow rule: {rule_id}")
            return True
        return False

    def enable_rule(self, rule_id: str) -> bool:
        """Enable a workflow rule"""
        if rule_id in self.rules:
            self.rules[rule_id].enabled = True
            self.rules[rule_id].updated_at = datetime.now()
            return True
        return False

    def disable_rule(self, rule_id: str) -> bool:
        """Disable a workflow rule"""
        if rule_id in self.rules:
            self.rules[rule_id].enabled = False
            self.rules[rule_id].updated_at = datetime.now()
            return True
        return False

    def trigger_workflow(self, trigger_type: TriggerType, data: Dict[str, Any]) -> List[str]:
        """Trigger workflows based on an event"""
        execution_ids = []
        
        # Find matching rules
        matching_rules = [
            rule for rule in self.rules.values()
            if rule.enabled and rule.trigger_type == trigger_type
        ]

        # Sort by priority (higher priority first)
        matching_rules.sort(key=lambda r: r.priority, reverse=True)

        for rule in matching_rules:
            if self._evaluate_conditions(rule, data):
                execution_id = self._execute_workflow(rule, data)
                if execution_id:
                    execution_ids.append(execution_id)

        return execution_ids

    def _evaluate_conditions(self, rule: WorkflowRule, data: Dict[str, Any]) -> bool:
        """Evaluate if rule conditions are met"""
        try:
            for condition_type, condition_config in rule.conditions.items():
                evaluator = self.condition_evaluators.get(condition_type)
                if evaluator:
                    if not evaluator(condition_config, data):
                        return False
                else:
                    logger.warning(f"No evaluator found for condition type: {condition_type}")
                    return False
            return True
        except Exception as e:
            logger.error(f"Error evaluating conditions for rule {rule.id}: {str(e)}")
            return False

    def _execute_workflow(self, rule: WorkflowRule, trigger_data: Dict[str, Any]) -> Optional[str]:
        """Execute a workflow rule"""
        execution_id = f"exec_{rule.id}_{datetime.now().timestamp()}"
        
        execution = WorkflowExecution(
            id=execution_id,
            rule_id=rule.id,
            status=WorkflowStatus.PENDING,
            trigger_data=trigger_data,
            started_at=datetime.now()
        )
        
        self.executions[execution_id] = execution

        # Submit to thread pool for async execution
        future = self.executor.submit(self._run_workflow_actions, execution, rule)
        
        return execution_id

    def _run_workflow_actions(self, execution: WorkflowExecution, rule: WorkflowRule):
        """Run workflow actions in a separate thread"""
        try:
            execution.status = WorkflowStatus.RUNNING
            logger.info(f"Starting workflow execution: {execution.id}")

            for action in rule.actions:
                action_type = action.get('type')
                action_config = action.get('config', {})
                
                handler = self.action_handlers.get(action_type)
                if handler:
                    result = handler(action_config, execution.trigger_data)
                    execution.results[action_type] = result
                else:
                    logger.warning(f"No handler found for action type: {action_type}")

            execution.status = WorkflowStatus.COMPLETED
            execution.completed_at = datetime.now()
            logger.info(f"Completed workflow execution: {execution.id}")

        except Exception as e:
            execution.status = WorkflowStatus.FAILED
            execution.error_message = str(e)
            execution.completed_at = datetime.now()
            logger.error(f"Workflow execution failed {execution.id}: {str(e)}")

    # Built-in Action Handlers
    def _send_notification(self, config: Dict[str, Any], trigger_data: Dict[str, Any]) -> Dict[str, Any]:
        """Send a notification"""
        try:
            # Import here to avoid circular imports
            from core.services.notification_service import notification_service
            
            notification_data = {
                'title': config.get('title', 'Workflow Notification'),
                'message': config.get('message', '').format(**trigger_data),
                'notification_type': config.get('type', 'info'),
                'user_id': config.get('user_id'),
                'is_email_related': config.get('is_email_related', False)
            }
            
            result = notification_service.create_notification(notification_data)
            return {'success': True, 'notification_id': result.get('id')}
        except Exception as e:
            logger.error(f"Failed to send notification: {str(e)}")
            return {'success': False, 'error': str(e)}

    def _update_inventory(self, config: Dict[str, Any], trigger_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update inventory based on workflow"""
        try:
            # Implementation would depend on your inventory update logic
            wafer_id = config.get('wafer_id') or trigger_data.get('wafer_id')
            updates = config.get('updates', {})
            
            # Apply template variables from trigger data
            for key, value in updates.items():
                if isinstance(value, str) and '{' in value:
                    updates[key] = value.format(**trigger_data)
            
            # Call inventory update API
            # This would integrate with your existing inventory management
            return {'success': True, 'updated_wafer': wafer_id, 'changes': updates}
        except Exception as e:
            logger.error(f"Failed to update inventory: {str(e)}")
            return {'success': False, 'error': str(e)}

    def _create_shipment(self, config: Dict[str, Any], trigger_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new shipment"""
        try:
            shipment_data = {
                'wafer_ids': config.get('wafer_ids', []),
                'destination': config.get('destination'),
                'priority': config.get('priority', 'normal'),
                'notes': config.get('notes', '').format(**trigger_data)
            }
            
            # Integration with shipment creation logic
            return {'success': True, 'shipment_data': shipment_data}
        except Exception as e:
            logger.error(f"Failed to create shipment: {str(e)}")
            return {'success': False, 'error': str(e)}

    def _send_email(self, config: Dict[str, Any], trigger_data: Dict[str, Any]) -> Dict[str, Any]:
        """Send an email"""
        try:
            # Integration with email service
            email_data = {
                'to': config.get('to', []),
                'cc': config.get('cc', []),
                'subject': config.get('subject', '').format(**trigger_data),
                'body': config.get('body', '').format(**trigger_data),
                'template': config.get('template')
            }
            
            return {'success': True, 'email_data': email_data}
        except Exception as e:
            logger.error(f"Failed to send email: {str(e)}")
            return {'success': False, 'error': str(e)}

    def _log_event(self, config: Dict[str, Any], trigger_data: Dict[str, Any]) -> Dict[str, Any]:
        """Log an event"""
        try:
            log_level = config.get('level', 'info').upper()
            message = config.get('message', '').format(**trigger_data)
            
            getattr(logger, log_level.lower())(f"Workflow Event: {message}")
            return {'success': True, 'logged': True}
        except Exception as e:
            logger.error(f"Failed to log event: {str(e)}")
            return {'success': False, 'error': str(e)}

    def _call_api(self, config: Dict[str, Any], trigger_data: Dict[str, Any]) -> Dict[str, Any]:
        """Call an external API"""
        try:
            import requests
            
            url = config.get('url', '').format(**trigger_data)
            method = config.get('method', 'GET').upper()
            headers = config.get('headers', {})
            data = config.get('data', {})
            
            # Apply template variables to data
            for key, value in data.items():
                if isinstance(value, str) and '{' in value:
                    data[key] = value.format(**trigger_data)
            
            response = requests.request(method, url, headers=headers, json=data, timeout=30)
            
            return {
                'success': response.status_code < 400,
                'status_code': response.status_code,
                'response': response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text
            }
        except Exception as e:
            logger.error(f"Failed to call API: {str(e)}")
            return {'success': False, 'error': str(e)}

    def _generate_report(self, config: Dict[str, Any], trigger_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a report"""
        try:
            report_type = config.get('type', 'inventory')
            format_type = config.get('format', 'pdf')
            
            # Integration with report generation logic
            return {'success': True, 'report_type': report_type, 'format': format_type}
        except Exception as e:
            logger.error(f"Failed to generate report: {str(e)}")
            return {'success': False, 'error': str(e)}

    # Built-in Condition Evaluators
    def _evaluate_threshold(self, condition: Dict[str, Any], data: Dict[str, Any]) -> bool:
        """Evaluate threshold conditions"""
        field = condition.get('field')
        operator = condition.get('operator', '>')
        threshold = condition.get('value')
        
        if field not in data:
            return False
        
        value = data[field]
        
        if operator == '>':
            return value > threshold
        elif operator == '>=':
            return value >= threshold
        elif operator == '<':
            return value < threshold
        elif operator == '<=':
            return value <= threshold
        elif operator == '==':
            return value == threshold
        elif operator == '!=':
            return value != threshold
        
        return False

    def _evaluate_time_based(self, condition: Dict[str, Any], data: Dict[str, Any]) -> bool:
        """Evaluate time-based conditions"""
        time_field = condition.get('field', 'timestamp')
        time_operator = condition.get('operator', 'within')
        time_value = condition.get('value')  # in minutes
        
        if time_field not in data:
            return False
        
        event_time = datetime.fromisoformat(data[time_field]) if isinstance(data[time_field], str) else data[time_field]
        current_time = datetime.now()
        time_diff = (current_time - event_time).total_seconds() / 60  # in minutes
        
        if time_operator == 'within':
            return time_diff <= time_value
        elif time_operator == 'after':
            return time_diff > time_value
        
        return False

    def _evaluate_inventory_level(self, condition: Dict[str, Any], data: Dict[str, Any]) -> bool:
        """Evaluate inventory level conditions"""
        # This would integrate with your inventory checking logic
        lot_id = condition.get('lot_id') or data.get('lot_id')
        min_level = condition.get('min_level', 0)
        
        # Mock implementation - replace with actual inventory check
        current_level = data.get('current_inventory', 0)
        return current_level <= min_level

    def _evaluate_custom_script(self, condition: Dict[str, Any], data: Dict[str, Any]) -> bool:
        """Evaluate custom script conditions (be careful with security)"""
        try:
            script = condition.get('script', 'True')
            # Very basic and limited evaluation - consider using a safer approach
            # This is just for demonstration
            allowed_names = {'data': data, 'len': len, 'str': str, 'int': int, 'float': float}
            return eval(script, {"__builtins__": {}}, allowed_names)
        except Exception as e:
            logger.error(f"Error evaluating custom script: {str(e)}")
            return False

    def get_execution_status(self, execution_id: str) -> Optional[WorkflowExecution]:
        """Get the status of a workflow execution"""
        return self.executions.get(execution_id)

    def get_rule_statistics(self) -> Dict[str, Any]:
        """Get statistics about workflow rules and executions"""
        total_rules = len(self.rules)
        enabled_rules = sum(1 for rule in self.rules.values() if rule.enabled)
        total_executions = len(self.executions)
        
        status_counts = {}
        for execution in self.executions.values():
            status = execution.status.value
            status_counts[status] = status_counts.get(status, 0) + 1
        
        return {
            'total_rules': total_rules,
            'enabled_rules': enabled_rules,
            'total_executions': total_executions,
            'execution_status_counts': status_counts
        }

    def cleanup_old_executions(self, days_old: int = 30):
        """Clean up old workflow executions"""
        cutoff_date = datetime.now() - timedelta(days=days_old)
        
        to_remove = [
            exec_id for exec_id, execution in self.executions.items()
            if execution.completed_at and execution.completed_at < cutoff_date
        ]
        
        for exec_id in to_remove:
            del self.executions[exec_id]
        
        logger.info(f"Cleaned up {len(to_remove)} old workflow executions")


# Global workflow engine instance
workflow_engine = WorkflowEngine()

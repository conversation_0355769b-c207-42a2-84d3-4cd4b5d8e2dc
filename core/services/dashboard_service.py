# Description: This file contains the implementation of the dashboard_service.py module. This module is responsible for fetching data from the database and Asana API to provide statistics for the dashboard. The get_dashboard_stats function is an API endpoint that retrieves inventory statistics from the database and Asana statistics using the dashboard service. The function combines the data and returns it as a JSON response. If an error occurs during the process, an error message is returned with a 500 status code.

import asana
import logging
from datetime import datetime, timedelta
from collections import defaultdict

logger = logging.getLogger(__name__)


class DashboardService:
    """Service for retrieving dashboard data from Asana"""

    def __init__(self, asana_token):
        """Initialize with Asana API token"""
        self.asana_token = asana_token
        self.client = None
        self.api_client = None
        self.tasks_api = None
        self.sections_api = None
        self.users_api = None
        self.initialize_client()

        # Mapping of section names to their respective status categories
        self.section_status_map = {
            "Incoming": "incoming",
            "Planned": "planned",
            "In preparation": "in_preparation",
            "Packaged": "packaged",
            "Waiting for Review": "waiting_review",
            "Sent": "sent",
            "Delivered": "delivered",
            "Returned": "returned",
        }

        # Set Asana workspace and project IDs
        self.workspace_gid = "1204968822616977"  # From your code
        self.shipment_project_gid = (
            "1206397258493005"  # Shipment project from your code
        )

        # Cache the section GIDs to avoid repeated API calls
        self.section_gids = {}

    def initialize_client(self):
        """Initialize Asana client"""
        if not self.asana_token:
            logger.error("Asana token not provided")
            return False
        try:
            # Configure Asana client
            configuration = asana.Configuration()
            configuration.access_token = self.asana_token

            # Add more debugging
            logger.info(
                f"Initializing Asana client with token: {self.asana_token[:5]}..."
            )

            # Create API client
            self.api_client = asana.ApiClient(configuration)

            # Create specific API instances as needed
            self.tasks_api = asana.TasksApi(self.api_client)
            self.sections_api = asana.SectionsApi(self.api_client)
            self.users_api = asana.UsersApi(self.api_client)

            # Set client flag to indicate successful initialization
            self.client = True

            # Verify connection by making a simple API call (with opts parameter)
            opts = {"opt_pretty": True, "opt_fields": ["name"]}
            user = self.users_api.get_user("me", opts=opts)
            logger.info(
                f"Asana client initialized successfully for user: {user.get('name', 'Elisee Kajingu')}"
            )

            return True
        except Exception as e:
            logger.error(f"Error initializing Asana client: {str(e)}")
            self.api_client = None
            self.client = None
            return False

    def get_shipment_stats(self, project_gid=None):
        """
        Get shipment statistics from Asana

        Returns:
            dict: Dictionary containing shipment statistics
        """
        if not self.client:
            logger.error("Asana client not initialized")
            return {}

        if not project_gid:
            project_gid = self.shipment_project_gid

        try:
            # Initialize stats dictionary
            stats = {
                "total_shipments": 0,
                "active_shipments": 0,
                "on_time_rate": 0,
                "section_counts": {},
            }

            # Get sections first if not cached
            if not self.section_gids:
                self.section_gids = self.get_section_gids(project_gid)

            # Fetch tasks from the project
            opts = {
                "opt_fields": [
                    "name",
                    "completed",
                    "completed_at",
                    "due_on",
                    "memberships.section.name",
                    "custom_fields.name",
                    "custom_fields.display_value",
                    "custom_fields.enum_value",
                ]
            }

            tasks = list(self.tasks_api.get_tasks_for_project(project_gid, opts=opts))

            # Process tasks
            total_tasks = len(tasks)
            on_time_tasks = 0
            active_tasks = 0
            section_counts = defaultdict(int)

            for task in tasks:
                task_data = task.to_dict() if hasattr(task, "to_dict") else task

                # Skip tasks that don't start with "Additional-shipment" or "Shipment"
                task_name = task_data.get("name", "")
                if not (
                    task_name.startswith("Additional-shipment:")
                    or task_name.startswith("Shipment :")
                ):
                    continue

                # Count by section
                for membership in task_data.get("memberships", []):
                    if "section" in membership and "name" in membership["section"]:
                        section_name = membership["section"]["name"]
                        section_counts[section_name] += 1

                # Check if active
                if not task_data.get("completed", False):
                    active_tasks += 1

                    # Check if on time
                    due_on = task_data.get("due_on")
                    if due_on:
                        due_date = datetime.strptime(due_on, "%Y-%m-%d").date()
                        if datetime.now().date() <= due_date:
                            on_time_tasks += 1

            # Calculate on-time rate (for active tasks only)
            on_time_rate = round(
                (on_time_tasks / active_tasks * 100) if active_tasks > 0 else 0, 1
            )

            # Compile stats
            stats = {
                "total_shipments": total_tasks,
                "active_shipments": active_tasks,
                "on_time_rate": on_time_rate,
                "section_counts": dict(section_counts),
            }

            return stats

        except Exception as e:
            logger.error(f"Error getting shipment stats: {str(e)}")
            return {}

    def get_section_gids(self, project_gid):
        """Get all section GIDs for a project"""
        try:
            # Using the sections endpoint with opts parameter
            opts = {"opt_pretty": True}  # Add the required opts parameter
            sections = self.sections_api.get_sections_for_project(
                project_gid, opts=opts
            )

            # Build section GID map
            section_gids = {}
            for section in sections:
                section_data = (
                    section.to_dict() if hasattr(section, "to_dict") else section
                )
                section_gids[section_data.get("name")] = section_data.get("gid")

            return section_gids

        except Exception as e:
            logger.error(f"Error getting section GIDs: {str(e)}")
            return {}

    def get_section_distribution(self, project_gid=None):
        """
        Get section distribution data for chart display

        Returns:
            dict: Dictionary containing labels and datasets for the chart
        """
        if not self.client:
            logger.error("Asana client not initialized")
            return {"labels": [], "datasets": [{"data": []}]}

        if not project_gid:
            project_gid = self.shipment_project_gid

        try:
            # Get shipment stats which includes section counts
            stats = self.get_shipment_stats(project_gid)
            section_counts = stats.get("section_counts", {})

            # Define sections of interest in the order you want them displayed
            sections_of_interest = [
                "Incoming",
                "Planned",
                "In preparation",
                "Packaged",
                "Waiting for Review",
                "Sent",
                "Delivered",
            ]

            # Prepare data for chart
            labels = []
            data = []

            # Add data for each section of interest
            for section in sections_of_interest:
                if section in section_counts:
                    labels.append(section)
                    data.append(section_counts[section])

            # Handle the case when no data is found
            if not labels:
                return {
                    "labels": sections_of_interest,
                    "datasets": [{"data": [0] * len(sections_of_interest)}],
                }

            return {"labels": labels, "datasets": [{"data": data}]}

        except Exception as e:
            logger.error(f"Error getting section distribution: {str(e)}")
            return {"labels": [], "datasets": [{"data": []}]}

    def get_monthly_deliveries(self, project_gid=None):
        """
        Get monthly delivery data for chart display

        Returns:
            dict: Dictionary containing labels and datasets for the chart
        """
        if not self.client:
            logger.error("Asana client not initialized")
            return {"labels": [], "datasets": [{"data": []}]}

        if not project_gid:
            project_gid = self.shipment_project_gid

        try:
            # Fetch completed tasks from the project with completed_at dates
            opts = {"opt_fields": ["name", "completed", "completed_at"]}

            tasks = list(self.tasks_api.get_tasks_for_project(project_gid, opts=opts))

            # Get last 12 months including current month
            today = datetime.now()
            months = []
            for i in range(11, -1, -1):
                month_date = (
                    today - timedelta(days=today.day - 1) - timedelta(days=30 * i)
                )
                months.append(month_date.strftime("%B %Y"))

            # Count deliveries by month
            monthly_counts = defaultdict(int)

            for task in tasks:
                task_data = task.to_dict() if hasattr(task, "to_dict") else task

                # Skip tasks that don't start with "Additional-shipment" or "Shipment"
                task_name = task_data.get("name", "")
                if not (
                    task_name.startswith("Additional-shipment:")
                    or task_name.startswith("Shipment :")
                ):
                    continue

                # Only count completed tasks
                if task_data.get("completed", False) and task_data.get("completed_at"):
                    completed_at = datetime.fromisoformat(
                        task_data["completed_at"].replace("Z", "+00:00")
                    )
                    month_key = completed_at.strftime("%B %Y")

                    # Only count if it's within our 12 month window
                    if month_key in months:
                        monthly_counts[month_key] += 1

            # Prepare data for chart - ensure all months are included
            data = []
            for month in months:
                data.append(monthly_counts.get(month, 0))

            # Calculate target values (slightly increasing over the year)
            target_data = []
            base_target = 5
            for i in range(12):
                if i < 3:
                    target_data.append(base_target)
                elif i < 6:
                    target_data.append(base_target + 3)
                elif i < 9:
                    target_data.append(base_target + 7)
                else:
                    target_data.append(base_target + 10)

            return {
                "labels": months,
                "datasets": [
                    {"label": "Deliveries", "data": data},
                    {"label": "Target", "data": target_data},
                ],
            }

        except Exception as e:
            logger.error(f"Error getting monthly deliveries: {str(e)}")
            return {"labels": [], "datasets": [{"data": []}]}

    def get_recent_shipments(self, project_gid=None, limit=5):
        """
        Get recent shipment tasks

        Args:
            project_gid: The project GID to fetch tasks from
            limit: Maximum number of shipments to return

        Returns:
            dict: Dictionary containing recent shipments and pagination info
        """
        if not self.client:
            logger.error("Asana client not initialized")
            return {"shipments": [], "pagination": {"total": 0}}

        if not project_gid:
            project_gid = self.shipment_project_gid

        try:
            # Fetch tasks from the project, sorted by creation time
            opts = {
                "opt_fields": [
                    "name",
                    "created_at",
                    "modified_at",
                    "completed",
                    "memberships.section.name",
                    "custom_fields.name",
                    "custom_fields.display_value",
                    "custom_fields.enum_value",
                ],
                "limit": limit,
            }

            tasks = list(self.tasks_api.get_tasks_for_project(project_gid, opts=opts))

            # Process tasks into shipment format
            shipments = []

            for task in tasks:
                task_data = task.to_dict() if hasattr(task, "to_dict") else task

                # Skip tasks that don't start with "Additional-shipment" or "Shipment"
                task_name = task_data.get("name", "")
                if not (
                    task_name.startswith("Additional-shipment:")
                    or task_name.startswith("Shipment :")
                ):
                    continue

                # Extract task title
                if task_name.startswith("Additional-shipment:"):
                    title = task_name.replace("Additional-shipment:", "").strip()
                else:
                    title = task_name.replace("Shipment :", "").strip()

                # Determine status from section
                status = "Unknown"
                for membership in task_data.get("memberships", []):
                    if "section" in membership and "name" in membership["section"]:
                        section_name = membership["section"]["name"]
                        status = section_name
                        break

                # Extract destination and wafer count from custom fields
                destination = ""
                wafer_count = 0

                for field in task_data.get("custom_fields", []):
                    if field.get("name") == "Shipping address":
                        destination = field.get("display_value", "")
                    elif field.get("name") == "Number of Wafers":
                        try:
                            wafer_count = int(field.get("display_value", "0"))
                        except (ValueError, TypeError):
                            wafer_count = 0

                # Create shipment object
                shipment = {
                    "id": f"SHP-{task_data.get('created_at', '')[:10].replace('-', '')[-4:]}-{task_data.get('gid', '')[-4:]}",
                    "status": status,
                    "destination": (
                        destination.split(",")[0] if destination else "Unknown"
                    ),
                    "date": datetime.fromisoformat(
                        task_data.get("modified_at", "").replace("Z", "+00:00")
                    ).strftime("%b %d, %Y"),
                    "wafers": wafer_count,
                }

                shipments.append(shipment)

                # Limit to requested number
                if len(shipments) >= limit:
                    break

            return {
                "shipments": shipments,
                "pagination": {"total": len(shipments), "limit": limit},
            }

        except Exception as e:
            logger.error(f"Error getting recent shipments: {str(e)}")
            return {"shipments": [], "pagination": {"total": 0}}

    def verify_connection(self):
        """Verify Asana API connection"""
        if not self.client:
            self.initialize_client()

        if not self.client:
            return False

        try:
            # Try a simple API call
            opts = {"opt_pretty": True, "opt_fields": ["name"]}
            user = self.users_api.get_user("me", opts=opts)
            return True
        except Exception as e:
            logger.error(f"Asana API connection failed: {str(e)}")
            return False

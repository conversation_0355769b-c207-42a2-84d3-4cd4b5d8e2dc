"""Label generation service for Talaria Dashboard."""

import logging
import os
import socket
from typing import Any, Dict

import qrcode
from flask import current_app
from PIL import Image, ImageDraw, ImageFont
from qrcode.constants import ERROR_CORRECT_L
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import cm
from reportlab.pdfgen import canvas
from reportlab.platypus import Image as RLImage
from reportlab.platypus import SimpleDocTemplate

logger = logging.getLogger(__name__)


class LabelGeneratorService:
    """Service for generating labels and packing slips."""

    def __init__(self):
        """Initialize the label generator service."""
        self.downloads_dir = self._setup_downloads_directory()
        self.static_dir = os.path.join(current_app.root_path, "static")

    def _setup_downloads_directory(self) -> str:
        """Setup downloads directory for generated files."""
        downloads_dir = os.path.join(current_app.root_path, "downloads", "labels")
        os.makedirs(downloads_dir, exist_ok=True)
        return downloads_dir

    def generate_label(self, form_data: Dict) -> Dict:
        """Generate a label based on the provided form data.

        Args:
            form_data: Dictionary containing form fields

        Returns:
            Dict with success status, file paths, and print status
        """
        try:
            # Create file paths
            base_filename = f"{form_data['project_name']}_{form_data['shipment_date']}"
            # Sanitize filename
            base_filename = "".join(
                c for c in base_filename if c.isalnum() or c in (" ", "-", "_")
            ).rstrip()
            base_filename = base_filename.replace(" ", "_")

            qr_image_path = os.path.join(self.downloads_dir, f"qr_{base_filename}.png")
            text_image_path = os.path.join(
                self.downloads_dir, f"text_{base_filename}.png"
            )
            packing_image_path = os.path.join(
                self.downloads_dir, f"packing_{base_filename}.png"
            )
            packing_pdf_path = os.path.join(
                self.downloads_dir, f"packing_{base_filename}.pdf"
            )
            label_pdf_path = os.path.join(
                self.downloads_dir, f"label_{base_filename}.pdf"
            )

            # Generate text content
            text_content = self._generate_text_content(form_data)

            # Generate QR code
            qr_img = self._generate_qr_code(form_data["wafer_list"], qr_image_path)

            # Create label image with text and QR code
            self._create_label_image(
                form_data["label_type"], text_content, qr_img, text_image_path
            )

            # Create packing slip
            self._create_packing_slip(
                form_data["label_type"],
                text_content,
                qr_img,
                form_data["comments"],
                packing_image_path,
                packing_pdf_path,
            )

            # Create final PDF label
            self._create_pdf_label(text_image_path, label_pdf_path)

            # Clean up temporary files
            for file_path in [qr_image_path, text_image_path, packing_image_path]:
                if os.path.exists(file_path):
                    os.remove(file_path)

            # Print the label if requested
            print_success = False
            if form_data["copy_number"] > 0 and form_data["printer_ip"]:
                print_success = self._print_label(
                    label_pdf_path, form_data["printer_ip"], form_data["copy_number"]
                )

            return {
                "success": True,
                "label_path": label_pdf_path,
                "packing_path": packing_pdf_path,
                "print_success": print_success,
            }

        except Exception as e:
            logger.error(f"Error generating label: {str(e)}")
            return {"success": False, "error": str(e)}

    def _generate_text_content(self, form_data: Dict) -> str:
        """Generate the text content for the label."""
        text_content = ""
        project_name = form_data["project_name"]

        # Format project name if too long
        if len(project_name) > 40:
            project_name = project_name[0:39] + "\n" + project_name[39:]

        # Generate content based on label type
        if "2" not in form_data["label_type"]:
            text_content += f"Project : {project_name}\n\n"
            text_content += f"Wafers number : {form_data['wafer_number']}\n\n"
            text_content += f"Date : {form_data['shipment_date']}"
        else:
            text_content += f"Purchase order : {form_data['po']}\n\n"
            text_content += f"Project ID : {form_data['device_id']}\n\n"
            text_content += f"Lot: {form_data['xfab_lot_id']}\n\n"
            text_content += f"Wafers number : {form_data['wafer_number']}\n\n"
            text_content += f"Date : {form_data['shipment_date']}"

        # Add wafer list
        wafer_list = form_data["wafer_list"]
        slot_id_list = [e.split(",")[0] for e in wafer_list.split(";") if "," in e]
        wafer_id_list = [e.split(",")[1] for e in wafer_list.split(";") if "," in e]

        text_content += "\n\nSlot ID,Wafer ID\n\n"
        for i in range(0, len(slot_id_list), 2):
            line = ""
            if i < len(slot_id_list):
                line += f"{slot_id_list[i]},{wafer_id_list[i]}  "
            if i + 1 < len(slot_id_list):
                line += f"{slot_id_list[i+1]},{wafer_id_list[i+1]}"
            text_content += line + "\n"

        return text_content

    def _generate_qr_code(self, wafer_list: str, qr_image_path: str) -> Any:
        """Generate a QR code containing the wafer list."""
        qr = qrcode.QRCode(
            version=1,
            error_correction=ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(wafer_list)
        qr.make(fit=True)

        img = qr.make_image(fill_color="black", back_color="white")
        img.save(qr_image_path)
        return img

    def _get_font_path(self, font_name: str = "Arial") -> str:
        """Get the path to a system font."""
        # Try different font paths based on the system
        font_paths = [
            "/System/Library/Fonts/Supplemental/Arial.ttf",  # macOS
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # Linux
            "C:\\Windows\\Fonts\\arial.ttf",  # Windows
            "/usr/share/fonts/TTF/arial.ttf",  # Some Linux distributions
        ]

        for path in font_paths:
            if os.path.exists(path):
                return path

        # Fallback - try to find any TTF font
        try:
            import glob

            fonts = glob.glob("/usr/share/fonts/**/*.ttf", recursive=True)
            if fonts:
                return fonts[0]
        except:
            pass

        # Last resort fallback
        return "/System/Library/Fonts/Supplemental/Arial.ttf"

    def _create_label_image(
        self,
        label_type: str,
        text_content: str,
        qr_img: Image.Image,
        text_image_path: str,
    ) -> None:
        """Create the label image with text and QR code."""
        # Convert cm to points (1 cm = 28.3465 points)
        image_width = int(9 * 28.3465 * 3)
        image_height = int(15 * 28.3465 * 3)
        text_image = Image.new("RGB", (image_width, image_height), (255, 255, 255))

        # Add logo if not a free label
        if "1" not in label_type:
            logo_path = os.path.join(self.static_dir, "img", "logo.gif")
            if os.path.exists(logo_path):
                try:
                    logo = Image.open(logo_path)
                    position_logo = (50, 50)
                    text_image.paste(logo, position_logo)
                except Exception as e:
                    logger.warning(f"Could not load logo: {str(e)}")

        # Add QR code
        position_qr = (420, 950)
        qr_size = int(11 * 28.3465)
        text_image.paste(qr_img.resize((qr_size, qr_size)), position_qr)

        # Add text
        draw = ImageDraw.Draw(text_image)
        font_path = self._get_font_path()
        try:
            font = ImageFont.truetype(font_path, 30)
        except Exception:
            font = ImageFont.load_default()

        position_text = (70, 100) if "1" in label_type else (70, 200)
        draw.text(position_text, text_content, fill=(0, 0, 0), font=font)

        # Save the image
        text_image.save(text_image_path)

    def _create_packing_slip(
        self,
        label_type: str,
        text_content: str,
        qr_img: Image.Image,
        comments: str,
        packing_image_path: str,
        packing_pdf_path: str,
    ) -> None:
        """Create a packing slip PDF."""
        # Create packing slip image
        width, height = 2480, 3500
        packing_image = Image.new("RGB", (width, height), (255, 255, 255))
        draw = ImageDraw.Draw(packing_image)

        # Add logo and address if not a free label
        if "1" not in label_type:
            logo_path = os.path.join(self.static_dir, "img", "logo.gif")
            if os.path.exists(logo_path):
                try:
                    logo = Image.open(logo_path)
                    position_logo = (20, 20)
                    packing_image.paste(logo, position_logo)
                except Exception as e:
                    logger.warning(f"Could not load logo for packing slip: {str(e)}")

            # Add Ligentec address
            position_text = (40, 170)
            ligentec_address = (
                "LIGENTEC France SAS\n224 Boulevard John Kennedy\n"
                "91100 Corbeil-Essonnes\nFrance"
            )
            font_path = self._get_font_path()
            try:
                font = ImageFont.truetype(font_path, 40)
            except Exception:
                font = ImageFont.load_default()
            draw.text(position_text, ligentec_address, fill=(0, 0, 0), font=font)

        # Add title
        try:
            font = ImageFont.truetype(self._get_font_path(), 100)
        except Exception:
            font = ImageFont.load_default()
        draw.text((1000, 270), "Packing slip", fill=(0, 0, 0), font=font)

        # Add content
        try:
            font = ImageFont.truetype(self._get_font_path(), 70)
        except Exception:
            font = ImageFont.load_default()
        draw.text((100, 700), text_content, fill=(0, 0, 0), font=font)

        # Add comments if provided
        if comments:
            draw.text(
                (100, 2000), f"Comments :\n\n{comments}", fill=(0, 0, 0), font=font
            )

        # Add QR code
        position_qr = (2000, 2000)
        qr_size = int(11 * 28.3465)
        packing_image.paste(qr_img.resize((qr_size, qr_size)), position_qr)

        # Save the image
        packing_image.save(packing_image_path)

        # Convert to PDF
        doc = SimpleDocTemplate(packing_pdf_path, pagesize=A4)
        elements = []
        packing_img = RLImage(packing_image_path)
        packing_img.drawHeight = 22 * cm
        packing_img.drawWidth = 16 * cm
        elements.append(packing_img)
        doc.build(elements)

    def _create_pdf_label(self, text_image_path: str, label_pdf_path: str) -> None:
        """Create the final PDF label."""
        # Convert cm to points
        image_width = 9 * 28.3465
        image_height = 15 * 28.3465

        # Create PDF
        c = canvas.Canvas(label_pdf_path, pagesize=(image_width, image_height))
        c.drawImage(text_image_path, 0, 0, width=image_width, height=image_height)
        c.showPage()
        c.save()

    def _print_label(self, label_path: str, printer_ip: str, copy_number: int) -> bool:
        """Print the label to a network printer."""
        try:
            # Connect to printer
            printer_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            printer_socket.settimeout(10)  # 10 second timeout
            printer_socket.connect((printer_ip, 9100))  # Standard printer port

            # Send PDF data
            with open(label_path, "rb") as pdf_file:
                pdf_data = pdf_file.read()
                for _ in range(copy_number):
                    printer_socket.sendall(pdf_data)

            printer_socket.close()
            logger.info(f"Successfully printed {copy_number} copies to {printer_ip}")
            return True
        except Exception as e:
            logger.error(f"Failed to print to {printer_ip}: {str(e)}")
            return False

"""
Smart Sync Engine
Provides intelligent recommendations for syncing wafers from Icarium to Talaria
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from collections import defaultdict

from database.db_config import get_db_cursor

# Configure logging
logger = logging.getLogger(__name__)


class SmartSyncEngine:
    """Engine for generating intelligent sync recommendations"""

    def __init__(self):
        self.priority_weights = {
            'lot_size': 0.3,        # Larger lots get higher priority
            'age': 0.25,            # Older wafers get higher priority
            'location': 0.2,        # Certain locations get priority
            'status': 0.15,         # Critical status gets priority
            'pattern': 0.1          # Historical patterns
        }

    def generate_smart_recommendations(self, hours_back: int = 168) -> Dict:
        """
        Generate intelligent sync recommendations
        
        Args:
            hours_back: Hours to look back for analysis (default: 1 week)
            
        Returns:
            Dictionary with recommendations and analysis
        """
        try:
            logger.info(f"🧠 Generating smart sync recommendations (last {hours_back} hours)")
            
            # Get unsynced wafers
            unsynced_wafers = self._get_unsynced_wafers(hours_back)
            
            if not unsynced_wafers:
                return {
                    'success': True,
                    'recommendations': [],
                    'analysis': {'message': 'No wafers need syncing'},
                    'total_wafers': 0
                }
            
            # Analyze patterns and generate recommendations
            lot_analysis = self._analyze_lots(unsynced_wafers)
            location_analysis = self._analyze_locations(unsynced_wafers)
            timing_analysis = self._analyze_timing_patterns(unsynced_wafers)
            priority_analysis = self._calculate_priorities(unsynced_wafers)
            
            # Generate specific recommendations
            recommendations = []
            
            # Batch sync recommendations
            batch_recs = self._generate_batch_recommendations(lot_analysis, priority_analysis)
            recommendations.extend(batch_recs)
            
            # Location-based recommendations
            location_recs = self._generate_location_recommendations(location_analysis)
            recommendations.extend(location_recs)
            
            # Time-sensitive recommendations
            urgent_recs = self._generate_urgent_recommendations(unsynced_wafers, timing_analysis)
            recommendations.extend(urgent_recs)
            
            # Efficiency recommendations
            efficiency_recs = self._generate_efficiency_recommendations(unsynced_wafers)
            recommendations.extend(efficiency_recs)
            
            # Sort recommendations by priority
            recommendations.sort(key=lambda x: x['priority_score'], reverse=True)
            
            # Generate analysis summary
            analysis = self._generate_analysis_summary(
                unsynced_wafers, lot_analysis, location_analysis, timing_analysis
            )
            
            logger.info(f"✅ Generated {len(recommendations)} smart recommendations")
            
            return {
                'success': True,
                'recommendations': recommendations[:10],  # Top 10 recommendations
                'analysis': analysis,
                'total_wafers': len(unsynced_wafers),
                'lots_affected': len(lot_analysis),
                'locations_affected': len(location_analysis)
            }
            
        except Exception as e:
            logger.error(f"❌ Error generating smart recommendations: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'recommendations': [],
                'analysis': {}
            }

    def _get_unsynced_wafers(self, hours_back: int) -> List[Dict]:
        """Get wafers that exist in Icarium but not in Talaria"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours_back)
            
            with get_db_cursor() as cursor:
                cursor.execute("""
                    SELECT 
                        i.wafer_id,
                        i.lot_id,
                        i.location_id,
                        i.status,
                        i.created_at,
                        i.updated_at
                    FROM wafers i
                    LEFT JOIN wafer_inventory t ON i.wafer_id = t.wafer_id
                    WHERE t.wafer_id IS NULL
                    AND i.created_at >= %s
                    ORDER BY i.created_at DESC
                """, (cutoff_time,))
                
                results = cursor.fetchall()
                
                return [
                    {
                        'wafer_id': row['wafer_id'],
                        'lot_id': row['lot_id'],
                        'location_id': row['location_id'],
                        'status': row['status'],
                        'created_at': row['created_at'],
                        'updated_at': row['updated_at'],
                        'age_hours': (datetime.now() - row['created_at']).total_seconds() / 3600
                    }
                    for row in results
                ]
                
        except Exception as e:
            logger.error(f"❌ Error getting unsynced wafers: {str(e)}")
            return []

    def _analyze_lots(self, wafers: List[Dict]) -> Dict:
        """Analyze wafers by lot"""
        lots = defaultdict(list)
        
        for wafer in wafers:
            lots[wafer['lot_id']].append(wafer)
        
        lot_analysis = {}
        for lot_id, lot_wafers in lots.items():
            lot_analysis[lot_id] = {
                'wafer_count': len(lot_wafers),
                'avg_age_hours': sum(w['age_hours'] for w in lot_wafers) / len(lot_wafers),
                'locations': list(set(w['location_id'] for w in lot_wafers if w['location_id'])),
                'statuses': list(set(w['status'] for w in lot_wafers if w['status'])),
                'oldest_wafer': max(lot_wafers, key=lambda w: w['age_hours']),
                'newest_wafer': min(lot_wafers, key=lambda w: w['age_hours'])
            }
        
        return lot_analysis

    def _analyze_locations(self, wafers: List[Dict]) -> Dict:
        """Analyze wafers by location"""
        locations = defaultdict(list)
        
        for wafer in wafers:
            location = wafer['location_id'] or 'Unknown'
            locations[location].append(wafer)
        
        location_analysis = {}
        for location, location_wafers in locations.items():
            location_analysis[location] = {
                'wafer_count': len(location_wafers),
                'lots': list(set(w['lot_id'] for w in location_wafers)),
                'avg_age_hours': sum(w['age_hours'] for w in location_wafers) / len(location_wafers),
                'priority_score': self._calculate_location_priority(location, len(location_wafers))
            }
        
        return location_analysis

    def _analyze_timing_patterns(self, wafers: List[Dict]) -> Dict:
        """Analyze timing patterns in wafer creation"""
        # Group by hour of day and day of week
        hourly_pattern = defaultdict(int)
        daily_pattern = defaultdict(int)
        
        for wafer in wafers:
            created_at = wafer['created_at']
            hourly_pattern[created_at.hour] += 1
            daily_pattern[created_at.strftime('%A')] += 1
        
        return {
            'hourly_distribution': dict(hourly_pattern),
            'daily_distribution': dict(daily_pattern),
            'peak_hour': max(hourly_pattern.items(), key=lambda x: x[1])[0] if hourly_pattern else None,
            'peak_day': max(daily_pattern.items(), key=lambda x: x[1])[0] if daily_pattern else None
        }

    def _calculate_priorities(self, wafers: List[Dict]) -> Dict:
        """Calculate priority scores for wafers"""
        priorities = {}
        
        for wafer in wafers:
            score = 0
            
            # Age factor (older = higher priority)
            age_score = min(wafer['age_hours'] / 168, 1.0)  # Normalize to 1 week
            score += age_score * self.priority_weights['age']
            
            # Location factor
            location_score = self._get_location_score(wafer['location_id'])
            score += location_score * self.priority_weights['location']
            
            # Status factor
            status_score = self._get_status_score(wafer['status'])
            score += status_score * self.priority_weights['status']
            
            priorities[wafer['wafer_id']] = {
                'total_score': score,
                'age_score': age_score,
                'location_score': location_score,
                'status_score': status_score
            }
        
        return priorities

    def _get_location_score(self, location: Optional[str]) -> float:
        """Get priority score for location"""
        location_priorities = {
            'Ligentec FR': 1.0,
            'Production': 0.9,
            'QC': 0.8,
            'Storage': 0.6,
            'Unknown': 0.3
        }
        return location_priorities.get(location, 0.5)

    def _get_status_score(self, status: Optional[str]) -> float:
        """Get priority score for status"""
        status_priorities = {
            'Critical': 1.0,
            'Ready': 0.9,
            'Available': 0.7,
            'Processing': 0.6,
            'Hold': 0.3,
            'Unknown': 0.4
        }
        return status_priorities.get(status, 0.5)

    def _generate_batch_recommendations(self, lot_analysis: Dict, priorities: Dict) -> List[Dict]:
        """Generate batch sync recommendations"""
        recommendations = []
        
        for lot_id, analysis in lot_analysis.items():
            if analysis['wafer_count'] >= 3:  # Batch threshold
                # Calculate average priority for the lot
                lot_wafers = [w for w in priorities.keys() if any(
                    w.startswith(lot_id) for w in priorities.keys()
                )]
                
                avg_priority = sum(priorities.get(w, {}).get('total_score', 0) for w in lot_wafers) / len(lot_wafers) if lot_wafers else 0
                
                # Boost priority for larger batches
                size_boost = min(analysis['wafer_count'] / 20, 0.5)
                priority_score = avg_priority + size_boost
                
                recommendations.append({
                    'id': f"batch_sync_{lot_id}",
                    'type': 'batch_sync',
                    'title': f"Batch Sync Lot {lot_id}",
                    'description': f"Sync {analysis['wafer_count']} wafers from lot {lot_id} in one operation",
                    'priority_score': priority_score,
                    'impact': 'high' if analysis['wafer_count'] > 10 else 'medium',
                    'effort': 'low',
                    'time_estimate': f"{analysis['wafer_count'] * 2} minutes",
                    'benefits': [
                        f"Sync {analysis['wafer_count']} wafers efficiently",
                        "Reduce individual sync overhead",
                        "Maintain lot integrity"
                    ],
                    'data': {
                        'lot_id': lot_id,
                        'wafer_count': analysis['wafer_count'],
                        'locations': analysis['locations'],
                        'avg_age_hours': analysis['avg_age_hours']
                    },
                    'action': {
                        'type': 'batch_sync_lot',
                        'parameters': {'lot_id': lot_id}
                    }
                })
        
        return recommendations

    def _generate_location_recommendations(self, location_analysis: Dict) -> List[Dict]:
        """Generate location-based recommendations"""
        recommendations = []
        
        for location, analysis in location_analysis.items():
            if analysis['wafer_count'] >= 5 and analysis['priority_score'] > 0.7:
                recommendations.append({
                    'id': f"location_sync_{location.replace(' ', '_')}",
                    'type': 'location_sync',
                    'title': f"Priority Sync: {location}",
                    'description': f"Sync {analysis['wafer_count']} wafers from high-priority location {location}",
                    'priority_score': analysis['priority_score'],
                    'impact': 'high',
                    'effort': 'medium',
                    'time_estimate': f"{analysis['wafer_count'] * 3} minutes",
                    'benefits': [
                        f"Clear {location} backlog",
                        "Improve location efficiency",
                        "Reduce location conflicts"
                    ],
                    'data': {
                        'location': location,
                        'wafer_count': analysis['wafer_count'],
                        'lots_affected': len(analysis['lots'])
                    },
                    'action': {
                        'type': 'sync_by_location',
                        'parameters': {'location': location}
                    }
                })
        
        return recommendations

    def _generate_urgent_recommendations(self, wafers: List[Dict], timing_analysis: Dict) -> List[Dict]:
        """Generate urgent/time-sensitive recommendations"""
        recommendations = []
        
        # Find wafers older than 48 hours
        urgent_wafers = [w for w in wafers if w['age_hours'] > 48]
        
        if urgent_wafers:
            recommendations.append({
                'id': 'urgent_old_wafers',
                'type': 'urgent_sync',
                'title': '🚨 Urgent: Old Wafers Need Sync',
                'description': f"{len(urgent_wafers)} wafers are over 48 hours old and still unsynced",
                'priority_score': 0.95,
                'impact': 'critical',
                'effort': 'medium',
                'time_estimate': f"{len(urgent_wafers) * 2} minutes",
                'benefits': [
                    "Prevent data staleness",
                    "Maintain sync freshness",
                    "Avoid inventory discrepancies"
                ],
                'data': {
                    'wafer_count': len(urgent_wafers),
                    'oldest_age_hours': max(w['age_hours'] for w in urgent_wafers),
                    'lots_affected': len(set(w['lot_id'] for w in urgent_wafers))
                },
                'action': {
                    'type': 'sync_urgent_wafers',
                    'parameters': {'max_age_hours': 48}
                }
            })
        
        return recommendations

    def _generate_efficiency_recommendations(self, wafers: List[Dict]) -> List[Dict]:
        """Generate efficiency-focused recommendations"""
        recommendations = []
        
        # Group by similar characteristics for efficient processing
        status_groups = defaultdict(list)
        for wafer in wafers:
            status_groups[wafer['status'] or 'Unknown'].append(wafer)
        
        for status, status_wafers in status_groups.items():
            if len(status_wafers) >= 5:
                recommendations.append({
                    'id': f"efficiency_sync_{status.replace(' ', '_')}",
                    'type': 'efficiency_sync',
                    'title': f"Efficient Sync: {status} Wafers",
                    'description': f"Sync {len(status_wafers)} wafers with {status} status together for efficiency",
                    'priority_score': 0.6 + (len(status_wafers) / 100),  # Boost for larger groups
                    'impact': 'medium',
                    'effort': 'low',
                    'time_estimate': f"{len(status_wafers) * 1.5} minutes",
                    'benefits': [
                        "Process similar wafers together",
                        "Reduce context switching",
                        "Improve sync efficiency"
                    ],
                    'data': {
                        'status': status,
                        'wafer_count': len(status_wafers),
                        'lots_involved': len(set(w['lot_id'] for w in status_wafers))
                    },
                    'action': {
                        'type': 'sync_by_status',
                        'parameters': {'status': status}
                    }
                })
        
        return recommendations

    def _calculate_location_priority(self, location: str, wafer_count: int) -> float:
        """Calculate priority score for a location"""
        base_score = self._get_location_score(location)
        
        # Boost score based on wafer count
        count_boost = min(wafer_count / 50, 0.3)
        
        return min(base_score + count_boost, 1.0)

    def _generate_analysis_summary(self, wafers: List[Dict], lot_analysis: Dict, 
                                 location_analysis: Dict, timing_analysis: Dict) -> Dict:
        """Generate comprehensive analysis summary"""
        total_wafers = len(wafers)
        avg_age = sum(w['age_hours'] for w in wafers) / total_wafers if wafers else 0
        
        # Find patterns
        largest_lot = max(lot_analysis.items(), key=lambda x: x[1]['wafer_count']) if lot_analysis else None
        busiest_location = max(location_analysis.items(), key=lambda x: x[1]['wafer_count']) if location_analysis else None
        
        return {
            'total_unsynced_wafers': total_wafers,
            'average_age_hours': round(avg_age, 1),
            'lots_affected': len(lot_analysis),
            'locations_affected': len(location_analysis),
            'largest_lot': {
                'lot_id': largest_lot[0],
                'wafer_count': largest_lot[1]['wafer_count']
            } if largest_lot else None,
            'busiest_location': {
                'location': busiest_location[0],
                'wafer_count': busiest_location[1]['wafer_count']
            } if busiest_location else None,
            'timing_insights': {
                'peak_creation_hour': timing_analysis.get('peak_hour'),
                'peak_creation_day': timing_analysis.get('peak_day')
            },
            'sync_efficiency_score': self._calculate_sync_efficiency_score(wafers, lot_analysis),
            'recommendations_summary': {
                'high_priority_actions': sum(1 for _ in lot_analysis.values() if _['wafer_count'] >= 10),
                'batch_opportunities': sum(1 for _ in lot_analysis.values() if _['wafer_count'] >= 3),
                'urgent_items': sum(1 for w in wafers if w['age_hours'] > 48)
            }
        }

    def _calculate_sync_efficiency_score(self, wafers: List[Dict], lot_analysis: Dict) -> float:
        """Calculate overall sync efficiency score (0-100)"""
        if not wafers:
            return 100.0
        
        # Factors that improve efficiency
        batch_potential = sum(1 for analysis in lot_analysis.values() if analysis['wafer_count'] >= 3)
        total_lots = len(lot_analysis)
        
        # Age factor (newer is better for efficiency)
        avg_age_hours = sum(w['age_hours'] for w in wafers) / len(wafers)
        age_efficiency = max(0, 100 - (avg_age_hours / 168 * 50))  # Penalty for old wafers
        
        # Batch efficiency
        batch_efficiency = (batch_potential / total_lots * 100) if total_lots > 0 else 100
        
        # Overall efficiency score
        efficiency_score = (age_efficiency * 0.6) + (batch_efficiency * 0.4)
        
        return round(min(efficiency_score, 100), 1)

"""
Notification service for managing in-app notifications.
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Optional

from core.models.notification_models import (
    EmailNotificationData,
    Notification,
    NotificationCreate,
)
from database.auth_db_config import get_auth_db_connection

logger = logging.getLogger(__name__)


class NotificationService:
    """Service for managing notifications."""

    def __init__(self):
        self.db_connection = None

    def get_db_session(self):
        """Get database session."""
        if not self.db_connection:
            self.db_connection = get_auth_db_connection()
        return self.db_connection

    def create_notification(
        self, notification_data: NotificationCreate
    ) -> Optional[Notification]:
        """Create a new notification."""
        try:
            conn = self.get_db_session()
            cursor = conn.cursor()

            # Insert notification
            insert_query = """
                INSERT INTO notifications (
                    user_id, title, message, notification_type, is_email_related,
                    email_subject, email_sender, email_recipients, related_entity_type,
                    related_entity_id, extra_data, expires_at
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING id, created_at, updated_at
            """

            cursor.execute(
                insert_query,
                (
                    notification_data.user_id,
                    notification_data.title,
                    notification_data.message,
                    notification_data.notification_type,
                    notification_data.is_email_related,
                    notification_data.email_subject,
                    notification_data.email_sender,
                    (
                        json.dumps(notification_data.email_recipients)
                        if notification_data.email_recipients
                        else None
                    ),
                    notification_data.related_entity_type,
                    notification_data.related_entity_id,
                    (
                        json.dumps(notification_data.extra_data)
                        if notification_data.extra_data
                        else None
                    ),
                    notification_data.expires_at,
                ),
            )

            result = cursor.fetchone()
            conn.commit()

            if result:
                logger.info(
                    f"Created notification {result[0]} for user "
                    f"{notification_data.user_id}"
                )
                return result[0]  # Return notification ID

        except Exception as e:
            logger.error(f"Error creating notification: {str(e)}")
            if self.db_connection:
                self.db_connection.rollback()
            return None

    def get_user_notifications(
        self, user_id: int, limit: int = 50, unread_only: bool = False
    ) -> List[Dict]:
        """Get notifications for a user."""
        try:
            conn = self.get_db_session()
            cursor = conn.cursor()

            where_clause = "WHERE user_id = %s"
            params = [user_id]

            if unread_only:
                where_clause += " AND is_read = FALSE"

            # Add expiration check
            where_clause += (
                " AND (expires_at IS NULL OR expires_at > CURRENT_TIMESTAMP)"
            )

            query = f"""
                SELECT id, user_id, title, message, notification_type, is_read,
                       is_email_related, email_subject, email_sender, email_recipients,
                       related_entity_type, related_entity_id, extra_data,
                       created_at, updated_at, expires_at
                FROM notifications
                {where_clause}
                ORDER BY created_at DESC
                LIMIT %s
            """

            params.append(limit)
            cursor.execute(query, params)
            results = cursor.fetchall()

            notifications = []
            for row in results:
                notification = {
                    "id": row[0],
                    "user_id": row[1],
                    "title": row[2],
                    "message": row[3],
                    "notification_type": row[4],
                    "is_read": row[5],
                    "is_email_related": row[6],
                    "email_subject": row[7],
                    "email_sender": row[8],
                    "email_recipients": json.loads(row[9]) if row[9] else None,
                    "related_entity_type": row[10],
                    "related_entity_id": row[11],
                    "extra_data": json.loads(row[12]) if row[12] else None,
                    "created_at": row[13].isoformat() if row[13] else None,
                    "updated_at": row[14].isoformat() if row[14] else None,
                    "expires_at": row[15].isoformat() if row[15] else None,
                }
                notifications.append(notification)

            return notifications

        except Exception as e:
            logger.error(f"Error getting user notifications: {str(e)}")
            return []

    def get_unread_count(self, user_id: int) -> int:
        """Get count of unread notifications for a user."""
        try:
            conn = self.get_db_session()
            cursor = conn.cursor()

            query = """
                SELECT COUNT(*)
                FROM notifications
                WHERE user_id = %s AND is_read = FALSE
                AND (expires_at IS NULL OR expires_at > CURRENT_TIMESTAMP)
            """

            cursor.execute(query, (user_id,))
            result = cursor.fetchone()
            return result[0] if result else 0

        except Exception as e:
            logger.error(f"Error getting unread count for user {user_id}: {str(e)}")
            logger.error(f"Exception type: {type(e).__name__}")
            if self.db_connection:
                try:
                    self.db_connection.rollback()
                except Exception:
                    pass
            return 0

    def mark_as_read(self, notification_id: int, user_id: int) -> bool:
        """Mark a notification as read."""
        try:
            conn = self.get_db_session()
            cursor = conn.cursor()

            query = """
                UPDATE notifications 
                SET is_read = TRUE, updated_at = CURRENT_TIMESTAMP
                WHERE id = %s AND user_id = %s
            """

            cursor.execute(query, (notification_id, user_id))
            conn.commit()

            return cursor.rowcount > 0

        except Exception as e:
            logger.error(f"Error marking notification as read: {str(e)}")
            if self.db_connection:
                self.db_connection.rollback()
            return False

    def mark_all_as_read(self, user_id: int) -> bool:
        """Mark all notifications as read for a user."""
        try:
            conn = self.get_db_session()
            cursor = conn.cursor()

            query = """
                UPDATE notifications 
                SET is_read = TRUE, updated_at = CURRENT_TIMESTAMP
                WHERE user_id = %s AND is_read = FALSE
            """

            cursor.execute(query, (user_id,))
            conn.commit()

            logger.info(
                f"Marked {cursor.rowcount} notifications as read for user {user_id}"
            )
            return True

        except Exception as e:
            logger.error(f"Error marking all notifications as read: {str(e)}")
            if self.db_connection:
                self.db_connection.rollback()
            return False

    def delete_notification(self, notification_id: int, user_id: int) -> bool:
        """Delete a notification."""
        try:
            conn = self.get_db_session()
            cursor = conn.cursor()

            query = "DELETE FROM notifications WHERE id = %s AND user_id = %s"
            cursor.execute(query, (notification_id, user_id))
            conn.commit()

            return cursor.rowcount > 0

        except Exception as e:
            logger.error(f"Error deleting notification: {str(e)}")
            if self.db_connection:
                self.db_connection.rollback()
            return False

    def create_email_notification(
        self, user_id: int, email_data: EmailNotificationData
    ) -> Optional[int]:
        """Create a notification when an email is sent."""
        try:
            # Create notification data
            notification_data = NotificationCreate(
                user_id=user_id,
                title=f"Email Sent: {email_data.subject}",
                message=(
                    f"Email sent to {', '.join(email_data.recipients[:3])}"
                    f"{'...' if len(email_data.recipients) > 3 else ''}"
                ),
                notification_type="email",
                is_email_related=True,
                email_subject=email_data.subject,
                email_sender=email_data.sender,
                email_recipients=json.dumps(email_data.recipients),
                related_entity_type=email_data.related_entity_type,
                related_entity_id=email_data.related_entity_id,
                extra_data={
                    "email_body_preview": (
                        email_data.body[:200] + "..."
                        if len(email_data.body) > 200
                        else email_data.body
                    ),
                    "cc_recipients": email_data.cc or [],
                    "timestamp": datetime.now().isoformat(),
                },
            )

            return self.create_notification(notification_data)

        except Exception as e:
            logger.error(f"Error creating email notification: {str(e)}")
            return None

    def cleanup_expired_notifications(self) -> int:
        """Clean up expired notifications."""
        try:
            conn = self.get_db_session()
            cursor = conn.cursor()

            query = (
                "DELETE FROM notifications "
                "WHERE expires_at IS NOT NULL "
                "AND expires_at < CURRENT_TIMESTAMP"
            )
            cursor.execute(query)
            conn.commit()

            deleted_count = cursor.rowcount
            logger.info(f"Cleaned up {deleted_count} expired notifications")
            return deleted_count

        except Exception as e:
            logger.error(f"Error cleaning up expired notifications: {str(e)}")
            if self.db_connection:
                self.db_connection.rollback()
            return 0


# Global notification service instance
notification_service = NotificationService()

# core/auth/user_manager.py
import bcrypt
import os
import psycopg2
import psycopg2.extras
from database.db_config import get_db_connection  # noqa F401


def get_auth_db_connection():
    """Get a connection to the authentication database."""
    # Get connection parameters from environment variables or files
    host = os.getenv("AUTH_DB_HOST", "pgdb.lig.local")
    host_file = os.getenv("AUTH_DB_HOST_FILE")
    if host_file and os.path.exists(host_file):
        with open(host_file, "r") as f:
            host = f.read().strip()

    dbname = os.getenv("AUTH_DB_NAME", "shipment_fr")
    dbname_file = os.getenv("AUTH_DB_NAME_FILE")
    if dbname_file and os.path.exists(dbname_file):
        with open(dbname_file, "r") as f:
            dbname = f.read().strip()

    user = os.getenv("AUTH_DB_USER", "operations_fr_inventory")
    user_file = os.getenv("AUTH_DB_USER_FILE")
    if user_file and os.path.exists(user_file):
        with open(user_file, "r") as f:
            user = f.read().strip()

    password = os.getenv("AUTH_DB_PASSWORD", "")
    password_file = os.getenv("AUTH_DB_PASSWORD_FILE")
    if password_file and os.path.exists(password_file):
        with open(password_file, "r") as f:
            password = f.read().strip()

    # Connect to the database with DictCursor
    conn = psycopg2.connect(
        host=host,
        dbname=dbname,
        user=user,
        password=password,
        cursor_factory=psycopg2.extras.RealDictCursor,
    )
    return conn


class UserManager:
    @staticmethod
    def get_user_by_email(email):
        """Get a user by email address"""
        conn = None
        cursor = None
        try:
            conn = get_auth_db_connection()
            cursor = conn.cursor()

            # Check if the columns exist
            cursor.execute(
                """
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'users' AND column_name IN ('profile_image', 'remember_token')
            """
            )
            existing_columns = [row["column_name"] for row in cursor.fetchall()]

            # Build the query based on existing columns
            base_columns = (
                "id, email, name, password_hash, role, created_at, last_login"
            )
            additional_columns = ""

            if "profile_image" in existing_columns:
                additional_columns += ", profile_image"

            if "remember_token" in existing_columns:
                additional_columns += ", remember_token"

            query = f"""
                SELECT {base_columns}{additional_columns}
                FROM users
                WHERE email = %s
            """

            cursor.execute(query, (email,))
            user = cursor.fetchone()

            # Add missing fields with default values if they don't exist in the database
            if user:
                if "profile_image" not in existing_columns:
                    user["profile_image"] = None

                if "remember_token" not in existing_columns:
                    user["remember_token"] = None

            return user
        except Exception as e:
            print(f"Error in get_user_by_email: {e}")
            if conn:
                conn.rollback()
            return None
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()

    @staticmethod
    def get_user_by_id(user_id):
        """Get a user by ID"""
        conn = None
        cursor = None
        try:
            conn = get_auth_db_connection()
            cursor = conn.cursor()

            # Check if the columns exist
            cursor.execute(
                """
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'users' AND column_name IN ('profile_image', 'remember_token')
            """
            )
            existing_columns = [row["column_name"] for row in cursor.fetchall()]

            # Build the query based on existing columns
            base_columns = (
                "id, email, name, password_hash, role, created_at, last_login"
            )
            additional_columns = ""

            if "profile_image" in existing_columns:
                additional_columns += ", profile_image"

            if "remember_token" in existing_columns:
                additional_columns += ", remember_token"

            query = f"""
                SELECT {base_columns}{additional_columns}
                FROM users
                WHERE id = %s
            """

            cursor.execute(query, (user_id,))
            user = cursor.fetchone()

            # Add missing fields with default values if they don't exist in the database
            if user:
                if "profile_image" not in existing_columns:
                    user["profile_image"] = None

                if "remember_token" not in existing_columns:
                    user["remember_token"] = None

            return user
        except Exception as e:
            print(f"Error in get_user_by_id: {e}")
            if conn:
                conn.rollback()
            return None
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()

    @staticmethod
    def add_user(email, name, role, password):
        """Add a new user"""
        conn = get_auth_db_connection()
        cursor = conn.cursor()

        try:
            # Hash the password
            password_hash = bcrypt.hashpw(
                password.encode("utf-8"), bcrypt.gensalt()
            ).decode("utf-8")

            query = (
                "INSERT INTO users (email, name, password_hash, role) "
                "VALUES (%s, %s, %s, %s) "
                "RETURNING id"
            )
            cursor.execute(query, (email, name, password_hash, role))

            result = cursor.fetchone()
            # With RealDictCursor, result is a dict
            user_id = result["id"] if result else None
            conn.commit()
            return user_id
        except Exception as e:
            conn.rollback()
            print(f"Error adding user: {e}")
            return None
        finally:
            cursor.close()
            conn.close()

    @staticmethod
    def update_user(user_id, name=None, role=None, password=None):
        """Update user information"""
        conn = get_auth_db_connection()
        cursor = conn.cursor()

        try:
            updates = []
            params = []

            if name is not None:
                updates.append("name = %s")
                params.append(name)

            if role is not None:
                updates.append("role = %s")
                params.append(role)

            if password is not None:
                password_hash = bcrypt.hashpw(
                    password.encode("utf-8"), bcrypt.gensalt()
                ).decode("utf-8")
                updates.append("password_hash = %s")
                params.append(password_hash)

            if not updates:
                return True  # Nothing to update

            query = f"UPDATE users SET {', '.join(updates)} WHERE id = %s"
            params.append(user_id)

            cursor.execute(query, params)
            conn.commit()
            return True
        except Exception as e:
            conn.rollback()
            print(f"Error updating user: {e}")
            return False
        finally:
            cursor.close()
            conn.close()

    @staticmethod
    def delete_user(user_id):
        """Delete a user"""
        conn = get_auth_db_connection()
        cursor = conn.cursor()

        try:
            cursor.execute("DELETE FROM users WHERE id = %s", (user_id,))
            conn.commit()
            return True
        except Exception as e:
            conn.rollback()
            print(f"Error deleting user: {e}")
            return False
        finally:
            cursor.close()
            conn.close()

    @staticmethod
    def get_all_users():
        """Get all users"""
        conn = get_auth_db_connection()
        cursor = conn.cursor()

        try:
            query = """
                SELECT id, email, name, role, created_at, last_login
                FROM users
                ORDER BY name
            """
            cursor.execute(query)
            users = cursor.fetchall()
            # With RealDictCursor, each result is already a dict
            return users
        finally:
            cursor.close()
            conn.close()

    @staticmethod
    def update_last_login(user_id):
        """Update the last login timestamp for a user"""
        conn = get_auth_db_connection()
        cursor = conn.cursor()

        try:
            query = "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = %s"
            cursor.execute(query, (user_id,))
            conn.commit()
            return True
        except Exception as e:
            conn.rollback()
            print(f"Error updating last login: {e}")
            return False
        finally:
            cursor.close()
            conn.close()

    @staticmethod
    def verify_password(stored_hash, password):
        """Verify a password against a stored hash"""
        return bcrypt.checkpw(password.encode("utf-8"), stored_hash.encode("utf-8"))

    @staticmethod
    def check_password(user, password):
        """Check if the provided password matches the user's password hash"""
        if not user or not password:
            return False
        return UserManager.verify_password(user["password_hash"], password)

    @staticmethod
    def get_user_permissions(role):
        """Get permissions for a specific user role"""
        # Define permissions for different roles
        permissions = {
            "admin": [
                "view",
                "create",
                "modify",
                "delete",
                "manage_users",
                "access_admin",
                "sync",
            ],
            "colleague": ["view"],
            "viewer": ["view"],
        }

        # Return permissions for the given role or empty list if role not found
        return permissions.get(role, [])

    @staticmethod
    def update_profile_image(user_id, image_path):
        """Update the profile image for a user"""
        conn = get_auth_db_connection()
        cursor = conn.cursor()

        try:
            # Check if the profile_image column exists
            cursor.execute(
                """
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'users' AND column_name = 'profile_image'
            """
            )
            column_exists = cursor.fetchone() is not None

            if not column_exists:
                # Column doesn't exist yet, so we need to add it
                cursor.execute(
                    """
                    ALTER TABLE users ADD COLUMN profile_image VARCHAR(255)
                """
                )
                conn.commit()

            # Now we can set the image path
            query = "UPDATE users SET profile_image = %s WHERE id = %s"
            cursor.execute(query, (image_path, user_id))
            conn.commit()
            return True
        except Exception as e:
            conn.rollback()
            print(f"Error updating profile image: {e}")
            return False
        finally:
            cursor.close()
            conn.close()

    @staticmethod
    def set_remember_token(user_id, token):
        """Set the remember token for a user"""
        conn = get_auth_db_connection()
        cursor = conn.cursor()

        try:
            # Check if the remember_token column exists
            cursor.execute(
                """
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'users' AND column_name = 'remember_token'
            """
            )
            column_exists = cursor.fetchone() is not None

            if not column_exists:
                # Column doesn't exist yet, so we need to add it
                cursor.execute(
                    """
                    ALTER TABLE users ADD COLUMN remember_token VARCHAR(255)
                """
                )
                conn.commit()

            # Now we can set the token
            query = "UPDATE users SET remember_token = %s WHERE id = %s"
            cursor.execute(query, (token, user_id))
            conn.commit()
            return True
        except Exception as e:
            conn.rollback()
            print(f"Error setting remember token: {e}")
            return False
        finally:
            cursor.close()
            conn.close()

    @staticmethod
    def get_user_by_remember_token(token):
        """Get a user by remember token"""
        # Check if the remember_token column exists
        conn = get_auth_db_connection()
        cursor = conn.cursor()

        try:
            # Check if the remember_token column exists
            cursor.execute(
                """
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'users' AND column_name = 'remember_token'
            """
            )
            column_exists = cursor.fetchone() is not None

            if not column_exists:
                # Column doesn't exist yet, so no user can be found
                return None

            # Column exists, proceed with the query
            query = """
                SELECT id, email, name, password_hash, role, created_at, last_login,
                profile_image, remember_token
                FROM users
                WHERE remember_token = %s
            """
            cursor.execute(query, (token,))
            user = cursor.fetchone()
            return user
        except Exception as e:
            print(f"Error in get_user_by_remember_token: {e}")
            return None
        finally:
            cursor.close()
            conn.close()

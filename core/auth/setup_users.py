import bcrypt
import os
import sys
import psycopg2
from pathlib import Path
from dotenv import load_dotenv

# Add the project root to the path so we can import from database
sys.path.insert(0, str(Path(__file__).resolve().parent.parent.parent))

# Load environment variables from .env file
env_path = Path(__file__).resolve().parent.parent.parent / ".env"
load_dotenv(dotenv_path=env_path)


def get_auth_db_connection():
    """Get a connection to the authentication database."""
    # Get connection parameters from environment variables
    host = os.getenv("AUTH_DB_HOST", "pgdb.lig.local")
    dbname = os.getenv("AUTH_DB_NAME", "shipment_fr")
    user = os.getenv("AUTH_DB_USER", "operations_fr_inventory")
    password = os.getenv("AUTH_DB_PASSWORD", "")

    print(f"Connecting to database: {dbname} on host: {host} as user: {user}")

    # Connect to the database
    conn = psycopg2.connect(host=host, dbname=dbname, user=user, password=password)
    return conn


def get_password_hashes():
    """Generate hashed passwords from environment variables or defaults."""
    # Get passwords from environment or use defaults
    master_password = os.getenv("MASTER_PASSWORD", "Lgt_kylia5169")
    colleague_password = os.getenv("COLLEAGUE_PASSWORD", "LigentecFR")

    # Generate password hashes
    master_hash = bcrypt.hashpw(
        master_password.encode("utf-8"), bcrypt.gensalt()
    ).decode("utf-8")
    colleague_hash = bcrypt.hashpw(
        colleague_password.encode("utf-8"), bcrypt.gensalt()
    ).decode("utf-8")

    return master_hash, colleague_hash


def clear_existing_users(cursor, conn):
    """Delete existing users from the database."""
    try:
        cursor.execute("DELETE FROM users")
        print("Deleted existing users")
    except Exception as e:
        conn.rollback()
        print(f"Error deleting users: {e}")
        print("Continuing with user creation...")


def create_admin_users(cursor, master_hash):
    """Create admin users with the master password hash."""
    # Admin users to create
    admin_users = [
        ("<EMAIL>", "Elisee Kajingu", "admin"),
        ("<EMAIL>", "Khalil Kechaou", "admin"),
        ("<EMAIL>", "Alexandru Romanescu", "admin"),
    ]

    for email, name, role in admin_users:
        try:
            cursor.execute(
                """INSERT INTO users (email, name, password_hash, role)
                VALUES (%s, %s, %s, %s)""",
                (email, name, master_hash, role),
            )
            print(f"Created admin user: {email}")
        except Exception as e:
            print(f"Error creating admin user {email}: {e}")


def create_colleague_users(cursor, colleague_hash):
    """Create colleague users with the colleague password hash."""
    # Colleague users to create with real names and emails
    colleague_users = [
        ("<EMAIL>", "Alaa Foudhaili", "colleague"),
        ("****************", "Giulio Tavani", "colleague"),
        ("<EMAIL>", "Moustapha Jaffal", "colleague"),
        ("<EMAIL>", "Houssein El Dirani", "colleague"),
        ("<EMAIL>", "Alexandre Bazin", "colleague"),
    ]

    for email, name, role in colleague_users:
        try:
            cursor.execute(
                """INSERT INTO users (email, name, password_hash, role)
                VALUES (%s, %s, %s, %s)""",
                (email, name, colleague_hash, role),
            )
            print(f"Created colleague user: {email}")
        except Exception as e:
            print(f"Error creating colleague user {email}: {e}")


def verify_users(cursor):
    """Verify and display the users that were created."""
    try:
        cursor.execute("SELECT id, email, name, role FROM users ORDER BY role, name")
        users = cursor.fetchall()
        print("\nCreated users:")
        for user in users:
            print(f"ID: {user[0]}, Email: {user[1]}, Name: {user[2]}, Role: {user[3]}")
    except Exception as e:
        print(f"Error verifying users: {e}")


def setup_users():
    """Set up admin and colleague users in the database."""
    conn = None
    cursor = None

    try:
        # Connect to the database
        conn = get_auth_db_connection()
        cursor = conn.cursor()

        # Print connection details
        print("Database connection established")
        cursor.execute("SELECT current_database()")
        current_db = cursor.fetchone()[0]
        print(f"Connected to database: {current_db}")

        # Get password hashes
        master_hash, colleague_hash = get_password_hashes()

        # Clear existing users
        clear_existing_users(cursor, conn)

        # Create users
        create_admin_users(cursor, master_hash)
        create_colleague_users(cursor, colleague_hash)

        # Commit changes
        conn.commit()
        print("All users created successfully")

        # Verify users
        verify_users(cursor)

    except Exception as e:
        print(f"Error setting up users: {e}")
        import traceback

        traceback.print_exc()
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()


if __name__ == "__main__":
    setup_users()

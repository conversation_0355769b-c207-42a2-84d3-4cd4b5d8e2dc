# core/auth/user_routes.py
import os
import psycopg2  # noqa: F401
from flask import Blueprint, render_template, request, redirect, url_for, flash, session
from core.auth.auth import login_required, admin_required
from core.auth.user_manager import User<PERSON>anager, get_auth_db_connection
from utils.settings_manager import is_valid_email

user_bp = Blueprint("users", __name__, url_prefix="/users")


@user_bp.route("/")
@login_required
@admin_required
def manage_users():
    """User management dashboard"""
    users = UserManager.get_all_users()
    return render_template("admin/users.html", users=users)


@user_bp.route("/add", methods=["GET", "POST"])
@login_required
@admin_required
def add_user():
    """Add a new user"""
    if request.method == "POST":
        email = request.form.get("email", "").lower()
        name = request.form.get("name", "")
        role = request.form.get("role", "colleague")
        password_type = request.form.get("password_type", "default")

        # Validate inputs
        if not email or not name:
            flash("Email and name are required.", "error")
            return render_template("admin/add_user.html")

        if not is_valid_email(email):
            flash("Please enter a valid email address.", "error")
            return render_template("admin/add_user.html")

        # Check if user already exists
        existing_user = UserManager.get_user_by_email(email)
        if existing_user:
            flash("A user with this email already exists.", "error")
            return render_template("admin/add_user.html")

        # Determine password based on role and password type
        if password_type == "custom":
            custom_password = request.form.get("custom_password", "")
            if not custom_password:
                flash("Custom password is required when selected.", "error")
                return render_template("admin/add_user.html")
            password = custom_password
        else:
            # Use default password based on role
            if role == "admin":
                password = os.getenv("MASTER_PASSWORD", "Lgt_kylia5169")
            else:
                password = os.getenv("COLLEAGUE_PASSWORD", "LigentecFR")

        # Add the user
        user_id = UserManager.add_user(email, name, role, password)
        if user_id:
            flash(f"User {email} added successfully.", "success")
            return redirect(url_for("users.manage_users"))
        else:
            flash("Error adding user.", "error")
            return render_template("admin/add_user.html")

    return render_template("admin/add_user.html")


@user_bp.route("/edit/<int:user_id>", methods=["GET", "POST"])
@login_required
@admin_required
def edit_user(user_id):
    """Edit an existing user"""
    user = UserManager.get_user_by_id(user_id)
    if not user:
        flash("User not found.", "error")
        return redirect(url_for("users.manage_users"))

    if request.method == "POST":
        name = request.form.get("name", "")
        role = request.form.get("role", "colleague")
        password_type = request.form.get("password_type", "no_change")

        # Validate inputs
        if not name:
            flash("Name is required.", "error")
            return render_template("admin/edit_user.html", user=user)

        # Determine if password should be updated
        password = None
        if password_type == "custom":
            custom_password = request.form.get("custom_password", "")
            if not custom_password:
                flash("Custom password is required when selected.", "error")
                return render_template("admin/edit_user.html", user=user)
            password = custom_password
        elif password_type == "default":
            # Reset to default password based on role
            if role == "admin":
                password = os.getenv("MASTER_PASSWORD", "Lgt_kylia5169")
            else:
                password = os.getenv("COLLEAGUE_PASSWORD", "LigentecFR")

        # Update the user
        success = UserManager.update_user(
            user_id, name=name, role=role, password=password
        )
        if success:
            flash(f"User {user['email']} updated successfully.", "success")
            return redirect(url_for("users.manage_users"))
        else:
            flash("Error updating user.", "error")
            return render_template("admin/edit_user.html", user=user)

    return render_template("admin/edit_user.html", user=user)


@user_bp.route("/delete/<int:user_id>", methods=["POST"])
@login_required
@admin_required
def delete_user(user_id):
    """Delete a user"""
    # Prevent deleting yourself
    if user_id == session.get("user_id"):
        flash("You cannot delete your own account.", "error")
        return redirect(url_for("users.manage_users"))

    user = UserManager.get_user_by_id(user_id)
    if not user:
        flash("User not found.", "error")
        return redirect(url_for("users.manage_users"))

    success = UserManager.delete_user(user_id)
    if success:
        flash(f"User {user['email']} deleted successfully.", "success")
    else:
        flash("Error deleting user.", "error")

    return redirect(url_for("users.manage_users"))


@user_bp.route("/database-viewer")
@login_required
@admin_required
def database_viewer():
    """Admin-only database viewer to see the contents of database tables"""
    # Get requested table name from query parameters
    table_name = request.args.get("table_name", "")
    page = request.args.get("page", 1, type=int)
    per_page = 25  # Number of rows per page

    conn = get_auth_db_connection()
    cursor = conn.cursor()

    try:
        # Get list of all tables in the database
        cursor.execute(
            """
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'public'
            ORDER BY table_name
        """
        )
        available_tables = [row["table_name"] for row in cursor.fetchall()]

        rows = []
        columns = []
        total_rows = 0
        total_pages = 1

        # If a table is selected, get its data
        if table_name and table_name in available_tables:
            # First, get column names
            cursor.execute(
                """
                SELECT column_name
                FROM information_schema.columns
                WHERE table_schema = 'public'
                AND table_name = %s
                ORDER BY ordinal_position
            """,
                (table_name,),
            )
            columns = [row["column_name"] for row in cursor.fetchall()]

            # Get total row count for pagination
            cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
            total_rows = cursor.fetchone()["count"]
            total_pages = (total_rows + per_page - 1) // per_page  # Ceiling division

            # Get data with pagination
            offset = (page - 1) * per_page
            cursor.execute(
                f"SELECT * FROM {table_name} LIMIT %s OFFSET %s", (per_page, offset)
            )
            rows = cursor.fetchall()

        return render_template(
            "admin/database_viewer.html",
            available_tables=available_tables,
            current_table=table_name,
            columns=columns,
            rows=rows,
            current_page=page,
            total_pages=total_pages,
            total_rows=total_rows,
        )

    except Exception as e:
        flash(f"Error accessing database: {str(e)}", "error")
        return render_template(
            "admin/database_viewer.html",
            available_tables=[],
            current_table="",
            columns=[],
            rows=[],
            current_page=1,
            total_pages=1,
            total_rows=0,
        )
    finally:
        cursor.close()
        conn.close()

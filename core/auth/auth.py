# core/auth/auth.py
from flask import (
    Blueprint,
    render_template,
    redirect,
    url_for,
    request,
    flash,
    session,
    # g,
)
from functools import wraps
import re
from werkzeug.security import check_password_hash, generate_password_hash  # noqa: F401
from database.auth_schema import setup_auth_tables
from core.auth.user_manager import UserManager

# Create a Blueprint for auth-related routes
auth_bp = Blueprint("auth", __name__)

# Ensure auth tables exist
setup_auth_tables()

# Password configuration
COLLEAGUE_PASSWORD = "LigentecFR"  # Shared password for regular users
MASTER_PASSWORD = "Lgt_kylia5169"  # Master key for admins


@auth_bp.route("/login", methods=["GET", "POST"])
def login():
    # Check if user is already logged in
    if session.get("user_email"):
        return redirect(url_for("home"))

    if request.method == "POST":
        email = request.form.get("username").lower()
        password = request.form.get("password")
        remember_me = request.form.get("remember-me") == "on"

        # Validate email format
        if not is_valid_email(email):
            flash("Please enter a valid email address.", "error")
            return render_template("login.html")

        # Get user from database
        user = UserManager.get_user_by_email(email)

        if user:
            # Check if it's an admin user
            if user["role"] == "admin":
                # Admin can use master password or their specific password
                if password == MASTER_PASSWORD or UserManager.check_password(
                    user, password
                ):
                    # Login successful for admin
                    session["user_id"] = user["id"]
                    session["user_email"] = user["email"]
                    session["user_role"] = user["role"]
                    session["user_name"] = user["name"]

                    # Update last login time
                    UserManager.update_last_login(user["id"])

                    # Handle remember me
                    response = redirect(url_for("home"))
                    if remember_me:
                        # Generate and store remember token
                        import secrets

                        token = secrets.token_hex(32)
                        UserManager.set_remember_token(user["id"], token)

                        # Set cookie to expire in 30 days
                        response.set_cookie(
                            "remember_token",
                            token,
                            max_age=30 * 24 * 60 * 60,
                            httponly=True,
                            secure=request.is_secure,
                        )

                    flash(f"Welcome, {session['user_name']}!", "success")
                    return response
                else:
                    flash("Invalid admin credentials.", "error")
                    return render_template("login.html")
            else:
                # Regular user can use colleague password or their specific password
                if password == COLLEAGUE_PASSWORD or UserManager.check_password(
                    user, password
                ):
                    # Login successful for colleague
                    session["user_id"] = user["id"]
                    session["user_email"] = user["email"]
                    session["user_role"] = user["role"]
                    session["user_name"] = user["name"]

                    # Update last login time
                    UserManager.update_last_login(user["id"])

                    # Handle remember me
                    response = redirect(url_for("home"))
                    if remember_me:
                        # Generate and store remember token
                        import secrets

                        token = secrets.token_hex(32)
                        UserManager.set_remember_token(user["id"], token)

                        # Set cookie to expire in 30 days
                        response.set_cookie(
                            "remember_token",
                            token,
                            max_age=30 * 24 * 60 * 60,
                            httponly=True,
                            secure=request.is_secure,
                        )

                    flash(f"Welcome, {session['user_name']}!", "success")
                    return response
                else:
                    flash("Invalid credentials.", "error")
                    return render_template("login.html")
        else:
            # User doesn't exist, but we'll allow login with colleague password
            # This maintains backward compatibility
            if password == COLLEAGUE_PASSWORD:
                # Create a new user account automatically
                name = email.split("@")[0].title()
                user_id = UserManager.add_user(
                    email, name, "colleague", COLLEAGUE_PASSWORD
                )

                if user_id:
                    session["user_id"] = user_id
                    session["user_email"] = email
                    session["user_role"] = "colleague"
                    session["user_name"] = name

                    # Handle remember me
                    response = redirect(url_for("home"))
                    if remember_me:
                        # Generate and store remember token
                        import secrets

                        token = secrets.token_hex(32)
                        UserManager.set_remember_token(user_id, token)

                        # Set cookie to expire in 30 days
                        response.set_cookie(
                            "remember_token",
                            token,
                            max_age=30 * 24 * 60 * 60,
                            httponly=True,
                            secure=request.is_secure,
                        )

                    flash(f"Welcome, {session['user_name']}!", "success")
                    return response

            flash("Invalid credentials.", "error")
            return render_template("login.html")

    # GET request - show login form
    return render_template("login.html")


@auth_bp.route("/logout")
def logout():
    # Get user ID before clearing session
    user_id = session.get("user_id")

    # Clear all session data
    session.clear()

    # Clear remember token in database if user was logged in
    if user_id:
        UserManager.set_remember_token(user_id, None)

    # Create response and clear remember token cookie
    response = redirect(url_for("auth.login"))
    response.delete_cookie("remember_token")

    flash("You have been logged out successfully.", "success")
    return response


def is_valid_email(email):
    """Check if the provided email is valid."""
    if not email:
        return False
    # Simple regex for email validation
    pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
    return bool(re.match(pattern, email))


# Decorator to require login
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if "user_email" not in session:
            flash("Please log in to access this page.", "error")
            return redirect(url_for("auth.login"))
        return f(*args, **kwargs)

    return decorated_function


# Decorator to require admin role
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if "user_role" not in session or session["user_role"] != "admin":
            flash("You need admin privileges to access this page.", "error")
            return redirect(url_for("home"))
        return f(*args, **kwargs)

    return decorated_function


# Decorator to check specific permissions
def permission_required(permission):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if "user_role" not in session:
                flash("Please log in to access this page.", "error")
                return redirect(url_for("auth.login"))

            user_role = session["user_role"]
            permissions = UserManager.get_user_permissions(user_role)

            if permission not in permissions:
                flash(f"You do not have permission to {permission}.", "error")
                return redirect(url_for("home"))

            return f(*args, **kwargs)

        return decorated_function

    return decorator


# Function to check permissions in templates and JavaScript
def check_permission(permission):
    """Check if the current user has a specific permission."""
    if "user_role" not in session:
        return False

    user_role = session["user_role"]
    permissions = UserManager.get_user_permissions(user_role)
    return permission in permissions


# Function to check if user is authenticated
def is_authenticated():
    """Check if the current user is authenticated."""
    return "user_email" in session


# Register context processor to make auth functions available in templates
@auth_bp.context_processor
def inject_auth_functions():
    return {"check_permission": check_permission, "is_authenticated": is_authenticated}

import re
import traceback

from flask_wtf import FlaskForm
from wtforms import (
    BooleanField,
    DateField,
    DecimalField,
    FileField,
    HiddenField,
    IntegerField,
    SelectField,
    StringField,
    SubmitField,
    TextAreaField,
)
from wtforms.validators import (
    DataRequired,
    Email,
    IPAddress,
    Length,
    NumberRange,
    Optional,
    ValidationError,
)

from database.db_config import get_db_cursor


class SelectLotForm(FlaskForm):
    lot = SelectField("Select Lot", choices=[], validators=[DataRequired()])
    submit = SubmitField("Submit")


class PrintLabelForm(FlaskForm):
    printer_ip = StringField(
        "Labeller IP address",
        default="*************",
        validators=[DataRequired(), IPAddress()],
    )

    copy_number = IntegerField(
        "Print labels (number of copies)", default=2, validators=[NumberRange(min=1)]
    )

    label_type = SelectField(
        "Label type",
        choices=[
            ("3", "Standard label"),
            ("1", "Label free (Without Ligentec logo)"),
            ("2", "Erfurt label"),
            ("4", "Substrate-wafer"),
        ],
        default="3",
        validators=[DataRequired()],
    )

    shipment_date = DateField(
        "Shipment date", format="%Y-%m-%d", validators=[DataRequired()]
    )

    label_title = TextAreaField("Label title", validators=[DataRequired()])

    po = StringField("Purchase order (Erfurt)", validators=[Optional()])

    project_id = StringField("Project ID (Erfurt)", validators=[Optional()])

    lot_id = StringField("X-FAB lot ID (Erfurt)", validators=[Optional()])

    # Substrate-wafer specific fields
    item_id = StringField("Item ID (Substrate-wafer)", validators=[Optional()])
    svm_lot_id = StringField("SVM lot ID (Substrate-wafer)", validators=[Optional()])
    unit_price = DecimalField(
        "Unit Price (Substrate-wafer)", validators=[Optional()], places=2
    )
    currency = SelectField(
        "Currency (Substrate-wafer)",
        choices=[
            ("USD", "$ USD (US Dollar)"),
            ("EUR", "€ EUR (Euro)"),
            ("CHF", "CHF (Swiss Franc)"),
            ("GBP", "£ GBP (British Pound)"),
            ("JPY", "¥ JPY (Japanese Yen)"),
            ("CAD", "$ CAD (Canadian Dollar)"),
            ("AUD", "$ AUD (Australian Dollar)"),
            ("CNY", "¥ CNY (Chinese Yuan)"),
        ],
        default="USD",
        validators=[Optional()],
    )

    comments = TextAreaField("Please enter your comments in the packing slip")

    shipment_origin = SelectField(
        "Shipment Origin",
        choices=[("france", "France"), ("switzerland", "Switzerland")],
        validators=[DataRequired()],
    )

    manual_wafer_count = IntegerField(
        "Number of wafers (Manual)", validators=[Optional(), NumberRange(min=1)]
    )

    submit = SubmitField("Print")

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        from flask import session

        if "asana_label_type" in session:
            self.label_type.default = session["asana_label_type"]

    def validate(self, extra_validators=None):
        """Database-driven form validation instead of Excel."""
        import logging

        logger = logging.getLogger(__name__)
        logger.info("Starting form validation")

        if not super().validate():
            logger.error(f"Base form validation failed. Errors: {self.errors}")
            return False

        try:
            # Run validation steps in sequence
            if not self._validate_wafer_selection(logger):
                return False

            if not self._validate_basic_fields(logger):
                return False

            if not self._validate_label_type_requirements(logger):
                return False

            logger.info("Form validation successful")
            return True

        except Exception as e:
            logger.error(f"Validation error: {str(e)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            self.errors["form"] = ["An error occurred during validation"]
            return False

    def _validate_wafer_selection(self, logger):
        """Validate that wafers are selected and valid."""
        from flask import session

        from database.db_operations import validate_wafers

        # Skip wafer validation for substrate-wafer label type
        if self.label_type.data == "4":  # Substrate-wafer type
            logger.info(
                "Substrate-wafer label type selected, skipping wafer validation"
            )

            # For substrate-wafer, we need to validate the required fields
            if not self.item_id.data:
                logger.error("Item ID is required for substrate-wafer label")
                self.errors["item_id"] = [
                    "Item ID is required for substrate-wafer label"
                ]
                return False

            if not self.manual_wafer_count.data:
                logger.error("Wafer count is required for substrate-wafer label")
                self.errors["manual_wafer_count"] = [
                    "Wafer count is required for substrate-wafer label"
                ]
                return False

            if not self.svm_lot_id.data:
                logger.error("SVM lot ID is required for substrate-wafer label")
                self.errors["svm_lot_id"] = [
                    "SVM lot ID is required for substrate-wafer label"
                ]
                return False

            return True

        # Standard validation for other label types
        paired_slots_wafers = session.get("paired_slots_wafers", [])
        logger.info(f"Paired slots and wafers: {paired_slots_wafers}")

        if not paired_slots_wafers:
            logger.error("No wafers selected")
            self.errors["wafers"] = ["No wafers selected"]
            return False

        wafer_ids = [pair[1] for pair in paired_slots_wafers]
        logger.info(f"Wafer IDs to validate: {wafer_ids}")

        validation_result = validate_wafers(wafer_ids)

        if not validation_result["all_valid"]:
            invalid_wafers = validation_result["invalid"]
            logger.error(f"Invalid wafers found: {invalid_wafers}")

            error_messages = []
            for wafer in invalid_wafers:
                error_messages.append(f"{wafer['wafer_id']} ({wafer['reason']})")

            self.errors["wafers"] = [f"Invalid wafers: {', '.join(error_messages)}"]
            return False

        return True

    def _validate_basic_fields(self, logger):
        """Validate required basic fields."""
        if not self.label_title.data:
            logger.error("Label title is missing")
            self.errors["label_title"] = ["Label title is required"]
            return False

        if not self.shipment_date.data:
            logger.error("Shipment date is missing")
            self.errors["shipment_date"] = ["Shipment date is required"]
            return False

        return True

    def _validate_label_type_requirements(self, logger):
        """Validate requirements specific to label types."""
        from flask import session

        # Check label-free priority over Erfurt
        if self.label_type.data == "2" and any(
            [self.po.data, self.project_id.data, self.lot_id.data]
        ):
            asana_label_free = session.get("asana_label_free", "").lower() == "yes"
            logger.info(f"Checking label-free status: {asana_label_free}")

            if asana_label_free:
                logger.info("Converting to label-free type")
                self.label_type.data = "1"
                self.po.data = ""
                self.project_id.data = ""
                self.lot_id.data = ""
                return True

        # Validate Erfurt label requirements
        if self.label_type.data == "2":
            logger.info("Validating Erfurt label requirements")
            return self._validate_erfurt_fields(logger)

        # Validate Substrate-wafer label requirements
        if self.label_type.data == "4":
            logger.info("Validating Substrate-wafer label requirements")
            return self._validate_substrate_wafer_fields(logger)

        return True

    def _validate_erfurt_fields(self, logger):
        """Validate fields required for Erfurt labels."""
        if not all([self.po.data, self.project_id.data, self.lot_id.data]):
            if not self.po.data:
                self.errors["po"] = ["Required for Erfurt label"]
            if not self.project_id.data:
                self.errors["project_id"] = ["Required for Erfurt label"]
            if not self.lot_id.data:
                self.errors["lot_id"] = ["Required for Erfurt label"]
            logger.error("Missing required Erfurt fields")
            return False
        return True

    def _validate_substrate_wafer_fields(self, logger):
        """Validate fields required for Substrate-wafer labels."""
        from flask import session

        # Check if we're in freestyle mode
        if session.get("freestyle_mode"):
            # In freestyle mode, we already validated the fields in the freestyle route
            return True

        if not all([self.item_id.data, self.svm_lot_id.data]):
            if not self.item_id.data:
                self.errors["item_id"] = ["Required for Substrate-wafer label"]
            if not self.svm_lot_id.data:
                self.errors["svm_lot_id"] = ["Required for Substrate-wafer label"]
            logger.error("Missing required Substrate-wafer fields")
            return False

        # Check wafer count
        if not self.manual_wafer_count.data:
            self.errors["manual_wafer_count"] = ["Required for Substrate-wafer label"]
            logger.error("Missing wafer count for Substrate-wafer label")
            return False

        return True


class AdvancedSearchForm(FlaskForm):
    lot = StringField("Lot Number")
    scribe_id = StringField("Scribe ID")
    location = SelectField(
        "Location",
        choices=[
            ("", "All Locations"),
            ("Ligentec FR", "Ligentec FR"),
            ("Sent to Customer", "Sent to Customer"),
        ],
    )

    arrival_date_from = DateField("From", format="%Y-%m-%d", validators=[Optional()])
    arrival_date_to = DateField("To", format="%Y-%m-%d", validators=[Optional()])

    sent_date_from = DateField("From", format="%Y-%m-%d", validators=[Optional()])
    sent_date_to = DateField("To", format="%Y-%m-%d", validators=[Optional()])

    received_date_from = DateField("From", format="%Y-%m-%d", validators=[Optional()])
    received_date_to = DateField("To", format="%Y-%m-%d", validators=[Optional()])

    submit = SubmitField("Search")


class UploadCSVForm(FlaskForm):
    file = FileField("Upload CSV File", validators=[DataRequired()])
    submit = SubmitField("Upload")


class ShipmentForm(FlaskForm):
    # Basic Info
    type = SelectField(
        "Type",
        choices=[("standard", "Standard"), ("critical", "Critical")],
        validators=[DataRequired()],
        name="type",
    )

    title = StringField(
        "Title", validators=[DataRequired(), Length(min=3)], name="title"
    )

    priority = SelectField(
        "Priority",
        choices=[("low", "Low"), ("medium", "Medium"), ("high", "High")],
        validators=[DataRequired()],
        name="priority",
    )

    wafer_choice = SelectField(
        "Wafer Choice",
        choices=[("random", "Random"), ("not_random", "Not Random")],
        validators=[DataRequired()],
        name="wafer_choice",
    )

    # Checkboxes
    need_reviewing = BooleanField("Need Reviewing")
    label_free = BooleanField("Label Free")
    keep_cassette_closed = BooleanField("Keep Cassette Closed")

    # Location fields
    destination_location_id = SelectField(
        "Location", validators=[DataRequired()], name="location_id"
    )
    contact_person = StringField(
        "Contact Person", validators=[DataRequired()], name="contact_person"
    )
    email = StringField("Email", validators=[DataRequired(), Email()], name="email")
    address = TextAreaField("Address", validators=[DataRequired()], name="address")
    telephone = StringField("Telephone", validators=[DataRequired()], name="telephone")
    destination_label = StringField(
        "Destination Label", validators=[DataRequired()], name="destination_label"
    )

    # Label Info
    label_title = StringField("Label Title", validators=[DataRequired()])
    number_of_wafers = IntegerField("Number of Wafers", validators=[DataRequired()])
    parcel_size = SelectField(
        "Parcel Size",
        choices=[
            ("40x40x35", "40x40x35cm (1 Cassette) 5kg"),
            ("40x20x35", "40x20x35cm (1 Cassette) 8kg"),
        ],
        validators=[DataRequired()],
    )

    # Metadata fields
    xfab_po = StringField("XFAB PO", validators=[Optional()])
    xfab_device_id = StringField("XFAB Device ID", validators=[Optional()])
    project_id = StringField("Project ID", validators=[Optional()])
    eiger_number = StringField("Eiger Number", validators=[Optional()])
    tapeout = StringField("Tapeout", validators=[Optional()])
    vendor_lot = StringField("Vendor Lot", validators=[Optional()])
    customer_lot = StringField("Customer Lot", validators=[Optional()])
    rib = SelectField(
        "Rib", choices=[("true", "True"), ("false", "False")], validators=[Optional()]
    )
    tox_target_sin = StringField("TOX Target SiN", validators=[Optional()])
    heater = SelectField(
        "Heater",
        choices=[("true", "True"), ("false", "False")],
        validators=[Optional()],
    )
    undercut = SelectField(
        "Undercut",
        choices=[("true", "True"), ("false", "False")],
        validators=[Optional()],
    )
    sin_tube_position = StringField("SiN Tube Position", validators=[Optional()])
    mask = SelectField(
        "Mask",
        choices=[("e-beam", "E-beam"), ("laser", "Laser")],
        validators=[Optional()],
    )

    # Hidden fields
    wafer_ids = HiddenField("Wafer IDs")

    def __init__(self, *args, **kwargs):
        super(ShipmentForm, self).__init__(*args, **kwargs)
        try:
            with get_db_cursor() as cursor:
                cursor.execute(
                    """
                    SELECT DISTINCT location_id, label
                    FROM locations
                    WHERE label IN (
                        'Ligentec France',
                        'Xfab FR',
                        'Ligentec FR Khalil',
                        'Ligentec FR Elisee'
                    )
                    ORDER BY label
                """
                )
                locations = cursor.fetchall()
                self.destination_location_id.choices = [
                    (loc["location_id"], loc["label"]) for loc in locations
                ] or [("", "Select Location")]
        except Exception as e:
            print(f"Error loading locations: {e}")
            self.destination_location_id.choices = [("", "Select Location")]

    def validate_telephone(self, field):
        """Custom validator for telephone numbers"""
        if not re.match(r"^\+?[\d\s\-()]{10,}$", field.data):
            raise ValidationError("Invalid phone number format")

    def validate_number_of_wafers(self, field):
        """Validate number of wafers matches selected wafers"""
        if field.data > 0:
            wafer_list = self.wafer_ids.data.split(",") if self.wafer_ids.data else []
            if len(wafer_list) != field.data:
                raise ValidationError(
                    f"Number of selected wafers ({len(wafer_list)}) does not match "
                    f"specified count ({field.data})"
                )

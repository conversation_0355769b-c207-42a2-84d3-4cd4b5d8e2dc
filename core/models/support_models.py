"""
Support ticket models for the application.
"""
import enum
from sqlalchemy import (
    Column,
    String,
    Integer,
    Text,
    TIMESTAMP,
    Foreign<PERSON>ey,
    Enum,
)
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import JSONB
from pydantic import BaseModel, <PERSON>
from typing import Optional, List, Dict
from datetime import datetime

from core.models.models import Base


class TicketStatus(enum.Enum):
    OPEN = "open"
    IN_PROGRESS = "in_progress"
    RESOLVED = "resolved"
    CLOSED = "closed"
    REOPENED = "reopened"


class TicketPriority(enum.Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


class TicketType(enum.Enum):
    TECHNICAL = "technical"
    ACCOUNT = "account"
    FEATURE = "feature"
    OTHER = "other"


class SupportTicket(Base):
    """SQLAlchemy model for support tickets."""
    
    __tablename__ = "support_tickets"
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    issue_type = Column(Enum(TicketType))
    priority = Column(Enum(TicketPriority))
    subject = Column(String(255), nullable=False)
    message = Column(Text, nullable=False)
    status = Column(Enum(TicketStatus), default=TicketStatus.OPEN)
    metadata = Column(JSONB)
    created_at = Column(TIMESTAMP(timezone=True), default=datetime.now)
    updated_at = Column(TIMESTAMP(timezone=True), default=datetime.now, onupdate=datetime.now)
    
    # Relationships
    attachments = relationship("SupportAttachment", back_populates="ticket", cascade="all, delete-orphan")
    comments = relationship("SupportComment", back_populates="ticket", cascade="all, delete-orphan")


class SupportAttachment(Base):
    """SQLAlchemy model for support ticket attachments."""
    
    __tablename__ = "support_attachments"
    
    id = Column(Integer, primary_key=True)
    ticket_id = Column(Integer, ForeignKey("support_tickets.id"))
    file_name = Column(String(255), nullable=False)
    file_path = Column(String(255), nullable=False)
    file_size = Column(Integer, nullable=False)
    file_type = Column(String(100), nullable=False)
    created_at = Column(TIMESTAMP(timezone=True), default=datetime.now)
    
    # Relationships
    ticket = relationship("SupportTicket", back_populates="attachments")


class SupportComment(Base):
    """SQLAlchemy model for support ticket comments."""
    
    __tablename__ = "support_comments"
    
    id = Column(Integer, primary_key=True)
    ticket_id = Column(Integer, ForeignKey("support_tickets.id"))
    user_id = Column(Integer, ForeignKey("users.id"))
    comment = Column(Text, nullable=False)
    created_at = Column(TIMESTAMP(timezone=True), default=datetime.now)
    
    # Relationships
    ticket = relationship("SupportTicket", back_populates="comments")


# Pydantic models for API validation and serialization
class SupportTicketCreate(BaseModel):
    """Pydantic model for creating a support ticket."""
    
    issue_type: str
    priority: str
    subject: str
    message: str
    
    class Config:
        from_attributes = True


class SupportTicketResponse(BaseModel):
    """Pydantic model for support ticket response."""
    
    id: int
    issue_type: str
    priority: str
    subject: str
    message: str
    status: str
    created_at: datetime
    
    class Config:
        from_attributes = True


class SupportTicketList(BaseModel):
    """Pydantic model for listing support tickets."""
    
    tickets: List[SupportTicketResponse]
    total: int
    
    class Config:
        from_attributes = True

"""
Notification models for the bell notification system.
"""

import enum
from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field
from sqlalchemy import (
    TIMESTAMP,
    Boolean,
    Column,
    Enum,
    ForeignKey,
    Integer,
    String,
    Text,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship

from core.models.models import Base


class NotificationType(enum.Enum):
    """Enumeration for notification types."""

    INFO = "info"
    SUCCESS = "success"
    WARNING = "warning"
    ERROR = "error"
    EMAIL = "email"


class Notification(Base):
    """SQLAlchemy model for notifications."""

    __tablename__ = "notifications"

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    title = Column(String(255), nullable=False)
    message = Column(Text, nullable=False)
    notification_type = Column(String(50), default="info")
    is_read = Column(Boolean, default=False)
    is_email_related = Column(Boolean, default=False)
    email_subject = Column(String(255))
    email_sender = Column(String(255))
    email_recipients = Column(Text)  # JSON array of recipients
    related_entity_type = Column(
        String(50)
    )  # 'support_ticket', 'shipment', 'rfq', etc.
    related_entity_id = Column(String(100))
    extra_data = Column(JSONB)
    created_at = Column(TIMESTAMP(timezone=True), default=datetime.now)
    updated_at = Column(
        TIMESTAMP(timezone=True), default=datetime.now, onupdate=datetime.now
    )
    expires_at = Column(TIMESTAMP(timezone=True))

    def to_dict(self) -> Dict[str, Any]:
        """Convert notification to dictionary."""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "title": self.title,
            "message": self.message,
            "notification_type": self.notification_type,
            "is_read": self.is_read,
            "is_email_related": self.is_email_related,
            "email_subject": self.email_subject,
            "email_sender": self.email_sender,
            "email_recipients": self.email_recipients,
            "related_entity_type": self.related_entity_type,
            "related_entity_id": self.related_entity_id,
            "extra_data": self.extra_data,
            "created_at": (
                self.created_at.isoformat() if self.created_at is not None else None
            ),
            "updated_at": (
                self.updated_at.isoformat() if self.updated_at is not None else None
            ),
            "expires_at": (
                self.expires_at.isoformat() if self.expires_at is not None else None
            ),
        }


# Pydantic models for API validation and serialization
class NotificationCreate(BaseModel):
    """Pydantic model for creating a notification."""

    user_id: int
    title: str = Field(..., max_length=255)
    message: str
    notification_type: str = "info"
    is_email_related: bool = False
    email_subject: Optional[str] = None
    email_sender: Optional[str] = None
    email_recipients: Optional[str] = None
    related_entity_type: Optional[str] = None
    related_entity_id: Optional[str] = None
    extra_data: Optional[Dict[str, Any]] = None
    expires_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class NotificationUpdate(BaseModel):
    """Pydantic model for updating a notification."""

    is_read: Optional[bool] = None
    title: Optional[str] = None
    message: Optional[str] = None
    notification_type: Optional[str] = None
    extra_data: Optional[Dict[str, Any]] = None
    expires_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class NotificationResponse(BaseModel):
    """Pydantic model for notification API responses."""

    id: int
    user_id: int
    title: str
    message: str
    notification_type: str
    is_read: bool
    is_email_related: bool
    email_subject: Optional[str] = None
    email_sender: Optional[str] = None
    email_recipients: Optional[str] = None
    related_entity_type: Optional[str] = None
    related_entity_id: Optional[str] = None
    extra_data: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: datetime
    expires_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class NotificationSummary(BaseModel):
    """Pydantic model for notification summary."""

    total_notifications: int
    unread_count: int
    notifications: List[NotificationResponse]

    class Config:
        from_attributes = True


class EmailNotificationData(BaseModel):
    """Pydantic model for email notification data."""

    subject: str
    sender: str
    recipients: List[str]
    cc: Optional[List[str]] = None
    body: str
    related_entity_type: Optional[str] = None
    related_entity_id: Optional[str] = None

    class Config:
        from_attributes = True

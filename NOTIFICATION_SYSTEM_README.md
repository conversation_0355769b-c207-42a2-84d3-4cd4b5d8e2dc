# 🔔 Bell Notification System

A comprehensive in-app notification system for Talaria Dashboard that integrates with your existing email infrastructure to provide real-time notifications within the application interface.

## ✨ Features

- **Real-time Bell Icon**: Interactive bell notification icon in the top navigation
- **Notification Badge**: Shows unread notification count with visual indicators
- **Email Integration**: Automatically creates in-app notifications when emails are sent
- **Notification Panel**: Dropdown panel showing recent notifications with actions
- **Multiple Notification Types**: Support for info, success, warning, error, and email notifications
- **Mark as Read/Unread**: Individual and bulk notification management
- **Auto-refresh**: Periodic updates of notification count
- **Responsive Design**: Works on desktop and mobile devices

## 🚀 Quick Setup

### 1. Database Setup

Run the setup script to create the necessary database tables:

```bash
python setup_notifications.py
```

This will:
- Create the `notifications` table
- Set up indexes for performance
- Create a `notification_summary` view
- Insert sample notifications for testing

### 2. Verify Installation

1. Start your Flask application
2. Log in to the dashboard
3. Look for the bell icon (🔔) in the top navigation bar
4. Click the bell to see the notification panel

## 📋 Database Schema

The notification system uses the following main table:

```sql
CREATE TABLE notifications (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    notification_type VARCHAR(50) DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    is_email_related BOOLEAN DEFAULT FALSE,
    email_subject VARCHAR(255),
    email_sender VARCHAR(255),
    email_recipients TEXT,
    related_entity_type VARCHAR(50),
    related_entity_id VARCHAR(100),
    extra_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE
);
```

## 🔧 API Endpoints

The system provides the following REST API endpoints:

### Get Notifications
```
GET /api/notifications
```
Parameters:
- `limit` (optional): Number of notifications to return (default: 50)
- `unread_only` (optional): Return only unread notifications (default: false)

### Get Unread Count
```
GET /api/notifications/unread-count
```

### Mark as Read
```
POST /api/notifications/{id}/read
```

### Mark All as Read
```
POST /api/notifications/mark-all-read
```

### Delete Notification
```
DELETE /api/notifications/{id}
```

### Create Email Notification
```
POST /api/notifications/email
```

## 🎨 Frontend Integration

The notification system automatically integrates with your existing interface:

### Bell Icon Location
The bell icon appears in the top navigation bar next to the user profile menu.

### Notification Badge
- Shows red badge with count when there are unread notifications
- Hides when no unread notifications
- Shows "99+" for counts over 99

### Notification Panel
- Dropdown panel that appears when clicking the bell
- Shows recent notifications with timestamps
- Color-coded by notification type
- Action buttons for mark as read, refresh, and close

## 📧 Email Integration

The system automatically creates in-app notifications when emails are sent through:

1. **Shipping Notifications**: When shipping emails are sent via `send_shipping_notification()`
2. **Test Emails**: When test emails are sent via `send_test_email()`
3. **Support Emails**: When support tickets are submitted
4. **RFQ Emails**: When RFQ automation emails are sent

### Adding Email Integration to New Features

To add notification integration to new email functionality:

```python
from core.services.notification_service import notification_service
from core.models.notification_models import EmailNotificationData

# After sending an email
user_id = session.get("user_id")
if user_id:
    email_data = EmailNotificationData(
        subject="Your Email Subject",
        sender="<EMAIL>",
        recipients=["<EMAIL>"],
        cc=["<EMAIL>"],
        body="Email body preview...",
        related_entity_type="your_entity_type",
        related_entity_id="entity_id"
    )
    notification_service.create_email_notification(user_id, email_data)
```

## 🔄 Real-time Updates

The system provides real-time updates through:

1. **Periodic Refresh**: Unread count updates every 30 seconds
2. **Manual Refresh**: Users can click the refresh button in the notification panel
3. **Auto-refresh on Actions**: Notification list refreshes after mark as read/delete actions

## 🎯 Notification Types

The system supports different notification types with visual indicators:

- **📘 Info** (blue): General information
- **✅ Success** (green): Successful operations
- **⚠️ Warning** (yellow): Warnings and alerts
- **❌ Error** (red): Error messages
- **📧 Email** (purple): Email-related notifications

## 🛠️ Customization

### Adding New Notification Types

1. Update the `NotificationType` enum in `core/models/notification_models.py`
2. Add corresponding icons and colors in `static/js/notifications.js`
3. Update the CSS classes as needed

### Styling Customization

The notification system uses Tailwind CSS classes. Key customization points:

- **Bell Icon**: Modify the button in `templates/base.html`
- **Notification Panel**: Update styles in `static/js/notifications.js`
- **Badge Colors**: Modify badge classes in the JavaScript

### Notification Retention

By default, notifications are kept indefinitely. To implement automatic cleanup:

1. Set `expires_at` when creating notifications
2. Run the cleanup endpoint periodically:
   ```
   POST /api/notifications/cleanup
   ```

## 🔍 Troubleshooting

### Bell Icon Not Showing
1. Check that `notifications.js` is loaded in `base.html`
2. Verify the user is logged in
3. Check browser console for JavaScript errors

### Notifications Not Creating
1. Verify database table exists: `SELECT * FROM notifications LIMIT 1;`
2. Check Flask logs for notification service errors
3. Ensure user_id is available in session

### Badge Not Updating
1. Check network tab for API call failures
2. Verify `/api/notifications/unread-count` endpoint is accessible
3. Check for JavaScript console errors

### Database Connection Issues
1. Verify database configuration in `database/.env`
2. Check that the auth database is accessible
3. Ensure the notifications table exists

## 📊 Performance Considerations

- **Indexes**: The system includes optimized indexes for common queries
- **Pagination**: API endpoints support limit parameters
- **Caching**: Consider implementing Redis caching for high-traffic scenarios
- **Cleanup**: Implement periodic cleanup of old notifications

## 🔐 Security

- **Authentication**: All endpoints require user authentication
- **Authorization**: Users can only access their own notifications
- **CSRF Protection**: API endpoints include CSRF token validation
- **SQL Injection**: Uses parameterized queries throughout

## 🚀 Future Enhancements

Potential improvements for the notification system:

1. **WebSocket Integration**: Real-time push notifications
2. **Email Preferences**: User-configurable email notification settings
3. **Notification Categories**: Grouping and filtering by categories
4. **Push Notifications**: Browser push notification support
5. **Notification Templates**: Customizable notification templates
6. **Analytics**: Notification engagement tracking

## 📞 Support

If you encounter issues with the notification system:

1. Check the troubleshooting section above
2. Review Flask application logs
3. Verify database connectivity and table structure
4. Test API endpoints directly using curl or Postman

The notification system is designed to be robust and fail gracefully - if notifications fail to create, it won't break your existing email functionality.

include:
  - project: 'templates/template-devops'
    file: 'templates/semantic-release.yml'
    ref: main
  #- project: 'templates/template-devops'
  #  file: 'templates/python-packaging.yml'
  #  ref: main
  #- project: 'templates/template-devops'
  #  file: 'templates/tests/unit.yml'
  #  ref: main
  - project: 'templates/template-devops'
    file: 'templates/app_deploy/app_deploy.yml'
    ref: main


stages:
  - prepare
  - test
  - build
  - release
  - deploy
  - cleanup


# config.py is a configuration file for the application.
import os
from datetime import timedelta

basedir = os.path.abspath(os.path.dirname(__file__))


class Config:
    PRINTER_CONFIG = {
        "default": {
            "ip": "*************",
            "port": 9100,
            "timeout": 20,
            "retry_attempts": 3,
            "auth_required": True,
            "api_key": "9StobnE5StzmPrwsNIY_INyfIyxJBB1tQfxd9FGdFRA",
            "allowed_ips": ["*************", "*************"],
        },
        "backup": {
            "ip": "*************",
            "port": 9100,
            "timeout": 20,
            "retry_attempts": 2,
            "auth_required": False,
            "api_key": None,
            "allowed_ips": ["*************"],
        },
    }

    # Support settings
    UPLOAD_FOLDER = os.path.join(basedir, "uploads")
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    SUPPORT_EMAIL = "<EMAIL>"

    # Email settings (if not already configured)
    MAIL_SERVER = "smtp.gmail.com"
    MAIL_PORT = 587
    MAIL_USE_TLS = True
    MAIL_USERNAME = os.environ.get("MAIL_USERNAME")
    MAIL_PASSWORD = os.environ.get("MAIL_PASSWORD")

    # Session settings
    SESSION_TYPE = "filesystem"
    PERMANENT_SESSION_LIFETIME = timedelta(days=7)

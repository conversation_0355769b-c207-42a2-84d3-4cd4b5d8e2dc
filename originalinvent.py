from flask import Blueprint, render_template, request, jsonify, current_app
from models import WaferInventoryModel, WaferModel, LocationModel, WaferInventory
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
from typing import Di<PERSON>, <PERSON><PERSON>, List
import traceback
from datetime import datetime, timezone
from db_config import get_db_cursor, get_db_url
import json
from offline_storage import OfflineStorageManager
from flask import current_app, g
from offline_utils import require_database
from db_helpers import check_online_status
from offline_utils import require_database
import sqlite3
from decimal import Decimal
from auth import login_required, permission_required
import re


inventory_bp = Blueprint('inventory', __name__)


def get_db_url():
    return current_app.config['DB_CONNECTION']


def get_engine():
    """Get SQLAlchemy engine using the database URL"""
    return create_engine(get_db_url())


def get_offline_storage():
    if 'offline_storage' not in g:
        g.offline_storage = OfflineStorageManager(
            current_app.config['DB_CONNECTION'])
    return g.offline_storage


@inventory_bp.route('/api/inventory/search', methods=['POST'])
@require_database()
@login_required
def search_inventory():
    try:
        data = request.get_json()
        current_app.logger.info(f"Parsed JSON data: {data}")

        # Get pagination parameters
        page = int(data.get('page', 1))
        page_size = int(data.get('page_size', 10))
        offset = (page - 1) * page_size

        try:
            # Try online search first
            with get_db_cursor() as cursor:
                # Build base query
                base_query = """
                    FROM wafer_inventory wi
                    LEFT JOIN wafers w ON wi.wafer_id = w.wafer_id
                    LEFT JOIN lots l ON w.lot_id = l.lot_id
                    LEFT JOIN locations loc ON wi.location_id = loc.location_id
                    LEFT JOIN xfab_fr_lots xfl ON w.lot_id = xfl.lot_id  
                    WHERE 1=1
                """
                # Build where clause and params
                where_clause = []
                params = []

                # Handle text search fields
                if data.get('wafer_id'):
                    where_clause.append("wi.wafer_id ILIKE %s")
                    params.append(f"%{data['wafer_id'].strip()}%")

                if data.get('lot_id'):
                    where_clause.append("w.lot_id ILIKE %s")
                    params.append(f"%{data['lot_id'].strip()}%")

                if data.get('xfab_id'):
                    where_clause.append("xfl.xfab_fr_lot_id ILIKE %s")
                    params.append(f"%{data['xfab_id'].strip()}%")

                if data.get('mask_set_id'):
                    where_clause.append("l.mask_set_id ILIKE %s")
                    params.append(f"%{data['mask_set_id'].strip()}%")

                if data.get('module_name'):
                    where_clause.append("wi.metadata->>'Modules' ILIKE %s")
                    params.append(f"%{data['module_name'].strip()}%")

                if data.get('cassette_id'):
                    where_clause.append("wi.cassette_id ILIKE %s")
                    params.append(f"%{data['cassette_id'].strip()}%")

                if data.get('slot_id'):
                    where_clause.append("wi.slot_id = %s")
                    params.append(data['slot_id'])

                if data.get('location_id'):
                    where_clause.append("wi.location_id = %s")
                    params.append(data['location_id'])

                # Handle date ranges
                if data.get('arrived_at_from'):
                    where_clause.append("wi.arrived_at >= %s::timestamp")
                    params.append(f"{data['arrived_at_from']} 00:00:00")

                if data.get('arrived_at_to'):
                    where_clause.append("wi.arrived_at <= %s::timestamp")
                    params.append(f"{data['arrived_at_to']} 23:59:59")

                if data.get('sent_at_from'):
                    where_clause.append("wi.sent_at >= %s::timestamp")
                    params.append(f"{data['sent_at_from']} 00:00:00")

                if data.get('sent_at_to'):
                    where_clause.append("wi.sent_at <= %s::timestamp")
                    params.append(f"{data['sent_at_to']} 23:59:59")

                # First get total count
                count_query = f"SELECT COUNT(*) as total {base_query}"
                if where_clause:
                    count_query += " AND " + " AND ".join(where_clause)

                cursor.execute(count_query, params)
                total_records = cursor.fetchone()['total']

                # Add sorting
                sort_field = data.get('sort_field')
                sort_direction = data.get('sort_direction', 'asc').upper()

                valid_sort_fields = {
                    'wafer_id': 'wi.wafer_id',
                    'lot_id': 'w.lot_id',
                    'xfab_id': 'xfl.xfab_fr_lot_id',
                    'module_name': "wi.metadata->>'Modules'",
                    'cassette_id': 'wi.cassette_id',
                    'mask_set_id': 'l.mask_set_id',
                    'slot_id': 'wi.slot_id',
                    'location_id': 'wi.location_id',
                    'arrived_at': 'wi.arrived_at',
                    'sent_at': 'wi.sent_at'
                }

                sort_clause = valid_sort_fields.get(
                    sort_field, 'wi.arrived_at')
                sort_direction = sort_direction if sort_direction in [
                    'ASC', 'DESC'] else 'DESC'

                # Get paginated data
                data_query = f"""
                    WITH sorted_data AS (
                        SELECT
                            wi.wafer_id,
                            wi.cassette_id,
                            wi.slot_id,
                            wi.arrived_at,
                            wi.sent_at,
                            wi.location_id,
                            COALESCE(w.lot_id, '') as lot_id,
                            COALESCE(xfl.xfab_fr_lot_id, '') as xfab_id,
                            COALESCE(wi.metadata->>'Modules', '') as module_name,
                            COALESCE(l.mask_set_id, '') as mask_set_id,
                            COALESCE(loc.label, wi.location_id) as location_label,
                            ROW_NUMBER() OVER (ORDER BY {sort_clause} {sort_direction}) as row_num
                        {base_query}
                        {' AND ' + ' AND '.join(where_clause)
                         if where_clause else ''}
                    )
                    SELECT * FROM sorted_data
                    LIMIT %s OFFSET %s
                """

                query_params = params + [page_size, offset]
                cursor.execute(data_query, query_params)
                results = cursor.fetchall()

                # Format results
                formatted_results = []
                for row in results:
                    formatted_result = {
                        'row_number': row['row_num'],
                        'wafer_id': row['wafer_id'],
                        'lot_id': row['lot_id'],
                        'xfab_id': row['xfab_id'],
                        'module_name': row['module_name'],
                        'cassette_id': row['cassette_id'],
                        'mask_set_id': row['mask_set_id'],
                        'slot_id': row['slot_id'],
                        'location_id': row['location_id'],
                        'location_label': row['location_label'],
                        'arrived_at': row['arrived_at'].isoformat() if row['arrived_at'] else None,
                        'sent_at': row['sent_at'].isoformat() if row['sent_at'] else None
                    }
                    formatted_results.append(formatted_result)

                # Cache results for offline use if available
                if hasattr(current_app, 'inventory_storage'):
                    current_app.inventory_storage.save_inventory(
                        formatted_results)

                return jsonify({
                    'success': True,
                    'data': formatted_results,
                    'pagination': {
                        'page': page,
                        'page_size': page_size,
                        'total': total_records,
                        'start': offset + 1,
                        'end': min(offset + page_size, total_records),
                        'total_pages': (total_records + page_size - 1) // page_size
                    },
                    'mode': 'online'
                })

        except Exception as db_error:
            current_app.logger.warning(
                f"Online search failed: {str(db_error)}")

            # Try offline search if available
            if hasattr(current_app, 'inventory_storage'):
                try:
                    # Get cached results
                    cached_results = current_app.inventory_storage.get_inventory(
                        data)

                    # Apply filters to cached results
                    filtered_results = filter_cached_results(
                        cached_results, data)

                    # Apply pagination
                    total_records = len(filtered_results)
                    paginated_results = filtered_results[offset:offset + page_size]

                    return jsonify({
                        'success': True,
                        'data': paginated_results,
                        'pagination': {
                            'page': page,
                            'page_size': page_size,
                            'total': total_records,
                            'start': offset + 1,
                            'end': min(offset + page_size, total_records),
                            'total_pages': (total_records + page_size - 1) // page_size
                        },
                        'mode': 'offline'
                    })
                except Exception as cache_error:
                    current_app.logger.error(
                        f"Offline search failed: {str(cache_error)}")
                    raise cache_error
            else:
                raise db_error

    except Exception as e:
        current_app.logger.error(f"Error in search_inventory: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f"Error searching inventory: {str(e)}",
            'data': []
        }), 500


def filter_cached_results(results, filters):
    """Apply filters to cached results"""
    filtered = results.copy()

    for key, value in filters.items():
        if value and key in ['wafer_id', 'lot_id', 'xfab_id', 'module_name', 'cassette_id']:
            filtered = [r for r in filtered if value.lower() in str(
                r.get(key, '')).lower()]
        elif key == 'slot_id' and value:
            filtered = [r for r in filtered if r.get('slot_id') == value]
        elif key == 'location_id' and value:
            filtered = [r for r in filtered if r.get('location_id') == value]
        # Handle date filters
        elif key.endswith('_from') and value:
            base_key = key.replace('_from', '')
            filtered = [r for r in filtered if r.get(
                base_key) and r[base_key] >= value]
        elif key.endswith('_to') and value:
            base_key = key.replace('_to', '')
            filtered = [r for r in filtered if r.get(
                base_key) and r[base_key] <= value]

    return filtered

# Function to add wafers to inventory


@inventory_bp.route('/api/inventory/add', methods=['POST'])
@require_database()
@login_required
@permission_required('add')
def add_inventory():
    try:
        data = request.get_json()
        current_app.logger.info(f"Add request data: {data}")

        # Extract data from request body
        wafer_id_list = data.get('wafer_ids', [])
        slot_id_list = data.get('slot_ids', [])
        cassette_id = data.get('cassette_id', '')
        location_id = data.get('location_id')
        arrived_at = data.get('arrived_at')
        sent_at = data.get('sent_at')
        module_name = data.get('module_name', '')
        lot_id = data.get('lot_id', '')
        xfab_fr_lot_id = data.get('xfab_id', '')
        mask_set_id = data.get('mask_set_id', '')

        # Validation
        if not wafer_id_list or len(wafer_id_list) != len(slot_id_list):
            return jsonify({
                'success': False,
                'message': 'Invalid wafer or slot IDs provided'
            }), 400

        engine = create_engine(get_db_url())
        with engine.connect() as conn:
            with conn.begin():
                try:
                    # 1. Vérifier l'existence des wafer_id dans wafer_inventory
                    existing_wafers = conn.execute(
                        text(
                            "SELECT wafer_id FROM wafer_inventory WHERE wafer_id = ANY(:wafer_ids)"),
                        {"wafer_ids": wafer_id_list}
                    ).fetchall()
                    if existing_wafers:
                        return jsonify({
                            'success': False,
                            'message': f"Error: At least one wafer_id already exists in wafer_inventory. ({', '.join(w[0] for w in existing_wafers)})"
                        }), 400

                    # 2. Vérifier l'existence de xfab_fr_lot_id et mask_set_id
                    xfab_lot_exists = conn.execute(
                        text(
                            "SELECT lot_id FROM xfab_fr_lots WHERE xfab_fr_lot_id = :xfab_fr_lot_id"),
                        {"xfab_fr_lot_id": xfab_fr_lot_id}
                    ).fetchone()
                    mask_set_exists = conn.execute(
                        text(
                            "SELECT mask_set_id FROM mask_sets WHERE mask_set_id = :mask_set_id"),
                        {"mask_set_id": mask_set_id}
                    ).fetchone()
                    if not xfab_lot_exists or not mask_set_exists:
                        return jsonify({
                            'success': False,
                            'message': f"Error: xfab_fr_lot_id or mask_set_id does not exist in their respective tables in ICARIUM"
                        }), 400

                    # 2'. Vérification si xfab_fr_lot_id est associé à un lot_id égal à lui-même et que lot_id existe déjà
                    existing_xfab_lot = xfab_lot_exists[0]
                    lot_exists = conn.execute(
                        text("SELECT lot_id FROM lots WHERE lot_id = :lot_id"),
                        {"lot_id": lot_id}
                    ).fetchone()
                    if existing_xfab_lot == xfab_fr_lot_id and lot_exists:
                        return jsonify({
                            'success': False,
                            'message': f"Error: xfab_fr_lot_id is already associated with an identical lot_id and the new lot_id already exists in lots"
                        }), 400

                    # 3. Vérifier l'existence de lot_id dans lots et la correspondance mask_set_id
                    lot_result = conn.execute(
                        text("SELECT mask_set_id FROM lots WHERE lot_id = :lot_id"),
                        {"lot_id": lot_id}
                    ).fetchone()
                    if lot_result and lot_result.mask_set_id != mask_set_id:
                        return jsonify({
                            'success': False,
                            'message': f"Error: The mask_set_id found in lots table is different from the one provided."
                        }), 400

                    # 4. Vérifier l'existence des wafer_id et la correspondance mask_set_id
                    for wafer_id in wafer_id_list:
                        wafer_lot_result = conn.execute(
                            text(
                                "SELECT w.lot_id, l.mask_set_id FROM wafers w JOIN lots l ON w.lot_id = l.lot_id WHERE w.wafer_id = :wafer_id"),
                            {"wafer_id": wafer_id}
                        ).fetchone()
                        if wafer_lot_result and wafer_lot_result.mask_set_id != mask_set_id:
                            return jsonify({
                                'success': False,
                                'message': f"Error: The mask_set_id of wafer : {wafer_id} does not match the provided mask_set_id"
                            }), 400

                    # 5. Créer lot_id s'il n'existe pas
                    if not lot_result:
                        conn.execute(
                            text("""
                                INSERT INTO lots (lot_id, metadata, mask_set_id, updated_at, updated_by, pending_mapping)
                                VALUES (:lot_id, :metadata, :mask_set_id, :updated_at, :updated_by, :pending_mapping)
                            """),
                            {
                                "lot_id": lot_id,
                                "metadata": json.dumps({}),  # JSON vide
                                "mask_set_id": mask_set_id,
                                "updated_at": datetime.now(timezone.utc),
                                "updated_by": "system",
                                "pending_mapping": False  # ou une autre valeur par défaut
                            }
                        )

                    # 6. Vérifier l'existence de lot_id dans xfab_fr_lots et mettre à jour si nécessaire
                    if not xfab_lot_exists:
                        conn.execute(
                            text(
                                "UPDATE xfab_fr_lots SET lot_id = :lot_id WHERE xfab_fr_lot_id = :xfab_fr_lot_id"),
                            {"lot_id": lot_id, "xfab_fr_lot_id": xfab_fr_lot_id}
                        )
                    elif xfab_lot_exists[0] != lot_id:
                        conn.execute(
                            text(
                                "UPDATE xfab_fr_lots SET lot_id = :lot_id WHERE xfab_fr_lot_id = :xfab_fr_lot_id"),
                            {"lot_id": lot_id, "xfab_fr_lot_id": xfab_fr_lot_id}
                        )

                    # 7. Ajouter ou mettre à jour les wafers dans wafers
                    for wafer_id in wafer_id_list:
                        wafer_exists = conn.execute(
                            text(
                                "SELECT lot_id FROM wafers WHERE wafer_id = :wafer_id"),
                            {"wafer_id": wafer_id}
                        ).fetchone()
                        if not wafer_exists:
                            conn.execute(
                                text(
                                    "INSERT INTO wafers (wafer_id, size, metadata, lot_id, updated_at, updated_by) VALUES (:wafer_id, :size, :metadata, :lot_id, :updated_at, :updated_by)"),
                                {
                                    "wafer_id": wafer_id,
                                    "size": Decimal('200'),
                                    "metadata": json.dumps({}),
                                    "lot_id": lot_id,
                                    "updated_at": datetime.now(timezone.utc),
                                    "updated_by": "system"
                                }
                            )
                        elif wafer_exists[0] != lot_id:
                            conn.execute(
                                text(
                                    "UPDATE wafers SET lot_id = :lot_id WHERE wafer_id = :wafer_id"),
                                {"lot_id": lot_id, "wafer_id": wafer_id}
                            )

                    # 8. Ajouter les wafers dans wafer_inventory
                    results = []
                    for wafer_id, slot_id in zip(wafer_id_list, slot_id_list):
                        conn.execute(
                            text("INSERT INTO wafer_inventory (wafer_id, cassette_id, slot_id, arrived_at, sent_at, location_id, metadata, updated_at, updated_by) VALUES (:wafer_id, :cassette_id, :slot_id, :arrived_at, :sent_at, :location_id, :metadata, :updated_at, :updated_by)"),
                            {
                                "wafer_id": wafer_id,
                                "cassette_id": cassette_id,
                                "slot_id": slot_id,
                                "arrived_at": arrived_at or datetime.now(timezone.utc),
                                "sent_at": sent_at,
                                "location_id": location_id,
                                "metadata": json.dumps({"Modules": module_name}),
                                "updated_at": datetime.now(timezone.utc),
                                "updated_by": "system"
                            }
                        )
                        results.append(
                            f"Success: wafer_id {wafer_id} added to inventory.")

                    return jsonify({
                        'success': True,
                        'message': 'Inventory updated successfully',
                        'results': results
                    })

                except Exception as e:
                    current_app.logger.error(
                        f"Error in add_inventory: {str(e)}")
                    raise

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f"Error adding to the wafer_inventory table: {str(e)}"
        }), 500

# Function to modify wafers in inventory


@inventory_bp.route('/api/inventory/modify', methods=['PUT'])
@require_database()
@login_required
@permission_required('modify')
def modify_inventory():
    try:
        data = request.get_json()
        current_app.logger.info(f"Body: {request.get_data()}")
        updates = data.get('updates', [])

        if not updates:
            return jsonify({
                'success': False,
                'message': 'No updates provided'
            }), 400

        engine = create_engine(get_db_url())
        with engine.connect() as conn:
            with conn.begin():
                try:

                    for update in updates:

                        wafer_id = update.get('wafer_id')
                        mask_set_id = update.get('mask_set_id')
                        lot_id = update.get('lot_id')
                        xfab_fr_lot_id = update.get('xfab_id')
                        current_app.logger.info(
                            f"Processing update for wafer_id: {wafer_id}")

                        # 1. Vérifier l'existence des wafer_id dans wafer_inventory
                        existing_wafer = conn.execute(
                            text(
                                "SELECT wafer_id FROM wafer_inventory WHERE wafer_id = :wafer_id"),
                            {"wafer_id": wafer_id}
                        ).fetchone()
                        if not existing_wafer:
                            return jsonify({
                                'success': False,
                                'message': f"Wafer : {wafer_id} not found in wafer inventory table"
                            }), 404

                        # 2. Vérifier l'existence de xfab_fr_lot_id et mask_set_id
                        xfab_lot_exists = conn.execute(
                            text(
                                "SELECT lot_id FROM xfab_fr_lots WHERE xfab_fr_lot_id = :xfab_fr_lot_id"),
                            {"xfab_fr_lot_id": xfab_fr_lot_id}
                        ).fetchone()
                        mask_set_exists = conn.execute(
                            text(
                                "SELECT mask_set_id FROM mask_sets WHERE mask_set_id = :mask_set_id"),
                            {"mask_set_id": mask_set_id}
                        ).fetchone()

                        if not xfab_lot_exists or not mask_set_exists:
                            return jsonify({
                                'success': False,
                                'message': f"Error: xfab_fr_lot_id or mask_set_id does not exist in their respective tables in ICARIUM"
                            }), 400

                        # 3. Vérifier si xfab_fr_lot_id est déjà associé à un lot_id identique et que lot_id existe dans lots
                        existing_xfab_lot = xfab_lot_exists[0]
                        lot_exists = conn.execute(
                            text("SELECT lot_id FROM lots WHERE lot_id = :lot_id"),
                            {"lot_id": lot_id}
                        ).fetchone()

                        if existing_xfab_lot == xfab_fr_lot_id and lot_exists:
                            return jsonify({
                                'success': False,
                                'message': f"Error: xfab_fr_lot_id is already associated with an identical lot_id and the new lot_id already exists in lots"
                            }), 400

                        # 4. Vérifier l'existence de lot_id dans lots et la correspondance mask_set_id
                        lot_result = conn.execute(
                            text(
                                "SELECT mask_set_id FROM lots WHERE lot_id = :lot_id"),
                            {"lot_id": lot_id}
                        ).fetchone()
                        if lot_result and lot_result[0] != mask_set_id:
                            return jsonify({
                                'success': False,
                                'message': f"Error: The mask_set_id found in lots table is different from the one provided."
                            }), 400
                        # 5. Vérifier l'existence des wafers et la correspondance mask_set_id
                        wafer_lot_result = conn.execute(
                            text(
                                "SELECT w.lot_id, l.mask_set_id FROM wafers w JOIN lots l ON w.lot_id = l.lot_id WHERE w.wafer_id = :wafer_id"),
                            {"wafer_id": wafer_id}
                        ).fetchone()
                        if wafer_lot_result and wafer_lot_result[1] != mask_set_id:
                            return jsonify({
                                'success': False,
                                'message': f"Error: The mask_set_id of wafer : {wafer_id} does not match the provided mask_set_id"
                            }), 400

                        # 6. Créer lot_id s'il n'existe pas
                        if not lot_result:
                            conn.execute(
                                text("""
                                    INSERT INTO lots (lot_id, metadata, mask_set_id, updated_at, updated_by, pending_mapping)
                                    VALUES (:lot_id, :metadata, :mask_set_id, :updated_at, :updated_by, :pending_mapping)
                                """),
                                {
                                    "lot_id": lot_id,
                                    "metadata": json.dumps({}),  # JSON vide
                                    "mask_set_id": mask_set_id,
                                    "updated_at": datetime.now(timezone.utc),
                                    "updated_by": "system",
                                    "pending_mapping": False  # ou une autre valeur par défaut
                                }
                            )

                        # 7. Vérifier l'existence et mise à jour de xfab_fr_lots
                        if not xfab_lot_exists:
                            conn.execute(
                                text(
                                    "UPDATE xfab_fr_lots SET lot_id = :lot_id WHERE xfab_fr_lot_id = :xfab_fr_lot_id"),
                                {"lot_id": lot_id, "xfab_fr_lot_id": xfab_fr_lot_id}
                            )
                        elif xfab_lot_exists[0] != lot_id:
                            conn.execute(
                                text(
                                    "UPDATE xfab_fr_lots SET lot_id = :lot_id WHERE xfab_fr_lot_id = :xfab_fr_lot_id"),
                                {"lot_id": lot_id, "xfab_fr_lot_id": xfab_fr_lot_id}
                            )

                        # 8. Vérifier et mettre à jour les wafers
                        wafer_exists = conn.execute(
                            text(
                                "SELECT lot_id FROM wafers WHERE wafer_id = :wafer_id"),
                            {"wafer_id": wafer_id}
                        ).fetchone()
                        if not wafer_exists:
                            conn.execute(
                                text(
                                    "INSERT INTO wafers (wafer_id, size, metadata, lot_id, updated_at, updated_by) VALUES (:wafer_id, :size, :metadata, :lot_id, :updated_at, :updated_by)"),
                                {
                                    "wafer_id": wafer_id,
                                    "size": Decimal('200'),
                                    "metadata": json.dumps({}),
                                    "lot_id": lot_id,
                                    "updated_at": datetime.now(timezone.utc),
                                    "updated_by": "system"
                                }
                            )
                        elif wafer_exists[0] != lot_id:

                            update_wafer = text("""
                                UPDATE wafers
                                SET lot_id = :lot_id,
                                    updated_at = :updated_at,
                                    updated_by = :updated_by
                                WHERE wafer_id = :wafer_id
                            """)
                            conn.execute(update_wafer, {
                                "wafer_id": wafer_id,
                                "lot_id": lot_id,
                                "updated_at": datetime.now(timezone.utc),
                                "updated_by": "system"
                            })

                        # 9. Mettre à jour wafer_inventory
                        metadata_result = conn.execute(text("SELECT metadata FROM wafer_inventory WHERE wafer_id = :wafer_id"), {
                                                       "wafer_id": wafer_id}).fetchone()
                        if metadata_result and metadata_result[0]:
                            if isinstance(metadata_result[0], str):
                                # Charger si c'est une chaîne JSON
                                current_metadata = json.loads(
                                    metadata_result[0])
                            else:
                                # C'est déjà un dictionnaire
                                current_metadata = metadata_result[0]
                        else:
                            current_metadata = {}
                        current_metadata["Modules"] = update.get(
                            'module_name', current_metadata.get('Modules', ''))

                        # Update inventory record
                        update_inventory = text("""
                            UPDATE wafer_inventory
                            SET metadata = :metadata,
                                cassette_id = COALESCE(:cassette_id, cassette_id),
                                slot_id = COALESCE(:slot_id, slot_id),
                                location_id = COALESCE(:location_id, location_id),
                                arrived_at = COALESCE(:arrived_at, arrived_at),
                                sent_at = COALESCE(:sent_at, sent_at),
                                updated_at = :updated_at,
                                updated_by = :updated_by
                            WHERE wafer_id = :wafer_id
                        """)

                        conn.execute(update_inventory, {
                            "wafer_id": wafer_id,
                            "metadata": json.dumps(current_metadata),
                            "cassette_id": update.get('cassette_id'),
                            "slot_id": update.get('slot_id'),
                            "location_id": update.get('location_id'),
                            "arrived_at": update.get('arrived_at'),
                            "sent_at": update.get('sent_at'),
                            "updated_at": datetime.now(timezone.utc),
                            "updated_by": "system"
                        })

                    return jsonify({
                        'success': True,
                        'message': f'Successfully modified {len(updates)} wafer(s)'
                    })

                except SQLAlchemyError as e:
                    if 'fk_lots_mask_set_id_mask_sets' in str(e):
                        available_masks = get_available_mask_sets(conn)
                        return jsonify({
                            'success': False,
                            'error_type': 'invalid_mask_set',
                            'message': 'The selected Mask Set ID is not valid. Please choose from the available options.',
                            'available_mask_sets': available_masks
                        }), 400
                    raise

    except Exception as e:
        current_app.logger.error(f"Error modifying inventory: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f"Error modifying inventory: {str(e)}"
        }), 500


# Function for wafer IDs corrections
@inventory_bp.route('/api/inventory/correct-wafer', methods=['POST'])
@require_database()
@login_required
@permission_required('modify')
def correct_wafer_id():
    try:
        data = request.get_json()
        old_wafer_id = data.get('old_wafer_id')
        new_wafer_id = data.get('new_wafer_id')
        correction_reason = data.get('reason', '')

        # Input validation
        if not old_wafer_id or not new_wafer_id:
            return jsonify({
                'success': False,
                'message': 'Both old and new wafer IDs are required'
            }), 400

        # Get database connection
        engine = create_engine(get_db_url())

        with engine.connect() as conn:
            # Start transaction
            with conn.begin():
                try:
                    # Check if old wafer exists
                    check_old_query = text("""
                        SELECT wafer_id, lot_id, size, metadata
                        FROM wafers 
                        WHERE wafer_id = :wafer_id
                    """)
                    old_wafer = conn.execute(check_old_query,
                                             {"wafer_id": old_wafer_id}).fetchone()

                    if not old_wafer:
                        return jsonify({
                            'success': False,
                            'message': f'Original wafer ID {old_wafer_id} not found'
                        }), 404

                    # Check if new ID already exists
                    check_new_query = text("""
                        SELECT 1 FROM wafers WHERE wafer_id = :wafer_id
                    """)
                    exists = conn.execute(check_new_query,
                                          {"wafer_id": new_wafer_id}).fetchone()

                    if exists:
                        return jsonify({
                            'success': False,
                            'message': f'Cannot correct to {new_wafer_id} - this ID already exists'
                        }), 400

                    # Step 1: Create new wafer with corrected ID
                    insert_query = text("""
                        WITH source_wafer AS (
                            SELECT 
                                :new_wafer_id as wafer_id,
                                lot_id,
                                size,
                                CASE 
                                    WHEN metadata IS NULL THEN 
                                        jsonb_build_object('correction_info', :correction_info::jsonb)
                                    ELSE
                                        jsonb_set(metadata, '{correction_info}', :correction_info::jsonb)
                                END as metadata,
                                :updated_at as updated_at,
                                :updated_by as updated_by
                            FROM wafers
                            WHERE wafer_id = :old_wafer_id
                        )
                        INSERT INTO wafers (
                            wafer_id, lot_id, size, metadata, updated_at, updated_by
                        )
                        SELECT * FROM source_wafer
                    """)

                    # Prepare correction info
                    correction_info = json.dumps({
                        "corrected_from": old_wafer_id,
                        "correction_date": datetime.now(timezone.utc).isoformat(),
                        "reason": correction_reason
                    })

                    # Execute insert
                    conn.execute(insert_query, {
                        "new_wafer_id": new_wafer_id,
                        "old_wafer_id": old_wafer_id,
                        "correction_info": correction_info,
                        "updated_at": datetime.now(timezone.utc),
                        "updated_by": "system"
                    })

                    # Step 2: Update inventory references
                    update_inventory = text("""
                        UPDATE wafer_inventory
                        SET wafer_id = :new_wafer_id,
                            updated_at = :updated_at,
                            updated_by = :updated_by
                        WHERE wafer_id = :old_wafer_id
                        RETURNING wafer_id
                    """)

                    inventory_result = conn.execute(update_inventory, {
                        "new_wafer_id": new_wafer_id,
                        "old_wafer_id": old_wafer_id,
                        "updated_at": datetime.now(timezone.utc),
                        "updated_by": "system"
                    })

                    # Step 3: Mark old wafer as corrected
                    update_old = text("""
                        UPDATE wafers
                        SET metadata = CASE 
                                WHEN metadata IS NULL THEN 
                                    jsonb_build_object(
                                        'status', 'corrected',
                                        'corrected_to', :new_wafer_id
                                    )
                                ELSE
                                    metadata || 
                                    jsonb_build_object(
                                        'status', 'corrected',
                                        'corrected_to', :new_wafer_id
                                    )
                            END,
                            updated_at = :updated_at,
                            updated_by = :updated_by
                        WHERE wafer_id = :old_wafer_id
                        RETURNING wafer_id
                    """)

                    old_update_result = conn.execute(update_old, {
                        "new_wafer_id": new_wafer_id,
                        "old_wafer_id": old_wafer_id,
                        "updated_at": datetime.now(timezone.utc),
                        "updated_by": "system"
                    })

                    # All operations successful
                    return jsonify({
                        'success': True,
                        'message': f'Successfully corrected wafer ID from {old_wafer_id} to {new_wafer_id}',
                        'details': {
                            'old_wafer_id': old_wafer_id,
                            'new_wafer_id': new_wafer_id,
                            'correction_time': datetime.now(timezone.utc).isoformat(),
                            'reason': correction_reason
                        }
                    })

                except SQLAlchemyError as db_error:
                    # Log the specific database error
                    current_app.logger.error(
                        f"Database error: {str(db_error)}")
                    # Transaction will automatically rollback
                    raise

    except Exception as e:
        # Log the full error with traceback
        current_app.logger.error(f"Error correcting wafer ID: {str(e)}")
        current_app.logger.error(traceback.format_exc())

        return jsonify({
            'success': False,
            'message': "Error correcting wafer ID",
            'error_details': str(e)
        }), 500


@inventory_bp.route('/api/inventory/update_modules', methods=['POST'])
@require_database()
@login_required
@permission_required('modify')
def update_modules():
    try:
        data = request.get_json()
        if not data or not data.get('updates'):
            return jsonify({
                'success': False,
                'message': 'No update data provided'
            }), 400

        # Should be list of {wafer_id: "", module_name: ""}
        updates = data['updates']

        with get_db_cursor() as cursor:
            cursor.execute("BEGIN")
            try:
                for update in updates:
                    cursor.execute("""
                        UPDATE wafer_inventory
                        SET metadata = jsonb_set(
                            jsonb_set(
                                COALESCE(metadata, '{}'::jsonb),
                                '{lgt}',
                                COALESCE(metadata->'lgt', '{}'::jsonb)
                            ),
                            '{lgt,Modules}',
                            %s::jsonb
                        ),
                        updated_at = NOW()
                        WHERE wafer_id = %s
                    """, (json.dumps(update['module_name']), update['wafer_id']))

                cursor.execute("COMMIT")
                return jsonify({
                    'success': True,
                    'message': f'Successfully updated {len(updates)} records'
                })

            except Exception as e:
                cursor.execute("ROLLBACK")
                raise e

    except Exception as e:
        current_app.logger.error(f"Error updating modules: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'Error updating modules: {str(e)}'
        }), 500


# Delete wafers from inventory Function

@inventory_bp.route('/api/inventory/delete', methods=['DELETE'])
@require_database()
@login_required
@permission_required('delete')
def delete_inventory():
    try:
        data = request.get_json()
        current_app.logger.info(f"Delete request data: {data}")

        wafer_ids = data.get('wafer_ids', [])
        if not wafer_ids:
            return jsonify({
                'success': False,
                'message': 'No wafer IDs provided'
            }), 400

        with get_db_cursor() as cursor:
            cursor.execute("BEGIN")
            try:
                placeholders = ','.join(['%s'] * len(wafer_ids))
                cursor.execute(f"""
                    DELETE FROM wafer_inventory
                    WHERE wafer_id IN ({placeholders})
                    RETURNING wafer_id
                """, wafer_ids)

                deleted_wafers = [row['wafer_id'] for row in cursor.fetchall()]

                if not deleted_wafers:
                    cursor.execute("ROLLBACK")
                    return jsonify({
                        'success': False,
                        'message': 'No matching wafers found to delete'
                    }), 404

                cursor.execute("COMMIT")
                return jsonify({
                    'success': True,
                    'message': f'Successfully deleted {len(deleted_wafers)} wafer(s)',
                    'deleted_wafers': deleted_wafers
                })

            except Exception as e:
                cursor.execute("ROLLBACK")
                raise e

    except Exception as e:
        current_app.logger.error(f"Error deleting inventory: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'Error deleting inventory: {str(e)}'
        }), 500


def validate_wafer_metadata(metadata: Dict) -> Tuple[bool, str]:
    """Validate wafer inventory metadata structure."""
    required_fields = {'lgt': ['Modules', 'xfab_lot_id', 'lgt_lot_id']}

    if not isinstance(metadata, dict):
        return False, "Metadata must be a dictionary"

    if 'lgt' not in metadata:
        return False, "Missing required 'lgt' section in metadata"

    for field in required_fields['lgt']:
        if field not in metadata['lgt']:
            return False, f"Missing required field '{field}' in lgt section"

    return True, ""


def track_wafer_change(cursor, wafer_id: str, change_type: str, old_values: Dict, new_values: Dict) -> None:
    """
    Track changes to wafer inventory with improved change detection and storage.
    """
    try:
        # Get current metadata
        cursor.execute("""
            SELECT metadata FROM wafer_inventory WHERE wafer_id = %s
        """, [wafer_id])
        result = cursor.fetchone()
        if not result:
            return

        current_metadata = result[0] or {}

        # Initialize change history if it doesn't exist
        if 'change_history' not in current_metadata:
            current_metadata['change_history'] = []

        # Identify specific changes
        changes = {}
        if old_values and new_values:
            # Compare all fields for changes
            all_fields = set(old_values.keys()) | set(new_values.keys())
            for field in all_fields:
                old_val = old_values.get(field)
                new_val = new_values.get(field)

                # Handle datetime objects
                if isinstance(old_val, datetime):
                    old_val = old_val.isoformat()
                if isinstance(new_val, datetime):
                    new_val = new_val.isoformat()

                # Only record if values are different
                if old_val != new_val:
                    changes[field] = {
                        'old': old_val,
                        'new': new_val
                    }

        # Create change record with detailed changes
        change_record = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'change_type': change_type,
            'changed_by': 'operations_fr_inventory',
            'changes': changes,
            'old_values': old_values,
            'new_values': new_values
        }

        # Add new change record to history
        current_metadata['change_history'] = [change_record] + \
            current_metadata.get('change_history', [])[:9]

        # Update metadata in database
        cursor.execute("""
            UPDATE wafer_inventory
            SET metadata = %s::jsonb
            WHERE wafer_id = %s
        """, [json.dumps(current_metadata), wafer_id])

    except Exception as e:
        current_app.logger.error(f"Error tracking change: {str(e)}")
        current_app.logger.error(traceback.format_exc())


def modify_inventory():
    try:
        data = request.get_json()
        current_app.logger.info(f"Modify request data: {data}")

        wafer_ids = data.get('wafer_ids', [])
        if not wafer_ids:
            return jsonify({
                'success': False,
                'message': 'No wafer IDs provided'
            }), 400

        with get_db_cursor() as cursor:
            cursor.execute("BEGIN")
            try:
                # Process updates for each wafer
                for wafer_id in wafer_ids:
                    # Get old values before update
                    cursor.execute("""
                        SELECT 
                            wi.*,
                            w.lot_id,
                            wi.metadata->'lgt'->>'xfab_lot_id' as xfab_id,
                            wi.metadata->'lgt'->>'Modules' as module_name
                        FROM wafer_inventory wi
                        LEFT JOIN wafers w ON wi.wafer_id = w.wafer_id
                        WHERE wi.wafer_id = %s
                    """, [wafer_id])

                    old_values = dict(
                        cursor.fetchone()) if cursor.rowcount > 0 else None
                    if not old_values:
                        continue

                    # Perform the update
                    update_fields = []
                    update_values = []

                    # Build update query dynamically based on provided fields
                    if data.get('cassette_id') is not None:
                        update_fields.append("cassette_id = %s")
                        update_values.append(data['cassette_id'])

                    if data.get('slot_id') is not None:
                        update_fields.append("slot_id = %s")
                        update_values.append(data['slot_id'])

                    if data.get('location_id'):
                        update_fields.append("location_id = %s")
                        update_values.append(data['location_id'])

                    if data.get('arrived_at'):
                        update_fields.append("arrived_at = %s")
                        update_values.append(data['arrived_at'])

                    if data.get('sent_at'):
                        update_fields.append("sent_at = %s")
                        update_values.append(data['sent_at'])

                    # Always update metadata and timestamp
                    update_fields.extend([
                        "metadata = metadata || %s::jsonb",
                        "updated_at = NOW()",
                        "updated_by = %s"
                    ])

                    # Prepare metadata update
                    metadata_update = {
                        'lgt': {
                            'Modules': data.get('module_name', old_values.get('module_name', '')),
                            'xfab_lot_id': data.get('xfab_id', old_values.get('xfab_id', '')),
                            'lgt_lot_id': data.get('lot_id', old_values.get('lot_id', ''))
                        }
                    }

                    update_values.extend([
                        json.dumps(metadata_update),
                        data.get('updated_by', 'system')
                    ])

                    # Add wafer_id to values
                    update_values.append(wafer_id)

                    # Execute update
                    update_query = f"""
                        UPDATE wafer_inventory
                        SET {', '.join(update_fields)}
                        WHERE wafer_id = %s
                        RETURNING *
                    """

                    cursor.execute(update_query, update_values)
                    new_values = dict(cursor.fetchone())

                    # Track the change with detailed before/after values
                    track_wafer_change(
                        cursor,
                        wafer_id,
                        'UPDATE',
                        old_values,
                        new_values
                    )

                cursor.execute("COMMIT")
                return jsonify({
                    'success': True,
                    'message': f'Successfully modified wafers in inventory',
                    'modified_wafers': wafer_ids
                })

            except Exception as e:
                cursor.execute("ROLLBACK")
                raise e

    except Exception as e:
        current_app.logger.error(f"Error modifying inventory: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'Error modifying inventory: {str(e)}'
        }), 500


def get_wafer_change_history(cursor, wafer_id: str) -> List[Dict]:
    """Retrieve change history for a wafer."""
    cursor.execute("""
        SELECT metadata->'change_history' as history
        FROM wafer_inventory
        WHERE wafer_id = %s
    """, [wafer_id])
    result = cursor.fetchone()

    # Properly handle the result
    if result and result['history']:
        return result['history']
    return []


def check_field_exist(connection, table_name: str, field_name: str, value: str) -> bool:
    """
    Check if a specific field value exists in a given table.

    :param connection: Database connection object
    :param table_name: Name of the table to check
    :param field_name: Name of the field to check
    :param value: Value to check for existence
    :return: True if the value exists, False otherwise
    """
    query = text(f"SELECT 1 FROM {table_name} WHERE {
                 field_name} = :value LIMIT 1")
    result = connection.execute(query, {"value": value}).fetchone()
    return result is not None


def insert_wafer_inventory(db_url: str, wafer_data: WaferInventoryModel) -> bool:
    """
    Insert a single wafer into the wafer_inventory table with proper validation and error handling.

    :param db_url: Database connection string
    :param wafer_data: WaferInventoryModel instance containing wafer data
    :return: True if successful, False otherwise
    """
    engine = create_engine(db_url)

    with engine.connect() as connection:
        with connection.begin():
            try:
                # Check if wafer already exists in inventory
                if check_field_exist(connection, "wafer_inventory", "wafer_id", wafer_data.wafer_id):
                    print(
                        f"Wafer {wafer_data.wafer_id} already exists in inventory")
                    return False

                # Build metadata
                inventory_metadata = {
                    'lgt': {
                        'Modules': wafer_data.metadata.get('lgt', {}).get('Modules', ''),
                        'xfab_lot_id': wafer_data.metadata.get('lgt', {}).get('xfab_lot_id', ''),
                        'lgt_lot_id': wafer_data.metadata.get('lgt', {}).get('lgt_lot_id', '')
                    }
                }

                # Insert wafer into inventory
                insert_query = text("""
                    INSERT INTO wafer_inventory (
                        wafer_id, cassette_id, slot_id, arrived_at, sent_at,
                        location_id, metadata, updated_at, updated_by
                    ) VALUES (
                        :wafer_id, :cassette_id, :slot_id, :arrived_at, :sent_at,
                        :location_id, :metadata, NOW(), :updated_by
                    )
                """)

                connection.execute(insert_query, {
                    "wafer_id": wafer_data.wafer_id,
                    "cassette_id": wafer_data.cassette_id,
                    "slot_id": wafer_data.slot_id,
                    "arrived_at": wafer_data.arrived_at or datetime.now(timezone.utc),
                    "sent_at": wafer_data.sent_at,
                    "location_id": wafer_data.location_id,
                    "metadata": json.dumps(inventory_metadata),
                    "updated_by": wafer_data.updated_by or 'system'
                })

                return True

            except Exception as e:
                print(f"Error inserting wafer inventory: {e}")
                raise


def batch_update_wafer_inventory(db_url: str, updates: List[Dict]) -> Tuple[int, List[str]]:
    """
    Batch update multiple wafers in inventory with transaction support.

    :param db_url: Database connection string
    :param updates: List of dictionaries containing update data
    :return: Tuple of (number of successful updates, list of failed wafer IDs)
    """
    engine = create_engine(db_url)
    successful_updates = 0
    failed_wafers = []

    with engine.connect() as connection:
        with connection.begin():
            try:
                for update in updates:
                    wafer_id = update.get('wafer_id')

                    # Get existing metadata
                    fetch_query = text("""
                        SELECT metadata FROM wafer_inventory 
                        WHERE wafer_id = :wafer_id
                    """)
                    result = connection.execute(
                        fetch_query, {"wafer_id": wafer_id}).fetchone()

                    if not result:
                        failed_wafers.append(wafer_id)
                        continue

                    existing_metadata = result[0]

                    # Update metadata
                    if 'metadata_updates' in update:
                        for key, value in update['metadata_updates'].items():
                            if key.startswith('lgt.'):
                                # Handle nested updates in lgt section
                                _, field = key.split('.')
                                existing_metadata['lgt'][field] = value
                            else:
                                existing_metadata[key] = value

                    # Build update query
                    update_fields = []
                    update_values = {"wafer_id": wafer_id,
                                     "metadata": json.dumps(existing_metadata)}

                    for field in ['cassette_id', 'slot_id', 'location_id', 'arrived_at', 'sent_at']:
                        if field in update:
                            update_fields.append(f"{field} = :{field}")
                            update_values[field] = update[field]

                    update_fields.append("updated_at = NOW()")
                    update_fields.append("metadata = :metadata")

                    update_query = text(f"""
                        UPDATE wafer_inventory
                        SET {', '.join(update_fields)}
                        WHERE wafer_id = :wafer_id
                    """)

                    result = connection.execute(update_query, update_values)

                    if result.rowcount > 0:
                        successful_updates += 1
                    else:
                        failed_wafers.append(wafer_id)

                return successful_updates, failed_wafers

            except Exception as e:
                print(f"Error in batch update: {e}")
                raise


def get_wafer_inventory_history(db_url: str, wafer_id: str) -> List[Dict]:
    """
    Retrieve the complete history of changes for a specific wafer in inventory.

    :param db_url: Database connection string
    :param wafer_id: ID of the wafer to get history for
    :return: List of change records
    """
    engine = create_engine(db_url)

    with engine.connect() as connection:
        try:
            query = text("""
                SELECT metadata->'change_history' as history
                FROM wafer_inventory
                WHERE wafer_id = :wafer_id
            """)

            result = connection.execute(
                query, {"wafer_id": wafer_id}).fetchone()

            if result and result[0]:
                # Format and clean up the history records
                history = result[0]
                formatted_history = []

                for record in history:
                    formatted_record = {
                        'timestamp': datetime.fromisoformat(record['timestamp']),
                        'change_type': record['change_type'],
                        'changed_by': record['changed_by'],
                        'changes': {}
                    }

                    # Compare old and new values to identify specific changes
                    old_values = record.get('old_values', {})
                    new_values = record.get('new_values', {})

                    for key in set(old_values.keys()) | set(new_values.keys()):
                        if old_values.get(key) != new_values.get(key):
                            formatted_record['changes'][key] = {
                                'old': old_values.get(key),
                                'new': new_values.get(key)
                            }

                    formatted_history.append(formatted_record)

                return formatted_history

            return []

        except Exception as e:
            print(f"Error retrieving wafer history: {e}")
            return []


@inventory_bp.route('/api/inventory/history/<wafer_id>', methods=['GET'])
@login_required
def get_wafer_history_endpoint(wafer_id):
    try:
        with get_db_cursor() as cursor:
            cursor.execute("""
                SELECT metadata->'change_history' as history
                FROM wafer_inventory
                WHERE wafer_id = %s
            """, [wafer_id])

            result = cursor.fetchone()
            # Correctly access the history from the result
            history = result['history'] if result and result['history'] else []

            return jsonify({
                'success': True,
                'history': history
            })

    except Exception as e:
        current_app.logger.error(f"Error fetching history: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f"Error fetching history: {str(e)}"
        }), 500


def get_available_mask_sets(conn) -> List[str]:
    """Get list of valid mask set IDs from the database"""
    query = text("SELECT mask_set_id FROM mask_sets ORDER BY mask_set_id")
    result = conn.execute(query)
    return [row[0] for row in result]
# Add this endpoint to your inventory_bp.py file


@inventory_bp.route('/api/inventory/sync-wafers', methods=['POST'])
@require_database()
@login_required
@permission_required('sync')
def sync_wafers_to_inventory():
    """
    Sync wafers from wafers table to wafer_inventory table
    Uses the existing populate_wafer_inventory_from_wafers function
    with improved statistics tracking
    """
    try:
        # Get database URL
        db_url = get_db_url()

        # Initialize statistics
        stats = {
            "total_wafers": 0,         # Total wafers in wafers table
            "existing_wafers": 0,       # Wafers already in inventory
            "added": 0,                # New wafers added to inventory
            "errors": 0                # Errors encountered
        }

        engine = create_engine(db_url)
        with engine.connect() as connection:
            with connection.begin():
                try:
                    # Get total count of wafers in wafers table
                    total_wafers_query = text("SELECT COUNT(*) FROM wafers")
                    stats["total_wafers"] = connection.execute(
                        total_wafers_query).scalar()

                    # Get count of existing wafers in inventory
                    existing_wafers_query = text(
                        "SELECT COUNT(*) FROM wafer_inventory")
                    stats["existing_wafers"] = connection.execute(
                        existing_wafers_query).scalar()

                    # Find wafers that exist in wafers table but not in wafer_inventory
                    missing_wafers_query = text("""
                        SELECT COUNT(w.wafer_id)
                        FROM wafers w
                        LEFT JOIN wafer_inventory wi ON w.wafer_id = wi.wafer_id
                        WHERE wi.wafer_id IS NULL
                    """)
                    missing_count = connection.execute(
                        missing_wafers_query).scalar()

                    # If there are no missing wafers, return early
                    if missing_count == 0:
                        return jsonify({
                            "success": True,
                            "message": "All wafers are already in inventory. No updates needed.",
                            "total_wafers": stats["total_wafers"],
                            "existing_wafers": stats["existing_wafers"],
                            "added": 0,
                            "skipped": stats["existing_wafers"],
                            "errors": 0
                        })

                    # Execute the populate function with the new missing wafers
                    added_count = populate_wafer_inventory_from_wafers(db_url)

                    # Update statistics
                    stats["added"] = added_count or 0

                    return jsonify({
                        "success": True,
                        "message": f"Sync complete. Added {stats['added']} wafers to inventory.",
                        "total_wafers": stats["total_wafers"],
                        "existing_wafers": stats["existing_wafers"],
                        "added": stats["added"],
                        "skipped": stats["existing_wafers"],
                        "errors": stats["errors"]
                    })

                except Exception as e:
                    current_app.logger.error(
                        f"Error in populate_wafer_inventory_from_wafers: {str(e)}")
                    stats["errors"] += 1
                    return jsonify({
                        "success": False,
                        "message": f"Error syncing wafers to inventory: {str(e)}",
                        "total_wafers": stats["total_wafers"],
                        "existing_wafers": stats["existing_wafers"],
                        "added": 0,
                        "errors": 1
                    }), 500

    except Exception as e:
        current_app.logger.error(
            f"Error syncing wafers to inventory: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            "success": False,
            "message": f"Error syncing wafers to inventory: {str(e)}"
        }), 500

# Your existing populate_wafer_inventory_from_wafers function with a return value


def populate_wafer_inventory_from_wafers(db_url: str) -> int:
    """ 
    Remplit la table wafer_inventory avec les données des tables wafers et lots.
    Assigne un slot_id en fonction du numéro d'occurrence du wafer_id dans un lot donné.
    :param db_url: Connection string de la base de données.
    :return: Number of wafers added to inventory
    """
    engine = create_engine(db_url)
    with engine.connect() as connection:
        with connection.begin():
            try:
                # Étape 1 : Récupérer les wafers déjà présents dans wafer_inventory
                existing_wafer_query = text("""
                    SELECT wafer_id FROM wafer_inventory
                """)
                existing_wafers = {row[0] for row in connection.execute(
                    existing_wafer_query).fetchall()}

                # Étape 2 : Récupérer les données de wafers et lots avec un JOIN
                fetch_query = text("""
                    SELECT w.wafer_id, w.lot_id 
                    FROM wafers w
                    JOIN lots l ON w.lot_id = l.lot_id
                    ORDER BY w.lot_id, w.wafer_id
                """)
                records = connection.execute(fetch_query).fetchall()

                # Étape 3 : Préparer les données pour wafer_inventory
                inventory_data = []
                lot_id_map = {}

                for record in records:
                    wafer_id, lot_id = record

                    # Vérifier si le wafer_id existe déjà
                    if wafer_id in existing_wafers:
                        continue

                    # Déterminer le slot_id pour ce wafer_id
                    if lot_id not in lot_id_map:
                        lot_id_map[lot_id] = 0
                    lot_id_map[lot_id] += 1
                    slot_id = lot_id_map[lot_id]

                    # Construire le metadata pour wafer_inventory
                    wafer_metadata = {
                        "Modules": "",
                        "label_info": ""
                    }

                    # Préparer une ligne pour wafer_inventory
                    inventory_data.append({
                        "wafer_id": wafer_id,
                        "cassette_id": "Xfab cassette",
                        "slot_id": slot_id,
                        "arrived_at": datetime(2025, 1, 1, 0, 0, 0, tzinfo=timezone.utc),
                        "sent_at": None,
                        "location_id": "xfab_fr",
                        # Convertir en JSON string
                        "metadata": json.dumps(wafer_metadata)
                    })

                # Étape 4 : Insérer les données dans wafer_inventory
                insert_query = text("""
                    INSERT INTO wafer_inventory (
                        wafer_id, cassette_id, slot_id, arrived_at, sent_at, location_id, metadata
                    ) VALUES (
                        :wafer_id, :cassette_id, :slot_id, :arrived_at, :sent_at, :location_id, :metadata
                    )
                """)

                for data in inventory_data:
                    connection.execute(insert_query, data)

                current_app.logger.info(
                    f"Table wafer_inventory remplie avec succès ({len(inventory_data)} lignes ajoutées).")
                return len(inventory_data)  # Return the number of rows added

            except Exception as e:
                current_app.logger.error(
                    f"Erreur lors du remplissage de wafer_inventory : {e}")
                raise


def init_offline_storage(app):
    def get_db():
        if 'db' not in g:
            g.db = sqlite3.connect(
                app.config['OFFLINE_DATABASE'],
                detect_types=sqlite3.PARSE_DECLTYPES
            )
            g.db.row_factory = sqlite3.Row
        return g.db

    @app.before_request
    def check_connectivity():
        app.config['ONLINE_MODE'] = check_online_status()
        if not hasattr(g, 'db'):
            g.db = get_db()

    @app.teardown_appcontext
    def close_db(e=None):
        db = g.pop('db', None)
        if db is not None:
            db.close()

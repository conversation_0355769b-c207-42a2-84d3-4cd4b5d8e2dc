-- Migration script to rename metadata column to extra_data
-- This fixes the SQLAlchemy reserved attribute name issue

-- Check if the table exists and has the old metadata column
DO $$
BEGIN
    -- Check if notifications table exists
    IF EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'notifications'
    ) THEN
        -- Check if metadata column exists
        IF EXISTS (
            SELECT FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'notifications' 
            AND column_name = 'metadata'
        ) THEN
            -- Rename metadata column to extra_data
            ALTER TABLE notifications RENAME COLUMN metadata TO extra_data;
            RAISE NOTICE 'Renamed metadata column to extra_data';
        ELSE
            RAISE NOTICE 'metadata column does not exist, no migration needed';
        END IF;
    ELSE
        RAISE NOTICE 'notifications table does not exist';
    END IF;
END $$;

-- Update the comment for the renamed column
COMMENT ON COLUMN notifications.extra_data IS 'Additional JSON data for the notification';

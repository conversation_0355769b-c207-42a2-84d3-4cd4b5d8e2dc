#!/usr/bin/env python3

"""
This script patches the extract_task_gid function in db_operations.py
to handle more Asana URL formats.
"""

import re
import sys

def patch_file():
    file_path = 'database/db_operations.py'
    
    # Read the file
    with open(file_path, 'r') as f:
        lines = f.readlines()
    
    # Find the patterns list in the extract_task_gid function
    start_line = -1
    end_line = -1
    
    for i, line in enumerate(lines):
        if '# Try different regex patterns' in line:
            start_line = i + 1
        elif start_line > 0 and ']' in line and end_line == -1:
            end_line = i + 1
    
    if start_line == -1 or end_line == -1:
        print("Could not find the patterns list in the file.")
        return False
    
    # Replace the patterns list with the updated one
    new_patterns = [
        '        patterns = [\n',
        '            r"/(\d+)$",  # Matches the last number in the URL\n',
        '            r"/task/(\d+)",  # Matches task/number format\n',
        '            r"/(\d+)/f$",  # Matches number/f format\n',
        '            r"/(\d+)\\?",  # Matches number followed by query parameters\n',
        '            r"/project/\\d+/task/(\d+)",  # Matches project/number/task/number format\n',
        '        ]\n'
    ]
    
    # Replace the lines
    lines[start_line:end_line] = new_patterns
    
    # Write the updated file
    with open(file_path, 'w') as f:
        f.writelines(lines)
    
    print(f"Successfully updated {file_path}")
    return True

if __name__ == "__main__":
    patch_file()

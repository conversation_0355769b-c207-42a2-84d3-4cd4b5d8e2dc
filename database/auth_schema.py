# database/auth_schema.py
import os  # noqa F401
from database.auth_db_config import get_auth_db_connection


def setup_auth_tables():
    """
    Verify authentication-related tables exist and set up default permissions if needed.
    """
    conn = get_auth_db_connection()
    cursor = conn.cursor()

    try:
        # Check if tables exist
        cursor.execute(
            """
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_name = 'users'
            );
        """
        )
        result = cursor.fetchone()
        users_table_exists = bool(result["exists"]) if result else False

        cursor.execute(
            """
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_name = 'permissions'
            );
        """
        )
        result = cursor.fetchone()
        permissions_table_exists = bool(result["exists"]) if result else False

        if not users_table_exists:
            print("Warning: 'users' table does not exist. Please create it manually.")

        if not permissions_table_exists:
            print(
                "Warning: 'permissions' table does not exist. "
                "Please create it manually."
            )

        if not users_table_exists or not permissions_table_exists:
            return

        # Check if we need to insert default permissions
        cursor.execute("SELECT COUNT(*) FROM permissions")
        result = cursor.fetchone()
        count = result["count"] if result else 0

        if count == 0:
            # Insert default permissions
            default_permissions = [
                ("admin", "view"),
                ("admin", "create"),
                ("admin", "modify"),
                ("admin", "delete"),
                ("admin", "sync"),
                ("colleague", "view"),
                ("colleague", "create"),
                ("colleague", "sync"),
            ]

            for role, permission in default_permissions:
                cursor.execute(
                    "INSERT INTO permissions (role, permission) VALUES (%s, %s)",
                    (role, permission),
                )

            print("Default permissions inserted")

        conn.commit()
        print("Authentication tables verification complete")

    except Exception as e:
        conn.rollback()
        print(f"Error verifying authentication tables: {str(e)}")
        import traceback

        print(f"Traceback: {traceback.format_exc()}")
    finally:
        cursor.close()
        conn.close()


if __name__ == "__main__":
    # This allows running this file directly to verify the tables
    setup_auth_tables()

-- Inventory table
CREATE TABLE IF NOT EXISTS inventory (
    id SERIAL PRIMARY KEY,
    cassette_id VARCHAR(100),
    lgt_lot_id VARCHAR(100) NOT NULL,
    xfab_lot_id VARCHAR(100),
    slot_id VARCHAR(50),
    scribe_id VARCHAR(100),
    location VARCHAR(100),
    modules TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Inventory indices
CREATE INDEX IF NOT EXISTS idx_inventory_lgt_lot_id ON inventory(lgt_lot_id);
CREATE INDEX IF NOT EXISTS idx_inventory_xfab_lot_id ON inventory(xfab_lot_id);
CREATE INDEX IF NOT EXISTS idx_inventory_location ON inventory(location);

-- Locations table
CREATE TABLE IF NOT EXISTS locations (
    id SERIAL PRIMARY KEY,
    location_name VARCHAR(100) NOT NULL UNIQUE,
    shipping_address TEXT NOT NULL,
    contact_person VARCHAR(100) NOT NULL,
    phone VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    asana_link TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Location indices
CREATE INDEX IF NOT EXISTS idx_location_name ON locations(location_name);

-- Add foreign key constraint to ensure inventory location references valid locations
ALTER TABLE inventory 
ADD CONSTRAINT fk_inventory_location 
FOREIGN KEY (location) 
REFERENCES locations(location_name) 
ON DELETE RESTRICT 
ON UPDATE CASCADE;




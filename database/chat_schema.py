"""
Initialize chat-related database tables in the authentication database.
"""

import os
import logging
from database.auth_db_config import get_auth_db_connection

# Configure logging
logger = logging.getLogger(__name__)


def setup_chat_tables():
    """
    Set up chat-related tables in the authentication database.
    """
    try:
        # Read the SQL schema file
        schema_path = os.path.join(os.path.dirname(__file__), "chat_schema.sql")
        with open(schema_path, "r") as f:
            schema_sql = f.read()

        # Execute the SQL to create tables
        with get_auth_db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(schema_sql)
            conn.commit()
            
        logger.info("Chat tables initialized successfully")
        return True
    except Exception as e:
        logger.error(f"Error initializing chat tables: {str(e)}")
        return False


if __name__ == "__main__":
    # This allows running this file directly to initialize the tables
    setup_chat_tables()

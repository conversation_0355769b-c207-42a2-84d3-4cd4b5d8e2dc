# database/auth_db_config.py
import os
import logging
import psycopg2
from psycopg2.extras import RealDictCursor
from contextlib import contextmanager
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add explicit path to .env file
env_path = os.path.join(os.path.dirname(__file__), ".env")
load_dotenv(env_path, override=True)

# Try to import the secrets handler if available
try:
    from secrets_handler import get_secret

    has_secrets_handler = True
except ImportError:
    has_secrets_handler = False

# Auth Database configuration
if has_secrets_handler:
    # Try to get from Docker secrets first, then fall back to environment variables
    AUTH_DB_HOST = get_secret(
        "auth_db_host", os.getenv("AUTH_DB_HOST", "pgdb.lig.local")
    )
    AUTH_DB_NAME = get_secret("auth_db_name", os.getenv("AUTH_DB_NAME", "shipment_fr"))
    AUTH_DB_USER = get_secret(
        "auth_db_user", os.getenv("AUTH_DB_USER", "operations_fr_inventory")
    )
    AUTH_DB_PASSWORD = get_secret("auth_db_password", os.getenv("AUTH_DB_PASSWORD"))
else:
    # Fall back to environment variables only
    AUTH_DB_HOST = os.getenv("AUTH_DB_HOST", "pgdb.lig.local")
    AUTH_DB_NAME = os.getenv("AUTH_DB_NAME", "shipment_fr")
    AUTH_DB_USER = os.getenv("AUTH_DB_USER", "operations_fr_inventory")
    AUTH_DB_PASSWORD = os.getenv("AUTH_DB_PASSWORD")

# Log database connection info (without password)
logger.info(
    f"Connecting to auth database: {AUTH_DB_NAME} on host: {AUTH_DB_HOST} as user: {AUTH_DB_USER}"
)


def get_auth_db_connection():
    """Get authentication database connection using psycopg2"""
    try:
        logger.info(f"Attempting to connect to auth database '{AUTH_DB_NAME}' on host '{AUTH_DB_HOST}'")
        conn = psycopg2.connect(
            host=AUTH_DB_HOST,
            database=AUTH_DB_NAME,
            user=AUTH_DB_USER,
            password=AUTH_DB_PASSWORD,
            cursor_factory=RealDictCursor,
            connect_timeout=5,  # Increased timeout
        )
        logger.info("Auth database connection established successfully")
        return conn
    except Exception as e:
        logger.error(f"Auth database connection error: {str(e)}")
        raise


@contextmanager
def get_auth_db_cursor():
    """Context manager for auth database cursors"""
    conn = get_auth_db_connection()
    try:
        cursor = conn.cursor()
        yield cursor
        conn.commit()
    except Exception as e:
        conn.rollback()
        logger.error(f"Auth database cursor error: {str(e)}")
        raise e
    finally:
        cursor.close()
        conn.close()


def test_auth_connection():
    """Test auth database connection"""
    try:
        with get_auth_db_cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            if result:
                return "Auth database connection successful"
    except Exception as e:
        logger.error(f"Auth database connection error: {str(e)}")
        return None
    return None

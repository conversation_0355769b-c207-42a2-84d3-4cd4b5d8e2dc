def extract_task_gid(url_task: str):
    """
    Extract the task_gid from the Asana task URL.
    Handles multiple URL formats.
    """
    try:
        if not url_task:
            return None

        # Remove trailing slashes and 'f' if present
        url_task = url_task.rstrip("/f").rstrip("/")

        # Try different regex patterns
        patterns = [
            r"/(\d+)$",  # Matches the last number in the URL
            r"/task/(\d+)",  # Matches task/number format
            r"/(\d+)/f$",  # Matches number/f format
            r"/(\d+)\?",  # Matches number followed by query parameters
            r"/project/\d+/task/(\d+)",  # Matches project/number/task/number format
        ]

        import re
        for pattern in patterns:
            match = re.search(pattern, url_task)
            if match:
                task_gid = match.group(1)
                # Validate that we got a reasonable task ID
                if task_gid and task_gid.isdigit():
                    return task_gid

        return None

    except Exception as e:
        print(f"Error extracting task GID from URL '{url_task}': {str(e)}")
        return None

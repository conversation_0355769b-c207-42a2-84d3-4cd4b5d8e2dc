# db_helpers.py - Core database functionality
from flask import current_app  # noqa F401
from functools import wraps
import logging
from database.db_config import get_db_connection  # noqa F401Ò

logger = logging.getLogger(__name__)


def require_database():
    """Decorator to ensure database access"""

    def decorator(f):
        @wraps(f)
        def wrapped(*args, **kwargs):
            # Simply pass through to the function since we've removed offline mode
            return f(*args, **kwargs)

        return wrapped

    return decorator

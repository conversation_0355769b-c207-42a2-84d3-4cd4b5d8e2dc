-- Users table for authentication
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    email VA<PERSON>HAR(255) NOT NULL UNIQUE,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'colleague',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP
);

-- Create indices for faster lookups
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);

-- Permissions table to store role-based permissions
CREATE TABLE IF NOT EXISTS permissions (
    id SERIAL PRIMARY KEY,
    role VARCHAR(50) NOT NULL,
    permission VARCHAR(50) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(role, permission)
);

-- Default permissions
INSERT INTO permissions (role, permission) 
VALUES 
('admin', 'view'),
('admin', 'create'),
('admin', 'modify'),
('admin', 'delete'),
('admin', 'manage_users'),
('admin', 'access_admin'),
('colleague', 'view'),
('viewer', 'view')
ON CONFLICT (role, permission) DO NOTHING;
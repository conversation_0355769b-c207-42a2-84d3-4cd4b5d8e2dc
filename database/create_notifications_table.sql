-- Create notifications table for bell notification system
-- This table will store in-app notifications for users

CREATE TABLE IF NOT EXISTS notifications (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    notification_type VARCHAR(50) DEFAULT 'info', -- 'info', 'success', 'warning', 'error', 'email'
    is_read BOOLEAN DEFAULT FALSE,
    is_email_related BOOLEAN DEFAULT FALSE,
    email_subject VARCHAR(255),
    email_sender VARCHAR(255),
    email_recipients TEXT, -- JSON array of recipients
    related_entity_type VARCHAR(50), -- 'support_ticket', 'shipment', 'rfq', 'inventory', etc.
    related_entity_id VARCHAR(100),
    extra_data JSONB, -- Additional data like email content, links, etc.
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE, -- Optional expiration for temporary notifications
    
    -- Foreign key constraint (assuming users table exists)
    CONSTRAINT fk_notifications_user_id 
        FOREIGN KEY (user_id) 
        REFERENCES users(id) 
        ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_notifications_user_unread ON notifications(user_id, is_read) WHERE is_read = FALSE;
CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(notification_type);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_notifications_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_notifications_updated_at ON notifications;
CREATE TRIGGER trigger_update_notifications_updated_at
    BEFORE UPDATE ON notifications
    FOR EACH ROW
    EXECUTE FUNCTION update_notifications_updated_at();

-- Insert some sample notifications for testing (optional)
-- You can remove this section after testing
-- Use the first available user ID for sample data
DO $$
DECLARE
    first_user_id INTEGER;
BEGIN
    -- Get the first user ID
    SELECT id INTO first_user_id FROM users ORDER BY id LIMIT 1;

    IF first_user_id IS NOT NULL THEN
        INSERT INTO notifications (user_id, title, message, notification_type, extra_data) VALUES
        (first_user_id, 'Welcome to Talaria!', 'Your notification system is now active. You will receive updates about shipments, support tickets, and system alerts here.', 'info', '{"source": "system", "welcome": true}'),
        (first_user_id, 'Email System Ready', 'Your email notification system is configured and ready to use.', 'success', '{"source": "system", "email_config": true}');

        RAISE NOTICE 'Sample notifications created for user ID: %', first_user_id;
    ELSE
        RAISE NOTICE 'No users found, skipping sample notifications';
    END IF;
END $$;

-- Create a view for easy notification querying with user info
CREATE OR REPLACE VIEW notification_summary AS
SELECT 
    n.id,
    n.user_id,
    u.name,
    u.email as user_email,
    n.title,
    n.message,
    n.notification_type,
    n.is_read,
    n.is_email_related,
    n.email_subject,
    n.email_sender,
    n.related_entity_type,
    n.related_entity_id,
    n.created_at,
    n.updated_at,
    n.expires_at,
    CASE 
        WHEN n.expires_at IS NOT NULL AND n.expires_at < CURRENT_TIMESTAMP THEN TRUE
        ELSE FALSE
    END as is_expired
FROM notifications n
LEFT JOIN users u ON n.user_id = u.id
ORDER BY n.created_at DESC;

COMMENT ON TABLE notifications IS 'Stores in-app notifications for the bell notification system';
COMMENT ON COLUMN notifications.notification_type IS 'Type of notification: info, success, warning, error, email';
COMMENT ON COLUMN notifications.is_email_related IS 'True if this notification was created from an email action';
COMMENT ON COLUMN notifications.extra_data IS 'Additional JSON data for the notification';
COMMENT ON COLUMN notifications.expires_at IS 'Optional expiration timestamp for temporary notifications';

"""
Database operations module for wafer inventory management.
This module provides database query functions that replace Excel-based operations.
"""

from sqlalchemy import create_engine, text, and_, or_, desc  # noqa: F401
from sqlalchemy.orm import sessionmaker, Session  # noqa: F401
from contextlib import contextmanager
from core.models.models import (
    Wafer,
    Location,
    WaferInventory,
    # Shipment,
    # ShipmentWafer,
    # ShipmentStatus,
)
from typing import List, Dict, Optional, Tuple, Any  # noqa: F401
from datetime import datetime, timedelta
import logging
import uuid  # noqa: F401
from database.db_config import (
    # get_db_connection,
    # get_db_cursor,
    # DATABASE_URL,
    engine,
)  # noqa: F401
import re
import traceback
import asana
from collections import defaultdict
import requests
import json  # noqa: F401
import os  # noqa: F401


# Configure logging
logger = logging.getLogger(__name__)

# Create SQLAlchemy session factory
SessionFactory = sessionmaker(bind=engine)


@contextmanager
def get_session():
    """Context manager for database sessions"""
    session = SessionFactory()
    try:
        # Set statement timeout at the database level
        session.execute(
            text("SET statement_timeout = 10000")
        )  # 10 seconds in milliseconds
        yield session
        session.commit()
    except Exception as e:
        session.rollback()
        logger.error(f"Database session error: {str(e)}")
        raise
    finally:
        session.close()


# =============================================
# QUERY FUNCTIONS
# =============================================


def get_all_lots():
    """Get all unique lot IDs from the database"""
    try:
        with get_session() as session:
            lots = session.query(Wafer.lot_id).distinct().all()
            return [lot[0] for lot in lots if lot[0]]
    except Exception as e:
        logger.error(f"Error getting lots: {str(e)}")
        return []


def get_wafers_for_lot(lot_id: str) -> List[Dict]:
    """
    Get all wafers for a specific lot or multiple lots (comma-separated)
    Args:
        lot_id: Single lot ID or multiple lot IDs separated by commas
    Returns:
        List of wafer dictionaries with inventory data
    """
    try:
        with get_session() as session:
            lot_list = [lot.strip() for lot in lot_id.split(",")]

            # Query wafers with inventory and location data
            query = (
                session.query(
                    Wafer.wafer_id.label("Scribe ID"),
                    Wafer.lot_id.label("Lot"),
                    WaferInventory.cassette_id.label("Cassette ID"),
                    WaferInventory.slot_id.label("Slot ID"),
                    Location.label.label("Location"),
                    WaferInventory.inventory_metadata,
                )
                .join(WaferInventory, Wafer.wafer_id == WaferInventory.wafer_id)
                .join(Location, WaferInventory.location_id == Location.location_id)
                .filter(Wafer.lot_id.in_(lot_list))
            )

            results = query.all()
            logger.info(f"Query returned {len(results)} wafers")

            # Format query results into dictionaries
            wafers = []
            for row in results:
                try:
                    # Check if row is a dict-like object (KeyedTuple from SQLAlchemy)
                    if hasattr(row, "_asdict"):
                        # Convert to dictionary if it's a named tuple
                        row_dict = row._asdict()
                    elif hasattr(row, "keys"):
                        # Already a dict-like object
                        row_dict = row
                    else:
                        # Handle as tuple using column names in order
                        keys = [
                            "Scribe ID",
                            "Lot",
                            "Cassette ID",
                            "Slot ID",
                            "Location",
                            "inventory_metadata",
                        ]
                        row_dict = dict(zip(keys, row))

                    wafer_dict = {
                        "Cassette ID": row_dict.get("Cassette ID") or "N/A",
                        "Slot ID": row_dict.get("Slot ID"),
                        "Lot": row_dict.get("Lot"),
                        "Scribe ID": row_dict.get("Scribe ID"),
                        "Location": row_dict.get("Location"),
                        "Asana Link": "N/A",
                    }

                    # Extract Asana link from metadata if available
                    metadata = row_dict.get("inventory_metadata")
                    if (
                        metadata
                        and isinstance(metadata, dict)
                        and "asana_link" in metadata
                    ):
                        wafer_dict["Asana Link"] = metadata["asana_link"]

                    wafers.append(wafer_dict)
                except Exception as row_error:
                    logger.error(f"Error processing row {row}: {str(row_error)}")
                    # Continue processing other rows

            logger.info(f"Processed {len(wafers)} wafer records")
            return wafers

    except Exception as e:
        logger.error(f"Error getting wafers for lot {lot_id}: {str(e)}")
        return []


def extract_task_gid(url_task: str) -> Optional[str]:
    """
    Extract the task_gid from the Asana task URL.
    Handles multiple URL formats.
    """
    try:
        if not url_task:
            return None

        # Remove trailing slashes and 'f' if present
        url_task = url_task.rstrip("/f").rstrip("/")

        # Try different regex patterns
        patterns = [
            r"/(\d+)$",  # Matches the last number in the URL
            r"/task/(\d+)",  # Matches task/number format
            r"/(\d+)/f$",  # Matches number/f format
            r"/(\d+)\?",  # Matches number followed by query parameters
            r"/project/\d+/task/(\d+)",  # Matches project/number/task/number format
        ]

        for pattern in patterns:
            match = re.search(pattern, url_task)
            if match:
                task_gid = match.group(1)
                # Validate that we got a reasonable task ID
                if task_gid and task_gid.isdigit():
                    return task_gid

        return None

    except Exception as e:
        logger.error(f"Error extracting task GID from URL '{url_task}': {str(e)}")
        return None

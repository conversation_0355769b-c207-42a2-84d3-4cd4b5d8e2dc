"""
Database models for the chat functionality.
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, Sequence, Tuple, Union

import psycopg2
from psycopg2.extras import RealDictCursor

from database.auth_db_config import get_auth_db_connection

# Configure logging
logger = logging.getLogger(__name__)

# Table creation SQL
CREATE_CHAT_TABLES_SQL = """
-- Chat training data table
CREATE TABLE IF NOT EXISTS chat_training_data (
    id SERIAL PRIMARY KEY,
    pattern TEXT NOT NULL,
    response TEXT NOT NULL,
    category VARCHAR(50) DEFAULT 'general',
    priority INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VA<PERSON><PERSON><PERSON>(100),
    active BOOLEAN DEFAULT TRUE
);

-- Chat conversation history
CREATE TABLE IF NOT EXISTS chat_history (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(100),
    user_message TEXT NOT NULL,
    bot_response TEXT NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    feedback INTEGER DEFAULT 0,
    session_id VARCHAR(100)
);

-- Chat feedback table
CREATE TABLE IF NOT EXISTS chat_feedback (
    id SERIAL PRIMARY KEY,
    chat_history_id INTEGER REFERENCES chat_history(id),
    rating INTEGER,
    comment TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
"""


def init_chat_tables():
    """Initialize the chat-related database tables."""
    try:
        with get_auth_db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(CREATE_CHAT_TABLES_SQL)
            conn.commit()
        logger.info("Chat tables initialized successfully")
        return True
    except Exception as e:
        logger.error(f"Error initializing chat tables: {str(e)}")
        return False


def add_training_data(
    pattern: str,
    response: str,
    category: str = "general",
    priority: int = 1,
    created_by: str = "system",
) -> bool:
    """
    Add new training data to the chatbot.

    Args:
        pattern: The pattern to match in user messages
        response: The response to provide
        category: Category for organizing training data
        priority: Priority level (higher numbers = higher priority)
        created_by: User who created this training data

    Returns:
        bool: Success status
    """
    try:
        with get_auth_db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    INSERT INTO chat_training_data
                    (pattern, response, category, priority, created_by)
                    VALUES (%s, %s, %s, %s, %s)
                    RETURNING id
                    """,
                    (pattern, response, category, priority, created_by),
                )
                result = cursor.fetchone()
            conn.commit()
        if result and hasattr(result, "__getitem__"):
            logger.info(f"Added new training data with ID: {result[0]}")
        else:
            logger.info("Added new training data but couldn't retrieve ID")
        return True
    except Exception as e:
        logger.error(f"Error adding training data: {str(e)}")
        return False


def get_training_data(
    category: Optional[str] = None, active_only: bool = True
) -> Sequence[Dict[str, Any]]:
    """
    Get training data, optionally filtered by category.

    Args:
        category: Optional category filter
        active_only: Whether to return only active training data

    Returns:
        List of training data dictionaries
    """
    try:
        with get_auth_db_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                if category:
                    query = """
                    SELECT * FROM chat_training_data
                    WHERE category = %s
                    """
                    params = (category,)
                    if active_only:
                        query += " AND active = TRUE"
                    query += " ORDER BY priority DESC, created_at DESC"
                    cursor.execute(query, params)
                else:
                    query = "SELECT * FROM chat_training_data"
                    if active_only:
                        query += " WHERE active = TRUE"
                    query += " ORDER BY priority DESC, created_at DESC"
                    cursor.execute(query)

                return cursor.fetchall()
    except Exception as e:
        logger.error(f"Error retrieving training data: {str(e)}")
        return []


def log_conversation(
    user_id: str, user_message: str, bot_response: str, session_id: Optional[str] = None
) -> int:
    """
    Log a conversation exchange to the history table.

    Args:
        user_id: Identifier for the user
        user_message: The message from the user
        bot_response: The response from the bot
        session_id: Optional session identifier

    Returns:
        int: The ID of the new history record, or -1 on error
    """
    try:
        with get_auth_db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    INSERT INTO chat_history
                    (user_id, user_message, bot_response, session_id)
                    VALUES (%s, %s, %s, %s)
                    RETURNING id
                    """,
                    (user_id, user_message, bot_response, session_id),
                )
                result = cursor.fetchone()
            conn.commit()
        if result and hasattr(result, "__getitem__"):
            return result[0]
        return -1
    except Exception as e:
        logger.error(f"Error logging conversation: {str(e)}")
        return -1


def add_feedback(
    chat_history_id: int, rating: int, comment: Optional[str] = None
) -> bool:
    """
    Add feedback for a specific chat exchange.

    Args:
        chat_history_id: The ID of the chat history entry
        rating: Numeric rating (typically 1-5)
        comment: Optional text feedback

    Returns:
        bool: Success status
    """
    try:
        with get_auth_db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    INSERT INTO chat_feedback
                    (chat_history_id, rating, comment)
                    VALUES (%s, %s, %s)
                    """,
                    (chat_history_id, rating, comment),
                )

                # Also update the feedback score in the history table
                cursor.execute(
                    """
                    UPDATE chat_history
                    SET feedback = %s
                    WHERE id = %s
                    """,
                    (rating, chat_history_id),
                )
            conn.commit()
        return True
    except Exception as e:
        logger.error(f"Error adding feedback: {str(e)}")
        return False


def get_similar_patterns(user_message: str, limit: int = 5) -> Sequence[Dict[str, Any]]:
    """
    Find training data with patterns similar to the user message.
    This is a simple implementation using LIKE queries.
    For production, consider using full-text search or embeddings.

    Args:
        user_message: The user's message
        limit: Maximum number of results to return

    Returns:
        List of matching training data
    """
    try:
        # Convert message to lowercase for case-insensitive matching
        message_lower = user_message.lower()

        # Extract words for partial matching
        words = [word for word in message_lower.split() if len(word) > 3]

        if not words:
            return []

        with get_auth_db_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # Build a query that checks for partial matches of significant words
                placeholders = []
                params = []

                for word in words:
                    placeholders.append("LOWER(pattern) LIKE %s")
                    params.append(f"%{word}%")

                query = f"""
                SELECT * FROM chat_training_data
                WHERE active = TRUE AND ({" OR ".join(placeholders)})
                ORDER BY priority DESC, created_at DESC
                LIMIT %s
                """
                params.append(limit)

                cursor.execute(query, params)
                return cursor.fetchall()
    except Exception as e:
        logger.error(f"Error finding similar patterns: {str(e)}")
        return []


def find_best_response(user_message: str) -> Optional[str]:
    """
    Find the best response for a user message based on pattern matching.

    Args:
        user_message: The user's message

    Returns:
        The best matching response or None if no match found
    """
    try:
        # Convert message to lowercase for case-insensitive matching
        message_lower = user_message.lower()

        with get_auth_db_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # First try exact matches
                cursor.execute(
                    """
                    SELECT response FROM chat_training_data
                    WHERE active = TRUE AND LOWER(pattern) = %s
                    ORDER BY priority DESC, created_at DESC
                    LIMIT 1
                    """,
                    (message_lower,),
                )
                result = cursor.fetchone()

                if result:
                    return result["response"]

                # If no exact match, try contains matches
                cursor.execute(
                    """
                    SELECT response FROM chat_training_data
                    WHERE active = TRUE AND %s LIKE CONCAT('%%', LOWER(pattern), '%%')
                    ORDER BY priority DESC, created_at DESC
                    LIMIT 1
                    """,
                    (message_lower,),
                )
                result = cursor.fetchone()

                if result:
                    return result["response"]

                # If still no match, try word matches
                words = message_lower.split()
                if words:
                    placeholders = []
                    params = []

                    for word in words:
                        if len(word) > 3:  # Only consider significant words
                            placeholders.append("LOWER(pattern) LIKE %s")
                            params.append(f"%{word}%")

                    if placeholders:
                        query = f"""
                        SELECT response FROM chat_training_data
                        WHERE active = TRUE AND ({" OR ".join(placeholders)})
                        ORDER BY priority DESC, created_at DESC
                        LIMIT 1
                        """
                        cursor.execute(query, params)
                        result = cursor.fetchone()

                        if result:
                            return result["response"]

                return None
    except Exception as e:
        logger.error(f"Error finding best response: {str(e)}")
        return None


def get_training_data_by_id(id: int) -> Optional[Dict[str, Any]]:
    """
    Get a single training data item by ID.

    Args:
        id: The ID of the training data item to retrieve

    Returns:
        Dictionary with training data or None if not found
    """
    try:
        with get_auth_db_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(
                    """
                    SELECT * FROM chat_training_data
                    WHERE id = %s
                    """,
                    (id,),
                )
                result = cursor.fetchone()
                return result
    except Exception as e:
        logger.error(f"Error retrieving training data by ID {id}: {str(e)}")
        return None


def update_training_data(
    id: int,
    pattern: str,
    response: str,
    category: str = "general",
    priority: int = 1,
    active: bool = True,
) -> bool:
    """
    Update existing training data.

    Args:
        id: The ID of the training data to update
        pattern: The pattern to match in user messages
        response: The response to provide
        category: Category for organizing training data
        priority: Priority level (higher numbers = higher priority)
        active: Whether this training data is active

    Returns:
        bool: Success status
    """
    try:
        with get_auth_db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    UPDATE chat_training_data
                    SET pattern = %s,
                        response = %s,
                        category = %s,
                        priority = %s,
                        active = %s,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = %s
                    """,
                    (pattern, response, category, priority, active, id),
                )
            conn.commit()
        logger.info(f"Updated training data with ID: {id}")
        return True
    except Exception as e:
        logger.error(f"Error updating training data with ID {id}: {str(e)}")
        return False


def delete_training_data(id: int) -> bool:
    """
    Delete training data by ID.

    Args:
        id: The ID of the training data to delete

    Returns:
        bool: Success status
    """
    try:
        with get_auth_db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    DELETE FROM chat_training_data
                    WHERE id = %s
                    """,
                    (id,),
                )
            conn.commit()
        logger.info(f"Deleted training data with ID: {id}")
        return True
    except Exception as e:
        logger.error(f"Error deleting training data with ID {id}: {str(e)}")
        return False

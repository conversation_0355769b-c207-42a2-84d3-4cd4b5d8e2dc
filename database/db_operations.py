"""
Database operations module for wafer inventory management.
This module provides database query functions that replace Excel-based operations.
"""

from sqlalchemy import create_engine, text, and_, or_, desc  # noqa: F401
from sqlalchemy.orm import sessionmaker, Session  # noqa: F401
from contextlib import contextmanager
from core.models.models import (
    Wafer,
    Location,
    WaferInventory,
    # Shipment,
    # ShipmentWafer,
    # ShipmentStatus,
)
from typing import List, Dict, Optional, Tuple, Any  # noqa: F401
from datetime import datetime, timedelta
import logging
import uuid  # noqa: F401
from database.db_config import (
    # get_db_connection,
    # get_db_cursor,
    # DATABASE_URL,
    engine,
)  # noqa: F401
import re
import traceback
import asana
from collections import defaultdict
import requests
import json  # noqa: F401
import os  # noqa: F401


# Configure logging
logger = logging.getLogger(__name__)

# Create SQLAlchemy session factory
SessionFactory = sessionmaker(bind=engine)


@contextmanager
def get_session():
    """Context manager for database sessions"""
    session = SessionFactory()
    try:
        # Set statement timeout at the database level
        session.execute(
            text("SET statement_timeout = 10000")
        )  # 10 seconds in milliseconds
        yield session
        session.commit()
    except Exception as e:
        session.rollback()
        logger.error(f"Database session error: {str(e)}")
        raise
    finally:
        session.close()


# =============================================
# QUERY FUNCTIONS
# =============================================


def get_all_lots():
    """Get all unique lot IDs from the database"""
    try:
        with get_session() as session:
            lots = session.query(Wafer.lot_id).distinct().all()
            return [lot[0] for lot in lots if lot[0]]
    except Exception as e:
        logger.error(f"Error getting lots: {str(e)}")
        return []


def get_wafers_for_lot(lot_id: str) -> List[Dict]:
    """
    Get all wafers for a specific lot or multiple lots (comma-separated)
    Args:
        lot_id: Single lot ID or multiple lot IDs separated by commas
    Returns:
        List of wafer dictionaries with inventory data
    """
    try:
        with get_session() as session:
            lot_list = [lot.strip() for lot in lot_id.split(",")]

            # Query wafers with inventory and location data
            query = (
                session.query(
                    Wafer.wafer_id.label("Scribe ID"),
                    Wafer.lot_id.label("Lot"),
                    WaferInventory.cassette_id.label("Cassette ID"),
                    WaferInventory.slot_id.label("Slot ID"),
                    Location.label.label("Location"),
                    WaferInventory.inventory_metadata,
                )
                .join(WaferInventory, Wafer.wafer_id == WaferInventory.wafer_id)
                .join(Location, WaferInventory.location_id == Location.location_id)
                .filter(Wafer.lot_id.in_(lot_list))
            )

            results = query.all()
            logger.info(f"Query returned {len(results)} wafers")

            # Format query results into dictionaries
            wafers = []
            for row in results:
                try:
                    # Check if row is a dict-like object (KeyedTuple from SQLAlchemy)
                    if hasattr(row, "_asdict"):
                        # Convert to dictionary if it's a named tuple
                        row_dict = row._asdict()
                    elif hasattr(row, "keys"):
                        # Already a dict-like object
                        row_dict = row
                    else:
                        # Handle as tuple using column names in order
                        keys = [
                            "Scribe ID",
                            "Lot",
                            "Cassette ID",
                            "Slot ID",
                            "Location",
                            "inventory_metadata",
                        ]
                        row_dict = dict(zip(keys, row))

                    wafer_dict = {
                        "Cassette ID": row_dict.get("Cassette ID") or "N/A",
                        "Slot ID": row_dict.get("Slot ID"),
                        "Lot": row_dict.get("Lot"),
                        "Scribe ID": row_dict.get("Scribe ID"),
                        "Location": row_dict.get("Location"),
                        "Asana Link": "N/A",
                    }

                    # Extract Asana link from metadata if available
                    metadata = row_dict.get("inventory_metadata")
                    if (
                        metadata
                        and isinstance(metadata, dict)
                        and "asana_link" in metadata
                    ):
                        wafer_dict["Asana Link"] = metadata["asana_link"]

                    wafers.append(wafer_dict)
                except Exception as row_error:
                    logger.error(f"Error processing row {row}: {str(row_error)}")
                    # Continue processing other rows

            logger.info(f"Processed {len(wafers)} wafer records")
            return wafers

    except Exception as e:
        logger.error(f"Error getting wafers for lot {lot_id}: {str(e)}")
        return []


def extract_task_gid(url_task: str) -> Optional[str]:
    """
    Extract the task_gid from the Asana task URL.
    Handles multiple URL formats.
    """
    try:
        if not url_task:
            return None

        # Handle direct GID input (just numbers)
        if url_task.isdigit():
            return url_task

        # Remove trailing slashes and 'f' if present
        url_task = url_task.rstrip("/f").rstrip("/")

        # Try different regex patterns
        patterns = [
            # Standard Asana URL patterns
            r"asana\.com/\d+/\d+/(\d+)(?:/[a-z])?",
            r"app\.asana\.com/\d+/\d+/\d+/(\d+)(?:/[a-z])?",
            r"app\.asana\.com/\d+/\d+/\d+/\d+/(\d+)",
            r"app\.asana\.com/\d+/\d+/\d+/\d+/\d+/(\d+)",
            # Common URL path patterns
            r"/(\d+)$",  # Matches the last number in the URL
            r"/task/(\d+)",  # Matches task/number format
            r"/(\d+)/f$",  # Matches number/f format
            r"/(\d+)\?",  # Matches number followed by query parameters
            r"/project/\d+/task/(\d+)",  # Matches project/number/task/number format
        ]

        for pattern in patterns:
            match = re.search(pattern, url_task)
            if match:
                task_gid = match.group(1)
                # Validate that we got a reasonable task ID
                if task_gid and task_gid.isdigit():
                    return task_gid

        # If still no match, check if the URL contains a number sequence of at least 8 digits
        long_number_match = re.search(r"(\d{8,})", url_task)
        if long_number_match:
            return long_number_match.group(1)

        return None

    except Exception as e:
        logger.error(f"Error extracting task GID from URL '{url_task}': {str(e)}")
        return None


def get_asana_task_info(task_gid: str) -> Optional[Dict[str, Optional[str]]]:
    """
    Retrieve task information from Asana using the API.

    Args:
    - task_gid (str): The Asana task GID

    Returns:
    - Optional[Dict[str, Optional[str]]]: Task information or None if retrieval fails
    """
    try:
        # Handle task_gid with colon (format like "1210206616318785:1")
        if task_gid and ":" in task_gid:
            task_gid = task_gid.split(":")[0]

        configuration = asana.Configuration()
        configuration.access_token = (
            "1/1204968822616977:ed9cf542d701d8a0809f0155bb7bd306"
        )
        api_client = asana.ApiClient(configuration)
        tasks_api_instance = asana.TasksApi(api_client)

        logger.info(f"Fetching task info for GID: {task_gid}")

        # Add the required opts parameter
        opts = {
            "opt_pretty": True,
            "opt_fields": "name,notes,due_on,completed,assignee,custom_fields",
        }
        # The timeout should be handled at the API client level, not in the method call
        task = tasks_api_instance.get_task(task_gid, opts=opts)
        task_data = task["data"] if isinstance(task, dict) and "data" in task else task

        logger.info(f"Retrieved raw task data: {task_data}")

        # Initialize task_info with base fields
        task_info = {
            "gid": task_gid,
            "Task Name": task_data.get("name", "N/A"),
            "Task Description": task_data.get("notes", "N/A"),
            "Due Date": str(task_data.get("due_on", "N/A")),
            "Completed": "Yes" if task_data.get("completed", False) else "No",
            "Assignee": task_data.get("assignee", {}).get("name", "N/A"),
            # Initialize the specific fields we're looking for
            "LGT Lot ID": "N/A",
            "Lot reservation": "N/A",
            "Account/Project manager": "N/A",
            "Sales order": "N/A",
            "Customer ID": "N/A",
            "Corridor": "N/A",
            "Lot project": "N/A",
            "Priority": "N/A",
            "Shipment task title": "N/A",
            "Need Reviewing?": "N/A",
            "label-free shipment ?": "N/A",
            "Keep Cassette closed": "N/A",
            "Wafer choice type": "N/A",
            "Ligentec label title": "N/A",
            "Number of Wafers": "N/A",
            "Wafers IDs": "N/A",
            "UPS Tracking Number": "N/A",
            "Contact person": "N/A",
        }

        # Process custom fields
        custom_fields = task_data.get("custom_fields", [])
        for field in custom_fields:
            field_name = field.get("name", "Unknown Field")
            field_gid = field.get("gid", "")

            # Get the field value based on type
            if field.get("display_value") is not None:
                field_value = field["display_value"]
            elif field.get("text_value") is not None:
                field_value = field["text_value"]
            elif field.get("number_value") is not None:
                field_value = str(field["number_value"])
            elif field.get("enum_value"):
                field_value = field["enum_value"].get("name", "N/A")
            else:
                field_value = "N/A"

            # Store by both name and GID for redundancy
            task_info[field_name] = field_value

            # Also store by GID if available
            if field_gid:
                task_info[field_gid] = field_value
                logger.info(
                    f"Mapped field GID {field_gid} ({field_name}) to value: {field_value}"
                )

        logger.info(f"Processed task info: {task_info}")
        return task_info

    except Exception as e:
        logger.error(f"Error retrieving Asana task info: {str(e)}")
        logger.error(traceback.format_exc())
        return None


def get_inventory_stats():
    """
    Get inventory statistics for dashboard display
    Returns:
        Dictionary with inventory statistics including dynamic percentages and trends
    """
    try:
        with get_session() as session:
            # Get locations where wafers are considered "in stock"
            in_stock_locations = ["Xfab FR", "Ligentec FR Khalil"]

            # Get location IDs for in-stock locations
            location_ids_query = session.query(Location.location_id).filter(
                Location.label.in_(in_stock_locations)
            )
            in_stock_location_ids = [row[0] for row in location_ids_query.all()]

            # Total lots in system
            total_lots = session.query(Wafer.lot_id).distinct().count()

            # Available lots (in stock locations)
            available_lots_query = (
                session.query(Wafer.lot_id)
                .distinct()
                .join(WaferInventory, Wafer.wafer_id == WaferInventory.wafer_id)
                .filter(WaferInventory.location_id.in_(in_stock_location_ids))
            )
            available_lots = available_lots_query.count()

            # Available wafers (in stock)
            available_wafers_query = session.query(WaferInventory).filter(
                WaferInventory.location_id.in_(in_stock_location_ids)
            )
            available_wafers = available_wafers_query.count()

            # Total wafers in system
            total_wafers = session.query(WaferInventory).count()

            # Shipped wafers (all wafers not in stock locations)
            shipped_wafers_query = session.query(WaferInventory).filter(
                ~WaferInventory.location_id.in_(in_stock_location_ids)
            )
            shipped_wafers = shipped_wafers_query.count()

            # Get wafers shipped in the last 30 days
            thirty_days_ago = datetime.now() - timedelta(days=30)
            recent_shipped_query = session.query(WaferInventory).filter(
                ~WaferInventory.location_id.in_(in_stock_location_ids),
                WaferInventory.updated_at >= thirty_days_ago,
            )
            recent_shipped = recent_shipped_query.count()

            # Get wafers shipped in the period 31-60 days ago (previous month)
            sixty_days_ago = datetime.now() - timedelta(days=60)
            prev_month_shipped_query = session.query(WaferInventory).filter(
                ~WaferInventory.location_id.in_(in_stock_location_ids),
                WaferInventory.updated_at >= sixty_days_ago,
                WaferInventory.updated_at < thirty_days_ago,
            )
            prev_month_shipped = prev_month_shipped_query.count()

            # Calculate quarterly target and progress
            # Assuming quarterly target is based on historical data plus growth factor
            # For example: 120% of average quarterly shipments from last year
            quarterly_target = max(
                shipped_wafers * 2.5, 100
            )  # Example calculation, adjust as needed
            quarterly_progress = (
                round((shipped_wafers / quarterly_target) * 100)
                if quarterly_target > 0
                else 0
            )

            # Calculate month-over-month change
            if prev_month_shipped > 0:
                monthly_change = round(
                    ((recent_shipped - prev_month_shipped) / prev_month_shipped) * 100,
                    1,
                )
            else:
                monthly_change = 0

            # Calculate available capacity percentage
            capacity_percentage = (
                round((available_lots / total_lots) * 100) if total_lots > 0 else 0
            )

            # Calculate available wafers percentage (% of monthly target)
            monthly_wafer_target = quarterly_target / 3  # Simplified example
            wafer_percentage = (
                round((available_wafers / monthly_wafer_target) * 100)
                if monthly_wafer_target > 0
                else 0
            )

            # Calculate lot change from previous month (mock data for now)
            # This would ideally use historical data from the database
            lot_change = (
                12  # Placeholder for demonstration, should be calculated from history
            )

            return {
                "available_lots": available_lots,
                "total_lots": total_lots,
                "capacity_percentage": capacity_percentage,
                "lot_change": lot_change,
                "available_wafers": available_wafers,
                "wafer_percentage": wafer_percentage,
                "shipped_wafers": shipped_wafers,
                "quarterly_target": quarterly_target,
                "quarterly_progress": quarterly_progress,
                "monthly_change": monthly_change,
            }
    except Exception as e:
        logger.error(f"Error getting inventory stats: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            "available_lots": 0,
            "total_lots": 0,
            "capacity_percentage": 0,
            "lot_change": 0,
            "available_wafers": 0,
            "wafer_percentage": 0,
            "shipped_wafers": 0,
            "quarterly_target": 0,
            "quarterly_progress": 0,
            "monthly_change": 0,
        }


def validate_wafers(wafer_ids: List[str]) -> Dict:
    """
    Validate wafers for shipment:
    - Check if they exist in the database
    - Check if they are in an available location

    Args:
        wafer_ids: List of wafer IDs to validate
    Returns:
        Dict with validation results
    """
    try:
        with get_session() as session:
            available_locations = ["Ligentec France", "Xfab FR", "Ligentec FR Khalil"]

            results = {"valid": [], "invalid": [], "all_valid": False}

            for wafer_id in wafer_ids:
                # Query for the wafer and its location
                query = (
                    session.query(Wafer.wafer_id, Location.label.label("location"))
                    .join(WaferInventory, Wafer.wafer_id == WaferInventory.wafer_id)
                    .join(Location, WaferInventory.location_id == Location.location_id)
                    .filter(Wafer.wafer_id == wafer_id)
                )

                wafer = query.first()

                if not wafer:
                    results["invalid"].append(
                        {"wafer_id": wafer_id, "reason": "Not found in inventory"}
                    )
                elif wafer.location not in available_locations:
                    results["invalid"].append(
                        {
                            "wafer_id": wafer_id,
                            "reason": f"Current location: {wafer.location}",
                        }
                    )
                else:
                    results["valid"].append(wafer_id)

            results["all_valid"] = len(results["invalid"]) == 0
            return results
    except Exception as e:
        logger.error(f"Error validating wafers: {str(e)}")
        return {"valid": [], "invalid": wafer_ids, "all_valid": False, "error": str(e)}


def update_wafer_locations(
    wafer_ids: List[str], asana_link: str = None, tracking_number: str = None
) -> Dict:
    """
    Update wafer locations to 'Sent to Customer' when shipped

    Args:
        wafer_ids: List of wafer IDs to update
        asana_link: Optional Asana task link
        tracking_number: Optional tracking number

    Returns:
        Dictionary with update results
    """
    try:
        with get_session() as session:
            # First validate the wafers
            validation = validate_wafers(wafer_ids)
            if not validation["all_valid"]:
                return {
                    "success": False,
                    "message": (
                        f"Some wafers cannot be updated: {validation['invalid']}"
                    ),
                }

            # Get the 'Sent to Customer' location ID
            sent_location = (
                session.query(Location)
                .filter(Location.label == "Sent to Customer")
                .first()
            )

            if not sent_location:
                return {
                    "success": False,
                    "message": "'Sent to Customer' location not found in database",
                }

            # Update each wafer
            now = datetime.now()
            updated_count = 0

            for wafer_id in wafer_ids:
                inventory = (
                    session.query(WaferInventory)
                    .filter(WaferInventory.wafer_id == wafer_id)
                    .first()
                )

                if inventory:
                    # Store original location for reference
                    original_location_id = inventory.location_id

                    # Update location
                    inventory.location_id = sent_location.location_id
                    inventory.sent_at = now
                    inventory.updated_at = now

                    # Update metadata
                    metadata = inventory.inventory_metadata or {}
                    if asana_link:
                        metadata["asana_link"] = asana_link
                    if tracking_number:
                        metadata["tracking_number"] = tracking_number

                    metadata["shipped_date"] = now.isoformat()
                    metadata["original_location_id"] = original_location_id

                    inventory.inventory_metadata = metadata
                    updated_count += 1

            session.commit()

            return {
                "success": True,
                "message": f"Successfully updated {updated_count} wafers",
                "updated_count": updated_count,
                "sent_date": now.strftime("%Y-%m-%d"),
                "received_date": adjust_date_for_holidays_and_weekends(now).strftime(
                    "%Y-%m-%d"
                ),
            }

    except Exception as e:
        logger.error(f"Error updating wafer locations: {str(e)}")
        return {"success": False, "message": f"Error: {str(e)}"}


def search_wafers(search_params: Dict) -> List[Dict]:
    """
    Advanced search for wafers based on various parameters

    Args:
        search_params: Dictionary with search parameters

    Returns:
        List of matching wafer dictionaries
    """
    try:
        with get_session() as session:
            # Base query joining all necessary tables
            query = (
                session.query(
                    Wafer.wafer_id,
                    Wafer.lot_id,
                    WaferInventory.cassette_id,
                    WaferInventory.slot_id,
                    WaferInventory.arrived_at,
                    WaferInventory.sent_at,
                    Location.label.label("location"),
                    WaferInventory.inventory_metadata,
                )
                .join(WaferInventory, Wafer.wafer_id == WaferInventory.wafer_id)
                .join(Location, WaferInventory.location_id == Location.location_id)
            )

            # Apply filters based on search parameters
            if search_params.get("lot"):
                query = query.filter(Wafer.lot_id.ilike(f"%{search_params['lot']}%"))

            if search_params.get("scribe_id"):
                query = query.filter(
                    Wafer.wafer_id.ilike(f"%{search_params['scribe_id']}%")
                )

            if search_params.get("location"):
                query = query.filter(Location.label == search_params["location"])

            # Date filters
            if search_params.get("arrival_date_from") and search_params.get(
                "arrival_date_to"
            ):
                query = query.filter(
                    WaferInventory.arrived_at.between(
                        search_params["arrival_date_from"],
                        search_params["arrival_date_to"],
                    )
                )

            if search_params.get("sent_date_from") and search_params.get(
                "sent_date_to"
            ):
                query = query.filter(
                    WaferInventory.sent_at.between(
                        search_params["sent_date_from"], search_params["sent_date_to"]
                    )
                )

            # Execute query
            results = query.all()

            # Format query results
            wafers = []
            for row in results:
                wafer = {
                    "wafer_id": row.wafer_id,
                    "lot_id": row.lot_id,
                    "cassette_id": row.cassette_id,
                    "slot_id": row.slot_id,
                    "location": row.location,
                    "arrived_at": (
                        row.arrived_at.isoformat() if row.arrived_at else None
                    ),
                    "sent_at": row.sent_at.isoformat() if row.sent_at else None,
                    "asana_link": "N/A",
                }

                # Extract Asana link from metadata
                if row.inventory_metadata and "asana_link" in row.inventory_metadata:
                    wafer["asana_link"] = row.inventory_metadata["asana_link"]

                wafers.append(wafer)

            return wafers

    except Exception as e:
        logger.error(f"Error in search_wafers: {str(e)}")
        return []


def get_locations():
    """Get all locations from the database"""
    try:
        with get_session() as session:
            locations = session.query(Location).all()
            return [
                {
                    "location_id": loc.location_id,
                    "label": loc.label,
                    "address": loc.address,
                    "email": loc.email,
                    "telephone": loc.telephone,
                    "contact_person": loc.contact_person,
                    "asana_link": loc.asana_link,
                }
                for loc in locations
            ]
    except Exception as e:
        logger.error(f"Error getting locations: {str(e)}")
        return []


def get_available_locations():
    """Get all locations that have wafers available for shipment"""
    try:
        with get_session() as session:
            available_locations = ["Ligentec France", "Xfab FR", "Ligentec FR Khalil"]

            locations = (
                session.query(Location)
                .filter(Location.label.in_(available_locations))
                .all()
            )

            return [
                {
                    "location_id": loc.location_id,
                    "label": loc.label,
                    "address": loc.address or "",
                    "email": loc.email or "",
                    "telephone": loc.telephone or "",
                    "contact_person": loc.contact_person or "",
                }
                for loc in locations
            ]
    except Exception as e:
        logger.error(f"Error getting available locations: {str(e)}")
        return []


def calculate_percentage_change(previous, current):
    if previous == 0:
        return 100 if current > 0 else 0
    return round(((current - previous) / previous) * 100, 1)


def get_shipment_stats_from_asana(project_gid):
    """
    Retrieve and analyze shipment statistics from Asana project.

    Args:
        project_gid: The Asana project GID to analyze

    Returns:
        Dictionary containing shipment statistics and chart data
    """
    try:
        # Fetch tasks from Asana
        tasks = _fetch_asana_tasks(project_gid)
        if not tasks:
            return None

        # Initialize tracking data
        section_counts, time_periods = _initialize_tracking_data()

        # Process each task
        monthly_deliveries = _process_tasks(tasks, section_counts, time_periods)

        # Calculate statistics and prepare chart data
        return _prepare_statistics_and_charts(
            section_counts, time_periods, monthly_deliveries
        )

    except Exception as e:
        logger.error(f"Error getting shipment stats from Asana: {str(e)}")
        return None


def _fetch_asana_tasks(project_gid):
    """Fetch tasks from Asana API for the given project."""
    headers = {
        "Authorization": "Bearer 1/1204968822616977:ed9cf542d701d8a0809f0155bb7bd306"
    }

    try:
        response = requests.get(
            f"https://app.asana.com/api/1.0/projects/{project_gid}/tasks",
            params={
                "opt_fields": "name,completed_at,created_at,memberships.section.name"
            },
            headers=headers,
            timeout=15,  # 15 second timeout
        )
        response.raise_for_status()
        return response.json()["data"]
    except requests.RequestException as e:
        logger.error(f"Failed to fetch Asana tasks: {str(e)}")
        return None


def _initialize_tracking_data():
    """Initialize data structures for tracking shipment statistics."""
    # Define sections to track
    sections = [
        "Incoming",
        "Planned",
        "In preparation",
        "packaged",
        "Waiting for review",
        "Sent",
        "Delivered",
    ]
    section_counts = {section: 0 for section in sections}

    # Define time periods for comparison
    now = datetime.now()
    time_periods = {
        "now": now,
        "week_ago": now - timedelta(days=7),
        "two_weeks_ago": now - timedelta(days=14),
        "month_ago": now - timedelta(days=30),
        "previous_month": now - timedelta(days=60),
        "year_start": datetime(now.year, 1, 1),
        "current_month": now.replace(day=1),
        "previous_week_sent": 0,
        "previous_month_delivered": 0,
        "weekly_delivered": 0,
        "monthly_delivered": 0,
        "yearly_delivered": 0,
        "yearly_incoming": 0,
    }

    return section_counts, time_periods


def _process_tasks(tasks, section_counts, time_periods):
    """Process Asana tasks and update statistics."""
    monthly_deliveries = defaultdict(int)

    for task in tasks:
        section = _get_task_section(task)

        # Update section counts
        if section in section_counts:
            section_counts[section] += 1

        # Process completed tasks
        if task.get("completed_at"):
            completed_at = datetime.fromisoformat(task["completed_at"].rstrip("Z"))
            _process_completed_task(
                task, section, completed_at, time_periods, monthly_deliveries
            )

        # Process created tasks
        if section == "Incoming" and task.get("created_at"):
            created_at = datetime.fromisoformat(task["created_at"].rstrip("Z"))
            if created_at >= time_periods["year_start"]:
                time_periods["yearly_incoming"] += 1

    return monthly_deliveries


def _get_task_section(task):
    """Extract section name from task data."""
    return next(
        (
            membership["section"]["name"]
            for membership in task.get("memberships", [])
            if "section" in membership
        ),
        None,
    )


def _process_completed_task(
    task, section, completed_at, time_periods, monthly_deliveries
):
    """Process a completed task and update relevant statistics."""
    # Track previous week's sent tasks
    if (
        section == "Sent"
        and time_periods["two_weeks_ago"] <= completed_at < time_periods["week_ago"]
    ):
        time_periods["previous_week_sent"] += 1

    # Track delivered tasks
    if section == "Delivered":
        # Previous month's delivered
        if time_periods["previous_month"] <= completed_at < time_periods["month_ago"]:
            time_periods["previous_month_delivered"] += 1

        # Recent time periods
        if completed_at >= time_periods["week_ago"]:
            time_periods["weekly_delivered"] += 1
        if completed_at >= time_periods["month_ago"]:
            time_periods["monthly_delivered"] += 1
        if completed_at >= time_periods["year_start"]:
            time_periods["yearly_delivered"] += 1

        # Monthly deliveries for the last 12 months
        _update_monthly_deliveries(
            completed_at, time_periods["current_month"], monthly_deliveries
        )


def _update_monthly_deliveries(completed_at, current_month, monthly_deliveries):
    """Update monthly delivery counts for the last 12 months."""
    for i in range(12):
        month_start = current_month - timedelta(days=30 * i)
        month_end = month_start + timedelta(days=30)
        if month_start <= completed_at < month_end:
            monthly_deliveries[month_start.strftime("%B %Y")] += 1


def _prepare_statistics_and_charts(section_counts, time_periods, monthly_deliveries):
    """Prepare final statistics and chart data."""
    # Calculate percentage changes
    weekly_sent_change = calculate_percentage_change(
        time_periods["previous_week_sent"], section_counts["Sent"]
    )
    monthly_delivered_change = calculate_percentage_change(
        time_periods["previous_month_delivered"], time_periods["monthly_delivered"]
    )

    # Sort monthly deliveries
    sorted_monthly_deliveries = dict(
        sorted(
            monthly_deliveries.items(),
            key=lambda x: datetime.strptime(x[0], "%B %Y"),
            reverse=True,
        )
    )

    # Prepare chart data
    section_data = _prepare_section_chart_data(section_counts)
    monthly_data = _prepare_monthly_chart_data(sorted_monthly_deliveries)

    return {
        "sections": section_counts,
        "section_data": section_data,
        "monthly_data": monthly_data,
        "weekly_sent": section_counts["Sent"],
        "weekly_sent_change": weekly_sent_change,
        "monthly_delivered": time_periods["monthly_delivered"],
        "monthly_delivered_change": monthly_delivered_change,
        "yearly": {
            "delivered": time_periods["yearly_delivered"],
            "incoming": time_periods["yearly_incoming"],
        },
        "monthly_deliveries": sorted_monthly_deliveries,
    }


def _prepare_section_chart_data(section_counts):
    """Prepare data for section chart visualization."""
    sections = list(section_counts.keys())
    return {
        "labels": sections,
        "datasets": [
            {
                "label": "Tasks per Section",
                "data": [section_counts[section] for section in sections],
                "backgroundColor": [
                    "#4B5563",
                    "#60A5FA",
                    "#34D399",
                    "#F59E0B",
                    "#EC4899",
                    "#8B5CF6",
                    "#10B981",
                ],
            }
        ],
    }


def _prepare_monthly_chart_data(sorted_monthly_deliveries):
    """Prepare data for monthly deliveries chart visualization."""
    return {
        "labels": list(sorted_monthly_deliveries.keys()),
        "datasets": [
            {
                "label": "Monthly Deliveries",
                "data": list(sorted_monthly_deliveries.values()),
                "borderColor": "#3B82F6",
                "backgroundColor": "rgba(59, 130, 246, 0.1)",
                "tension": 0.4,
            }
        ],
    }


def adjust_date_for_holidays_and_weekends(date):
    """
    Helper function to adjust dates for holidays and weekends.
    This should match your existing function.
    """
    # Adding placeholder logic for demonstration
    from datetime import timedelta

    # Default adding 3 business days
    result_date = date + timedelta(days=3)

    # Adjust for weekends
    while result_date.weekday() >= 5:  # 5 = Saturday, 6 = Sunday
        result_date += timedelta(days=1)

    # You should add holiday adjustment logic here based on your requirements

    return result_date

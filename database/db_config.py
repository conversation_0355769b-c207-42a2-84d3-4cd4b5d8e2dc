# db_config.py

from sqlalchemy import create_engine, text  # noqa: F401
from contextlib import contextmanager
from psycopg2.extras import RealDictCursor
import os
from dotenv import load_dotenv
import psycopg2
import logging
from typing import Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Add explicit path to .env file
env_path = os.path.join(os.path.dirname(__file__), ".env")
load_dotenv(env_path, override=True)

# Try to import the secrets handler if available
try:
    from secrets_handler import get_secret

    has_secrets_handler = True
except ImportError:
    has_secrets_handler = False

# Database configuration
if has_secrets_handler:
    # Try to get from Docker secrets first, then fall back to environment variables
    DB_HOST = get_secret("db_host", os.getenv("DB_HOST", "pgdb.lig.local"))
    DB_NAME = get_secret("db_name", os.getenv("DB_NAME", "icarium_test"))
    DB_USER = get_secret("db_user", os.getenv("DB_USER", "operations_fr_inventory"))
    DB_PASSWORD = get_secret("db_password", os.getenv("DB_PASSWORD"))
else:
    # Fall back to environment variables only
    DB_HOST = os.getenv("DB_HOST", "pgdb.lig.local")
    DB_NAME = os.getenv("DB_NAME", "icarium_test")
    DB_USER = os.getenv("DB_USER", "operations_fr_inventory")
    DB_PASSWORD = os.getenv("DB_PASSWORD")

# Log database connection info (without password)
logger.info(f"Connecting to database: {DB_NAME} on host: {DB_HOST} as user: {DB_USER}")

# Create database URL
DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}/{DB_NAME}"

# Log connection string (without password)
safe_url = f"postgresql://{DB_USER}:***@{DB_HOST}/{DB_NAME}"
logger.info(f"Using database URL: {safe_url}")

# Create SQLAlchemy engine
try:
    engine = create_engine(
        DATABASE_URL,
        connect_args={"connect_timeout": 5},  # Increased timeout
        pool_timeout=10,  # Increased timeout for getting a connection from the pool
        pool_recycle=3600,  # Recycle connections after 1 hour
        pool_pre_ping=True,  # Check connection validity before use
        max_overflow=10,  # Allow up to 10 overflow connections
        pool_size=5,  # Maintain 5 connections in the pool
    )
    logger.info("SQLAlchemy engine created successfully")
except Exception as e:
    logger.error(f"Error creating SQLAlchemy engine: {str(e)}")
    raise


def get_db_connection():
    """Get database connection using psycopg2"""
    try:
        logger.info(f"Attempting to connect to database '{DB_NAME}' on host '{DB_HOST}'")
        conn = psycopg2.connect(
            host=DB_HOST,
            database=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD,
            cursor_factory=RealDictCursor,
            connect_timeout=5,  # Increased timeout
        )
        logger.info("Database connection established successfully")
        return conn
    except Exception as e:
        logger.error(f"Database connection error: {str(e)}")
        raise


@contextmanager
def get_db_cursor():
    """Context manager for database cursors"""
    conn = get_db_connection()
    try:
        cursor = conn.cursor()
        yield cursor
        conn.commit()
    except Exception as e:
        conn.rollback()
        logger.error(f"Database cursor error: {str(e)}")
        raise e
    finally:
        cursor.close()
        conn.close()


def get_db_url():
    """Get database URL string"""
    try:
        # Ensure we're using the environment variables
        db_url = os.getenv("DB_CONNECTION")
        if not db_url:
            db_url = DATABASE_URL  # Fallback to constructed URL
        return db_url
    except Exception as e:
        logger.error(f"Error getting database URL: {str(e)}")
        raise


def get_sqlalchemy_connection():
    """Get SQLAlchemy connection"""
    try:
        conn = engine.connect()
        return conn
    except Exception as e:
        logger.error(f"SQLAlchemy connection error: {str(e)}")
        raise


def test_connection() -> Optional[str]:
    """Test database connection"""
    try:
        with get_db_cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            if result:
                return "Database connection successful"
    except Exception as e:
        logger.error(f"Database connection error: {str(e)}")
        return None
    return None


if __name__ == "__main__":
    test_connection()
    print("Database connection successful")

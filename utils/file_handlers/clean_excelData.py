import pandas as pd
import numpy as np

# Load your original Excel file
excel_file_path = "/Users/<USER>/Documents/Wafer Inventory LGT FR.xlsx"
df = pd.read_excel(excel_file_path)

# Replace NaN values with null for valid JSON (or any other placeholder value you need)
df_clean = df.replace({np.nan: None})

# Save the cleaned data to a JSON file
json_file_path = "/Users/<USER>/Documents/Wafer_Inventory.json"
df_clean.to_json(json_file_path, orient="records", indent=4)

print(f"Cleaned data saved to {json_file_path}")

# Code by <PERSON><PERSON>ec Label<PERSON>
# Started on the 20 September 2024 V.01

# system_monitor.py
from datetime import datetime
import psutil
import threading
from collections import deque
import time
from flask import current_app as app


class SystemMonitor:
    def __init__(self):
        self.metrics_history = {
            "cpu": deque(maxlen=60),
            "memory": deque(maxlen=60),
            "disk": deque(maxlen=60),
            "network": deque(maxlen=60),
            "response_times": deque(maxlen=60),
        }
        self.alert_thresholds = {
            "cpu": 80,
            "memory": 85,
            "disk": 90,
            "response_time": 1000,
        }
        self.monitoring_active = False
        self.monitor_thread = None
        self.last_sync_time = None

    def start_monitoring(self):
        """Start the monitoring thread if not already running"""
        if not self.monitoring_active:
            self.monitoring_active = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop)
            self.monitor_thread.daemon = True
            self.monitor_thread.start()
            return True
        return False

    def stop_monitoring(self):
        """Stop the monitoring thread"""
        if self.monitoring_active:
            self.monitoring_active = False
            if self.monitor_thread:
                self.monitor_thread.join()
            return True
        return False

    def _monitor_loop(self):
        """Main monitoring loop that collects metrics periodically"""
        while self.monitoring_active:
            try:
                metrics = self.collect_metrics()
                self._update_metrics_history(metrics)
                self._check_alerts(metrics)
                time.sleep(10)
            except Exception as e:
                app.logger.error(f"Error in monitor loop: {str(e)}")

    def collect_metrics(self):
        """Collect all system metrics in one pass"""
        try:
            return {
                "timestamp": datetime.now(),
                "cpu": psutil.cpu_percent(interval=1),
                "memory": psutil.virtual_memory().percent,
                "disk": psutil.disk_usage("/").percent,
                "network": self._get_network_stats(),
                "io": self._get_io_stats(),
                "processes": self._get_top_processes(limit=10),
                "connections": len(psutil.net_connections()),
                "last_sync": self.last_sync_time,
            }
        except Exception as e:
            app.logger.error(f"Error collecting metrics: {str(e)}")
            return {}

    def _get_network_stats(self):
        """Get network usage statistics"""
        net_io = psutil.net_io_counters()
        return {
            "bytes_sent": net_io.bytes_sent,
            "bytes_recv": net_io.bytes_recv,
            "packets_sent": net_io.packets_sent,
            "packets_recv": net_io.packets_recv,
        }

    def _get_io_stats(self):
        """Get disk I/O statistics"""
        io = psutil.disk_io_counters()
        return {
            "read_bytes": io.read_bytes,
            "write_bytes": io.write_bytes,
            "read_count": io.read_count,
            "write_count": io.write_count,
        }

    def _get_top_processes(self, limit=10):
        """Get information about top system processes"""
        processes = []
        for proc in psutil.process_iter(
            ["pid", "name", "cpu_percent", "memory_percent"]
        ):
            try:
                info = proc.info
                if info["cpu_percent"] > 0:  # Only include active processes
                    processes.append(info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        return sorted(processes, key=lambda x: x["cpu_percent"], reverse=True)[:limit]

    def _update_metrics_history(self, metrics):
        """Update historical metrics"""
        try:
            for metric_type in self.metrics_history:
                if metric_type in metrics:
                    self.metrics_history[metric_type].append(metrics[metric_type])
        except Exception as e:
            app.logger.error(f"Error updating metrics history: {str(e)}")

    def _check_alerts(self, metrics):
        """Check metrics against thresholds and generate alerts"""
        alerts = []
        try:
            if metrics["cpu"] > self.alert_thresholds["cpu"]:
                alerts.append(f"High CPU usage: {metrics['cpu']}%")
            if metrics["memory"] > self.alert_thresholds["memory"]:
                alerts.append(f"High memory usage: {metrics['memory']}%")
            if metrics["disk"] > self.alert_thresholds["disk"]:
                alerts.append(f"High disk usage: {metrics['disk']}%")

            if alerts:
                app.logger.warning("System alerts: " + ", ".join(alerts))
                # Could add notification system here
        except Exception as e:
            app.logger.error(f"Error checking alerts: {str(e)}")

    def get_health_report(self):
        """Generate comprehensive health report"""
        try:
            current_metrics = self.collect_metrics()
            return {
                "status": (
                    "healthy" if self._is_healthy(current_metrics) else "unhealthy"
                ),
                "current_metrics": current_metrics,
                "historical_metrics": {
                    k: list(v) for k, v in self.metrics_history.items()
                },
                "thresholds": self.alert_thresholds,
                "monitoring_active": self.monitoring_active,
                "timestamp": datetime.now().isoformat(),
            }
        except Exception as e:
            app.logger.error(f"Error generating health report: {str(e)}")
            return {"status": "error", "message": str(e)}

    def _is_healthy(self, metrics):
        """Check if system is healthy based on current metrics"""
        try:
            return all(
                [
                    metrics["cpu"] < self.alert_thresholds["cpu"],
                    metrics["memory"] < self.alert_thresholds["memory"],
                    metrics["disk"] < self.alert_thresholds["disk"],
                ]
            )
        except Exception:
            return False


# Initialize singleton instance
system_monitor = SystemMonitor()


def get_health_metrics():
    """Get current health metrics"""
    try:
        return system_monitor.collect_metrics()
    except Exception as e:
        app.logger.error(f"Error getting health metrics: {str(e)}")
        return {"error": str(e)}

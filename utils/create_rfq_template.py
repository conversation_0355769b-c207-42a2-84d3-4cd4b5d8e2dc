"""Utility script to create RFQ Excel template.

This script creates a sample Excel template file that users can download
and use as a reference for the RFQ email automation feature.

Developed for Ligentec SA - RFQ Email Automation Feature
"""

import pandas as pd
import os
from datetime import datetime


def create_rfq_template():
    """Create a sample RFQ Excel template file."""
    
    # Sample data for the template
    sample_data = [
        {
            'Order_ID': 'RFQ001',
            'Project_Name': 'Sample Project Alpha',
            'Links': 'https://example.com/project-alpha-link1, https://example.com/project-alpha-link2',
            'Priority': 'High',
            'Notes': 'Urgent project requiring immediate quotation'
        },
        {
            'Order_ID': 'RFQ002',
            'Project_Name': 'Sample Project Beta',
            'Links': 'https://example.com/project-beta-link1',
            'Priority': 'Medium',
            'Notes': 'Standard project timeline'
        },
        {
            'Order_ID': 'RFQ003',
            'Project_Name': 'Sample Project Gamma',
            'Links': 'https://example.com/project-gamma-link1, https://example.com/project-gamma-link2, https://example.com/project-gamma-link3',
            'Priority': 'Low',
            'Notes': 'Future planning project'
        }
    ]
    
    # Create DataFrame
    df = pd.DataFrame(sample_data)
    
    # Create the template file
    template_path = os.path.join('static', 'templates', 'RFQ_Template.xlsx')
    
    # Ensure directory exists
    os.makedirs(os.path.dirname(template_path), exist_ok=True)
    
    # Save to Excel with formatting
    with pd.ExcelWriter(template_path, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='RFQ_Data', index=False)
        
        # Get the workbook and worksheet
        workbook = writer.book
        worksheet = writer.sheets['RFQ_Data']
        
        # Add some basic formatting
        from openpyxl.styles import Font, PatternFill, Alignment
        
        # Header formatting
        header_font = Font(bold=True, color='FFFFFF')
        header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
        
        for cell in worksheet[1]:
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = Alignment(horizontal='center')
        
        # Auto-adjust column widths
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    print(f"RFQ template created: {template_path}")
    return template_path


if __name__ == "__main__":
    create_rfq_template()

# utils/health_check.py
from datetime import datetime, timedelta
import psutil
import psycopg2
import time
import socket as socket_module  # noqa: F401
from flask import current_app as app
from database.db_config import DB_HOST, DB_NAME, DB_USER, DB_PASSWORD


def get_health_metrics():
    """Get system health metrics with real-time data"""
    try:
        # Get basic metrics
        metrics = {
            "dbResponse": get_db_response_time(),
            "cpu": get_cpu_usage(),
            "memory": get_memory_usage(),
            "lastSync": get_last_sync_time(),
            "integrations": {
                "asana": {
                    "status": check_asana_connection(),
                    "lastSync": get_last_sync_time(),
                },
                "drive": {
                    "status": check_google_drive_connection(),
                    "storage": get_drive_storage(),
                },
                "printer": {
                    "status": check_printer_status(),
                    "labelsToday": get_labels_printed_today(),
                },
                "database": {
                    "status": check_db_connection(),
                    "connections": get_active_connections(),
                },
            },
            "systemUptime": get_system_uptime(),
            # Add system insights data
            "processing_time": f"{round(get_db_response_time(), 1)}ms",
            "success_rate": f"{get_system_success_rate()}%",
            "system_load": get_system_load_data(),
            "recent_events": get_recent_events(limit=5),
        }
        return metrics
    except Exception as e:
        app.logger.error(f"Error collecting health metrics: {str(e)}")
        return {
            # ... existing fallback data ...
            "processing_time": "0ms",
            "success_rate": "0%",
            "system_load": [],
            "recent_events": [],
        }


def get_system_success_rate():
    """Calculate system success rate based on recent API calls"""
    try:
        # In a real system, you would track API call successes and failures
        # This is a placeholder implementation that returns a high success rate
        return 99.8
    except Exception as e:
        app.logger.error(f"Error calculating success rate: {str(e)}")
        return 0


def get_system_load_data():
    """Get system load data for heatmap"""
    try:
        # Generate sample heatmap data (in a real system, you'd get actual load data)
        hours = ["12am", "4am", "8am", "12pm", "4pm", "8pm"]
        days = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]

        import random

        data = []

        for day_idx, day in enumerate(days):
            for hour_idx, hour in enumerate(hours):
                # Create higher load during business hours
                is_business_hours = hour_idx in [2, 3, 4] and day_idx < 5
                load = (
                    random.randint(50, 90)
                    if is_business_hours
                    else random.randint(10, 40)
                )

                data.append({"day": day, "hour": hour, "value": load})

        return data
    except Exception as e:
        app.logger.error(f"Error getting system load data: {str(e)}")
        return []


def get_recent_events(limit=5):
    """Get recent system events"""
    try:
        # In a real system, you'd get this from your logs or event store
        # This is just sample data for demonstration
        events = [
            {
                "type": "success",
                "description": "System startup completed",
                "timestamp": (datetime.now() - timedelta(hours=1)).strftime(
                    "%Y-%m-%d %H:%M:%S"
                ),
            },
            {
                "type": "label_generation",
                "description": "Label generated for batch #12345",
                "timestamp": (datetime.now() - timedelta(minutes=45)).strftime(
                    "%Y-%m-%d %H:%M:%S"
                ),
            },
            {
                "type": "inventory_update",
                "description": "Inventory updated for 3 wafers",
                "timestamp": (datetime.now() - timedelta(minutes=30)).strftime(
                    "%Y-%m-%d %H:%M:%S"
                ),
            },
            {
                "type": "success",
                "description": "Database backup completed",
                "timestamp": (datetime.now() - timedelta(minutes=15)).strftime(
                    "%Y-%m-%d %H:%M:%S"
                ),
            },
            {
                "type": "label_generation",
                "description": "Packing slip generated for shipping",
                "timestamp": (datetime.now() - timedelta(minutes=5)).strftime(
                    "%Y-%m-%d %H:%M:%S"
                ),
            },
        ]
        return events[:limit]
    except Exception as e:
        app.logger.error(f"Error getting recent events: {str(e)}")
        return []


def get_db_response_time():
    """Get actual database response time in ms"""
    try:
        start_time = time.time()
        conn = psycopg2.connect(
            host=DB_HOST,
            database=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD,
            connect_timeout=2,
        )
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        cursor.fetchone()
        cursor.close()
        conn.close()
        end_time = time.time()
        return round((end_time - start_time) * 1000, 2)  # Convert to ms
    except Exception as e:
        app.logger.error(f"Error measuring DB response time: {str(e)}")
        return -1  # Return negative value to indicate error


def get_cpu_usage():
    """Get actual CPU usage percentage"""
    try:
        return round(psutil.cpu_percent(interval=0.5), 1)
    except Exception as e:
        app.logger.error(f"Error getting CPU usage: {str(e)}")
        return -1


def get_memory_usage():
    """Get actual memory usage percentage"""
    try:
        return round(psutil.virtual_memory().percent, 1)
    except Exception as e:
        app.logger.error(f"Error getting memory usage: {str(e)}")
        return -1


def get_active_connections():
    """Get actual number of database connections"""
    try:
        conn = psycopg2.connect(
            host=DB_HOST,
            database=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD,
            connect_timeout=2,
        )
        cursor = conn.cursor()
        cursor.execute("SELECT count(*) FROM pg_stat_activity")
        count = cursor.fetchone()[0]
        cursor.close()
        conn.close()
        return count
    except Exception as e:
        app.logger.error(f"Error getting active connections: {str(e)}")
        return 0


def get_last_sync_time():
    """Get time of last database sync"""
    try:
        conn = psycopg2.connect(
            host=DB_HOST,
            database=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD,
            connect_timeout=2,
        )
        cursor = conn.cursor()
        cursor.execute("SELECT max(updated_at) FROM wafer_inventory")
        last_update = cursor.fetchone()[0]
        cursor.close()
        conn.close()

        if last_update:
            now = datetime.now()
            diff = (now - last_update.replace(tzinfo=None)).total_seconds()
            if diff < 60:
                return f"{int(diff)} seconds ago"
            elif diff < 3600:
                return f"{int(diff / 60)} minutes ago"
            else:
                return f"{int(diff / 3600)} hours ago"
        return "Unknown"
    except Exception as e:
        app.logger.error(f"Error getting last sync time: {str(e)}")
        return "Unknown"


def check_db_connection():
    """Check if database connection is working"""
    try:
        conn = psycopg2.connect(
            host=DB_HOST,
            database=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD,
            connect_timeout=2,
        )
        conn.close()
        return True
    except Exception as e:
        app.logger.error(f"Database connection check failed: {str(e)}")
        return False


def check_asana_connection():
    """Check if Asana connection is working"""
    try:
        dashboard_service = app.config.get("DASHBOARD_SERVICE")
        if dashboard_service:
            # Just check if the service exists and is initialized
            # This avoids trying to call API methods
            return dashboard_service.client is not None
        return False
    except Exception as e:
        app.logger.error(f"Asana connection check failed: {str(e)}")
        return False


def check_google_drive_connection():
    """Check if Google Drive connection is working"""
    try:
        # In a real implementation, you would check the actual connection
        # For now, return true based on your logs showing successful connection
        return True
    except Exception as e:
        app.logger.error(f"Google Drive connection check failed: {str(e)}")
        return False


def get_drive_storage():
    """Get Drive storage usage"""
    try:
        # This would be implemented with actual Drive API calls
        # For now, return a placeholder value
        return "45.2 GB"
    except Exception as e:
        app.logger.error(f"Error getting Drive storage: {str(e)}")
        return "Unknown"


def check_printer_status():
    """Check if printer is available"""
    try:
        # For testing purposes, let's assume printer is available
        # In production, you'd check actual printer status
        return True

        # When you want to implement real printer checking:
        # printer_ip = app.config.get('DEFAULT_PRINTER_IP', '*************')
        # sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        # sock.settimeout(1)  # Short timeout
        # result = sock.connect_ex((printer_ip, 9100))
        # sock.close()
        # return result == 0
    except Exception as e:
        app.logger.error(f"Printer connection check failed: {str(e)}")
        return False


def get_labels_printed_today():
    """Get count of labels printed today"""
    try:
        import os
        from datetime import datetime
        from pathlib import Path

        # Use the DOWNLOAD_FOLDER from app config
        download_folder = app.config.get("DOWNLOAD_FOLDER")

        # If not configured in app, use safe defaults
        if not download_folder:
            # Check for Docker environment
            if os.path.exists("/.dockerenv") or os.environ.get("DOCKER_CONTAINER"):
                download_folder = "/app/downloads"
            else:
                # Try user's Downloads folder
                download_folder = str(Path.home() / "Downloads")
                # If that doesn't exist, use /app/downloads as fallback
                if not os.path.exists(download_folder):
                    download_folder = "/app/downloads"

        app.logger.info(f"Using download folder for label count: {download_folder}")

        # Create directory if it doesn't exist
        os.makedirs(download_folder, exist_ok=True)

        # Count label files created today
        today = datetime.now().strftime("%Y%m%d")

        # Use try/except to handle potential permission issues
        try:
            count = sum(
                1
                for f in os.listdir(download_folder)
                if f.endswith("_label.pdf") and today in f
            )
            return count
        except PermissionError:
            app.logger.warning(f"Permission denied when accessing {download_folder}")
            return 0

    except Exception as e:
        app.logger.error(f"Error counting printed labels: {str(e)}")
        return 0


def get_system_uptime():
    """Get system uptime information"""
    try:
        uptime_seconds = int(time.time() - psutil.boot_time())
        days = uptime_seconds // 86400
        hours = (uptime_seconds % 86400) // 3600
        return {"days": days, "hours": hours}
    except Exception as e:
        app.logger.error(f"Error getting system uptime: {str(e)}")
        return {"days": 0, "hours": 0}

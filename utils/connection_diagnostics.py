#!/usr/bin/env python3
"""
Connection Diagnostics Tool
Helps diagnose connection timeout issues in Talaria Dashboard
"""

import os
import sys
import time
import psycopg2
import requests
from datetime import datetime
from contextlib import contextmanager

# Add the parent directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_config import get_db_connection, DB_HOST, DB_NAME, DB_USER
from database.auth_db_config import get_auth_db_connection, AUTH_DB_HOST, AUTH_DB_NAME, AUTH_DB_USER


def test_database_connections():
    """Test both main and auth database connections"""
    print("🔍 Testing Database Connections...")
    print("=" * 50)
    
    # Test main database
    print(f"\n📊 Testing Main Database: {DB_NAME}@{DB_HOST}")
    try:
        start_time = time.time()
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT 1, current_timestamp, version()")
        result = cursor.fetchone()
        end_time = time.time()
        
        print(f"✅ Main DB Connection: SUCCESS ({(end_time - start_time)*1000:.2f}ms)")
        print(f"   Server Time: {result[1]}")
        print(f"   Version: {result[2][:50]}...")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Main DB Connection: FAILED - {str(e)}")
    
    # Test auth database
    print(f"\n🔐 Testing Auth Database: {AUTH_DB_NAME}@{AUTH_DB_HOST}")
    try:
        start_time = time.time()
        conn = get_auth_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT 1, current_timestamp")
        result = cursor.fetchone()
        end_time = time.time()
        
        print(f"✅ Auth DB Connection: SUCCESS ({(end_time - start_time)*1000:.2f}ms)")
        print(f"   Server Time: {result[1]}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Auth DB Connection: FAILED - {str(e)}")


def test_api_endpoints():
    """Test API endpoints that are failing"""
    print("\n🌐 Testing API Endpoints...")
    print("=" * 50)
    
    base_url = "http://localhost:5000"
    endpoints = [
        "/api/notifications/unread-count",
        "/api/icarium/smart-sync-recommendations?hours=168",
        "/api/dashboard/stats",
        "/health"
    ]
    
    for endpoint in endpoints:
        print(f"\n🔗 Testing: {endpoint}")
        try:
            start_time = time.time()
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            end_time = time.time()
            
            print(f"   Status: {response.status_code}")
            print(f"   Response Time: {(end_time - start_time)*1000:.2f}ms")
            
            if response.status_code == 200:
                print("   ✅ SUCCESS")
            elif response.status_code == 401:
                print("   🔒 AUTHENTICATION REQUIRED")
            elif response.status_code == 403:
                print("   🚫 FORBIDDEN")
            else:
                print(f"   ❌ ERROR: {response.status_code}")
                
        except requests.exceptions.Timeout:
            print("   ⏰ TIMEOUT (>10s)")
        except requests.exceptions.ConnectionError:
            print("   🔌 CONNECTION ERROR")
        except Exception as e:
            print(f"   ❌ ERROR: {str(e)}")


def check_database_performance():
    """Check database performance and active connections"""
    print("\n⚡ Database Performance Check...")
    print("=" * 50)
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Check active connections
        cursor.execute("""
            SELECT count(*) as active_connections,
                   count(*) FILTER (WHERE state = 'active') as active_queries,
                   count(*) FILTER (WHERE state = 'idle') as idle_connections
            FROM pg_stat_activity 
            WHERE datname = %s
        """, (DB_NAME,))
        
        result = cursor.fetchone()
        print(f"📊 Active Connections: {result[0]}")
        print(f"🔄 Active Queries: {result[1]}")
        print(f"💤 Idle Connections: {result[2]}")
        
        # Check for long-running queries
        cursor.execute("""
            SELECT pid, now() - pg_stat_activity.query_start AS duration, query 
            FROM pg_stat_activity 
            WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes'
            AND datname = %s
        """, (DB_NAME,))
        
        long_queries = cursor.fetchall()
        if long_queries:
            print(f"\n⚠️  Found {len(long_queries)} long-running queries:")
            for query in long_queries:
                print(f"   PID {query[0]}: {query[1]} - {query[2][:100]}...")
        else:
            print("\n✅ No long-running queries found")
            
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Performance check failed: {str(e)}")


def check_notification_system():
    """Check notification system specifically"""
    print("\n🔔 Notification System Check...")
    print("=" * 50)
    
    try:
        from core.services.notification_service import NotificationService
        
        # Test notification service initialization
        notification_service = NotificationService()
        print("✅ Notification service initialized")
        
        # Test getting unread count for a test user (user_id = 1)
        start_time = time.time()
        unread_count = notification_service.get_unread_count(1)
        end_time = time.time()
        
        print(f"📬 Unread count for user 1: {unread_count}")
        print(f"⏱️  Query time: {(end_time - start_time)*1000:.2f}ms")
        
    except Exception as e:
        print(f"❌ Notification system check failed: {str(e)}")


def check_icarium_integration():
    """Check Icarium integration services"""
    print("\n🧠 Icarium Integration Check...")
    print("=" * 50)
    
    try:
        from core.services.smart_sync_engine import SmartSyncEngine
        
        # Test smart sync engine
        smart_engine = SmartSyncEngine()
        print("✅ Smart sync engine initialized")
        
        # Test generating recommendations (with short timeout)
        start_time = time.time()
        result = smart_engine.generate_smart_recommendations(24)  # Last 24 hours
        end_time = time.time()
        
        print(f"🧠 Smart recommendations: {result.get('success', False)}")
        print(f"⏱️  Generation time: {(end_time - start_time)*1000:.2f}ms")
        
        if result.get('success'):
            print(f"📊 Total recommendations: {len(result.get('recommendations', []))}")
        else:
            print(f"❌ Error: {result.get('error', 'Unknown error')}")
        
    except Exception as e:
        print(f"❌ Icarium integration check failed: {str(e)}")


def main():
    """Run all diagnostic tests"""
    print("🔧 Talaria Dashboard Connection Diagnostics")
    print("=" * 60)
    print(f"Timestamp: {datetime.now().isoformat()}")
    
    test_database_connections()
    test_api_endpoints()
    check_database_performance()
    check_notification_system()
    check_icarium_integration()
    
    print("\n" + "=" * 60)
    print("🏁 Diagnostics Complete")
    print("\nIf you see timeouts or connection errors, try:")
    print("1. Restart the Flask application")
    print("2. Check database connection pool settings")
    print("3. Review application logs for detailed errors")
    print("4. Verify network connectivity to database server")


if __name__ == "__main__":
    main()

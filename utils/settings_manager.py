# utils/settings_manager.py
import json
import os
import re
from typing import Dict, Any, Optional

SETTINGS_FILE = "settings.json"


def load_settings() -> Dict[str, Any]:
    """Load settings from JSON file"""
    try:
        if os.path.exists(SETTINGS_FILE):
            with open(SETTINGS_FILE, "r") as f:
                return json.load(f)
        return {}
    except Exception as e:
        # Use print instead of app.logger
        print(f"Error loading settings: {str(e)}")
        return {}


def save_settings(settings: Dict[str, Any]) -> bool:
    """Save settings to JSON file"""
    try:
        with open(SETTINGS_FILE, "w") as f:
            json.dump(settings, f, indent=4)
        return True
    except Exception as e:
        # Use print instead of app.logger
        print(f"Error saving settings: {str(e)}")
        return False


def validate_settings(settings: Dict[str, Any]) -> bool:
    """Validate settings before saving"""
    try:
        # Validate general settings
        if "general" in settings:
            if settings["general"].get("language") not in ["en", "fr", "de"]:
                return False

        # Validate printer settings
        if "printer" in settings:
            copies = int(settings["printer"].get("defaultCopies", 1))
            if copies < 1 or copies > 10:
                return False

        # Validate storage settings
        if "storage" in settings:
            retention = int(settings["storage"].get("retentionDays", 30))
            if retention < 7 or retention > 365:
                return False

        # Validate notification settings
        if "notifications" in settings:
            recipients = settings["notifications"].get("recipients", "")
            if recipients:
                emails = [email.strip() for email in recipients.split(",")]
                for email in emails:
                    if not is_valid_email(email):
                        return False

        return True

    except Exception as e:
        # Use print instead of app.logger
        print(f"Settings validation error: {str(e)}")
        return False


def is_valid_email(email: str) -> bool:
    """Basic email validation"""
    pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
    return bool(re.match(pattern, email))


def apply_settings(settings: Dict[str, Any], app) -> None:
    """Apply new settings to the application"""
    try:
        # Apply language setting
        if "general" in settings:
            app.config["BABEL_DEFAULT_LOCALE"] = settings["general"].get(
                "language", "en"
            )
            app.config["TIMEZONE"] = settings["general"].get("timezone", "UTC")

        # Apply printer settings
        if "printer" in settings:
            app.config["PRINTER_IP"] = settings["printer"].get("printerIp")
            app.config["DEFAULT_COPIES"] = int(
                settings["printer"].get("defaultCopies", 1)
            )

        # Apply storage settings
        if "storage" in settings:
            app.config["STORAGE_MODE"] = settings["storage"].get("mode", "hybrid")
            app.config["BACKUP_FREQUENCY"] = settings["storage"].get(
                "backupFrequency", "daily"
            )

        # Apply notification settings
        if "notifications" in settings:
            app.config["NOTIFICATION_RECIPIENTS"] = settings["notifications"].get(
                "recipients", ""
            )

    except Exception as e:
        # Use print instead of app.logger
        print(f"Error applying settings: {str(e)}")
        raise

    app.logger.info("Settings applied successfully")

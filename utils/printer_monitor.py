# printer_monitor.py
import threading
import time
import socket
from flask import current_app as app


def setup_printer_monitoring(app):
    """Set up automated printer connection monitoring"""

    def monitor_printer():
        while True:
            try:
                # Get printer IP from configuration
                printer_ip = app.config.get("DEFAULT_PRINTER_IP", "*************")

                # Check printer status
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(2)
                result = sock.connect_ex((printer_ip, 9100))
                sock.close()

                status = result == 0

                # Store status in app config for quick access
                app.config["PRINTER_STATUS"] = status
                app.config["PRINTER_LAST_CHECK"] = time.time()

                # Log status change if needed
                if (
                    "PRINTER_PREV_STATUS" in app.config
                    and app.config["PRINTER_PREV_STATUS"] != status
                ):
                    if status:
                        app.logger.info(f"Printer {printer_ip} is now online")
                    else:
                        app.logger.warning(f"Printer {printer_ip} is now offline")

                app.config["PRINTER_PREV_STATUS"] = status

            except Exception as e:
                app.logger.error(f"Error in printer monitoring: {str(e)}")

            # Check every 5 minutes
            time.sleep(300)

    # Start the monitoring thread
    monitor_thread = threading.Thread(target=monitor_printer, daemon=True)
    monitor_thread.start()

    app.logger.info("Printer monitoring started")
    return monitor_thread

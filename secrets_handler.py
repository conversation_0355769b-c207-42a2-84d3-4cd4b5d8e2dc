import os


def get_secret(secret_name, default=None):
    """
    Get secret from Docker Swarm secrets or environment variable.

    Docker Swarm mounts secrets as files in the /run/secrets/ directory.
    This function tries to read from these files first, and falls back
    to environment variables if the secret file doesn't exist.

    Args:
        secret_name (str): Name of the secret
        default (str, optional): Default value if secret not found

    Returns:
        str: The secret value or default
    """
    try:
        with open(f"/run/secrets/{secret_name}", "r") as secret_file:
            return secret_file.read().strip()
    except IOError:
        # Fallback to environment variable
        return os.environ.get(secret_name, default)

# Application configuration
app:
  name: Talaria Dashboard
  environment: ${FLASK_ENV}
  debug: false
  secret_key: ${SECRET_KEY}
  download_folder: ${DOWNLOAD_FOLDER}

# Database configuration
database:
  host: ${DB_HOST}
  name: ${DB_NAME}
  user: ${DB_USER}
  password: ${DB_PASSWORD}

# Authentication database configuration
auth_database:
  host: ${AUTH_DB_HOST}
  name: ${AUTH_DB_NAME}
  user: ${AUTH_DB_USER}
  password: ${AUTH_DB_PASSWORD}

# Mail configuration
mail:
  server: ${MAIL_SERVER}
  port: ${MAIL_PORT}
  default_sender: ${MAIL_DEFAULT_SENDER}

# UPS configuration
ups:
  client_id: ${UPS_CLIENT_ID}
  client_secret: ${UPS_CLIENT_SECRET}
  account_number: ${UPS_ACCOUNT_NUMBER}
  use_sandbox: ${UPS_USE_SANDBOX}
  default:
    shipper_name: ${UPS_DEFAULT_SHIPPER_NAME}
    attention_name: ${UPS_DEFAULT_ATTENTION_NAME}
    shipper_phone: ${UPS_DEFAULT_SHIPPER_PHONE}
    address_line1: ${UPS_DEFAULT_ADDRESS_LINE1}
    address_line2: ${UPS_DEFAULT_ADDRESS_LINE2}
    city: ${UPS_DEFAULT_CITY}
    state: ${UPS_DEFAULT_STATE}
    postal_code: ${UPS_DEFAULT_POSTAL_CODE}
    country_code: ${UPS_DEFAULT_COUNTRY_CODE}
    email: ${UPS_DEFAULT_EMAIL}

# Google Drive configuration
google_drive:
  file_id: ${GOOGLE_DRIVE_FILE_ID}

# Shipping configuration
shipping:
  default_recipients: ${DEFAULT_SHIPPING_RECIPIENTS}

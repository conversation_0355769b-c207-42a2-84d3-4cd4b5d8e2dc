# Docker Swarm Secrets Implementation Guide

This guide explains how to implement Docker Swarm secrets to replace environment variables in the Talaria Dashboard application.

## Prerequisites

- Docker Engine installed (version 19.03 or higher)
- Docker Compose installed
- Basic familiarity with Docker concepts

## Step 1: Initialize Docker Swarm

Please initialize Docker Swarm inn the machine if not already:

```bash
docker swarm init
```

If you're using multiple nodes, you'll need to join them to the swarm using the command provided after initialization.

## Step 2: Create Docker Secrets

Create secrets for  environment variables. Replace the placeholder values with actual values:

```bash
# Create secrets for database configuration
echo "your_db_password" | docker secret create db_password -
echo "operations_fr_inventory" | docker secret create db_user -
echo "pgdb.lig.local" | docker secret create db_host -
echo "icarium_test" | docker secret create db_name -

# Create secrets for mail configuration
echo "<EMAIL>" | docker secret create mail_username -
echo "your_email_password" | docker secret create mail_password -
```

## Step 3: Modify Your Application to Use Secrets

In your application, you need to modify how environment variables are accessed. Docker Swarm mounts secrets as files in the `/run/secrets/` directory.

Create a file called `secrets_handler.py` in your project root:

```python
import os

def get_secret(secret_name, default=None):
    """Get secret from Docker Swarm secrets or environment variable."""
    try:
        with open(f'/run/secrets/{secret_name}', 'r') as secret_file:
            return secret_file.read().strip()
    except IOError:
        # Fallback to environment variable
        return os.environ.get(secret_name, default)
```

## Step 4: Update  Database Configuration

Update  `db_config.py` file to use secrets:

```python
# Replace your current environment variable imports with:
from secrets_handler import get_secret

# Replace environment variable access with:
DB_HOST = get_secret('db_host', "pgdb.lig.local")
DB_NAME = get_secret('db_name', "icarium_test")
DB_USER = get_secret('db_user', "operations_fr_inventory")
DB_PASSWORD = get_secret('db_password')
```

Similarly, update  email configuration in `config.py`:

```python
MAIL_USERNAME = get_secret('mail_username')
MAIL_PASSWORD = get_secret('mail_password')
```

## Step 5: Deploy Your Stack

Deploy your application as a stack to Docker Swarm:

```bash
docker stack deploy -c docker-compose.yml talaria
```

## Step 6: Verify Deployment

Check if your services are running:

```bash
docker stack services talaria
```

Check the logs of your web service:

```bash
docker service logs talaria_web
```

## Step 7: Manage Secrets

List all secrets:

```bash
docker secret ls
```

To update a secret, you need to remove and recreate it:

```bash
# First update the service to not use the secret
docker service update --secret-rm db_password talaria_web

# Remove the old secret
docker secret rm db_password

# Create the new secret
echo "new_password" | docker secret create db_password -

# Update the service to use the new secret
docker service update --secret-add db_password talaria_web
```

## Notes on Development vs. Production

For development, you can still use your `.env` file. The `secrets_handler.py` will fall back to environment variables if secrets are not available.

## Benefits of Using Docker Swarm Secrets

1. **Security**: Secrets are encrypted at rest and in transit
2. **Isolation**: Each container only has access to the secrets it needs
3. **Auditing**: Secret operations can be logged and audited
4. **Version Control**: Secrets can be versioned and rotated
5. **Centralized Management**: All secrets are managed in one place

## Limitations

1. Secrets are only available to swarm services, not standalone containers
2. Secrets are stored as files, which might require code changes in your application
3. Secret values are limited to 500KB in size

## Troubleshooting

If your application can't access secrets:

1. Make sure your service is defined with the secrets section in docker-compose.yml
2. Check if the secrets exist: `docker secret ls`
3. Verify the container can access the secret files: `docker exec <container_id> ls -la /run/secrets/`
4. Check application logs for errors related to secrets access

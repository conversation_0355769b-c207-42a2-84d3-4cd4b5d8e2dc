# Version control
.git
.gitignore

# Python bytecode files
**/__pycache__
**/*.pyc
**/*.pyo
**/*.pyd
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
talaria_env/

# Temporary files
temp/

# Development environments
.env
.venv
.idea
.vscode
*.swp
*.swo

# Node.js
node_modules
npm-debug.log
yarn-debug.log
yarn-error.log

# Distribution / packaging
dist/
build/
*.egg-info/

# Local development config
.DS_Store
.trunk
.ruff_cache
.flake8

# Logs
logs/
*.log

# Backups
backups/
*.bak

# Secrets (if you have any checked into git)
# Note: We're not ignoring secrets/ because it's mounted as volumes in docker-compose.yml
# secrets/

# Test files
tests/
test/
htmlcov/
.coverage
.pytest_cache/
coverage.xml
*.cover

# Documentation
README.md
LICENSE
CONTRIBUTING.md
CHANGELOG.md
docs/
*.md

# Docker files (not needed inside the container)
Dockerfile
docker-compose.yml
docker-swarm-implementation.md
.dockerignore

# Deployment scripts (not needed inside the container)
deploy.py
start.sh
start_production.sh
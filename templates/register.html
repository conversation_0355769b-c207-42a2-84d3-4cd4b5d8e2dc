{% extends "base.html" %}

{% block content %}
<div class="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-blue-50 to-violet-50">
    <div class="bg-white p-8 rounded-2xl shadow-xl w-full max-w-md">
        <!-- Logo Section -->
        <div class="text-center mb-8">
            <div
                class="relative w-24 h-24 mx-auto mb-6 rounded-full overflow-hidden bg-gradient-to-r from-blue-100 to-indigo-100 p-1 shadow-lg">
                <div
                    class="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-indigo-500/20 rounded-full animate-pulse">
                </div>
                <div class="logo-iridescent">
                    <img src="{{ url_for('static', filename='img/logoTalaria.jpeg') }}" alt="Talaria Logo"
                        class="w-full h-full object-contain rounded-full p-2 bg-white/90 backdrop-blur-sm">
                </div>
                <div class="absolute inset-0 rounded-full shadow-inner"></div>
            </div>
            <h2
                class="text-3xl font-bold text-gray-900 bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                Create Account
            </h2>
            <p class="mt-2 text-sm text-gray-600">Join us to get started</p>
        </div>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
        {% for category, message in messages %}
        <div
            class="mb-4 p-4 rounded-lg {% if category == 'error' %}bg-red-50 text-red-800{% else %}bg-green-50 text-green-800{% endif %} text-sm">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    {% if category == 'error' %}
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    {% else %}
                    <i class="fas fa-check-circle mr-2"></i>
                    {% endif %}
                </div>
                <div>{{ message }}</div>
            </div>
        </div>
        {% endfor %}
        {% endif %}
        {% endwith %}

        <!-- Registration Form -->
        <form class="space-y-6" action="{{ url_for('auth.register') }}" method="POST">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

            <div class="space-y-4">
                <!-- Full Name -->
                <div>
                    <label for="fullname" class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-user text-gray-400"></i>
                        </div>
                        <input id="fullname" name="fullname" type="text" required
                            class="pl-10 block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            placeholder="Enter your full name">
                    </div>
                </div>

                <!-- Email -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-envelope text-gray-400"></i>
                        </div>
                        <input id="email" name="email" type="email" required
                            class="pl-10 block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            placeholder="Enter your email">
                    </div>
                </div>

                <!-- Password -->
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-lock text-gray-400"></i>
                        </div>
                        <input id="password" name="password" type="password" required
                            class="pl-10 block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            placeholder="Create a strong password">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer"
                            id="togglePassword">
                            <i class="fas fa-eye text-gray-400 hover:text-gray-600 transition-colors"></i>
                        </div>
                    </div>
                </div>

                <!-- Confirm Password -->
                <div>
                    <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-1">Confirm
                        Password</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-lock text-gray-400"></i>
                        </div>
                        <input id="confirm_password" name="confirm_password" type="password" required
                            class="pl-10 block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            placeholder="Confirm your password">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer"
                            id="toggleConfirmPassword">
                            <i class="fas fa-eye text-gray-400 hover:text-gray-600 transition-colors"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Terms and Conditions -->
            <div class="flex items-center">
                <input id="terms" name="terms" type="checkbox" required
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label for="terms" class="ml-2 block text-sm text-gray-700">
                    I agree to the
                    <a href="#" class="text-blue-600 hover:text-blue-500">Terms and Conditions</a>
                </label>
            </div>

            <button type="submit"
                class="w-full flex justify-center py-2.5 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150">
                <i class="fas fa-user-plus mr-2"></i>
                Create Account
            </button>
        </form>

        <!-- Login Link -->
        <div class="mt-6 text-center">
            <p class="text-sm text-gray-600">
                Already have an account?
                <a href="{{ url_for('auth.login') }}"
                    class="font-medium text-blue-600 hover:text-blue-500 transition duration-150">
                    Sign in here
                </a>
            </p>
        </div>

        <!-- Registration Info -->
        <div class="mt-6 bg-blue-50 p-4 rounded-lg text-sm text-blue-800">
            <p class="font-semibold mb-1"><i class="fas fa-info-circle mr-1"></i> Registration Information:</p>
            <ul class="list-disc pl-5 space-y-1">
                <li>New accounts are created with colleague access</li>
                <li>Contact an administrator for elevated permissions</li>
            </ul>
        </div>
    </div>

    <!-- Footer -->
    <div class="mt-8 text-center text-sm text-gray-600">
        <p>© 2025 Ligentec. All rights reserved.</p>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Toggle password visibility for password field
        const togglePassword = document.getElementById('togglePassword');
        const passwordInput = document.getElementById('password');

        togglePassword.addEventListener('click', function () {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);

            const eyeIcon = this.querySelector('i');
            eyeIcon.classList.toggle('fa-eye');
            eyeIcon.classList.toggle('fa-eye-slash');
        });

        // Toggle password visibility for confirm password field
        const toggleConfirmPassword = document.getElementById('toggleConfirmPassword');
        const confirmPasswordInput = document.getElementById('confirm_password');

        toggleConfirmPassword.addEventListener('click', function () {
            const type = confirmPasswordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            confirmPasswordInput.setAttribute('type', type);

            const eyeIcon = this.querySelector('i');
            eyeIcon.classList.toggle('fa-eye');
            eyeIcon.classList.toggle('fa-eye-slash');
        });

        // Password match validation
        const form = document.querySelector('form');
        form.addEventListener('submit', function (event) {
            if (passwordInput.value !== confirmPasswordInput.value) {
                event.preventDefault();
                alert('Passwords do not match. Please try again.');
            }
        });
    });
</script>
{% endblock %}
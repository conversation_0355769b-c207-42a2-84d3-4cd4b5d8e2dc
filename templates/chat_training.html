{% extends "home/base.html" %} {% block title %}Chatbot Training{% endblock %}
{% block styles %} {{ super() }}
<style>
  .training-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
  }

  .training-header {
    margin-bottom: 30px;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 15px;
  }

  .training-form {
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .training-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
  }

  .training-table th,
  .training-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
  }

  .training-table th {
    background-color: #f2f2f2;
    font-weight: bold;
  }

  .training-table tr:hover {
    background-color: #f5f5f5;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
  }

  .action-buttons button {
    padding: 5px 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
  }

  .edit-btn {
    background-color: #4caf50;
    color: white;
  }

  .delete-btn {
    background-color: #f44336;
    color: white;
  }

  .category-filter {
    margin-bottom: 20px;
  }

  .pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }

  .pagination a {
    color: black;
    float: left;
    padding: 8px 16px;
    text-decoration: none;
    transition: background-color 0.3s;
    border: 1px solid #ddd;
    margin: 0 4px;
  }

  .pagination a.active {
    background-color: #4caf50;
    color: white;
    border: 1px solid #4caf50;
  }

  .pagination a:hover:not(.active) {
    background-color: #ddd;
  }
</style>
{% endblock %} {% block content %}
<div class="training-container">
  <div class="training-header">
    <h1>Chatbot Training Interface</h1>
    <p>
      Use this interface to train the Talaria Assistant chatbot with new
      patterns and responses.
    </p>
  </div>

  <div class="training-form">
    <h2>Add New Training Data</h2>
    <form
      id="trainingForm"
      method="POST"
      action="{{ url_for('chat_training.add_training') }}"
    >
      <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" />
      <div class="form-group">
        <label for="pattern">Pattern (what the user might say):</label>
        <input
          type="text"
          class="form-control"
          id="pattern"
          name="pattern"
          required
          placeholder="Enter a phrase or keyword the user might type"
        />
        <small class="form-text text-muted"
          >This is what the chatbot will try to match in user messages.</small
        >
      </div>

      <div class="form-group">
        <label for="response">Response (what the chatbot should reply):</label>
        <textarea
          class="form-control"
          id="response"
          name="response"
          rows="3"
          required
          placeholder="Enter the chatbot's response to this pattern"
        ></textarea>
      </div>

      <div class="form-row">
        <div class="form-group col-md-6">
          <label for="category">Category:</label>
          <select class="form-control" id="category" name="category">
            <option value="general">General</option>
            <option value="inventory">Inventory</option>
            <option value="shipment">Shipment</option>
            <option value="labels">Labels</option>
            <option value="support">Support</option>
            <option value="technical">Technical</option>
          </select>
        </div>

        <div class="form-group col-md-6">
          <label for="priority">Priority (1-10):</label>
          <input
            type="number"
            class="form-control"
            id="priority"
            name="priority"
            min="1"
            max="10"
            value="5"
          />
          <small class="form-text text-muted"
            >Higher priority patterns are matched first.</small
          >
        </div>
      </div>

      <button type="submit" class="btn btn-primary">Add Training Data</button>
    </form>
  </div>

  <div class="category-filter">
    <h3>Filter Training Data</h3>
    <div class="form-row">
      <div class="form-group col-md-4">
        <select class="form-control" id="categoryFilter">
          <option value="">All Categories</option>
          <option value="general">General</option>
          <option value="inventory">Inventory</option>
          <option value="shipment">Shipment</option>
          <option value="labels">Labels</option>
          <option value="support">Support</option>
          <option value="technical">Technical</option>
        </select>
      </div>
      <div class="form-group col-md-4">
        <button class="btn btn-secondary" id="applyFilter">Apply Filter</button>
      </div>
    </div>
  </div>

  <h2>Existing Training Data</h2>
  <table class="training-table">
    <thead>
      <tr>
        <th>ID</th>
        <th>Pattern</th>
        <th>Response</th>
        <th>Category</th>
        <th>Priority</th>
        <th>Created By</th>
        <th>Status</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody id="trainingDataTable">
      {% for item in training_data %}
      <tr>
        <td>{{ item.id }}</td>
        <td>{{ item.pattern }}</td>
        <td>{{ item.response }}</td>
        <td>{{ item.category }}</td>
        <td>{{ item.priority }}</td>
        <td>{{ item.created_by }}</td>
        <td>{{ "Active" if item.active else "Inactive" }}</td>
        <td class="action-buttons">
          <button class="edit-btn" data-id="{{ item.id }}">Edit</button>
          <button class="delete-btn" data-id="{{ item.id }}">Delete</button>
        </td>
      </tr>
      {% endfor %}
    </tbody>
  </table>

  <div class="pagination">
    <!-- Pagination will be added dynamically -->
  </div>
</div>

<div
  class="modal fade"
  id="editModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="editModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="editModalLabel">Edit Training Data</h5>
        <button
          type="button"
          class="close"
          data-dismiss="modal"
          aria-label="Close"
        >
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form id="editForm">
          <input type="hidden" id="edit_id" name="id" />
          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" />

          <div class="form-group">
            <label for="edit_pattern">Pattern:</label>
            <input
              type="text"
              class="form-control"
              id="edit_pattern"
              name="pattern"
              required
            />
          </div>

          <div class="form-group">
            <label for="edit_response">Response:</label>
            <textarea
              class="form-control"
              id="edit_response"
              name="response"
              rows="3"
              required
            ></textarea>
          </div>

          <div class="form-row">
            <div class="form-group col-md-6">
              <label for="edit_category">Category:</label>
              <select class="form-control" id="edit_category" name="category">
                <option value="general">General</option>
                <option value="inventory">Inventory</option>
                <option value="shipment">Shipment</option>
                <option value="labels">Labels</option>
                <option value="support">Support</option>
                <option value="technical">Technical</option>
              </select>
            </div>

            <div class="form-group col-md-6">
              <label for="edit_priority">Priority:</label>
              <input
                type="number"
                class="form-control"
                id="edit_priority"
                name="priority"
                min="1"
                max="10"
                value="5"
              />
            </div>
          </div>

          <div class="form-group">
            <div class="form-check">
              <input
                class="form-check-input"
                type="checkbox"
                id="edit_active"
                name="active"
              />
              <label class="form-check-label" for="edit_active"> Active </label>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">
          Close
        </button>
        <button type="button" class="btn btn-primary" id="saveEdit">
          Save changes
        </button>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block scripts %} {{ super() }}
<script>
  $(document).ready(function () {
    // Handle edit button clicks
    $(document).on("click", ".edit-btn", function () {
      const id = $(this).data("id");
      // Fetch the training data for this ID
      $.ajax({
        url: "{{ url_for('chat_training.get_training_item') }}",
        type: "GET",
        data: { id: id },
        success: function (data) {
          if (data.success) {
            const item = data.data;
            $("#edit_id").val(item.id);
            $("#edit_pattern").val(item.pattern);
            $("#edit_response").val(item.response);
            $("#edit_category").val(item.category);
            $("#edit_priority").val(item.priority);
            $("#edit_active").prop("checked", item.active);
            $("#editModal").modal("show");
          } else {
            alert("Error: " + data.message);
          }
        },
      });
    });

    // Handle save edit button
    $("#saveEdit").click(function () {
      const formData = $("#editForm").serialize();
      $.ajax({
        url: "{{ url_for('chat_training.update_training') }}",
        type: "POST",
        data: formData,
        success: function (data) {
          if (data.success) {
            $("#editModal").modal("hide");
            // Reload the page to show updated data
            location.reload();
          } else {
            alert("Error: " + data.message);
          }
        },
      });
    });

    // Handle delete button clicks
    $(document).on("click", ".delete-btn", function () {
      if (confirm("Are you sure you want to delete this training data?")) {
        const id = $(this).data("id");
        $.ajax({
          url: "{{ url_for('chat_training.delete_training') }}",
          type: "POST",
          data: {
            id: id,
            csrf_token: "{{ csrf_token() }}",
          },
          success: function (data) {
            if (data.success) {
              // Reload the page to show updated data
              location.reload();
            } else {
              alert("Error: " + data.message);
            }
          },
        });
      }
    });

    // Handle category filter
    $("#applyFilter").click(function () {
      const category = $("#categoryFilter").val();
      window.location.href =
        "{{ url_for('chat_training.training_interface') }}" +
        (category ? "?category=" + category : "");
    });
  });
</script>
{% endblock %}

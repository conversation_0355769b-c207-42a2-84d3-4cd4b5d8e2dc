{% extends "base.html" %}

{% block title %}About - Ligentec Labeller{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header Section -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">About Talaria Dashboard</h1>
        <p class="mt-2 text-sm text-gray-600 dark:text-gray-300">Talaria is a comprehensive wafer management dashboard
            specializing in the shipment and logistics of semiconductor
            products. It offers real-time insights into production, inventory, and shipping processes, enabling
            efficient and accurate management of wafer shipments.</p>
    </div>

    <!-- System Health Monitor -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-8">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">System Health Monitor</h2>
            <button id="refreshHealth" class="text-blue-500 hover:text-blue-600">
                <i class="fas fa-sync-alt"></i>
                <span class="ml-2">Refresh</span>
            </button>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Database Status -->
            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium">Database Connection</h3>
                    <span id="dbStatus" class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">
                        <i class="fas fa-check-circle mr-1"></i>
                        Operational
                    </span>
                </div>
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Response Time</span>
                        <span id="dbResponseTime">45ms</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Last Sync</span>
                        <span id="dbLastSync">2 min ago</span>
                    </div>
                </div>
            </div>

            <!-- API Health -->
            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium">API Services</h3>
                    <span id="apiStatus" class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">
                        <i class="fas fa-check-circle mr-1"></i>
                        All Systems Go
                    </span>
                </div>
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Asana API</span>
                        <span id="asanaStatus" class="text-green-500">Connected</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Google Drive</span>
                        <span id="driveStatus" class="text-green-500">Connected</span>
                    </div>
                </div>
            </div>

            <!-- System Resources -->
            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium">System Resources</h3>
                    <span id="resourceStatus" class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">
                        <i class="fas fa-check-circle mr-1"></i>
                        Optimal
                    </span>
                </div>
                <div class="space-y-2 text-sm">
                    <div>
                        <div class="flex justify-between mb-1">
                            <span class="text-gray-600 dark:text-gray-400">CPU Usage</span>
                            <span id="cpuUsage">32%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-500 h-2 rounded-full" style="width: 32%"></div>
                        </div>
                    </div>
                    <div>
                        <div class="flex justify-between mb-1">
                            <span class="text-gray-600 dark:text-gray-400">Memory</span>
                            <span id="memoryUsage">45%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-green-500 h-2 rounded-full" style="width: 45%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Industrial Modules Overview -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-8">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">Industrial Modules</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Inventory Management -->
            <div class="flex items-start">
                <div
                    class="flex-shrink-0 h-12 w-12 rounded-md bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                    <i class="fas fa-warehouse text-blue-500"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-md font-medium text-gray-900 dark:text-white">Inventory Control</h3>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Advanced wafer and lot tracking with
                        real-time updates</p>
                    <ul class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                        <li>• Location tracking</li>
                        <li>• Stock management</li>
                        <li>• Automated alerts</li>
                    </ul>
                </div>
            </div>

            <!-- Production Tracking -->
            <div class="flex items-start">
                <div
                    class="flex-shrink-0 h-12 w-12 rounded-md bg-green-100 dark:bg-green-900 flex items-center justify-center">
                    <i class="fas fa-industry text-green-500"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-md font-medium text-gray-900 dark:text-white">Production Control</h3>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">End-to-end production monitoring and
                        control</p>
                    <ul class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                        <li>• Process tracking</li>
                        <li>• Quality control</li>
                        <li>• Performance metrics</li>
                    </ul>
                </div>
            </div>

            <!-- Shipping Management -->
            <div class="flex items-start">
                <div
                    class="flex-shrink-0 h-12 w-12 rounded-md bg-purple-100 dark:bg-purple-900 flex items-center justify-center">
                    <i class="fas fa-truck-loading text-purple-500"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-md font-medium text-gray-900 dark:text-white">Logistics Hub</h3>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Comprehensive shipping and logistics
                        management</p>
                    <ul class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                        <li>• Label generation</li>
                        <li>• Shipment tracking</li>
                        <li>• Delivery monitoring</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Integration Status Dashboard -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-8">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">Integration Status</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Asana Integration Status -->
            <div class="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                        <i class="fab fa-asana text-xl text-purple-500 mr-2"></i>
                        <span class="font-medium">Asana</span>
                    </div>
                    <span id="asanaConnectionStatus" class="flex items-center text-green-500">
                        <i class="fas fa-circle text-xs mr-1"></i>
                        Connected
                    </span>
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400">
                    Last sync: <span id="asanaLastSync">2 minutes ago</span>
                </div>
            </div>

            <!-- Google Drive Integration -->
            <div class="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                        <i class="fab fa-google-drive text-xl text-blue-500 mr-2"></i>
                        <span class="font-medium">Google Drive</span>
                    </div>
                    <span id="driveConnectionStatus" class="flex items-center text-green-500">
                        <i class="fas fa-circle text-xs mr-1"></i>
                        Connected
                    </span>
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400">
                    Storage used: <span id="driveStorage">45.2 GB</span>
                </div>
            </div>

            <!-- Printer Connection -->
            <div class="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                        <i class="fas fa-print text-xl text-gray-500 mr-2"></i>
                        <span class="font-medium">Label Printer</span>
                    </div>
                    <span id="printerStatus" class="flex items-center text-green-500">
                        <i class="fas fa-circle text-xs mr-1"></i>
                        Ready
                    </span>
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400">
                    Labels printed today: <span id="labelCount">127</span>
                </div>
            </div>

            <!-- Database Status -->
            <div class="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                        <i class="fas fa-database text-xl text-orange-500 mr-2"></i>
                        <span class="font-medium">Database</span>
                    </div>
                    <span id="dbConnectionStatus" class="flex items-center text-green-500">
                        <i class="fas fa-circle text-xs mr-1"></i>
                        Active
                    </span>
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400">
                    Active connections: <span id="dbConnections">12</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Metrics -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-8">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">System Performance</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- System Performance Chart -->
            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <canvas id="performanceChart" height="200"></canvas>
            </div>
            <!-- Response Time Chart -->
            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <canvas id="responseTimeChart" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Interactive System Insights -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-8">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">Interactive System Insights</h2>

        <!-- Real-time Activity Monitor -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <!-- Real-time Activity -->
            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h3 class="text-md font-medium mb-4">Real-time Activity</h3>
                <div id="activityFeed" class="h-40 overflow-y-auto">
                    <!-- Activities will be inserted here -->
                </div>
            </div>

            <!-- System Load Heatmap -->
            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h3 class="text-md font-medium mb-4">System Load Heatmap</h3>
                <div id="heatmapContainer"></div>
            </div>
        </div>

        <!-- Interactive Metrics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- Processing Time Card -->
            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <div class="flex justify-between items-center">
                    <h4 class="text-sm font-medium">Avg Processing Time</h4>
                    <span id="processingTime" class="text-lg font-bold text-blue-500">0ms</span>
                </div>
                <div id="processingTimeChart" class="mt-2 h-16"></div>
            </div>

            <!-- Success Rate Card -->

            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <div class="flex justify-between items-center">
                    <h4 class="text-sm font-medium">Success Rate</h4>
                    <span id="successRate" class="text-lg font-bold text-green-500">0%</span>
                </div>
                <div id="successRateChart" class="mt-2 h-16"></div>
            </div>

            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <div class="flex justify-between items-center">
                    <h4 class="text-sm font-medium">System Uptime</h4>
                    <span id="uptimeMetric" class="text-lg font-bold text-purple-500">0d 0h</span>
                </div>
                <div id="uptimeChart" class="mt-2 h-16"></div>
            </div>
        </div>
    </div>
</div>

<!-- Interactive Modal for Metric Details -->
<div id="metricModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-2xl w-full mx-4">
        <div class="flex justify-between items-center mb-4">
            <h3 id="modalTitle" class="text-lg font-semibold"></h3>
            <button onclick="closeMetricModal()" class="text-gray-500 hover:text-gray-700" aria-label="Close modal">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div id="modalContent"></div>
    </div>
</div>

<!-- Changelog Section -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
    <div class="flex items-center justify-between mb-6">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Changelog</h2>
        <div class="flex space-x-2">
            <button
                class="changelog-filter active px-3 py-1 text-sm rounded-full bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300">
                All
            </button>
            <button
                class="changelog-filter px-3 py-1 text-sm rounded-full hover:bg-blue-100 text-gray-600 dark:text-gray-400">
                Features
            </button>
            <button
                class="changelog-filter px-3 py-1 text-sm rounded-full hover:bg-blue-100 text-gray-600 dark:text-gray-400">
                Fixes
            </button>
        </div>
    </div>

    <div class="space-y-6">
        <!-- Latest Release -->
        <div class="changelog-item" data-type="feature">
            <div class="flex items-center">
                <div
                    class="flex-shrink-0 h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                    <i class="fas fa-star text-blue-500"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-md font-medium text-gray-900 dark:text-white">
                        Version 1.0.0 - Major Release
                        <span class="ml-2 text-sm text-gray-500">March 17, 2025</span>
                    </h3>
                    <div class="mt-2 text-sm text-gray-600 dark:text-gray-400 space-y-2">
                        <p>• Launched comprehensive Talaria dashboard</p>
                        <p>• Integrated real-time monitoring system</p>
                        <p>• Added advanced analytics capabilities</p>
                        <p>• Implemented multi-location support</p>
                        <p>• Enhanced user experience</p>
                        <p>• Enhanced security measures</p>
                        <p>• Enhanced error handling</p>
                        <p>• Enhanced performance</p>
                        <p>• Enhanced integration with external systems</p>
                        <p>• Enhanced accessibility</p>
                        <p>. OS agnostic</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Previous Updates -->
        <div class="changelog-item" data-type="fix">
            <div class="flex items-center">
                <div
                    class="flex-shrink-0 h-8 w-8 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
                    <i class="fas fa-wrench text-green-500"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-md font-medium text-gray-900 dark:text-white">
                        Version 0.9.5 - Performance Update
                        <span class="ml-2 text-sm text-gray-500">December 15, 2024</span>
                    </h3>
                    <div class="mt-2 text-sm text-gray-600 dark:text-gray-400 space-y-2">
                        <p>• Improved system response time</p>
                        <p>• Enhanced database performance</p>
                        <p>• Optimized label generation process</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
{% endblock %}

{% block extra_js %}

<script src="{{ url_for('static', filename='js/about.js') }}"></script>
{% endblock %}
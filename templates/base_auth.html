<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Talaria Dashboard{% endblock %}</title>

    <!-- Tailwind CSS -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@3.3.3/dist/tailwind.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>

<body class="bg-gray-50" {% block content %} <div
    class="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-blue-50 to-violet-50">
    <div class="bg-white p-8 rounded-2xl shadow-xl w-full max-w-md">
        <!-- Logo Section -->
        <div class="text-center mb-8">
            <img src="{{ url_for('static', filename='img/logo.gif') }}" alt="Logo" class="mx-auto h-12 mb-4">
            <h2 class="text-3xl font-bold text-gray-900">Welcome to Ligentec Labeller</h2>
            <p class="mt-2 text-sm text-gray-600">Please sign in to continue</p>
        </div>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
        {% for category, message in messages %}
        <div
            class="mb-4 p-4 rounded-lg {% if category == 'error' %}bg-red-50 text-red-800{% else %}bg-green-50 text-green-800{% endif %} text-sm">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    {% if category == 'error' %}
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    {% else %}
                    <i class="fas fa-check-circle mr-2"></i>
                    {% endif %}
                </div>
                <div>{{ message }}</div>
            </div>
        </div>
        {% endfor %}
        {% endif %}
        {% endwith %}

        <!-- Login Form -->
        <form class="space-y-6" action="{{ url_for('auth.login') }}" method="POST">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

            <div class="space-y-4">
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-envelope text-gray-400"></i>
                        </div>
                        <input id="username" name="username" type="email" required
                            class="pl-10 block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            placeholder="Enter your email address">
                    </div>
                </div>

                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-lock text-gray-400"></i>
                        </div>
                        <input id="password" name="password" type="password" required
                            class="pl-10 block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            placeholder="Enter your password">
                    </div>
                </div>
            </div>

            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <input id="remember-me" name="remember-me" type="checkbox"
                        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="remember-me" class="ml-2 block text-sm text-gray-700">Remember me</label>
                </div>
            </div>

            <button type="submit"
                class="w-full flex justify-center py-2.5 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150">
                <i class="fas fa-sign-in-alt mr-2"></i>
                Sign in
            </button>
        </form>

        <!-- Login Instructions -->
        <div class="mt-6 bg-blue-50 p-4 rounded-lg text-sm text-blue-800">
            <p class="font-semibold mb-1"><i class="fas fa-info-circle mr-1"></i> Login Information:</p>
            <ul class="list-disc pl-5 space-y-1">
                <li><strong>Admin users:</strong> Use your email and master password</li>
                <li><strong>Colleagues:</strong> Use your email and the shared team password</li>
            </ul>
        </div>

        <!-- System Info -->
        <div class="mt-8 text-center">
            <p class="text-xs text-gray-500">Ligentec Labeller System v1.0.0</p>
            <p class="text-xs text-gray-500">© 2025 Ligentec. All rights reserved.</p>
        </div>
    </div>
    </div>
    {% endblock %}
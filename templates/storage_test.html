{# templates/storage_test.html #}
{% extends "base.html" %}

{% block title %}Storage Test Dashboard{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-800 dark:text-white">Storage Test Dashboard</h1>
        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">Monitor and test offline storage functionality</p>
    </div>

    <!-- Status Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <!-- Storage Status Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold mb-4">Storage Status</h2>
            <div id="storage-status" class="space-y-2">
                <div class="flex justify-between items-center">
                    <span>Mode:</span>
                    <div class="flex items-center">
                        <div id="mode-indicator" class="h-3 w-3 rounded-full mr-2"></div>
                        <span id="storage-mode" class="font-medium"></span>
                    </div>
                </div>
                <div class="flex justify-between items-center">
                    <span>Network:</span>
                    <div id="network-status" class="flex items-center">
                        <span class="font-medium">Checking...</span>
                    </div>
                </div>
                <div class="flex justify-between">
                    <span>Database:</span>
                    <span id="db-status" class="font-medium"></span>
                </div>
                <div class="flex justify-between">
                    <span>Last Sync:</span>
                    <span id="last-sync" class="font-medium"></span>
                </div>
            </div>
        </div>

        <!-- Database Stats Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold mb-4">Database Stats</h2>
            <div id="db-stats" class="space-y-2">
                <div class="flex justify-between">
                    <span>Records:</span>
                    <span id="record-count" class="font-medium"></span>
                </div>
                <div class="flex justify-between">
                    <span>Size:</span>
                    <span id="db-size" class="font-medium"></span>
                </div>
                <div class="flex justify-between">
                    <span>Tables:</span>
                    <span id="table-count" class="font-medium"></span>
                </div>
                <div class="flex justify-between">
                    <span>Last Successful Sync:</span>
                    <span id="last-successful-sync" class="font-medium"></span>
                </div>
            </div>
        </div>

        <!-- Sync Queue Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold mb-4">Sync Queue</h2>
            <div id="sync-stats" class="space-y-2">
                <div class="flex justify-between">
                    <span>Pending:</span>
                    <span id="pending-count" class="font-medium"></span>
                </div>
                <div class="flex justify-between">
                    <span>Last Operation:</span>
                    <span id="last-operation" class="font-medium"></span>
                </div>
            </div>
            <div class="mt-4">
                <h3 class="text-sm font-medium mb-2">Pending Operations</h3>
                <div class="space-y-2" id="pending-operations">
                    <div class="text-sm text-gray-500">No pending operations</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Actions -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
        <h2 class="text-lg font-semibold mb-4">Test Actions</h2>
        <div class="flex flex-wrap gap-4">
            <button onclick="runStorageTest()"
                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors duration-200">
                Test Storage
            </button>
            <button onclick="runCrudTest()"
                class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md transition-colors duration-200">
                Test CRUD Operations
            </button>
            <button onclick="refreshStats()"
                class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md transition-colors duration-200">
                Refresh Statistics
            </button>
            <button onclick="toggleMode()"
                class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md transition-colors duration-200">
                Toggle Online/Offline Mode
            </button>
            <button onclick="testConnection()"
                class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors duration-200">
                Test Connection
            </button>
            <button onclick="backupData()"
                class="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-md transition-colors duration-200">
                Backup Data
            </button>
        </div>
    </div>

    <!-- Test Results -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 class="text-lg font-semibold mb-4">Test Results</h2>
        <div id="test-results" class="space-y-4">
            <!-- Results will be displayed here -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Global Variables
    let isLoading = false;
    // Utility function to format timestamps
    function formatTimestamp(timestamp) {
        return new Date(timestamp).toLocaleString();
    }

    // Function to show a message in the test results area
    function showResult(success, message, isLoading = false) {
        const resultsDiv = document.getElementById('test-results');

        cleanupTestResults();

        const resultHtml = `
        <div class="p-4 rounded-lg ${getResultClass(success, isLoading)}">
            <div class="flex items-center">
                ${getResultIcon(success, isLoading)}
                <div>
                    <p class="font-medium">${message}</p>
                    <p class="text-sm">${formatTimestamp(new Date())}</p>
                </div>
            </div>
        </div>
    `;

        resultsDiv.insertAdjacentHTML('afterbegin', resultHtml);
    }

    function cleanupTestResults() {
        const resultsDiv = document.getElementById('test-results');
        const maxResults = 5;

        while (resultsDiv.children.length > maxResults) {
            resultsDiv.removeChild(resultsDiv.lastChild);
        }
    }

    // Add to storage_test.html JavaScript:
    function getResultClass(success, isLoading) {
        if (isLoading) return 'bg-blue-100 text-blue-800';
        return success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
    }

    function getResultIcon(success, isLoading) {
        if (isLoading) {
            return '<div class="animate-spin mr-2"><i class="fas fa-circle-notch"></i></div>';
        }
        return `<i class="fas ${success ? 'fa-check-circle' : 'fa-exclamation-circle'} mr-2"></i>`;
    }

    function updateStatusIndicator(element, status, text) {
        if (!element) return;

        element.className = `h-3 w-3 rounded-full mr-2 ${status ? 'bg-green-500' : 'bg-yellow-500'
            }`;
        if (text) {
            element.nextElementSibling.textContent = text;
        }
    }

    // Function to update the dashboard with statistics
    async function updateDashboard() {
        try {
            const response = await fetch('/monitor/offline_storage');
            const data = await response.json();

            if (data.success) {
                const stats = data.statistics;

                // Update status
                document.getElementById('storage-mode').textContent = stats.status.online_mode ? 'Online' : 'Offline';
                document.getElementById('db-status').textContent = stats.database.exists ? 'Connected' : 'Not Found';
                document.getElementById('last-sync').textContent = formatTimestamp(stats.timestamp);

                // Update database stats
                document.getElementById('record-count').textContent = stats.database.inventory_count;
                document.getElementById('db-size').textContent = `${Math.round(stats.database.size / 1024)} KB`;
                document.getElementById('table-count').textContent = Object.keys(stats.database).length;

                // Update sync stats
                document.getElementById('pending-count').textContent = stats.sync_queue.pending_operations;
                document.getElementById('last-operation').textContent =
                    Object.keys(stats.sync_queue.operations).length > 0 ?
                        stats.sync_queue.operations[0] : 'None';
            }
        } catch (error) {
            console.error('Error updating dashboard:', error);
            showResult(false, 'Failed to update dashboard statistics');
        }
    }

    // Function to run storage test
    async function runStorageTest() {
        try {
            const response = await fetch('/test_storage');
            const data = await response.json();

            if (data.success) {
                showResult(true, `Storage test completed successfully. Mode: ${data.storage_info.mode}`);
                updateDashboard();
            } else {
                showResult(false, `Storage test failed: ${data.error}`);
            }
        } catch (error) {
            showResult(false, `Error running storage test: ${error.message}`);
        }
    }

    // Function to run CRUD test
    async function runCrudTest() {
        try {
            const response = await fetch('/test_offline_crud');
            const data = await response.json();

            if (data.success) {
                const results = data.test_results;
                showResult(true, `CRUD test completed. Create: ${results.create}, Read: ${results.read}, Update: ${results.update}, Delete: ${results.delete}`);
                updateDashboard();
            } else {
                showResult(false, `CRUD test failed: ${data.error}`);
            }
        } catch (error) {
            showResult(false, `Error running CRUD test: ${error.message}`);
        }
    }

    async function toggleMode() {
        try {
            const response = await fetch('/toggle-offline-mode');
            const data = await response.json();
            if (data.success) {
                document.getElementById('storage-mode').textContent = data.mode;
                updateIndicators();
                showResult(true, `Switched to ${data.mode} mode`);
                updateDashboard();
            }
        } catch (error) {
            showResult(false, `Error toggling mode: ${error.message}`);
        }
    }

    async function testConnection() {
        try {
            const response = await fetch('/test-connection');
            const data = await response.json();

            showResult(data.success,
                `Connection test ${data.success ? 'successful' : 'failed'}: ${data.message}`);
        } catch (error) {
            showResult(false, `Connection test error: ${error.message}`);
        }
    }

    async function backupData() {
        try {
            const response = await fetch('/backup-offline-data');
            const data = await response.json();

            showResult(data.success,
                `Backup ${data.success ? 'created' : 'failed'}: ${data.message}`);
        } catch (error) {
            showResult(false, `Backup error: ${error.message}`);
        }
    }

    function updateIndicators() {
        const modeIndicator = document.getElementById('mode-indicator');
        const storageMode = document.getElementById('storage-mode').textContent;

        // Update mode indicator
        modeIndicator.className = `h-3 w-3 rounded-full mr-2 ${storageMode === 'Online' ? 'bg-green-500' : 'bg-yellow-500'
            }`;
    }

    function updateNetworkStatus() {
        const statusElement = document.getElementById('network-status');
        if (navigator.onLine) {
            statusElement.innerHTML = `
            <span class="text-green-500 mr-1">●</span>
            <span class="font-medium">Connected</span>
        `;
        } else {
            statusElement.innerHTML = `
            <span class="text-red-500 mr-1">●</span>
            <span class="font-medium">Disconnected</span>
        `;
        }
    }


    function initializeNetworkMonitoring() {
        try {
            updateNetworkStatus();
            window.addEventListener('online', updateNetworkStatus);
            window.addEventListener('offline', updateNetworkStatus);
        } catch (error) {
            console.error('Network monitoring error:', error);
            document.getElementById('network-status').innerHTML = `
            <span class="text-red-500 mr-1">●</span>
            <span class="font-medium">Status Unknown</span>
        `;
        }
    }
    // Initialize everything when the page loads
    document.addEventListener('DOMContentLoaded', () => {
        updateDashboard();
        updateNetworkStatus();
        initializeNetworkMonitoring();
        setInterval(updateDashboard, 30000); // Update every 30 seconds
    });

    // Add network status event listeners
    window.addEventListener('online', updateNetworkStatus);
    window.addEventListener('offline', updateNetworkStatus);

    // Function to refresh statistics
    function refreshStats() {
        updateDashboard();
        showResult(true, 'Statistics refreshed');
    }

    // Initialize dashboard
    document.addEventListener('DOMContentLoaded', () => {
        updateDashboard();
        // Update every 30 seconds
        setInterval(updateDashboard, 30000);
    });
</script>
{% endblock %}
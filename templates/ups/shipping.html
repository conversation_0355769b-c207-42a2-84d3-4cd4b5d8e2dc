{% extends "base.html" %}

{% block title %}UPS Shipping{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/shipment_management.css') }}">
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-800 dark:text-white">UPS Shipping</h1>
        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">Create shipping labels, track packages, and manage UPS
            shipments</p>
    </div>

    <!-- UPS Authentication Status -->
    <div class="mb-4 p-4 bg-gray-100 rounded">
        <div class="flex justify-between items-center">
            <h4 class="font-semibold">UPS Authentication Status</h4>
            <div id="auth-status-indicator" class="w-3 h-3 rounded-full bg-gray-400"></div>
        </div>
        <div id="ups-auth-status" class="my-2">Checking authentication...</div>
        <div class="mt-2">
            <a href="/ups/auth/login" id="connect-ups-btn"
                class="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
                Connect to UPS
            </a>
        </div>
    </div>

    <!-- Tabs -->
    <div class="mb-6">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8">
                <button type="button" id="tab-create-label"
                    class="tab-btn active whitespace-nowrap py-4 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600">
                    Create Shipping Label
                </button>
                <button type="button" id="tab-track-package"
                    class="tab-btn whitespace-nowrap py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                    Track Package
                </button>
            </nav>
        </div>
    </div>

    <!-- Create Label Tab -->
    <div id="panel-create-label" class="tab-panel">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <form id="ups-shipping-form" class="space-y-6">
                <!-- Asana Integration -->
                <div class="border-b pb-4 mb-4">
                    <h3 class="text-lg font-medium mb-3">Asana Integration</h3>
                    <div class="grid grid-cols-1 gap-4">
                        <div>
                            <label for="asana_url" class="block text-sm font-medium mb-1">Asana Task URL</label>
                            <input type="text" id="asana_url" name="asana_url" class="w-full border rounded-md p-2"
                                placeholder="Paste Asana task URL to link this shipment">
                        </div>
                    </div>
                </div>

                <!-- Recipient Information -->
                <div class="border-b pb-4 mb-4">
                    <h3 class="text-lg font-medium mb-3">Recipient Information</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="recipient_name" class="block text-sm font-medium mb-1">Recipient Name*</label>
                            <input type="text" id="recipient_name" name="recipient_name"
                                class="w-full border rounded-md p-2" required>
                        </div>
                        <div>
                            <label for="attention_name" class="block text-sm font-medium mb-1">Attention Name</label>
                            <input type="text" id="attention_name" name="attention_name"
                                class="w-full border rounded-md p-2">
                        </div>
                        <div>
                            <label for="phone" class="block text-sm font-medium mb-1">Phone Number*</label>
                            <input type="text" id="phone" name="phone" class="w-full border rounded-md p-2" required>
                        </div>
                        <div>
                            <label for="email" class="block text-sm font-medium mb-1">Email</label>
                            <input type="email" id="email" name="email" class="w-full border rounded-md p-2">
                        </div>
                    </div>
                </div>

                <!-- Address -->
                <div class="border-b pb-4 mb-4">
                    <h3 class="text-lg font-medium mb-3">Shipping Address</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="md:col-span-2">
                            <label for="address_line1" class="block text-sm font-medium mb-1">Address Line 1*</label>
                            <input type="text" id="address_line1" name="address_line1"
                                class="w-full border rounded-md p-2" required>
                        </div>
                        <div class="md:col-span-2">
                            <label for="address_line2" class="block text-sm font-medium mb-1">Address Line 2</label>
                            <input type="text" id="address_line2" name="address_line2"
                                class="w-full border rounded-md p-2">
                        </div>
                        <div>
                            <label for="city" class="block text-sm font-medium mb-1">City*</label>
                            <input type="text" id="city" name="city" class="w-full border rounded-md p-2" required>
                        </div>
                        <div>
                            <label for="state_province" class="block text-sm font-medium mb-1">State/Province</label>
                            <input type="text" id="state_province" name="state_province"
                                class="w-full border rounded-md p-2">
                        </div>
                        <div>
                            <label for="postal_code" class="block text-sm font-medium mb-1">Postal Code*</label>
                            <input type="text" id="postal_code" name="postal_code" class="w-full border rounded-md p-2"
                                required>
                        </div>
                        <div>
                            <label for="country_code" class="block text-sm font-medium mb-1">Country Code*</label>
                            <input type="text" id="country_code" name="country_code"
                                class="w-full border rounded-md p-2" required placeholder="e.g., US, FR, DE">
                        </div>
                        <div class="md:col-span-2 flex justify-end">
                            <button type="button" id="validate-address-btn"
                                class="px-3 py-1 bg-gray-600 text-white rounded hover:bg-gray-700">
                                Validate Address
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Package Information -->
                <div class="border-b pb-4 mb-4">
                    <h3 class="text-lg font-medium mb-3">Package Information</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label for="length" class="block text-sm font-medium mb-1">Length (cm)*</label>
                            <input type="number" id="length" name="length" class="w-full border rounded-md p-2"
                                required>
                        </div>
                        <div>
                            <label for="width" class="block text-sm font-medium mb-1">Width (cm)*</label>
                            <input type="number" id="width" name="width" class="w-full border rounded-md p-2" required>
                        </div>
                        <div>
                            <label for="height" class="block text-sm font-medium mb-1">Height (cm)*</label>
                            <input type="number" id="height" name="height" class="w-full border rounded-md p-2"
                                required>
                        </div>
                        <div>
                            <label for="weight" class="block text-sm font-medium mb-1">Weight (kg)*</label>
                            <input type="number" id="weight" name="weight" class="w-full border rounded-md p-2"
                                required>
                        </div>
                        <div>
                            <label for="package_type" class="block text-sm font-medium mb-1">Package Type*</label>
                            <select id="package_type" name="package_type" class="w-full border rounded-md p-2" required>
                                <option value="02">Small Box</option>
                                <option value="03">Medium Box</option>
                                <option value="04">Large Box</option>
                                <option value="21">UPS Express Box</option>
                                <option value="01">UPS Tube</option>
                                <option value="04">UPS Pak</option>
                                <option value="30">Pallet</option>
                            </select>
                        </div>
                        <div>
                            <label for="description" class="block text-sm font-medium mb-1">Description</label>
                            <input type="text" id="description" name="description" class="w-full border rounded-md p-2"
                                placeholder="e.g., Wafer Shipment">
                        </div>
                    </div>
                </div>

                <!-- Shipping Options -->
                <div class="border-b pb-4 mb-4">
                    <h3 class="text-lg font-medium mb-3">Shipping Options</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="service_code" class="block text-sm font-medium mb-1">Service Type*</label>
                            <select id="service_code" name="service_code" class="w-full border rounded-md p-2" required>
                                <option value="">-- Select Service --</option>
                                <option value="03">UPS Ground</option>
                                <option value="01">UPS Next Day Air</option>
                                <option value="02">UPS 2nd Day Air</option>
                                <option value="12">UPS 3 Day Select</option>
                                <option value="11">UPS Standard</option>
                                <option value="07">UPS Worldwide Express</option>
                                <option value="08">UPS Worldwide Expedited</option>
                                <option value="65">UPS Worldwide Saver</option>
                            </select>
                        </div>
                        <div>
                            <label for="reference_number" class="block text-sm font-medium mb-1">Reference
                                Number</label>
                            <input type="text" id="reference_number" name="reference_number"
                                class="w-full border rounded-md p-2">
                        </div>
                        <div class="md:col-span-2 flex justify-end">
                            <button type="button" id="get-rates-btn"
                                class="px-3 py-1 bg-gray-600 text-white rounded hover:bg-gray-700">
                                Get Shipping Rates
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end">
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        Create Shipping Label
                    </button>
                </div>
            </form>

            <!-- Results Containers -->
            <div id="ups-address-result" class="hidden mt-6"></div>
            <div id="ups-rates-result" class="hidden mt-6"></div>
            <div id="ups-label-result" class="hidden mt-6"></div>
        </div>
    </div>

    <!-- Track Package Tab -->
    <div id="panel-track-package" class="tab-panel hidden">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <form id="ups-tracking-form" class="space-y-6">
                <div class="grid grid-cols-1 gap-4">
                    <div>
                        <label for="tracking-number" class="block text-sm font-medium mb-1">Tracking Number</label>
                        <input type="text" id="tracking-number" name="tracking_number"
                            class="w-full border rounded-md p-2" required>
                    </div>
                    <div class="flex justify-end">
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            Track Package
                        </button>
                    </div>
                </div>
            </form>

            <!-- Tracking Results Container -->
            <div id="ups-tracking-result" class="hidden mt-6"></div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/ups_integration.js') }}"></script>
<script>
    // Tab switching functionality
    document.addEventListener('DOMContentLoaded', function () {
        const tabButtons = document.querySelectorAll('.tab-btn');
        const tabPanels = document.querySelectorAll('.tab-panel');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Remove active class from all buttons
                tabButtons.forEach(btn => {
                    btn.classList.remove('active', 'border-blue-500', 'text-blue-600');
                    btn.classList.add('border-transparent', 'text-gray-500');
                });

                // Add active class to clicked button
                button.classList.add('active', 'border-blue-500', 'text-blue-600');
                button.classList.remove('border-transparent', 'text-gray-500');

                // Hide all panels
                tabPanels.forEach(panel => {
                    panel.classList.add('hidden');
                });

                // Show corresponding panel
                const panelId = button.id.replace('tab-', 'panel-');
                document.getElementById(panelId).classList.remove('hidden');
            });
        });

        // Check UPS authentication status
        const authStatusElement = document.getElementById('ups-auth-status');
        const authStatusIndicator = document.getElementById('auth-status-indicator');
        const connectButton = document.getElementById('connect-ups-btn');

        async function checkUPSAuth() {
            try {
                // Clear previous status classes
                authStatusElement.classList.remove("text-green-600", "text-red-600", "text-yellow-600");
                authStatusIndicator.classList.remove("bg-green-500", "bg-red-500", "bg-yellow-500", "bg-gray-400");

                // Set to loading state
                authStatusElement.textContent = "Checking UPS connection...";
                authStatusIndicator.classList.add("bg-yellow-500");

                const response = await fetch("/ups/api/check-auth");
                if (response.ok) {
                    const data = await response.json();
                    if (data.authenticated) {
                        // Connected
                        authStatusElement.textContent = "Connected to UPS";
                        authStatusElement.classList.add("text-green-600");
                        authStatusIndicator.classList.add("bg-green-500");

                        // Hide connect button
                        connectButton.classList.add("hidden");
                    } else {
                        // Not connected
                        authStatusElement.textContent = "Not connected to UPS";
                        authStatusElement.classList.add("text-red-600");
                        authStatusIndicator.classList.add("bg-red-500");

                        // Show connect button
                        connectButton.classList.remove("hidden");
                    }
                } else {
                    // Error
                    authStatusElement.textContent = "Not connected to UPS";
                    authStatusElement.classList.add("text-red-600");
                    authStatusIndicator.classList.add("bg-red-500");

                    // Show connect button
                    connectButton.classList.remove("hidden");
                }
            } catch (error) {
                // Exception
                authStatusElement.textContent = "Error checking UPS connection";
                authStatusElement.classList.add("text-red-600");
                authStatusIndicator.classList.add("bg-red-500");
                console.error("UPS auth check error:", error);

                // Show connect button
                connectButton.classList.remove("hidden");
            }
        }

        // Call the function to check auth status
        checkUPSAuth();
    });
</script>
{% endblock %}
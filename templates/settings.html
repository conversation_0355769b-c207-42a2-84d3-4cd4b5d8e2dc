{% extends "base.html" %}

{% block title %}Settings - Ligentec Labeller{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header Section -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">System Settings</h1>
        <p class="mt-2 text-sm text-gray-600 dark:text-gray-300">Configure your Ligentec Labeller system preferences and
            options</p>
    </div>

    <!-- Settings Grid -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- General Settings Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white">General Settings</h2>
                <i class="fas fa-cog text-gray-400"></i>
            </div>
            <div class="space-y-4">
                <!-- Language Selection -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        System Language
                    </label>
                    <select id="language"
                        class="w-full px-3 py-2 rounded-md border border-gray-300 dark:border-gray-600 dark:bg-gray-700"
                        title="System language">
                        <option value="en">English</option>
                        <option value="fr">French</option>
                        <option value="de">German</option>
                    </select>
                </div>

                <!-- Time Zone -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Time Zone
                    </label>
                    <select id="timezone"
                        class="w-full px-3 py-2 rounded-md border border-gray-300 dark:border-gray-600 dark:bg-gray-700"
                        title="Time zone for date and time">
                        <option value="UTC">UTC</option>
                        <option value="Europe/Paris">Europe/Paris</option>
                        <option value="Europe/Berlin">Europe/Berlin</option>
                    </select>
                </div>

                <!-- Date Format -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Date Format
                    </label>
                    <select id="dateFormat"
                        class="w-full px-3 py-2 rounded-md border border-gray-300 dark:border-gray-600 dark:bg-gray-700"
                        title="Date format for displaying dates">
                        <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                        <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                        <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Printer Settings Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Printer Settings</h2>
                <i class="fas fa-print text-gray-400"></i>
            </div>
            <div class="space-y-4">
                <!-- Printer IP -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Printer IP Address
                    </label>
                    <div class="flex space-x-2">
                        <input type="text" id="printerIp"
                            class="flex-1 px-3 py-2 rounded-md border border-gray-300 dark:border-gray-600 dark:bg-gray-700"
                            placeholder="*************">
                        <button id="testPrinter" class="px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                            Test
                        </button>
                    </div>
                </div>

                <!-- Default Copies -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Default Copies
                    </label>
                    <input type="number" id="defaultCopies"
                        class="w-full px-3 py-2 rounded-md border border-gray-300 dark:border-gray-600 dark:bg-gray-700"
                        min="1" max="10" value="1" title="Default number of copies for printing">
                </div>

                <!-- Label Size -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Label Size
                    </label>
                    <select id="labelSize"
                        class="w-full px-3 py-2 rounded-md border border-gray-300 dark:border-gray-600 dark:bg-gray-700"
                        title="Label size for printing">
                        <option value="small">Small (2.25" x 1.25")</option>
                        <option value="medium">Medium (3" x 2")</option>
                        <option value="large">Large (4" x 3")</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Storage Settings Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Storage Settings</h2>
                <i class="fas fa-database text-gray-400"></i>
            </div>
            <div class="space-y-4">
                <!-- Storage Mode -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Storage Mode
                    </label>
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <input type="radio" id="googleDrive" name="storage" value="googleDrive" class="mr-2">
                            <label for="googleDrive">Google Drive</label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="local" name="storage" value="local" class="mr-2">
                            <label for="local">Local Storage</label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="hybrid" name="storage" value="hybrid" class="mr-2">
                            <label for="hybrid">Hybrid (Recommended)</label>
                        </div>
                    </div>
                </div>

                <!-- Backup Frequency -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Backup Frequency
                    </label>
                    <select id="backupFrequency"
                        class="w-full px-3 py-2 rounded-md border border-gray-300 dark:border-gray-600 dark:bg-gray-700"
                        title="Backup frequency for hybrid storage">
                        <option value="daily">Daily</option>
                        <option value="weekly">Weekly</option>
                        <option value="monthly">Monthly</option>
                    </select>
                </div>

                <!-- Retention Period -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Backup Retention (days)
                    </label>
                    <input type="number" id="retentionDays"
                        class="w-full px-3 py-2 rounded-md border border-gray-300 dark:border-gray-600 dark:bg-gray-700"
                        min="7" max="365" value="30" title="Minimum retention period is 7 days">
                </div>
            </div>
        </div>

        <!-- Notification Settings Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Notification Settings</h2>
                <i class="fas fa-bell text-gray-400"></i>
            </div>
            <div class="space-y-4">
                <!-- Email Notifications -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Email Notifications
                    </label>
                    <div class="space-y-2">
                        <div class="flex items-center justify-between">
                            <span class="text-sm">Shipping Updates</span>
                            <label class="switch">
                                <input type="checkbox" id="shippingUpdates">
                                <span class="slider round"
                                    title="Receive email notifications for shipping updates"></span>
                            </label>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">System Alerts</span>
                            <label class="switch">
                                <input type="checkbox" id="systemAlerts"
                                    title="Receive email notifications for system alerts">
                                <span class="slider round"></span>
                            </label>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">Backup Status</span>
                            <label class="switch">
                                <input type="checkbox" id="backupStatus"
                                    title="Receive email notifications for backup status">
                                <span class="slider round"></span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Email Recipients -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Notification Recipients
                    </label>
                    <input type="text" id="recipients"
                        class="w-full px-3 py-2 rounded-md border border-gray-300 dark:border-gray-600 dark:bg-gray-700"
                        placeholder="<EMAIL>, <EMAIL>">
                </div>
            </div>
        </div>
    </div>

    <!-- Save Button -->
    <div class="mt-6 flex justify-end">
        <button id="saveSettings"
            class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 flex items-center">
            <i class="fas fa-save mr-2"></i>
            Save Settings
        </button>
    </div>
</div>

<!-- Add custom styles for toggle switches -->
<style>
    .switch {
        position: relative;
        display: inline-block;
        width: 48px;
        height: 24px;
    }

    .switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 24px;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }

    input:checked+.slider {
        background-color: #3B82F6;
    }

    input:focus+.slider {
        box-shadow: 0 0 1px #3B82F6;
    }

    input:checked+.slider:before {
        transform: translateX(24px);
    }
</style>
{% endblock %}

{% block scripts %}

<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Load current settings
        loadSettings();

        // Test Printer Connection
        const testPrinterBtn = document.getElementById('testPrinter');
        if (testPrinterBtn) {
            testPrinterBtn.addEventListener('click', async function () {
                const printerIp = document.getElementById('printerIp').value;
                if (!printerIp) {
                    showAlert('Please enter a printer IP address', 'error');
                    return;
                }

                try {
                    const response = await fetch('/test_printer_connection', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': getCsrfToken()
                        },
                        body: JSON.stringify({ printer_ip: printerIp })
                    });

                    const data = await response.json();
                    showAlert(data.message, data.success ? 'success' : 'error');
                } catch (error) {
                    showAlert('Failed to test printer connection', 'error');
                }
            });
        }

        // Save Settings
        const saveSettingsBtn = document.getElementById('saveSettings');
        if (saveSettingsBtn) {
            saveSettingsBtn.addEventListener('click', async function () {
                const settings = {
                    general: {
                        language: document.getElementById('language').value,
                        timezone: document.getElementById('timezone').value,
                        dateFormat: document.getElementById('dateFormat').value
                    },
                    printer: {
                        printerIp: document.getElementById('printerIp').value,
                        defaultCopies: document.getElementById('defaultCopies').value,
                        labelSize: document.getElementById('labelSize').value
                    },
                    storage: {
                        mode: document.querySelector('input[name="storage"]:checked')?.value,
                        backupFrequency: document.getElementById('backupFrequency').value,
                        retentionDays: document.getElementById('retentionDays').value
                    },
                    notifications: {
                        shippingUpdates: document.getElementById('shippingUpdates').checked,
                        systemAlerts: document.getElementById('systemAlerts').checked,
                        backupStatus: document.getElementById('backupStatus').checked,
                        recipients: document.getElementById('recipients').value
                    }
                };

                try {
                    const response = await fetch('/api/settings', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': getCsrfToken()
                        },
                        body: JSON.stringify(settings)
                    });

                    const data = await response.json();
                    if (data.success) {
                        showAlert('Settings saved successfully', 'success');
                    } else {
                        throw new Error(data.message);
                    }
                } catch (error) {
                    showAlert('Failed to save settings: ' + error.message, 'error');
                }
            });
        }

        // Helper Functions
        async function loadSettings() {
            try {
                const response = await fetch('/api/settings');
                const settings = await response.json();

                if (settings) {
                    // Populate General Settings
                    document.getElementById('language').value = settings.general?.language || 'en';
                    document.getElementById('timezone').value = settings.general?.timezone || 'UTC';
                    document.getElementById('dateFormat').value = settings.general?.dateFormat || 'DD/MM/YYYY';

                    // Populate Printer Settings
                    document.getElementById('printerIp').value = settings.printer?.printerIp || '';
                    document.getElementById('defaultCopies').value = settings.printer?.defaultCopies || 1;
                    document.getElementById('labelSize').value = settings.printer?.labelSize || 'medium';

                    // Populate Storage Settings
                    const storageMode = settings.storage?.mode || 'hybrid';
                    document.querySelector(`input[name="storage"][value="${storageMode}"]`).checked = true;
                    document.getElementById('backupFrequency').value = settings.storage?.backupFrequency || 'daily';
                    document.getElementById('retentionDays').value = settings.storage?.retentionDays || 30;

                    // Populate Notification Settings
                    document.getElementById('shippingUpdates').checked = settings.notifications?.shippingUpdates || false;
                    document.getElementById('systemAlerts').checked = settings.notifications?.systemAlerts || false;
                    document.getElementById('backupStatus').checked = settings.notifications?.backupStatus || false;
                    document.getElementById('recipients').value = settings.notifications?.recipients || '';
                }
            } catch (error) {
                console.error('Failed to load settings:', error);
                showAlert('Failed to load current settings', 'error');
            }
        }

        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg ${type === 'success' ? 'bg-green-500' :
                type === 'error' ? 'bg-red-500' :
                    'bg-blue-500'
                } text-white`;
            alertDiv.textContent = message;

            document.body.appendChild(alertDiv);
            setTimeout(() => alertDiv.remove(), 3000);
        }

        function getCsrfToken() {
            return document.querySelector('meta[name="csrf-token"]')?.content ||
                document.querySelector('input[name="csrf_token"]')?.value;
        }
    });
</script>
{% endblock %}
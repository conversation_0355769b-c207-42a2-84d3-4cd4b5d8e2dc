{% extends "base.html" %}

{% block title %}Manual Selection{% endblock %}

{% block content %}
<div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="max-w-3xl mx-auto">
        <!-- Header -->
        <div class="mb-8 text-center">
            <h1 class="text-3xl font-bold text-gray-900">Manual Selection</h1>
            <p class="mt-2 text-gray-600">Select a single lot or enter multiple lots to process</p>
        </div>

        <!-- Selection Form -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <form id="manualSelectionForm" method="POST" action="{{ url_for('manual_selection') }}"
                class="p-6 space-y-6">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">

                <!-- Single Lot Selection -->
                <div>
                    <label for="lot" class="block text-sm font-medium text-gray-700">Select Single Lot</label>
                    <select name="lot" id="lot"
                        class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="">-- Select Lot --</option>
                        {% for choice in form.lot.choices %}
                        <option value="{{ choice[0] }}">{{ choice[1] }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Multiple Lots Input -->
                <div>
                    <label for="multiple_lots" class="block text-sm font-medium text-gray-700">
                        Or Enter Multiple Lots
                    </label>
                    <div class="mt-1">
                        <textarea id="multiple_lots" name="multiple_lots" rows="3"
                            class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                            placeholder="Enter lots separated by commas (e.g., LOT001, LOT002)"></textarea>
                    </div>
                    <p class="mt-1 text-sm text-gray-500">Separate multiple lot numbers with commas</p>
                </div>

                <!-- Submit Button -->
                <div class="mt-6">
                    <button type="submit"
                        class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Continue to Wafer Selection
                    </button>
                </div>
            </form>
        </div>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
        <div class="mt-6 space-y-4">
            {% for category, message in messages %}
            <div
                class="p-4 rounded-md {{ 'bg-red-100 border border-red-400 text-red-700' if category == 'error' else 'bg-green-100 border border-green-400 text-green-700' }}">
                {{ message }}
            </div>
            {% endfor %}
        </div>
        {% endif %}
        {% endwith %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const form = document.getElementById('manualSelectionForm');
        const lotSelect = document.getElementById('lot');
        const multipleLotsInput = document.getElementById('multiple_lots');

        // Clear other field when one is being used
        lotSelect.addEventListener('change', function () {
            if (this.value) {
                multipleLotsInput.value = '';
            }
        });

        multipleLotsInput.addEventListener('input', function () {
            if (this.value) {
                lotSelect.value = '';
            }
        });

        // Form submission
        form.addEventListener('submit', function (e) {
            e.preventDefault();

            const selectedLot = lotSelect.value;
            const multipleLots = multipleLotsInput.value.trim();

            if (!selectedLot && !multipleLots) {
                Swal.fire({
                    icon: 'error',
                    title: 'Selection Required',
                    text: 'Please select a lot or enter multiple lots'
                });
                return;
            }

            // Submit form normally
            this.submit();
        });
    });
</script>
{% endblock %}
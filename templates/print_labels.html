{% extends "base.html" %} {% block title %}Print Labels{% endblock %} {% block
head %} {{ super() }}
<meta name="csrf-token" content="{{ csrf_token }}" />
{% endblock %} {% block content %}
<div
  class="min-h-screen bg-gray-100 py-6 flex flex-col justify-center sm:py-12"
>
  <div class="relative py-3 sm:max-w-3xl sm:mx-auto">
    <div class="relative px-4 py-10 bg-white shadow-lg sm:rounded-3xl sm:p-20">
      <!-- Header with breadcrumb navigation -->
      <div class="mb-8">
        <div class="flex items-center text-sm text-gray-500 mb-4">
          <a href="{{ url_for('home') }}" class="hover:text-indigo-600">Home</a>
          <svg
            class="h-4 w-4 mx-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 5l7 7-7 7"
            ></path>
          </svg>
          <a href="{{ url_for('select_lot') }}" class="hover:text-indigo-600"
            >Select Lot</a
          >
          <svg
            class="h-4 w-4 mx-2"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 5l7 7-7 7"
            ></path>
          </svg>
          <span class="font-medium text-indigo-600">Print Labels</span>
        </div>
        <h2 class="text-3xl font-extrabold text-gray-900">Print Labels</h2>
        <p class="mt-2 text-gray-600">
          Create and print labels for the selected wafers.
        </p>
      </div>

      <!-- Alert banner for validation status -->
      <div id="validation-alert" class="mb-8 rounded-md p-4 hidden">
        <!-- Content will be dynamically added via JavaScript -->
      </div>

      <form
        id="finalForm"
        method="POST"
        action="{{ url_for('print_labels') }}"
        class="space-y-6"
      >
        {{ form.hidden_tag() }}

        <!-- Form layout with two columns for wider screens -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Label type field (always visible) -->
          <div class="col-span-1">
            <label
              for="{{ form.label_type.id }}"
              class="block text-sm font-medium text-gray-700"
            >
              {{ form.label_type.label.text }}
              <span class="text-red-500">*</span>
            </label>
            {{ form.label_type(class="mt-1 block w-full border border-gray-300
            rounded-md shadow-sm py-2 px-3 focus:outline-none
            focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm") }}
            <div
              id="{{ form.label_type.id }}-error"
              class="mt-1 text-sm text-red-600 hidden"
            ></div>
          </div>

          <!-- Shipment date field (always visible) -->
          <div class="col-span-1">
            <label
              for="{{ form.shipment_date.id }}"
              class="block text-sm font-medium text-gray-700"
            >
              {{ form.shipment_date.label.text }}
              <span class="text-red-500">*</span>
            </label>
            {{ form.shipment_date(class="mt-1 block w-full border
            border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none
            focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm") }}
            <div
              id="{{ form.shipment_date.id }}-error"
              class="mt-1 text-sm text-red-600 hidden"
            ></div>
          </div>

          <!-- Label title field (always visible) -->
          <div class="col-span-1">
            <label
              for="{{ form.label_title.id }}"
              class="block text-sm font-medium text-gray-700"
            >
              {{ form.label_title.label.text }}
              <span class="text-red-500">*</span>
            </label>
            {{ form.label_title(class="mt-1 block w-full border border-gray-300
            rounded-md shadow-sm py-2 px-3 focus:outline-none
            focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm") }}
            <div
              id="{{ form.label_title.id }}-error"
              class="mt-1 text-sm text-red-600 hidden"
            ></div>
          </div>

          <!-- Shipment origin field (always visible) -->
          <div class="col-span-1">
            <label
              for="{{ form.shipment_origin.id }}"
              class="block text-sm font-medium text-gray-700"
            >
              {{ form.shipment_origin.label.text }}
              <span class="text-red-500">*</span>
            </label>
            {{ form.shipment_origin(class="mt-1 block w-full border
            border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none
            focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm") }}
            <div
              id="{{ form.shipment_origin.id }}-error"
              class="mt-1 text-sm text-red-600 hidden"
            ></div>
          </div>

          <!-- Wafer count field (always visible) -->
          <div class="col-span-1">
            <label
              for="{{ form.manual_wafer_count.id }}"
              class="block text-sm font-medium text-gray-700"
            >
              {{ form.manual_wafer_count.label.text }}
            </label>
            {{ form.manual_wafer_count(class="mt-1 block w-full border
            border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none
            focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm") }}
            <div
              id="{{ form.manual_wafer_count.id }}-error"
              class="mt-1 text-sm text-red-600 hidden"
            ></div>
          </div>

          <!-- Erfurt-specific fields (conditionally visible) -->
          <div class="erfurt-field col-span-1">
            <label
              for="{{ form.po.id }}"
              class="block text-sm font-medium text-gray-700"
            >
              {{ form.po.label.text }}
            </label>
            {{ form.po(class="mt-1 block w-full border border-gray-300
            rounded-md shadow-sm py-2 px-3 focus:outline-none
            focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm") }}
            <div
              id="{{ form.po.id }}-error"
              class="mt-1 text-sm text-red-600 hidden"
            ></div>
          </div>

          <div class="erfurt-field col-span-1">
            <label
              for="{{ form.project_id.id }}"
              class="block text-sm font-medium text-gray-700"
            >
              {{ form.project_id.label.text }}
            </label>
            {{ form.project_id(class="mt-1 block w-full border border-gray-300
            rounded-md shadow-sm py-2 px-3 focus:outline-none
            focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm") }}
            <div
              id="{{ form.project_id.id }}-error"
              class="mt-1 text-sm text-red-600 hidden"
            ></div>
          </div>

          <div class="erfurt-field col-span-1">
            <label
              for="{{ form.lot_id.id }}"
              class="block text-sm font-medium text-gray-700"
            >
              {{ form.lot_id.label.text }}
            </label>
            {{ form.lot_id(class="mt-1 block w-full border border-gray-300
            rounded-md shadow-sm py-2 px-3 focus:outline-none
            focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm") }}
            <div
              id="{{ form.lot_id.id }}-error"
              class="mt-1 text-sm text-red-600 hidden"
            ></div>
          </div>

          <!-- Substrate-wafer specific fields (conditionally visible) -->
          <div class="substrate-wafer-field col-span-1">
            <label
              for="{{ form.item_id.id }}"
              class="block text-sm font-medium text-gray-700"
            >
              {{ form.item_id.label.text }} <span class="text-red-500">*</span>
            </label>
            {{ form.item_id(class="mt-1 block w-full border border-gray-300
            rounded-md shadow-sm py-2 px-3 focus:outline-none
            focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm") }}
            <div
              id="{{ form.item_id.id }}-error"
              class="mt-1 text-sm text-red-600 hidden"
            ></div>
          </div>

          <div class="substrate-wafer-field col-span-1">
            <label
              for="{{ form.svm_lot_id.id }}"
              class="block text-sm font-medium text-gray-700"
            >
              {{ form.svm_lot_id.label.text }}
              <span class="text-red-500">*</span>
            </label>
            {{ form.svm_lot_id(class="mt-1 block w-full border border-gray-300
            rounded-md shadow-sm py-2 px-3 focus:outline-none
            focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm") }}
            <div
              id="{{ form.svm_lot_id.id }}-error"
              class="mt-1 text-sm text-red-600 hidden"
            ></div>
          </div>

          <div class="substrate-wafer-field col-span-1">
            <label
              for="{{ form.unit_price.id }}"
              class="block text-sm font-medium text-gray-700"
            >
              {{ form.unit_price.label.text }}
            </label>
            {{ form.unit_price(class="mt-1 block w-full border border-gray-300
            rounded-md shadow-sm py-2 px-3 focus:outline-none
            focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",
            step="0.01", min="0") }}
            <div
              id="{{ form.unit_price.id }}-error"
              class="mt-1 text-sm text-red-600 hidden"
            ></div>
          </div>

          <div class="substrate-wafer-field col-span-1">
            <label
              for="{{ form.currency.id }}"
              class="block text-sm font-medium text-gray-700"
            >
              {{ form.currency.label.text }}
            </label>
            {{ form.currency(class="mt-1 block w-full border border-gray-300
            rounded-md shadow-sm py-2 px-3 focus:outline-none
            focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm") }}
            <div
              id="{{ form.currency.id }}-error"
              class="mt-1 text-sm text-red-600 hidden"
            ></div>
          </div>

          <!-- Comments field (full width) -->
          <div class="col-span-1 md:col-span-2">
            <label
              for="{{ form.comments.id }}"
              class="block text-sm font-medium text-gray-700"
            >
              {{ form.comments.label.text }}
            </label>
            {{ form.comments(class="mt-1 block w-full border border-gray-300
            rounded-md shadow-sm py-2 px-3 focus:outline-none
            focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm", rows=3)
            }}
            <div
              id="{{ form.comments.id }}-error"
              class="mt-1 text-sm text-red-600 hidden"
            ></div>
          </div>
        </div>

        <!-- Wafer selection section -->
        <div class="mt-8">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900">Selected Wafers</h3>
            <span class="text-sm text-gray-500" id="wafer-count-display">
              {{ session.get('paired_slots_wafers', [])|length }} wafer{% if
              session.get('paired_slots_wafers', [])|length != 1 %}s{% endif %}
              selected
            </span>
          </div>

          <!-- Improved wafer selection UI with status indicators -->
          <div
            class="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden shadow-sm"
          >
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                    >
                      Current Slot ID
                    </th>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                    >
                      New Slot ID
                    </th>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                    >
                      Wafer ID (Scribe ID)
                    </th>
                    <th
                      scope="col"
                      class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider"
                    >
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody
                  class="bg-white divide-y divide-gray-200"
                  id="wafer-table-body"
                >
                  {% for item in session.get('paired_slots_wafers', []) %}
                  <tr class="wafer-row" data-wafer-id="{{ item[1] }}">
                    {% if item is string %}
                    <td
                      colspan="4"
                      class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"
                    >
                      {{ item }}
                    </td>
                    {% elif item is iterable and item|length == 2 %}
                    <td
                      class="px-6 py-4 whitespace-nowrap text-sm text-gray-700"
                    >
                      {{ item[0] }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <input
                        type="text"
                        name="new_slot_ids[]"
                        value="{{ item[0] }}"
                        class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        required
                        title="New Slot ID"
                        placeholder="Enter new slot ID"
                      />
                    </td>
                    <td
                      class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700"
                    >
                      {{ item[1] }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span
                        class="wafer-status inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                      >
                        Checking...
                      </span>
                    </td>
                    {% else %}
                    <td
                      colspan="4"
                      class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"
                    >
                      Invalid data format
                    </td>
                    {% endif %}
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Button row with back and submit buttons -->
        <div
          class="flex justify-between items-center pt-5 border-t border-gray-200"
        >
          <a
            href="{{ url_for('select_lot') }}"
            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <svg
              class="mr-2 -ml-1 h-5 w-5 text-gray-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M10 19l-7-7m0 0l7-7m-7 7h18"
              ></path>
            </svg>
            Back
          </a>
          <div class="flex space-x-3">
            <button
              type="button"
              id="validate-wafers-btn"
              class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              <svg
                class="mr-2 -ml-1 h-5 w-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M5 13l4 4L19 7"
                ></path>
              </svg>
              Validate Wafers
            </button>
            <button
              type="submit"
              id="submit-btn"
              disabled
              class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg
                class="mr-2 -ml-1 h-5 w-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"
                ></path>
              </svg>
              Print Labels
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Modal for wafer issues -->
<div
  id="wafer-issues-modal"
  class="fixed inset-0 z-50 hidden overflow-y-auto"
  aria-labelledby="modal-title"
  role="dialog"
  aria-modal="true"
>
  <div
    class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"
  >
    <div
      class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
      aria-hidden="true"
    ></div>
    <span
      class="hidden sm:inline-block sm:align-middle sm:h-screen"
      aria-hidden="true"
      >&#8203;</span
    >
    <div
      class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
    >
      <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
        <div class="sm:flex sm:items-start">
          <div
            class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10"
          >
            <svg
              class="h-6 w-6 text-red-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
          </div>
          <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
            <h3
              class="text-lg leading-6 font-medium text-gray-900"
              id="modal-title"
            >
              Wafer Validation Issues
            </h3>
            <div class="mt-2">
              <p class="text-sm text-gray-500 mb-2">
                The following wafers cannot be processed for the following
                reasons:
              </p>
              <ul
                id="wafer-issues-list"
                class="text-sm text-gray-700 list-disc list-inside"
              >
                <!-- List items will be inserted dynamically -->
              </ul>
            </div>
          </div>
        </div>
      </div>
      <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
        <button
          type="button"
          id="wafer-issues-close-btn"
          class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm"
        >
          Got it
        </button>
      </div>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
  document.addEventListener("DOMContentLoaded", function () {
    const finalForm = document.getElementById("finalForm");
    const labelTypeSelect = document.getElementById("label_type");
    const submitBtn = document.getElementById("submit-btn");
    const validateWafersBtn = document.getElementById("validate-wafers-btn");
    const validationAlert = document.getElementById("validation-alert");
    const waferIssuesModal = document.getElementById("wafer-issues-modal");
    const waferIssuesList = document.getElementById("wafer-issues-list");
    const waferIssuesCloseBtn = document.getElementById(
      "wafer-issues-close-btn"
    );

    let waferValidationStatus = false;

    // Utility function to get CSRF token
    function getCsrfToken() {
      const token = document.querySelector('meta[name="csrf-token"]')?.content;
      if (!token) throw new Error("CSRF token not found");
      return token;
    }

    // Function to toggle Erfurt-specific fields
    function toggleErfurtFields(isVisible) {
      const erfurtFields = document.querySelectorAll(".erfurt-field");
      erfurtFields.forEach((field) => {
        field.style.display = isVisible ? "block" : "none";
      });
    }

    // Display error alert
    function showAlert(type, message) {
      validationAlert.className = `mb-8 rounded-md p-4 ${
        type === "error"
          ? "bg-red-50 text-red-800"
          : "bg-green-50 text-green-800"
      }`;
      validationAlert.innerHTML = `
        <div class="flex items-center">
          <div class="flex-shrink-0">
            ${
              type === "error"
                ? '<svg class="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>'
                : '<svg class="h-5 w-5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" /></svg>'
            }
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium">${message}</p>
          </div>
        </div>
      `;
      validationAlert.classList.remove("hidden");
    }

    // Function to handle wafer validation
    function validateWafers() {
      // Get all wafer IDs from the table
      const waferRows = document.querySelectorAll(".wafer-row");
      if (waferRows.length === 0) {
        showAlert("error", "No wafers selected for validation.");
        return;
      }

      // Extract wafer IDs
      const waferIds = Array.from(waferRows).map((row) => {
        return row.dataset.waferId;
      });

      // Update status indicators
      waferRows.forEach((row) => {
        const statusCell = row.querySelector(".wafer-status");
        statusCell.className =
          "wafer-status inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800";
        statusCell.textContent = "Validating...";
      });

      // Show loading status
      showAlert("info", "Validating wafer availability...");

      // Send validation request
      fetch("/validate_wafers", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRFToken": getCsrfToken(),
          "X-Requested-With": "XMLHttpRequest",
        },
        body: JSON.stringify({ waferIds }),
      })
        .then((response) => response.json())
        .then((data) => {
          if (data.success) {
            // All wafers valid - update UI
            waferRows.forEach((row) => {
              const statusCell = row.querySelector(".wafer-status");
              statusCell.className =
                "wafer-status inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800";
              statusCell.textContent = "Available";
            });

            showAlert(
              "success",
              "All wafers are available for printing. You can now proceed."
            );
            waferValidationStatus = true;
            submitBtn.disabled = false;
          } else {
            // Handle invalid wafers
            if (data.message && data.message.includes("<br>")) {
              // Format the error message for display
              const messageParts = data.message.split("<br>");
              const title = messageParts[0];

              // Extract wafer IDs and reasons
              const waferIssues = [];
              for (let i = 1; i < messageParts.length; i++) {
                if (
                  messageParts[i].trim() &&
                  !messageParts[i].includes("contact support")
                ) {
                  waferIssues.push(messageParts[i].trim());
                }
              }

              // Update UI to show which wafers are invalid
              waferRows.forEach((row) => {
                const waferId = row.dataset.waferId;
                const statusCell = row.querySelector(".wafer-status");

                // Check if this wafer ID is in the error message
                const isInvalid = waferIssues.some((issue) =>
                  issue.includes(waferId)
                );

                if (isInvalid) {
                  // Find the reason for this wafer
                  const issue = waferIssues.find((issue) =>
                    issue.includes(waferId)
                  );
                  const reason =
                    issue.match(/\(([^)]+)\)/)?.[1] || "Not available";

                  statusCell.className =
                    "wafer-status inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800";
                  statusCell.textContent = reason;
                } else {
                  statusCell.className =
                    "wafer-status inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800";
                  statusCell.textContent = "Available";
                }
              });

              // Show modal with details
              showWaferIssuesModal(waferIssues);
              showAlert(
                "error",
                "Some wafers are not available for printing. See details below."
              );
            } else {
              // Generic error
              showAlert("error", data.message || "Failed to validate wafers");

              // Mark all wafers as invalid
              waferRows.forEach((row) => {
                const statusCell = row.querySelector(".wafer-status");
                statusCell.className =
                  "wafer-status inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800";
                statusCell.textContent = "Error";
              });
            }

            waferValidationStatus = false;
            submitBtn.disabled = true;
          }
        })
        .catch((error) => {
          console.error("Error validating wafers:", error);
          showAlert(
            "error",
            "An error occurred during validation. Please try again."
          );

          // Reset status indicators
          waferRows.forEach((row) => {
            const statusCell = row.querySelector(".wafer-status");
            statusCell.className =
              "wafer-status inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800";
            statusCell.textContent = "Not validated";
          });

          waferValidationStatus = false;
          submitBtn.disabled = true;
        });
    }

    // Show the wafer issues modal
    function showWaferIssuesModal(issues) {
      // Clear previous issues
      waferIssuesList.innerHTML = "";

      // Add each issue as a list item
      issues.forEach((issue) => {
        const li = document.createElement("li");
        li.className = "mb-1";
        li.textContent = issue;
        waferIssuesList.appendChild(li);
      });

      // Show the modal
      waferIssuesModal.classList.remove("hidden");
    }

    // Hide the wafer issues modal
    function hideWaferIssuesModal() {
      waferIssuesModal.classList.add("hidden");
    }

    // Function to handle Asana data population
    function populateFormFromAsana(taskData) {
      if (!taskData) return;

      // Function to safely set form field value
      function setFieldValue(fieldName, value) {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (field && value) {
          field.value = value;
          // For textareas, adjust height
          if (field.tagName.toLowerCase() === "textarea") {
            field.style.height = "auto";
            field.style.height = field.scrollHeight + "px";
          }
        }
      }

      // Set values from session data
      if (taskData.label_type) {
        setFieldValue("label_type", taskData.label_type);
        toggleErfurtFields(taskData.label_type === "2");
        toggleSubstrateWaferFields(taskData.label_type === "4");
      }

      // Set label title
      if (taskData.label_title) {
        setFieldValue("label_title", taskData.label_title);
      }

      // Set comments if available
      if (taskData.comments) {
        setFieldValue("comments", taskData.comments);
      }

      // Set other fields
      if (taskData.wafer_count) {
        setFieldValue("manual_wafer_count", taskData.wafer_count);
      }

      if (taskData.po) {
        setFieldValue("po", taskData.po);
      }

      if (taskData.project_id) {
        setFieldValue("project_id", taskData.project_id);
      }

      if (taskData.lot_id) {
        setFieldValue("lot_id", taskData.lot_id);
      }

      // Set Substrate-wafer fields if available
      if (taskData.item_id) {
        setFieldValue("item_id", taskData.item_id);
      }

      if (taskData.svm_lot_id) {
        setFieldValue("svm_lot_id", taskData.svm_lot_id);
      }

      if (taskData.unit_price) {
        setFieldValue("unit_price", taskData.unit_price);
      }

      if (taskData.currency) {
        setFieldValue("currency", taskData.currency);
      }

      if (taskData.shipment_origin) {
        setFieldValue("shipment_origin", taskData.shipment_origin);
      }
    }

    // Function to toggle Substrate-wafer fields
    function toggleSubstrateWaferFields(isVisible) {
      const substrateWaferFields = document.querySelectorAll(
        ".substrate-wafer-field"
      );
      substrateWaferFields.forEach((field) => {
        field.style.display = isVisible ? "block" : "none";
      });
    }

    // Initialize label type handling
    if (labelTypeSelect) {
      // Initial state
      toggleErfurtFields(labelTypeSelect.value === "2");
      toggleSubstrateWaferFields(labelTypeSelect.value === "4");

      // Handle changes
      labelTypeSelect.addEventListener("change", (e) => {
        toggleErfurtFields(e.target.value === "2");
        toggleSubstrateWaferFields(e.target.value === "4");
      });
    }

    // Set up validation button event
    if (validateWafersBtn) {
      validateWafersBtn.addEventListener("click", validateWafers);
    }

    // Set up wafer issues modal close button
    if (waferIssuesCloseBtn) {
      waferIssuesCloseBtn.addEventListener("click", hideWaferIssuesModal);
    }

    // Initial data fetch
    fetch("/print_labels", {
      headers: {
        "X-Requested-With": "XMLHttpRequest",
        Accept: "application/json",
        "X-CSRFToken": getCsrfToken(),
      },
    })
      .then((response) => {
        if (!response.ok)
          throw new Error(`HTTP error! status: ${response.status}`);
        return response.json();
      })
      .then((data) => {
        console.log("Received form data:", data);
        if (data.success) {
          populateFormFromAsana(data.data);
        } else {
          throw new Error(data.message || "Failed to load form data");
        }
      })
      .catch((error) => {
        console.error("Error fetching form data:", error);
        showAlert("error", "Failed to load form data. Please try again.");
      });

    // Form submission handler
    if (finalForm) {
      finalForm.addEventListener("submit", function (e) {
        e.preventDefault();

        // Validate wafers if not already validated
        if (!waferValidationStatus) {
          Swal.fire({
            icon: "warning",
            title: "Validation Required",
            text: "Please validate the wafers before proceeding.",
            confirmButtonColor: "#4F46E5",
          });
          return;
        }

        // Create FormData object
        const formData = new FormData(this);

        // Add new slot IDs
        const slotInputs = document.querySelectorAll(
          'input[name="new_slot_ids[]"]'
        );
        const newSlotIds = Array.from(slotInputs).map((input) => input.value);
        formData.append("new_slot_ids", JSON.stringify(newSlotIds));

        // Show loading indicator
        Swal.fire({
          title: "Processing...",
          text: "Generating labels and packing slip",
          allowOutsideClick: false,
          showConfirmButton: false,
          didOpen: () => {
            Swal.showLoading();
          },
        });

        // Submit form
        fetch(finalForm.action, {
          method: "POST",
          headers: {
            "X-CSRFToken": getCsrfToken(),
            "X-Requested-With": "XMLHttpRequest",
            Accept: "application/json",
          },
          body: formData,
        })
          .then((response) => {
            if (!response.ok) {
              return response.json().then((data) => {
                throw new Error(
                  data.message || `HTTP error! status: ${response.status}`
                );
              });
            }
            return response.json();
          })
          .then((data) => {
            console.log("Server response:", data);

            if (data.status === "success") {
              console.log("Success response received");
              console.log("About to redirect to:", data.redirect);

              try {
                window.location.href = data.redirect;
                console.log("Redirect initiated");
              } catch (e) {
                console.error("Redirect failed:", e);
              }
            } else {
              throw new Error(data.message || "Form submission failed");
            }
          })
          .catch((error) => {
            console.error("Form submission error:", error);
            Swal.fire({
              icon: "error",
              title: "Error",
              text: error.message || "An unexpected error occurred",
              confirmButtonColor: "#4F46E5",
            });
          });
      });
    }

    // Trigger wafer validation on load
    setTimeout(validateWafers, 1000);
  });
</script>

<style>
  .my-swal {
    z-index: 9999;
  }

  /* Add smooth transitions for form fields */
  [name="po"],
  [name="project_id"],
  [name="lot_id"] {
    transition: all 0.3s ease-in-out;
  }

  /* Style for textareas to handle auto-expansion */
  textarea {
    min-height: 100px;
    transition: height 0.2s ease;
  }

  .swal2-html-container {
    text-align: left !important;
  }

  /* Animations */
  @keyframes fadeIn {
    from {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }

  .wafer-status {
    animation: fadeIn 0.3s ease-out;
  }

  /* Make the modal look nice */
  #wafer-issues-modal {
    animation: fadeIn 0.3s ease-out;
  }
</style>
{% endblock %}

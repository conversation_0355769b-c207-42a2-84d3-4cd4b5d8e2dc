<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Label</title>
    <style>
        body {
            font-family: Arial, sans-serif;
        }

        .label {
            width: 400px;
            height: 200px;
            border: 2px solid black;
            padding: 20px;
            font-size: 14px;
            margin: 20px auto;
            position: relative;
        }

        .qr-code {
            position: absolute;
            bottom: 20px;
            right: 20px;
        }

        .details {
            margin-bottom: 20px;
        }

        h3 {
            text-align: center;
            margin-bottom: 10px;
        }
    </style>
</head>

<body>
    <div class="label" id="label">
        <h3>Shipment Label</h3>
        <div class="details">
            <p>Project: {{ project_name }}</p>
            <p>Date: {{ shipment_date }}</p>
            <p>Wafer Count: {{ wafer_count }}</p>
            <p>Device ID: {{ device_id }}</p>
        </div>
        <div class="qr-code">
            <img id="qrCode" alt="QR Code">
        </div>
    </div>

    <button onclick="printLabel()">Print Label</button>
    <button onclick="downloadPDF()">Download as PDF</button>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.3.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcode-generator/1.4.4/qrcode.min.js"></script>
    <script>
        // Generate QR Code
        const qrCode = qrcode(0, 'L');
        qrCode.addData('{{ wafer_list }}');
        qrCode.make();
        document.getElementById('qrCode').src = qrCode.createImgTag(4);

        // Print the label
        function printLabel() {
            window.print();
        }

        // Download as PDF
        function downloadPDF() {
            const { jsPDF } = window.jspdf;
            const pdf = new jsPDF();
            pdf.html(document.getElementById('label'), {
                callback: function (doc) {
                    doc.save('label.pdf');
                },
                x: 10,
                y: 10,
            });
        }
    </script>
</body>

</html>
{% extends "base.html" %} {% block title %}User Preferences - Talaria
Dashboard{% endblock %} {% block breadcrumb %}
<li class="flex items-center">
  <span class="mx-2"><i class="fas fa-chevron-right text-gray-400"></i></span>
  <span class="text-gray-900 font-medium flex items-center">
    <i class="fas fa-sliders-h mr-1"></i>Preferences
  </span>
</li>
{% endblock %} {% block extra_css %}
<link
  rel="stylesheet"
  href="{{ url_for('static', filename='css/preferences.css') }}"
/>
{% endblock %} {% block content %}
<div class="container mx-auto px-4 py-6">
  <div class="max-w-6xl mx-auto">
    <!-- Header with Quick Actions -->
    <div class="mb-8">
      <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            <i class="fas fa-user-cog mr-3 text-blue-600"></i>
            User Preferences
          </h1>
          <p class="text-gray-600 dark:text-gray-400">
            Customize your Talaria Dashboard experience and manage your profile
            settings
          </p>
        </div>
        <div class="mt-4 md:mt-0 flex space-x-3">
          <button id="export-preferences" class="btn-secondary">
            <i class="fas fa-download mr-2"></i>Export Settings
          </button>
          <button id="import-preferences" class="btn-secondary">
            <i class="fas fa-upload mr-2"></i>Import Settings
          </button>
          <input type="file" id="import-file" accept=".json" class="hidden" />
        </div>
      </div>
    </div>

    <!-- Quick Settings Bar -->
    <div class="quick-settings-bar">
      <div class="flex flex-wrap items-center justify-between gap-4">
        <div class="flex items-center space-x-6">
          <div class="flex items-center space-x-2">
            <i class="fas fa-palette text-blue-600"></i>
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300"
              >Quick Theme:</span
            >
            <div class="flex space-x-2">
              <button
                class="theme-quick-btn"
                data-theme="light"
                title="Light Theme"
              >
                <i class="fas fa-sun"></i>
              </button>
              <button
                class="theme-quick-btn"
                data-theme="dark"
                title="Dark Theme"
              >
                <i class="fas fa-moon"></i>
              </button>
              <button
                class="theme-quick-btn"
                data-theme="system"
                title="System Theme"
              >
                <i class="fas fa-desktop"></i>
              </button>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <i class="fas fa-text-height text-green-600"></i>
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300"
              >Font Size:</span
            >
            <div class="flex space-x-1">
              <button
                class="font-size-btn"
                data-size="small"
                title="Small Font"
              >
                A
              </button>
              <button
                class="font-size-btn"
                data-size="medium"
                title="Medium Font"
              >
                A
              </button>
              <button
                class="font-size-btn"
                data-size="large"
                title="Large Font"
              >
                A
              </button>
            </div>
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-600 dark:text-gray-400"
            >Last saved:</span
          >
          <span id="last-saved" class="text-sm font-medium text-blue-600"
            >Never</span
          >
        </div>
      </div>
    </div>

    <!-- Tabbed Interface -->
    <div class="preferences-container">
      <!-- Tab Navigation -->
      <div class="tab-navigation">
        <nav class="flex space-x-8 px-6" aria-label="Tabs">
          <button class="preference-tab active" data-tab="profile">
            <i class="fas fa-user mr-2"></i>Profile
          </button>
          <button class="preference-tab" data-tab="appearance">
            <i class="fas fa-palette mr-2"></i>Appearance
          </button>
          <button class="preference-tab" data-tab="notifications">
            <i class="fas fa-bell mr-2"></i>Notifications
          </button>
          <button class="preference-tab" data-tab="workflow">
            <i class="fas fa-cogs mr-2"></i>Workflow
          </button>
          <button class="preference-tab" data-tab="security">
            <i class="fas fa-shield-alt mr-2"></i>Security
          </button>
          <button class="preference-tab" data-tab="advanced">
            <i class="fas fa-sliders-h mr-2"></i>Advanced
          </button>
        </nav>
      </div>

      <!-- Tab Content -->
      <div class="tab-content-container">
        <!-- Profile Tab -->
        <div id="profile-tab" class="tab-content active">
          <h2 class="tab-title">
            <i class="fas fa-user-circle mr-2 text-blue-600"></i>Profile
            Information
          </h2>

          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Profile Picture Section -->
            <div class="lg:col-span-1">
              <div class="profile-picture-section">
                <div class="profile-picture-container">
                  <div id="profile-image-container" class="profile-image">
                    {% if get_user_info().profile_image %}
                    <img
                      id="current-profile-image"
                      src="{{ url_for('profile_image_file', filename=get_user_info().profile_image) }}"
                      alt="Profile Picture"
                      class="w-full h-full object-cover"
                    />
                    {% else %}
                    <span class="profile-initial"
                      >{{ get_user_info().name[:1] }}</span
                    >
                    {% endif %}
                  </div>
                  <button
                    type="button"
                    id="change-profile-picture"
                    class="profile-change-btn"
                    title="Change profile picture"
                  >
                    <i class="fas fa-camera"></i>
                  </button>
                </div>

                <form id="profile-image-form" class="hidden mt-4">
                  <input
                    type="file"
                    id="profile-image-upload"
                    name="profile_image"
                    accept="image/*"
                    class="hidden"
                  />
                  <button type="submit" class="btn-primary">
                    <i class="fas fa-upload mr-2"></i>Upload Image
                  </button>
                </form>

                <div class="profile-picture-info">
                  <p>Recommended: Square image, max 5MB</p>
                  <p>Formats: JPG, PNG, GIF</p>
                </div>
              </div>
            </div>

            <!-- Profile Details -->
            <div class="lg:col-span-2">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-6">
                  <div class="profile-info-card">
                    <label class="profile-label">Full Name</label>
                    <div class="profile-value">
                      <i class="fas fa-user text-gray-400 mr-2"></i>
                      {{ get_user_info().name }}
                    </div>
                  </div>

                  <div class="profile-info-card">
                    <label class="profile-label">Email Address</label>
                    <div class="profile-value">
                      <i class="fas fa-envelope text-gray-400 mr-2"></i>
                      {{ get_user_info().email }}
                    </div>
                  </div>

                  <div class="profile-info-card">
                    <label class="profile-label">Role</label>
                    <div class="profile-value">
                      <i class="fas fa-id-badge text-gray-400 mr-2"></i>
                      <span class="role-badge role-{{ get_user_info().role }}">
                        {{ get_user_info().role.title() }}
                      </span>
                    </div>
                  </div>
                </div>

                <div class="space-y-6">
                  <div class="profile-info-card">
                    <label class="profile-label">Last Login</label>
                    <div class="profile-value">
                      <i class="fas fa-clock text-gray-400 mr-2"></i>
                      {% if get_user_info().last_login %} {{
                      get_user_info().last_login.strftime('%B %d, %Y at %I:%M
                      %p') }} {% else %}
                      <span class="text-blue-600">First login session</span>
                      {% endif %}
                    </div>
                  </div>

                  <div class="profile-info-card">
                    <label class="profile-label">Account Created</label>
                    <div class="profile-value">
                      <i class="fas fa-calendar-plus text-gray-400 mr-2"></i>
                      {% if get_user_info().created_at %} {{
                      get_user_info().created_at.strftime('%B %d, %Y') }} {%
                      else %} Unknown {% endif %}
                    </div>
                  </div>

                  <div class="profile-info-card">
                    <label class="profile-label">Session Status</label>
                    <div class="profile-value">
                      <i class="fas fa-circle text-green-500 mr-2"></i>
                      <span class="text-green-600 font-medium"
                        >Active Session</span
                      >
                    </div>
                  </div>
                </div>
              </div>

              <!-- Quick Actions -->
              <div class="profile-actions">
                <h3
                  class="text-lg font-medium text-gray-900 dark:text-white mb-4"
                >
                  Quick Actions
                </h3>
                <div class="flex flex-wrap gap-3">
                  <button class="btn-secondary" onclick="downloadUserData()">
                    <i class="fas fa-download mr-2"></i>Download My Data
                  </button>
                  <button class="btn-secondary" onclick="clearUserCache()">
                    <i class="fas fa-broom mr-2"></i>Clear Cache
                  </button>
                  <button class="btn-secondary" onclick="resetAllPreferences()">
                    <i class="fas fa-undo mr-2"></i>Reset All Settings
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Appearance Tab -->
        <div id="appearance-tab" class="tab-content">
          <h2 class="tab-title">
            <i class="fas fa-palette mr-2 text-purple-600"></i>Appearance &
            Display
          </h2>

          <form id="appearance-form" class="space-y-8">
            <!-- Theme Settings -->
            <div class="preference-section">
              <h3 class="section-title">Theme Preferences</h3>
              <div class="theme-options">
                <label class="theme-option">
                  <input
                    type="radio"
                    name="theme"
                    value="light"
                    class="sr-only"
                  />
                  <div class="theme-card">
                    <div class="theme-preview light-preview"></div>
                    <div class="theme-info">
                      <i class="fas fa-sun text-yellow-500"></i>
                      <span>Light</span>
                    </div>
                  </div>
                </label>

                <label class="theme-option">
                  <input
                    type="radio"
                    name="theme"
                    value="dark"
                    class="sr-only"
                  />
                  <div class="theme-card">
                    <div class="theme-preview dark-preview"></div>
                    <div class="theme-info">
                      <i class="fas fa-moon text-blue-500"></i>
                      <span>Dark</span>
                    </div>
                  </div>
                </label>

                <label class="theme-option">
                  <input
                    type="radio"
                    name="theme"
                    value="system"
                    class="sr-only"
                  />
                  <div class="theme-card">
                    <div class="theme-preview system-preview"></div>
                    <div class="theme-info">
                      <i class="fas fa-desktop text-gray-500"></i>
                      <span>System</span>
                    </div>
                  </div>
                </label>
              </div>
            </div>

            <!-- Typography Settings -->
            <div class="preference-section">
              <h3 class="section-title">Typography</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="form-group">
                  <label class="form-label">Font Family</label>
                  <select name="fontFamily" class="form-select">
                    <option value="inter">Inter (Default)</option>
                    <option value="roboto">Roboto</option>
                    <option value="opensans">Open Sans</option>
                    <option value="arial">Arial</option>
                    <option value="helvetica">Helvetica</option>
                  </select>
                </div>

                <div class="form-group">
                  <label class="form-label">Font Size</label>
                  <div class="font-size-slider">
                    <input
                      type="range"
                      name="fontSize"
                      min="12"
                      max="20"
                      value="16"
                      class="slider"
                    />
                    <div class="slider-labels">
                      <span>Small</span>
                      <span>Medium</span>
                      <span>Large</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Color Scheme -->
            <div class="preference-section">
              <h3 class="section-title">Color Accent</h3>
              <div class="color-options">
                <label class="color-option">
                  <input
                    type="radio"
                    name="accentColor"
                    value="blue"
                    class="sr-only"
                  />
                  <div class="color-swatch bg-blue-600"></div>
                  <span>Blue</span>
                </label>
                <label class="color-option">
                  <input
                    type="radio"
                    name="accentColor"
                    value="green"
                    class="sr-only"
                  />
                  <div class="color-swatch bg-green-600"></div>
                  <span>Green</span>
                </label>
                <label class="color-option">
                  <input
                    type="radio"
                    name="accentColor"
                    value="purple"
                    class="sr-only"
                  />
                  <div class="color-swatch bg-purple-600"></div>
                  <span>Purple</span>
                </label>
                <label class="color-option">
                  <input
                    type="radio"
                    name="accentColor"
                    value="red"
                    class="sr-only"
                  />
                  <div class="color-swatch bg-red-600"></div>
                  <span>Red</span>
                </label>
              </div>
            </div>
          </form>
        </div>

        <!-- Notifications Tab -->
        <div id="notifications-tab" class="tab-content">
          <h2 class="tab-title">
            <i class="fas fa-bell mr-2 text-yellow-600"></i>Notification
            Preferences
          </h2>

          <form id="notifications-form" class="space-y-8">
            <!-- Email Notifications -->
            <div class="preference-section">
              <h3 class="section-title">Email Notifications</h3>
              <div class="space-y-4">
                <div class="notification-item">
                  <div class="notification-info">
                    <h4 class="notification-title">Shipment Updates</h4>
                    <p class="notification-desc">
                      Receive emails when shipment status changes
                    </p>
                  </div>
                  <label class="toggle-switch">
                    <input type="checkbox" name="emailNotifications" checked />
                    <span class="toggle-slider"></span>
                  </label>
                </div>

                <div class="notification-item">
                  <div class="notification-info">
                    <h4 class="notification-title">Inventory Alerts</h4>
                    <p class="notification-desc">
                      Get notified about low inventory levels
                    </p>
                  </div>
                  <label class="toggle-switch">
                    <input type="checkbox" name="inventoryAlerts" />
                    <span class="toggle-slider"></span>
                  </label>
                </div>

                <div class="notification-item">
                  <div class="notification-info">
                    <h4 class="notification-title">System Updates</h4>
                    <p class="notification-desc">
                      Important system announcements and updates
                    </p>
                  </div>
                  <label class="toggle-switch">
                    <input type="checkbox" name="systemUpdates" checked />
                    <span class="toggle-slider"></span>
                  </label>
                </div>
              </div>
            </div>

            <!-- Browser Notifications -->
            <div class="preference-section">
              <h3 class="section-title">Browser Notifications</h3>
              <div class="space-y-4">
                <div class="notification-item">
                  <div class="notification-info">
                    <h4 class="notification-title">Desktop Notifications</h4>
                    <p class="notification-desc">
                      Show desktop notifications for important events
                    </p>
                  </div>
                  <label class="toggle-switch">
                    <input type="checkbox" name="browserNotifications" />
                    <span class="toggle-slider"></span>
                  </label>
                </div>

                <div class="notification-item">
                  <div class="notification-info">
                    <h4 class="notification-title">Sound Alerts</h4>
                    <p class="notification-desc">
                      Play sound for notifications
                    </p>
                  </div>
                  <label class="toggle-switch">
                    <input type="checkbox" name="soundAlerts" />
                    <span class="toggle-slider"></span>
                  </label>
                </div>
              </div>
            </div>
          </form>
        </div>

        <!-- Workflow Tab -->
        <div id="workflow-tab" class="tab-content">
          <h2 class="tab-title">
            <i class="fas fa-cogs mr-2 text-green-600"></i>Workflow Settings
          </h2>

          <form id="workflow-form" class="space-y-8">
            <!-- Default Views -->
            <div class="preference-section">
              <h3 class="section-title">Default Views</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="form-group">
                  <label class="form-label">Dashboard Layout</label>
                  <select name="dashboardLayout" class="form-select">
                    <option value="grid">Grid View</option>
                    <option value="list">List View</option>
                    <option value="compact">Compact View</option>
                  </select>
                </div>

                <div class="form-group">
                  <label class="form-label">Inventory View</label>
                  <select name="inventoryView" class="form-select">
                    <option value="table">Table View</option>
                    <option value="cards">Card View</option>
                    <option value="detailed">Detailed View</option>
                  </select>
                </div>
              </div>
            </div>

            <!-- Automation Settings -->
            <div class="preference-section">
              <h3 class="section-title">Automation</h3>
              <div class="space-y-4">
                <div class="notification-item">
                  <div class="notification-info">
                    <h4 class="notification-title">Auto-save Forms</h4>
                    <p class="notification-desc">
                      Automatically save form data as you type
                    </p>
                  </div>
                  <label class="toggle-switch">
                    <input type="checkbox" name="autoSave" checked />
                    <span class="toggle-slider"></span>
                  </label>
                </div>

                <div class="notification-item">
                  <div class="notification-info">
                    <h4 class="notification-title">Quick Actions</h4>
                    <p class="notification-desc">
                      Enable keyboard shortcuts and quick actions
                    </p>
                  </div>
                  <label class="toggle-switch">
                    <input type="checkbox" name="quickActions" checked />
                    <span class="toggle-slider"></span>
                  </label>
                </div>
              </div>
            </div>
          </form>
        </div>

        <!-- Security Tab -->
        <div id="security-tab" class="tab-content">
          <h2 class="tab-title">
            <i class="fas fa-shield-alt mr-2 text-red-600"></i>Security &
            Privacy
          </h2>

          <form id="security-form" class="space-y-8">
            <!-- Session Settings -->
            <div class="preference-section">
              <h3 class="section-title">Session Management</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="form-group">
                  <label class="form-label">Session Timeout</label>
                  <select name="sessionTimeout" class="form-select">
                    <option value="30">30 minutes</option>
                    <option value="60">1 hour</option>
                    <option value="240">4 hours</option>
                    <option value="480">8 hours</option>
                  </select>
                </div>

                <div class="form-group">
                  <label class="form-label">Remember Me Duration</label>
                  <select name="rememberDuration" class="form-select">
                    <option value="7">7 days</option>
                    <option value="30">30 days</option>
                    <option value="90">90 days</option>
                  </select>
                </div>
              </div>
            </div>

            <!-- Privacy Settings -->
            <div class="preference-section">
              <h3 class="section-title">Privacy</h3>
              <div class="space-y-4">
                <div class="notification-item">
                  <div class="notification-info">
                    <h4 class="notification-title">Activity Logging</h4>
                    <p class="notification-desc">
                      Log user activities for security purposes
                    </p>
                  </div>
                  <label class="toggle-switch">
                    <input type="checkbox" name="activityLogging" checked />
                    <span class="toggle-slider"></span>
                  </label>
                </div>

                <div class="notification-item">
                  <div class="notification-info">
                    <h4 class="notification-title">Usage Analytics</h4>
                    <p class="notification-desc">
                      Help improve the system by sharing usage data
                    </p>
                  </div>
                  <label class="toggle-switch">
                    <input type="checkbox" name="usageAnalytics" />
                    <span class="toggle-slider"></span>
                  </label>
                </div>
              </div>
            </div>
          </form>
        </div>

        <!-- Advanced Tab -->
        <div id="advanced-tab" class="tab-content">
          <h2 class="tab-title">
            <i class="fas fa-sliders-h mr-2 text-indigo-600"></i>Advanced
            Settings
          </h2>

          <form id="advanced-form" class="space-y-8">
            <!-- Performance Settings -->
            <div class="preference-section">
              <h3 class="section-title">Performance</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="form-group">
                  <label class="form-label">Cache Duration</label>
                  <select name="cacheDuration" class="form-select">
                    <option value="5">5 minutes</option>
                    <option value="15">15 minutes</option>
                    <option value="30">30 minutes</option>
                    <option value="60">1 hour</option>
                  </select>
                </div>

                <div class="form-group">
                  <label class="form-label">Items Per Page</label>
                  <select name="itemsPerPage" class="form-select">
                    <option value="25">25 items</option>
                    <option value="50">50 items</option>
                    <option value="100">100 items</option>
                    <option value="200">200 items</option>
                  </select>
                </div>
              </div>
            </div>

            <!-- Developer Options -->
            <div class="preference-section">
              <h3 class="section-title">Developer Options</h3>
              <div class="space-y-4">
                <div class="notification-item">
                  <div class="notification-info">
                    <h4 class="notification-title">Debug Mode</h4>
                    <p class="notification-desc">
                      Enable detailed logging and debug information
                    </p>
                  </div>
                  <label class="toggle-switch">
                    <input type="checkbox" name="debugMode" />
                    <span class="toggle-slider"></span>
                  </label>
                </div>

                <div class="notification-item">
                  <div class="notification-info">
                    <h4 class="notification-title">Beta Features</h4>
                    <p class="notification-desc">
                      Access experimental features (may be unstable)
                    </p>
                  </div>
                  <label class="toggle-switch">
                    <input type="checkbox" name="betaFeatures" />
                    <span class="toggle-slider"></span>
                  </label>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Save Button -->
    <div class="save-section">
      <button id="save-all-preferences" class="btn-primary-large">
        <i class="fas fa-save mr-2"></i>Save All Preferences
      </button>
    </div>
  </div>
</div>
{% endblock %} {% block extra_js %}
<script src="{{ url_for('static', filename='js/preferences.js') }}"></script>
{% endblock %}

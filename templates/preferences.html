{% extends "base.html" %} {% block title %}User Preferences - Talaria
Dashboard{% endblock %} {% block breadcrumb %}
<li class="flex items-center">
  <span class="mx-2"><i class="fas fa-chevron-right text-gray-400"></i></span>
  <span class="text-gray-900 font-medium flex items-center">
    <i class="fas fa-sliders-h mr-1"></i>Preferences
  </span>
</li>
{% endblock %} {% block content %}
<div class="container mx-auto px-4 py-6">
  <div class="max-w-6xl mx-auto">
    <!-- Header with Quick Actions -->
    <div class="mb-8">
      <div class="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            <i class="fas fa-user-cog mr-3 text-blue-600"></i>
            User Preferences
          </h1>
          <p class="text-gray-600 dark:text-gray-400">
            Customize your Talaria Dashboard experience and manage your profile
            settings
          </p>
        </div>
        <div class="mt-4 md:mt-0 flex space-x-3">
          <button id="export-preferences" class="btn-secondary">
            <i class="fas fa-download mr-2"></i>Export Settings
          </button>
          <button id="import-preferences" class="btn-secondary">
            <i class="fas fa-upload mr-2"></i>Import Settings
          </button>
          <input type="file" id="import-file" accept=".json" class="hidden" />
        </div>
      </div>
    </div>

    <!-- Quick Settings Bar -->
    <div
      class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 rounded-lg p-4 mb-8 border border-blue-200 dark:border-gray-600"
    >
      <div class="flex flex-wrap items-center justify-between gap-4">
        <div class="flex items-center space-x-6">
          <div class="flex items-center space-x-2">
            <i class="fas fa-palette text-blue-600"></i>
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300"
              >Quick Theme:</span
            >
            <div class="flex space-x-2">
              <button
                class="theme-quick-btn"
                data-theme="light"
                title="Light Theme"
              >
                <i class="fas fa-sun"></i>
              </button>
              <button
                class="theme-quick-btn"
                data-theme="dark"
                title="Dark Theme"
              >
                <i class="fas fa-moon"></i>
              </button>
              <button
                class="theme-quick-btn"
                data-theme="system"
                title="System Theme"
              >
                <i class="fas fa-desktop"></i>
              </button>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <i class="fas fa-text-height text-green-600"></i>
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300"
              >Font Size:</span
            >
            <div class="flex space-x-1">
              <button
                class="font-size-btn"
                data-size="small"
                title="Small Font"
              >
                A
              </button>
              <button
                class="font-size-btn"
                data-size="medium"
                title="Medium Font"
              >
                A
              </button>
              <button
                class="font-size-btn"
                data-size="large"
                title="Large Font"
              >
                A
              </button>
            </div>
          </div>
        </div>
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-600 dark:text-gray-400"
            >Last saved:</span
          >
          <span id="last-saved" class="text-sm font-medium text-blue-600"
            >Never</span
          >
        </div>
      </div>
    </div>

    <!-- Profile Section -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
      <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
        Profile Information
      </h2>

      <div class="flex flex-col md:flex-row">
        <!-- Profile Picture -->
        <div class="w-full md:w-1/3 flex flex-col items-center mb-6 md:mb-0">
          <div class="relative">
            <div
              id="profile-image-container"
              class="w-32 h-32 rounded-full bg-blue-100 flex items-center justify-center overflow-hidden mb-4"
            >
              {% if get_user_info().profile_image %}
              <img
                id="current-profile-image"
                src="{{ url_for('profile_image_file', filename=get_user_info().profile_image) }}"
                alt="Profile Picture"
                class="w-full h-full object-cover"
              />
              {% else %}
              <span class="text-blue-600 text-5xl font-bold"
                >{{ get_user_info().name[:1] }}</span
              >
              {% endif %}
            </div>
            <button
              type="button"
              id="change-profile-picture"
              class="absolute bottom-0 right-0 bg-blue-600 text-white rounded-full p-2 shadow-lg"
              title="Change profile picture"
              aria-label="Change profile picture"
            >
              <i class="fas fa-camera"></i>
            </button>
          </div>

          <form id="profile-image-form" class="hidden">
            <label for="profile-image-upload" class="sr-only"
              >Choose profile picture</label
            >
            <input
              type="file"
              id="profile-image-upload"
              name="profile_image"
              accept="image/*"
              class="hidden"
              title="Choose profile picture"
              aria-label="Choose profile picture"
            />
            <button
              type="submit"
              class="px-4 py-2 bg-blue-600 text-white rounded-lg mt-2"
            >
              Upload
            </button>
          </form>
        </div>

        <!-- Profile Details -->
        <div class="w-full md:w-2/3 md:pl-8">
          <div class="space-y-4">
            <div>
              <label
                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >Name</label
              >
              <p class="text-gray-900 dark:text-white font-medium">
                {{ get_user_info().name }}
              </p>
            </div>

            <div>
              <label
                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >Email</label
              >
              <p class="text-gray-900 dark:text-white font-medium">
                {{ get_user_info().email }}
              </p>
            </div>

            <div>
              <label
                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >Role</label
              >
              <p class="text-gray-900 dark:text-white font-medium capitalize">
                {{ get_user_info().role }}
              </p>
            </div>

            <!-- Last login information -->
            <div class="mt-4">
              <label
                class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >Last Login</label
              >
              <p class="text-gray-900 dark:text-white font-medium">
                {% if get_user_info().last_login %} {{
                get_user_info().last_login.strftime('%Y-%m-%d %H:%M:%S') }} {%
                else %} First login {% endif %}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Preferences Form -->
    <form id="preferences-form" class="space-y-8">
      <!-- Display Settings -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          Display Settings
        </h2>
        <div class="space-y-4">
          <!-- Theme Preference -->
          <div>
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
              >Theme Mode</label
            >
            <div class="flex space-x-4">
              <label class="inline-flex items-center">
                <input
                  type="radio"
                  name="theme"
                  value="light"
                  class="form-radio text-blue-600"
                />
                <span class="ml-2 text-gray-700 dark:text-gray-300">Light</span>
              </label>
              <label class="inline-flex items-center">
                <input
                  type="radio"
                  name="theme"
                  value="dark"
                  class="form-radio text-blue-600"
                />
                <span class="ml-2 text-gray-700 dark:text-gray-300">Dark</span>
              </label>
              <label class="inline-flex items-center">
                <input
                  type="radio"
                  name="theme"
                  value="system"
                  class="form-radio text-blue-600"
                />
                <span class="ml-2 text-gray-700 dark:text-gray-300"
                  >System Default</span
                >
              </label>
            </div>
          </div>

          <!-- Font Size -->
          <div>
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
              >Font Size</label
            >
            <select
              name="fontSize"
              class="form-select w-full max-w-xs rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              title="Font Size"
            >
              <option value="small">Small</option>
              <option value="medium">Medium</option>
              <option value="large">Large</option>
            </select>
          </div>

          <!-- Font Family -->
          <div>
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
              >Font Family</label
            >
            <select
              name="fontFamily"
              class="form-select w-full max-w-xs rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              title="Font Family"
            >
              <option value="inter">Inter</option>
              <option value="roboto">Roboto</option>
              <option value="opensans">Open Sans</option>
              <option value="arial">Arial</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Notification Settings -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          Notification Preferences
        </h2>
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">
                Email Notifications
              </h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                Receive email updates about shipments
              </p>
            </div>
            <label class="switch">
              <input type="checkbox" name="emailNotifications" />
              <span class="slider round" title="Email Notifications"></span>
            </label>
          </div>

          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">
                Browser Notifications
              </h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                Show desktop notifications
              </p>
            </div>
            <label class="switch">
              <input type="checkbox" name="browserNotifications" />
              <span class="slider round" title="Browser Notifications"></span>
            </label>
          </div>
        </div>
      </div>

      <!-- Dashboard Layout -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          Dashboard Layout
        </h2>
        <div class="space-y-4">
          <div>
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
              >Default View</label
            >
            <select
              name="defaultView"
              class="form-select w-full max-w-xs rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              title="Default View"
            >
              <option value="grid">Grid View</option>
              <option value="list">List View</option>
              <option value="compact">Compact View</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex justify-end space-x-4">
        <button
          type="button"
          onclick="resetPreferences()"
          class="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
        >
          Reset to Default
        </button>
        <button
          type="submit"
          class="px-4 py-2 text-white bg-blue-600 rounded-lg hover:bg-blue-700"
        >
          Save Changes
        </button>
      </div>
    </form>
  </div>
</div>
{% endblock %} {% block extra_css %}
<style>
  /* Switch Toggle Styles */
  .switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
  }

  .switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 34px;
  }

  .slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
  }

  input:checked + .slider {
    background-color: #3b82f6;
  }

  input:checked + .slider:before {
    transform: translateX(26px);
  }

  /* Dark mode adjustments */
  .dark .slider {
    background-color: #4b5563;
  }

  .dark input:checked + .slider {
    background-color: #3b82f6;
  }
</style>
{% endblock %} {% block extra_js %}
<script>
  document.addEventListener("DOMContentLoaded", function () {
    const preferencesForm = document.getElementById("preferences-form");
    const profileImageForm = document.getElementById("profile-image-form");
    const changeProfilePictureBtn = document.getElementById(
      "change-profile-picture"
    );
    const profileImageUpload = document.getElementById("profile-image-upload");

    // Load saved preferences
    loadPreferences();

    // Handle preferences form submission
    preferencesForm.addEventListener("submit", function (e) {
      e.preventDefault();
      savePreferences();
    });

    // Handle profile image button click
    changeProfilePictureBtn.addEventListener("click", function () {
      profileImageUpload.click();
    });

    // Handle file selection
    profileImageUpload.addEventListener("change", function () {
      if (this.files && this.files[0]) {
        // Show the form with upload button
        profileImageForm.classList.remove("hidden");

        // Preview the image
        const reader = new FileReader();
        reader.onload = function (e) {
          const container = document.getElementById("profile-image-container");

          // Check if there's already an image preview
          let img = document.getElementById("current-profile-image");
          if (!img) {
            // Create new image element if it doesn't exist
            img = document.createElement("img");
            img.id = "current-profile-image";
            img.className = "w-full h-full object-cover";
            img.alt = "Profile Picture";

            // Remove the initial if it exists
            const initial = container.querySelector("span");
            if (initial) {
              container.removeChild(initial);
            }

            container.appendChild(img);
          }

          // Set the image source to the file preview
          img.src = e.target.result;
        };
        reader.readAsDataURL(this.files[0]);
      }
    });

    // Handle profile image form submission
    profileImageForm.addEventListener("submit", function (e) {
      e.preventDefault();
      uploadProfileImage();
    });
  });

  async function loadPreferences() {
    try {
      const response = await fetch("/api/preferences");
      if (!response.ok) {
        throw new Error("Failed to load preferences");
      }

      const prefs = await response.json();

      // Apply preferences to form
      // Theme
      const themeInput = document.querySelector(
        `input[name="theme"][value="${prefs.theme}"]`
      );
      if (themeInput) themeInput.checked = true;

      // Font size
      const fontSizeSelect = document.querySelector('select[name="fontSize"]');
      if (fontSizeSelect) fontSizeSelect.value = prefs.fontSize;

      // Font family
      const fontFamilySelect = document.querySelector(
        'select[name="fontFamily"]'
      );
      if (fontFamilySelect) fontFamilySelect.value = prefs.fontFamily;

      // Email notifications
      const emailNotificationsInput = document.querySelector(
        'input[name="emailNotifications"]'
      );
      if (emailNotificationsInput)
        emailNotificationsInput.checked = prefs.emailNotifications;

      // Browser notifications
      const browserNotificationsInput = document.querySelector(
        'input[name="browserNotifications"]'
      );
      if (browserNotificationsInput)
        browserNotificationsInput.checked = prefs.browserNotifications;

      // Default view
      const defaultViewSelect = document.querySelector(
        'select[name="defaultView"]'
      );
      if (defaultViewSelect) defaultViewSelect.value = prefs.defaultView;

      // Language
      const languageSelect = document.querySelector('select[name="language"]');
      if (languageSelect && prefs.language)
        languageSelect.value = prefs.language;

      // Date format
      const dateFormatSelect = document.querySelector(
        'select[name="dateFormat"]'
      );
      if (dateFormatSelect && prefs.dateFormat)
        dateFormatSelect.value = prefs.dateFormat;

      // Time format
      const timeFormatInput = document.querySelector(
        `input[name="timeFormat"][value="${prefs.timeFormat || "12h"}"]`
      );
      if (timeFormatInput) timeFormatInput.checked = true;

      // Dashboard layout
      const dashboardLayoutSelect = document.querySelector(
        'select[name="dashboardLayout"]'
      );
      if (dashboardLayoutSelect && prefs.dashboardLayout)
        dashboardLayoutSelect.value = prefs.dashboardLayout;

      // Apply font settings
      applyFontSettings(prefs.fontSize, prefs.fontFamily);

      console.log("Preferences loaded successfully:", prefs);
    } catch (error) {
      console.error("Error loading preferences:", error);
      showToast("Error loading preferences", "error");
    }
  }

  async function savePreferences() {
    try {
      const form = document.getElementById("preferences-form");
      const formData = new FormData(form);

      // Convert form data to object
      const prefs = {};
      for (const [key, value] of formData.entries()) {
        // Handle checkboxes properly
        if (key === "emailNotifications" || key === "browserNotifications") {
          prefs[key] = true;
        } else {
          prefs[key] = value;
        }
      }

      // Add unchecked checkboxes (they don't get included in FormData)
      if (!formData.has("emailNotifications")) prefs.emailNotifications = false;
      if (!formData.has("browserNotifications"))
        prefs.browserNotifications = false;

      console.log("Saving preferences:", prefs);

      const response = await fetch("/api/preferences", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRFToken": "{{ csrf_token }}",
        },
        body: JSON.stringify(prefs),
      });

      if (response.ok) {
        showToast("Preferences saved successfully", "success");
        applyFontSettings(prefs.fontSize, prefs.fontFamily);
      } else {
        throw new Error("Failed to save preferences");
      }
    } catch (error) {
      console.error("Error saving preferences:", error);
      showToast("Error saving preferences", "error");
    }
  }

  async function uploadProfileImage() {
    try {
      const formData = new FormData(
        document.getElementById("profile-image-form")
      );

      const response = await fetch("/api/profile/upload-image", {
        method: "POST",
        headers: {
          "X-CSRFToken": "{{ csrf_token }}",
        },
        body: formData,
      });

      if (response.ok) {
        const result = await response.json();
        showToast("Profile picture updated successfully", "success");

        // Hide the upload form
        document.getElementById("profile-image-form").classList.add("hidden");
      } else {
        throw new Error("Failed to upload profile image");
      }
    } catch (error) {
      console.error("Error uploading profile image:", error);
      showToast("Error uploading profile image", "error");
    }
  }

  async function resetPreferences() {
    try {
      // Call the reset API endpoint
      const response = await fetch("/api/preferences/reset", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRFToken": "{{ csrf_token }}",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to reset preferences");
      }

      const result = await response.json();

      if (result.success) {
        // Apply the default preferences returned from the server
        const defaultPrefs = result.preferences;

        // Apply defaults to form
        const themeInput = document.querySelector(
          `input[name="theme"][value="${defaultPrefs.theme}"]`
        );
        if (themeInput) themeInput.checked = true;

        const fontSizeSelect = document.querySelector(
          'select[name="fontSize"]'
        );
        if (fontSizeSelect) fontSizeSelect.value = defaultPrefs.fontSize;

        const fontFamilySelect = document.querySelector(
          'select[name="fontFamily"]'
        );
        if (fontFamilySelect) fontFamilySelect.value = defaultPrefs.fontFamily;

        const emailNotificationsInput = document.querySelector(
          'input[name="emailNotifications"]'
        );
        if (emailNotificationsInput)
          emailNotificationsInput.checked = defaultPrefs.emailNotifications;

        const browserNotificationsInput = document.querySelector(
          'input[name="browserNotifications"]'
        );
        if (browserNotificationsInput)
          browserNotificationsInput.checked = defaultPrefs.browserNotifications;

        const defaultViewSelect = document.querySelector(
          'select[name="defaultView"]'
        );
        if (defaultViewSelect)
          defaultViewSelect.value = defaultPrefs.defaultView;

        const languageSelect = document.querySelector(
          'select[name="language"]'
        );
        if (languageSelect) languageSelect.value = defaultPrefs.language;

        const dateFormatSelect = document.querySelector(
          'select[name="dateFormat"]'
        );
        if (dateFormatSelect) dateFormatSelect.value = defaultPrefs.dateFormat;

        const timeFormatInput = document.querySelector(
          `input[name="timeFormat"][value="${defaultPrefs.timeFormat}"]`
        );
        if (timeFormatInput) timeFormatInput.checked = true;

        const dashboardLayoutSelect = document.querySelector(
          'select[name="dashboardLayout"]'
        );
        if (dashboardLayoutSelect)
          dashboardLayoutSelect.value = defaultPrefs.dashboardLayout;

        // Apply font settings
        applyFontSettings(defaultPrefs.fontSize, defaultPrefs.fontFamily);

        showToast("Preferences reset to default", "info");
      } else {
        throw new Error("Failed to reset preferences");
      }
    } catch (error) {
      console.error("Error resetting preferences:", error);
      showToast("Error resetting preferences", "error");

      // Fallback to client-side reset if server reset fails
      const defaultPrefs = {
        theme: "system",
        fontSize: "medium",
        fontFamily: "inter",
        emailNotifications: true,
        browserNotifications: true,
        defaultView: "grid",
        language: "en",
        dateFormat: "MM/DD/YYYY",
        timeFormat: "12h",
        dashboardLayout: "default",
      };

      // Apply defaults to form
      document.querySelector(
        `input[name="theme"][value="${defaultPrefs.theme}"]`
      ).checked = true;
      document.querySelector('select[name="fontSize"]').value =
        defaultPrefs.fontSize;
      document.querySelector('select[name="fontFamily"]').value =
        defaultPrefs.fontFamily;
      document.querySelector('input[name="emailNotifications"]').checked =
        defaultPrefs.emailNotifications;
      document.querySelector('input[name="browserNotifications"]').checked =
        defaultPrefs.browserNotifications;
      document.querySelector('select[name="defaultView"]').value =
        defaultPrefs.defaultView;

      if (document.querySelector('select[name="language"]')) {
        document.querySelector('select[name="language"]').value =
          defaultPrefs.language;
      }

      if (document.querySelector('select[name="dateFormat"]')) {
        document.querySelector('select[name="dateFormat"]').value =
          defaultPrefs.dateFormat;
      }

      if (
        document.querySelector(
          `input[name="timeFormat"][value="${defaultPrefs.timeFormat}"]`
        )
      ) {
        document.querySelector(
          `input[name="timeFormat"][value="${defaultPrefs.timeFormat}"]`
        ).checked = true;
      }

      if (document.querySelector('select[name="dashboardLayout"]')) {
        document.querySelector('select[name="dashboardLayout"]').value =
          defaultPrefs.dashboardLayout;
      }

      applyFontSettings(defaultPrefs.fontSize, defaultPrefs.fontFamily);
    }
  }

  function applyFontSettings(size, family) {
    const root = document.documentElement;
    const sizes = {
      small: "14px",
      medium: "16px",
      large: "18px",
    };

    root.style.fontSize = sizes[size];
    root.style.fontFamily = family;
  }

  function showToast(message, type) {
    const colors = {
      success: "bg-green-500",
      error: "bg-red-500",
      info: "bg-blue-500",
    };

    const toast = document.createElement("div");
    toast.className = `fixed bottom-4 right-4 px-6 py-3 rounded-lg text-white ${colors[type]} transition-opacity duration-300`;
    toast.textContent = message;

    document.body.appendChild(toast);

    setTimeout(() => {
      toast.style.opacity = "0";
      setTimeout(() => toast.remove(), 300);
    }, 3000);
  }
</script>
{% endblock %}

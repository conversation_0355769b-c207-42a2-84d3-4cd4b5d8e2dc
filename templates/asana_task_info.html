{% extends "base.html" %}
{% block content %}

<head>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<div class="container mx-auto px-4 py-8">
    <h2 class="text-3xl font-bold text-center text-gray-800 mb-8">Asana Task Information</h2>
    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <!-- Task Overview -->
        <div class="bg-blue-50 p-6 border-b border-blue-100">
            <h3 class="text-xl font-semibold text-blue-800 mb-4">Task Overview</h3>
            <div class="mb-4">
                <p class="text-lg font-medium text-gray-700">
                    {{ task_info.get('Task Name', 'N/A') }}
                    <button onclick="editField(this)" data-field-name="Task Name"
                        data-current-value="{{ task_info.get('Task Name', '') }}"
                        class="ml-2 text-blue-500 hover:text-blue-700" title="Edit Task Name">
                        <i class="fas fa-edit"></i>
                    </button>
                </p>
            </div>
            <div class="mb-4">
                <p class="text-sm text-gray-600">
                    {{ task_info.get('Task Description', 'No description available.') }}
                    <button onclick="editField(this)" data-field-name="Task Description"
                        data-current-value="{{ task_info.get('Task Description', '') }}"
                        class="ml-2 text-blue-500 hover:text-blue-700" title="Edit Task Description">
                        <i class="fas fa-edit"></i>
                    </button>
                </p>
            </div>
            <div class="mt-4 flex items-center">
                <span class="text-sm font-medium text-gray-500 mr-2">Due Date:</span>
                <span
                    class="text-sm font-semibold {% if task_info.get('Due Date') != 'N/A' %}text-red-600{% else %}text-gray-600{% endif %}">
                    {{ task_info.get('Due Date', 'N/A') }}
                </span>
                <button onclick="editField(this)" data-field-name="Due Date"
                    data-current-value="{{ task_info.get('Due Date', '') }}"
                    class="ml-2 text-blue-500 hover:text-blue-700" title="Edit Due Date">
                    <i class="fas fa-edit"></i>
                </button>
            </div>
            <div class="mt-2 flex items-center">
                <span class="text-sm font-medium text-gray-500 mr-2">Status:</span>
                <span
                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {% if task_info.get('Completed') == 'Yes' %}bg-green-100 text-green-800{% else %}bg-yellow-100 text-yellow-800{% endif %}">
                    {{ 'Completed' if task_info.get('Completed') == 'Yes' else 'In Progress' }}
                </span>
                <button onclick="editField(this)" data-field-name="Completed"
                    data-current-value="{{ task_info.get('Completed', '') }}"
                    class="ml-2 text-blue-500 hover:text-blue-700" title="Edit Status">
                    <i class="fas fa-edit"></i>
                </button>
            </div>
            <div class="mt-2 flex items-center">
                <span class="text-sm font-medium text-gray-500 mr-2">Assignee:</span>
                <span class="text-sm text-gray-700">{{ task_info.get('Assignee', 'Not Assigned') }}</span>
                <button onclick="editField(this)" data-field-name="Assignee"
                    data-current-value="{{ task_info.get('Assignee', '') }}"
                    class="ml-2 text-blue-500 hover:text-blue-700" title="Edit Assignee">
                    <i class="fas fa-edit"></i>
                </button>
            </div>
        </div>
        <!-- Custom Fields -->
        <div class="p-6">
            <h3 class="text-xl font-semibold text-gray-800 mb-4">Custom Fields</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                {% for field, value in task_info.items() %}
                {% if field not in ['Task Name', 'Task Description', 'Due Date', 'Completed', 'Assignee', 'gid'] %}
                <div class="border rounded-md p-3 {% if field == 'Wafers IDs' %}bg-gray-50{% endif %}">
                    <p class="text-sm font-medium text-gray-500">
                        {% if field == 'XFAB Device ID' %}Project ID{% else %}{{ field }}{% endif %}
                    </p>
                    <p class="mt-1 text-sm text-gray-900">
                        {{ value if value else 'N/A' }}
                        <button onclick="editField(this)" data-field-name="{{ field }}" data-current-value="{{ value }}"
                            class="ml-2 text-blue-500 hover:text-blue-700" type="button" title="Edit Field">
                            <i class="fas fa-edit"></i>
                        </button>
                    </p>
                </div>
                {% endif %}
                {% endfor %}
            </div>
        </div>
    </div>
    <div class="text-center mt-8 space-y-4">
        <a href="{{ url_for('select_lot') }}"
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg shadow-lg transition duration-300 inline-block">
            Back to Lot Selection
        </a>

        <button id="populatePrintLabels"
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg shadow-lg transition duration-300 block w-full md:w-auto md:inline-block">
            Next
        </button>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Convert Python values to JavaScript safely
        const csrfToken = JSON.parse('{{ csrf_token|tojson }}');
        const taskGid = JSON.parse('{{ task_info.gid|tojson }}');

        document.getElementById('populatePrintLabels').addEventListener('click', function () {
            Swal.fire({
                title: 'Processing...',
                text: 'Please wait while we prepare your labels.',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Use POST with proper headers instead of GET
            fetch(`/populate_print_labels/${taskGid}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken,
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({}) // Empty object for POST body
            })
                .then(response => {
                    // First check status code
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.message || `HTTP error! status: ${response.status}`);
                        });
                    }

                    // Try to parse as JSON first
                    const contentType = response.headers.get('content-type');
                    if (contentType && contentType.includes('application/json')) {
                        return response.json();
                    } else {
                        // If not JSON, handle as HTML redirect
                        window.location.href = "{{ url_for('print_labels') }}";
                        return { success: true };
                    }
                })
                .then(data => {
                    console.log('Response data:', data);
                    if (data.success) {
                        window.location.href = "{{ url_for('print_labels') }}";
                    } else {
                        throw new Error(data.message || 'Failed to process the request');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: error.message || 'An unexpected error occurred. Please try again.'
                    });
                });
        });

        // Keep existing editField function
        function editField(button) {
            const fieldName = button.dataset.fieldName;
            const currentValue = button.dataset.currentValue;

            Swal.fire({
                title: `Edit ${fieldName}`,
                input: 'text',
                inputValue: currentValue,
                showCancelButton: true,
                inputValidator: (value) => {
                    if (!value) {
                        return 'You need to write something!';
                    }
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    updateAsanaTask(fieldName, result.value);
                }
            });
        }

        // Keep existing updateAsanaTask function
        function updateAsanaTask(fieldName, newValue) {
            fetch('/update_asana_task', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify({
                    task_gid: taskGid,
                    field_name: fieldName,
                    new_value: newValue
                }),
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Success!',
                            text: 'Task updated successfully!',
                            confirmButtonText: 'OK'
                        }).then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Failed to update task: ' + data.message,
                        });
                    }
                })
                .catch((error) => {
                    console.error('Error:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'An error occurred while updating the task.',
                    });
                });
        }
    });
</script>
{% endblock %}
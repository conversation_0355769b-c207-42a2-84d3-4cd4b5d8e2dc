{% extends "base.html" %} {% block title %}Label & Packing Slip Generation{%
endblock %} {% block breadcrumb %}
<li class="flex items-center">
  <span class="mx-2"><i class="fas fa-chevron-right text-gray-400"></i></span>
  <span class="text-gray-900 font-medium flex items-center">
    <i class="fas fa-tag mr-1"></i>Generate Labels
  </span>
</li>
{% endblock %} {% block extra_css %}
<link
  rel="stylesheet"
  href="{{ url_for('static', filename='css/select_lot.css') }}"
/>
{% endblock %} {% block content %}
<div class="container mx-auto px-4 py-8">
  <!-- Header with gradient text -->
  <div class="mb-8">
    <h1 class="text-3xl font-bold gradient-text text-center">
      Label & Packing Slip Generation
    </h1>
    <p class="mt-2 text-gray-600 dark:text-gray-400 text-center">
      Generate shipping labels and packing slips for wafer shipments
    </p>
  </div>

  <!-- Main Card with animation and shadow -->
  <div class="bg-white dark:bg-gray-800 rounded-xl shadow-xl card-hover">
    <!-- Tab Navigation with animated indicator -->
    <div class="tab-container">
      <nav class="tabs-nav">
        <button type="button" class="tab-button active" data-tab="asana">
          <i class="fas fa-tasks mr-2"></i>
          Asana Automation
        </button>
        <button type="button" class="tab-button" data-tab="manual">
          <i class="fas fa-edit mr-2"></i>
          Manual Selection
        </button>
        <button type="button" class="tab-button" data-tab="search">
          <i class="fas fa-search mr-2"></i>
          Quick Search
        </button>
        <button type="button" class="tab-button" data-tab="freestyle">
          <i class="fas fa-tag mr-2"></i>
          Substrate-wafer-label
        </button>
        <button type="button" class="tab-button" data-tab="icarium-sync">
          <i class="fas fa-brain mr-2"></i>
          Smart Sync
        </button>
        <div class="tab-indicator"></div>
      </nav>
    </div>

    <!-- Tab Contents with improved spacing and animations -->
    <div class="p-6">
      <form
        method="POST"
        action="{{ url_for('select_lot') }}"
        id="lotSelectionForm"
        class="form-container space-y-6"
      >
        <!-- CSRF Token -->
        <input type="hidden" name="csrf_token" value="{{ csrf_token }}" />

        <!-- Asana Tab -->
        <div id="asana-tab" class="tab-content active">
          <div class="form-group">
            <label for="asana_link" class="form-label">Asana Task Link</label>
            <div class="input-with-icon">
              <i class="fas fa-link"></i>
              <input
                type="text"
                name="asana_link"
                id="asana_link"
                class="form-input"
                placeholder="Paste your Asana task link here"
              />
            </div>
            <p class="help-text mt-2">
              <i class="fas fa-info-circle mr-1"></i>
              Enter the Asana task link to automatically retrieve wafer
              information.
            </p>
          </div>
        </div>

        <!-- Manual Selection Tab -->
        <div id="manual-tab" class="tab-content hidden">
          <div class="form-group">
            <label for="lot" class="form-label">Select Single Lot</label>
            <select name="lot" id="lot" class="form-select">
              <option value="">-- Select Lot --</option>
              {% for lot in form.lot.choices %}
              <option value="{{ lot[0] }}">{{ lot[1] }}</option>
              {% endfor %}
            </select>
          </div>

          <div class="form-group">
            <label for="multiple_lots" class="form-label"
              >Or Enter Multiple Lots</label
            >
            <textarea
              name="multiple_lots"
              id="multiple_lots"
              rows="3"
              class="form-textarea"
              placeholder="Enter multiple lots separated by commas"
            ></textarea>
          </div>
        </div>

        <!-- Search Tab -->
        <div id="search-tab" class="tab-content hidden">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="form-group">
              <label for="search_lot" class="form-label">Lot Number</label>
              <div class="input-with-icon">
                <i class="fas fa-hashtag text-gray-400"></i>
                <input
                  type="text"
                  id="search_lot"
                  name="search_lot"
                  class="form-input pl-10"
                  placeholder="Enter lot number"
                />
              </div>
            </div>
            <div class="form-group">
              <label for="search_scribe" class="form-label">Scribe ID</label>
              <div class="input-with-icon">
                <i class="fas fa-fingerprint text-gray-400"></i>
                <input
                  type="text"
                  id="search_scribe"
                  name="search_scribe"
                  class="form-input pl-10"
                  placeholder="Enter scribe ID"
                />
              </div>
            </div>
          </div>

          <div class="form-group">
            <label for="search_date_range" class="form-label">Date Range</label>
            <div class="date-range">
              <div class="input-with-icon">
                <i class="fas fa-calendar text-gray-400"></i>
                <input
                  type="date"
                  id="search_date_from"
                  name="search_date_from"
                  class="form-input pl-10"
                  title="Start Date"
                />
              </div>
              <div class="input-with-icon">
                <i class="fas fa-calendar-alt text-gray-400"></i>
                <input
                  type="date"
                  id="search_date_to"
                  name="search_date_to"
                  class="form-input pl-10"
                  title="End Date"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Free Style Tab -->
        <div id="freestyle-tab" class="tab-content hidden">
          <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg mb-6">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <i class="fas fa-info-circle text-blue-500 mt-1"></i>
              </div>
              <div class="ml-3">
                <h3
                  class="text-sm font-medium text-blue-800 dark:text-blue-200"
                >
                  Substrate-wafer Label Generation
                </h3>
                <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                  <p>
                    Here you can generate a label for a substrate-wafer pair
                    that is not attached to any Asana task. This is useful for
                    substrates that are not part of a larger shipment or for
                    standalone wafer shipments.
                  </p>
                  <p class="mt-1">
                    Enter the required information below to generate a
                    Substrate-wafer label.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="form-group">
              <label for="fs_item_id" class="form-label"
                >Item ID <span class="text-red-500">*</span></label
              >
              <div class="input-with-icon">
                <i class="fas fa-barcode text-gray-400"></i>
                <input
                  type="text"
                  id="fs_item_id"
                  name="fs_item_id"
                  class="form-input pl-10"
                  placeholder="Enter Item ID (e.g., LI10XA)"
                />
              </div>
            </div>
            <div class="form-group">
              <label for="fs_wafer_count" class="form-label"
                >Wafer Count <span class="text-red-500">*</span></label
              >
              <div class="input-with-icon">
                <i class="fas fa-layer-group text-gray-400"></i>
                <input
                  type="number"
                  id="fs_wafer_count"
                  name="fs_wafer_count"
                  min="1"
                  class="form-input pl-10"
                  placeholder="Enter number of wafers"
                />
              </div>
            </div>
          </div>

          <div class="form-group">
            <label for="fs_svm_lot_id" class="form-label"
              >SVM Lot ID <span class="text-red-500">*</span></label
            >
            <div class="input-with-icon">
              <i class="fas fa-fingerprint text-gray-400"></i>
              <input
                type="text"
                id="fs_svm_lot_id"
                name="fs_svm_lot_id"
                class="form-input pl-10"
                placeholder="Enter SVM Lot ID (e.g., 0030710_001)"
              />
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="form-group">
              <label for="fs_unit_price" class="form-label">Unit Price</label>
              <div class="input-with-icon">
                <i class="fas fa-dollar-sign text-gray-400"></i>
                <input
                  type="number"
                  id="fs_unit_price"
                  name="fs_unit_price"
                  step="0.01"
                  min="0"
                  class="form-input pl-10"
                  placeholder="Enter unit price"
                />
              </div>
            </div>
            <div class="form-group">
              <label for="fs_currency" class="form-label">Currency</label>
              <div class="input-with-icon">
                <i class="fas fa-coins text-gray-400"></i>
                <select
                  id="fs_currency"
                  name="fs_currency"
                  class="form-select pl-10"
                >
                  <option value="USD">$ USD (US Dollar)</option>
                  <option value="EUR">€ EUR (Euro)</option>
                  <option value="CHF">CHF (Swiss Franc)</option>
                  <option value="GBP">£ GBP (British Pound)</option>
                  <option value="JPY">¥ JPY (Japanese Yen)</option>
                  <option value="CAD">$ CAD (Canadian Dollar)</option>
                  <option value="AUD">$ AUD (Australian Dollar)</option>
                  <option value="CNY">¥ CNY (Chinese Yuan)</option>
                </select>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label for="fs_comments" class="form-label">Comments</label>
            <textarea
              id="fs_comments"
              name="fs_comments"
              rows="3"
              class="form-textarea"
              placeholder="Enter any additional information or comments"
            ></textarea>
          </div>
        </div>

        <!-- Icarium Smart Sync Tab -->
        <div id="icarium-sync-tab" class="tab-content hidden">
          <div
            class="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900 dark:to-blue-900 p-6 rounded-lg mb-6"
          >
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <i class="fas fa-brain text-purple-500 text-2xl mt-1"></i>
              </div>
              <div class="ml-4">
                <h3
                  class="text-lg font-semibold text-purple-800 dark:text-purple-200"
                >
                  🧠 Smart Icarium-Talaria Sync
                </h3>
                <div class="mt-2 text-sm text-purple-700 dark:text-purple-300">
                  <p>
                    Intelligent wafer synchronization between Icarium and
                    Talaria with AI-powered recommendations.
                  </p>
                  <p class="mt-1">
                    Get smart suggestions for batch operations, priority
                    syncing, and efficiency optimization.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- Smart Sync Dashboard Container -->
          <div id="smart-sync-dashboard" class="min-h-96">
            <!-- Dashboard will be loaded here by JavaScript -->
          </div>
        </div>

        <!-- Action Buttons with improved styling and hover effects -->
        <div class="flex flex-wrap justify-end gap-4 mt-8">
          <button type="button" id="eigerCsvBtn" class="btn btn-success">
            <i class="fas fa-file-csv mr-2"></i>
            Generate Eiger CSV
          </button>
          {% if check_permission('modify') %}
          <button type="button" id="sendEmailBtn" class="btn btn-info">
            <i class="fas fa-envelope mr-2"></i>
            Send Email
          </button>
          <button type="button" id="testEmailBtn" class="btn btn-primary">
            <i class="fas fa-vial mr-2"></i>
            Test Email
          </button>
          {% endif %}
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-arrow-right mr-2"></i>
            Continue
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
{% endblock %} {% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="{{ url_for('static', filename='js/select_lot.js') }}"></script>
{% endblock %}

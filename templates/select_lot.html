{% extends "base.html" %} {% block title %}Label & Packing Slip Generation{%
endblock %} {% block breadcrumb %}
<li class="flex items-center">
  <span class="mx-2"><i class="fas fa-chevron-right text-gray-400"></i></span>
  <span class="text-gray-900 font-medium flex items-center">
    <i class="fas fa-tag mr-1"></i>Generate Labels
  </span>
</li>
{% endblock %} {% block extra_css %}
<link
  rel="stylesheet"
  href="{{ url_for('static', filename='css/select_lot.css') }}"
/>
{% endblock %} {% block content %}
<div class="container mx-auto px-4 py-8">
  <!-- Header with gradient text -->
  <div class="mb-8">
    <h1 class="text-3xl font-bold gradient-text text-center">
      Label & Packing Slip Generation
    </h1>
    <p class="mt-2 text-gray-600 dark:text-gray-400 text-center">
      Generate shipping labels and packing slips for wafer shipments
    </p>
  </div>

  <!-- Main Card with animation and shadow -->
  <div class="bg-white dark:bg-gray-800 rounded-xl shadow-xl card-hover">
    <!-- Tab Navigation with animated indicator -->
    <div class="tab-container">
      <nav class="tabs-nav">
        <button type="button" class="tab-button active" data-tab="asana">
          <i class="fas fa-tasks mr-2"></i>
          Asana Automation
        </button>
        <button type="button" class="tab-button" data-tab="manual">
          <i class="fas fa-edit mr-2"></i>
          Manual Selection
        </button>
        <button type="button" class="tab-button" data-tab="search">
          <i class="fas fa-search mr-2"></i>
          Quick Search
        </button>
        <button type="button" class="tab-button" data-tab="freestyle">
          <i class="fas fa-tag mr-2"></i>
          Substrate-wafer-label
        </button>
        <button type="button" class="tab-button" data-tab="icarium-sync">
          <i class="fas fa-brain mr-2"></i>
          Smart Sync
        </button>
        <div class="tab-indicator"></div>
      </nav>
    </div>

    <!-- Tab Contents with improved spacing and animations -->
    <div class="p-6">
      <form
        method="POST"
        action="{{ url_for('select_lot') }}"
        id="lotSelectionForm"
        class="form-container space-y-6"
      >
        <!-- CSRF Token -->
        <input type="hidden" name="csrf_token" value="{{ csrf_token }}" />

        <!-- Asana Tab -->
        <div id="asana-tab" class="tab-content active">
          <div class="form-group">
            <label for="asana_link" class="form-label">Asana Task Link</label>
            <div class="input-with-icon">
              <i class="fas fa-link"></i>
              <input
                type="text"
                name="asana_link"
                id="asana_link"
                class="form-input"
                placeholder="Paste your Asana task link here"
              />
            </div>
            <div class="help-text mt-2">
              <div class="bg-blue-50 dark:bg-blue-900/50 rounded-lg p-3">
                <div class="flex items-start space-x-2">
                  <i class="fas fa-info-circle text-blue-600 mt-0.5"></i>
                  <div class="text-sm">
                    <p class="font-medium text-blue-800 dark:text-blue-200 mb-1">How to use Asana Integration:</p>
                    <ul class="text-blue-700 dark:text-blue-300 space-y-1 text-xs">
                      <li>• Copy the full URL from your Asana task</li>
                      <li>• Example: https://app.asana.com/0/project/task</li>
                      <li>• The system will automatically extract wafer details</li>
                      <li>• Works with tasks containing wafer information in custom fields</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Manual Selection Tab -->
        <div id="manual-tab" class="tab-content hidden">
          <!-- Tutorial Section for Manual Selection -->
          <div class="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/50 dark:to-blue-900/50 rounded-xl p-4 mb-6">
            <div class="flex items-start space-x-3">
              <div class="p-2 bg-green-100 dark:bg-green-800 rounded-lg">
                <i class="fas fa-hand-point-right text-green-600 dark:text-green-300"></i>
              </div>
              <div>
                <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Manual Lot Selection Guide</h3>
                <p class="text-sm text-gray-700 dark:text-gray-300 mb-3">
                  Use this method when you know the specific lot numbers you want to process, or when working with lots not linked to Asana tasks.
                </p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div class="bg-white dark:bg-gray-800 rounded-lg p-3">
                    <h4 class="font-medium text-gray-900 dark:text-white mb-1">Single Lot</h4>
                    <p class="text-xs text-gray-600 dark:text-gray-400">Choose one lot from the dropdown list of available inventory</p>
                  </div>
                  <div class="bg-white dark:bg-gray-800 rounded-lg p-3">
                    <h4 class="font-medium text-gray-900 dark:text-white mb-1">Multiple Lots</h4>
                    <p class="text-xs text-gray-600 dark:text-gray-400">Enter comma-separated lot numbers for batch processing</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label for="lot" class="form-label">Select Single Lot</label>
            <select name="lot" id="lot" class="form-select">
              <option value="">-- Select Lot --</option>
              {% for lot in form.lot.choices %}
              <option value="{{ lot[0] }}">{{ lot[1] }}</option>
              {% endfor %}
            </select>
            <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
              <i class="fas fa-lightbulb mr-1"></i>
              <strong>Tip:</strong> Lots are sorted by most recent first. Use the search field above to quickly find specific lots.
            </div>
          </div>

          <div class="form-group">
            <label for="multiple_lots" class="form-label">Or Enter Multiple Lots</label>
            <textarea
              name="multiple_lots"
              id="multiple_lots"
              rows="3"
              class="form-textarea"
              placeholder="Example: LOT001, LOT002, LOT003"
            ></textarea>
            <div class="mt-2">
              <div class="bg-yellow-50 dark:bg-yellow-900/50 rounded-lg p-3">
                <div class="flex items-start space-x-2">
                  <i class="fas fa-exclamation-triangle text-yellow-600 mt-0.5"></i>
                  <div class="text-sm">
                    <p class="font-medium text-yellow-800 dark:text-yellow-200 mb-1">Multiple Lots Format:</p>
                    <ul class="text-yellow-700 dark:text-yellow-300 space-y-1 text-xs">
                      <li>• Separate lot numbers with commas</li>
                      <li>• Spaces around commas are automatically removed</li>
                      <li>• Example: LOT001, LOT002, LOT003</li>
                      <li>• You can also use line breaks instead of commas</li>
                      <li>• Maximum 50 lots per batch for performance</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Search Tab -->
        <div id="search-tab" class="tab-content hidden">
          <!-- Search Tutorial Section -->
          <div class="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/50 dark:to-pink-900/50 rounded-xl p-4 mb-6">
            <div class="flex items-start space-x-3">
              <div class="p-2 bg-purple-100 dark:bg-purple-800 rounded-lg">
                <i class="fas fa-search text-purple-600 dark:text-purple-300"></i>
              </div>
              <div>
                <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Smart Search Guide</h3>
                <p class="text-sm text-gray-700 dark:text-gray-300 mb-3">
                  Use advanced search criteria to find specific wafers based on lot numbers, scribe IDs, or production dates.
                </p>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <div class="bg-white dark:bg-gray-800 rounded-lg p-3">
                    <h4 class="font-medium text-gray-900 dark:text-white mb-1 text-xs">Lot Number Search</h4>
                    <p class="text-xs text-gray-600 dark:text-gray-400">Find by exact lot number or partial matches</p>
                  </div>
                  <div class="bg-white dark:bg-gray-800 rounded-lg p-3">
                    <h4 class="font-medium text-gray-900 dark:text-white mb-1 text-xs">Scribe ID Search</h4>
                    <p class="text-xs text-gray-600 dark:text-gray-400">Search by wafer scribe identifiers</p>
                  </div>
                  <div class="bg-white dark:bg-gray-800 rounded-lg p-3">
                    <h4 class="font-medium text-gray-900 dark:text-white mb-1 text-xs">Date Range Filter</h4>
                    <p class="text-xs text-gray-600 dark:text-gray-400">Filter by production or creation dates</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="form-group">
              <label for="search_lot" class="form-label">Lot Number</label>
              <div class="input-with-icon">
                <i class="fas fa-hashtag text-gray-400"></i>
                <input
                  type="text"
                  id="search_lot"
                  name="search_lot"
                  class="form-input pl-10"
                  placeholder="e.g., LOT001 or partial: LOT"
                />
              </div>
              <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                <i class="fas fa-info-circle mr-1"></i>
                Supports partial matching: "LOT" will find "LOT001", "LOT002", etc.
              </div>
            </div>
            <div class="form-group">
              <label for="search_scribe" class="form-label">Scribe ID</label>
              <div class="input-with-icon">
                <i class="fas fa-fingerprint text-gray-400"></i>
                <input
                  type="text"
                  id="search_scribe"
                  name="search_scribe"
                  class="form-input pl-10"
                  placeholder="e.g., ABC123 or partial: ABC"
                />
              </div>
              <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                <i class="fas fa-info-circle mr-1"></i>
                Case-insensitive search. Use partial IDs for broader results.
              </div>
            </div>
          </div>

          <div class="form-group">
            <label for="search_date_range" class="form-label">Date Range Filter</label>
            <div class="date-range">
              <div class="input-with-icon">
                <i class="fas fa-calendar text-gray-400"></i>
                <input
                  type="date"
                  id="search_date_from"
                  name="search_date_from"
                  class="form-input pl-10"
                  title="Start Date"
                />
              </div>
              <div class="input-with-icon">
                <i class="fas fa-calendar-alt text-gray-400"></i>
                <input
                  type="date"
                  id="search_date_to"
                  name="search_date_to"
                  class="form-input pl-10"
                  title="End Date"
                />
              </div>
            </div>
            <div class="mt-2">
              <div class="bg-indigo-50 dark:bg-indigo-900/50 rounded-lg p-3">
                <div class="flex items-start space-x-2">
                  <i class="fas fa-calendar-check text-indigo-600 mt-0.5"></i>
                  <div class="text-sm">
                    <p class="font-medium text-indigo-800 dark:text-indigo-200 mb-1">Date Search Tips:</p>
                    <ul class="text-indigo-700 dark:text-indigo-300 space-y-1 text-xs">
                      <li>• Leave both dates empty to search all dates</li>
                      <li>• Use only "Start Date" to find items from that date onwards</li>
                      <li>• Use only "End Date" to find items up to that date</li>
                      <li>• Combine with lot/scribe filters for precise results</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Search Results Preview -->
          <div class="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div class="flex items-center space-x-2 mb-2">
              <i class="fas fa-chart-bar text-gray-600"></i>
              <h4 class="font-medium text-gray-900 dark:text-white">Search Results Preview</h4>
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              Results will appear here after you click "Continue" with search criteria.
              The system will show matching lots with their details for your selection.
            </p>
          </div>
        </div>

        <!-- Free Style Tab -->
        <div id="freestyle-tab" class="tab-content hidden">
          <!-- Enhanced Freestyle Tutorial Section -->
          <div class="bg-gradient-to-r from-teal-50 to-cyan-50 dark:from-teal-900/50 dark:to-cyan-900/50 rounded-xl p-4 mb-6">
            <div class="flex items-start space-x-3">
              <div class="p-2 bg-teal-100 dark:bg-teal-800 rounded-lg">
                <i class="fas fa-tag text-teal-600 dark:text-teal-300"></i>
              </div>
              <div>
                <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Standalone Label Generation</h3>
                <p class="text-sm text-gray-700 dark:text-gray-300 mb-3">
                  Create labels for substrate-wafer pairs that aren't connected to Asana tasks. Perfect for one-off shipments, samples, or direct customer orders.
                </p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div class="bg-white dark:bg-gray-800 rounded-lg p-3">
                    <div class="flex items-center mb-2">
                      <i class="fas fa-check-circle text-green-600 mr-2"></i>
                      <h4 class="font-medium text-gray-900 dark:text-white text-sm">When to Use</h4>
                    </div>
                    <ul class="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                      <li>• Standalone substrate shipments</li>
                      <li>• Sample wafers for customers</li>
                      <li>• Emergency or rush orders</li>
                      <li>• Non-Asana managed inventory</li>
                    </ul>
                  </div>
                  <div class="bg-white dark:bg-gray-800 rounded-lg p-3">
                    <div class="flex items-center mb-2">
                      <i class="fas fa-clipboard-list text-blue-600 mr-2"></i>
                      <h4 class="font-medium text-gray-900 dark:text-white text-sm">Required Information</h4>
                    </div>
                    <ul class="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                      <li>• Item ID (Product identifier)</li>
                      <li>• Wafer Count (Quantity)</li>
                      <li>• SVM Lot ID (Batch identifier)</li>
                      <li>• Optional: Price & Currency</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="form-group">
              <label for="fs_item_id" class="form-label"
                >Item ID <span class="text-red-500">*</span></label
              >
              <div class="input-with-icon">
                <i class="fas fa-barcode text-gray-400"></i>
                <input
                  type="text"
                  id="fs_item_id"
                  name="fs_item_id"
                  class="form-input pl-10"
                  placeholder="Enter Item ID (e.g., LI10XA)"
                />
              </div>
              <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                <i class="fas fa-info-circle mr-1"></i>
                <strong>Format:</strong> Usually 6-8 characters. Examples: LI10XA, SI200B, GE150C
              </div>
            </div>
            <div class="form-group">
              <label for="fs_wafer_count" class="form-label"
                >Wafer Count <span class="text-red-500">*</span></label
              >
              <div class="input-with-icon">
                <i class="fas fa-layer-group text-gray-400"></i>
                <input
                  type="number"
                  id="fs_wafer_count"
                  name="fs_wafer_count"
                  min="1"
                  max="1000"
                  class="form-input pl-10"
                  placeholder="Enter number of wafers"
                />
              </div>
              <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                <i class="fas fa-info-circle mr-1"></i>
                <strong>Range:</strong> 1-1000 wafers. Enter the total quantity in this shipment.
              </div>
            </div>
          </div>

          <div class="form-group">
            <label for="fs_svm_lot_id" class="form-label"
              >SVM Lot ID <span class="text-red-500">*</span></label
            >
            <div class="input-with-icon">
              <i class="fas fa-fingerprint text-gray-400"></i>
              <input
                type="text"
                id="fs_svm_lot_id"
                name="fs_svm_lot_id"
                class="form-input pl-10"
                placeholder="Enter SVM Lot ID (e.g., 0030710_001)"
              />
            </div>
            <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
              <i class="fas fa-info-circle mr-1"></i>
              <strong>Format:</strong> Usually numeric with underscore. Examples: 0030710_001, 0031205_002
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="form-group">
              <label for="fs_unit_price" class="form-label">Unit Price</label>
              <div class="input-with-icon">
                <i class="fas fa-dollar-sign text-gray-400"></i>
                <input
                  type="number"
                  id="fs_unit_price"
                  name="fs_unit_price"
                  step="0.01"
                  min="0"
                  class="form-input pl-10"
                  placeholder="Enter unit price"
                />
              </div>
              <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                <i class="fas fa-dollar-sign mr-1"></i>
                <strong>Optional:</strong> Price per wafer. Use decimal format (e.g., 12.50)
              </div>
            </div>
            <div class="form-group">
              <label for="fs_currency" class="form-label">Currency</label>
              <div class="input-with-icon">
                <i class="fas fa-coins text-gray-400"></i>
                <select
                  id="fs_currency"
                  name="fs_currency"
                  class="form-select pl-10"
                >
                  <option value="USD">$ USD (US Dollar)</option>
                  <option value="EUR">€ EUR (Euro)</option>
                  <option value="CHF">CHF (Swiss Franc)</option>
                  <option value="GBP">£ GBP (British Pound)</option>
                  <option value="JPY">¥ JPY (Japanese Yen)</option>
                  <option value="CAD">$ CAD (Canadian Dollar)</option>
                  <option value="AUD">$ AUD (Australian Dollar)</option>
                  <option value="CNY">¥ CNY (Chinese Yuan)</option>
                </select>
              </div>
              <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                <i class="fas fa-globe mr-1"></i>
                <strong>Default:</strong> USD. Select appropriate currency for pricing.
              </div>
            </div>
          </div>

          <div class="form-group">
            <label for="fs_comments" class="form-label">Comments</label>
            <textarea
              id="fs_comments"
              name="fs_comments"
              rows="3"
              class="form-textarea"
              placeholder="Enter any additional information or special instructions..."
            ></textarea>
            <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
              <i class="fas fa-comment mr-1"></i>
              <strong>Optional:</strong> Special handling instructions, customer notes, or internal comments for this shipment.
            </div>
          </div>

          <!-- Freestyle Summary Section -->
          <div class="mt-6 p-4 bg-emerald-50 dark:bg-emerald-900/50 rounded-lg border border-emerald-200 dark:border-emerald-800">
            <div class="flex items-start space-x-3">
              <i class="fas fa-clipboard-check text-emerald-600 mt-1"></i>
              <div>
                <h4 class="font-medium text-emerald-800 dark:text-emerald-200 mb-2">Pre-Generation Checklist</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <p class="font-medium text-emerald-800 dark:text-emerald-200 mb-1">Before Clicking Continue:</p>
                    <ul class="text-emerald-700 dark:text-emerald-300 space-y-1 text-xs">
                      <li>✓ Verify Item ID format and accuracy</li>
                      <li>✓ Confirm wafer count matches physical inventory</li>
                      <li>✓ Double-check SVM Lot ID spelling</li>
                      <li>✓ Review pricing information if applicable</li>
                    </ul>
                  </div>
                  <div>
                    <p class="font-medium text-emerald-800 dark:text-emerald-200 mb-1">What Happens Next:</p>
                    <ul class="text-emerald-700 dark:text-emerald-300 space-y-1 text-xs">
                      <li>→ System validates all required fields</li>
                      <li>→ Label template is generated</li>
                      <li>→ You can preview before printing</li>
                      <li>→ Optional email notifications sent</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Icarium Smart Sync Tab -->
        <div id="icarium-sync-tab" class="tab-content hidden">
          <!-- Header Section with Modern Gradient -->
          <div class="smart-sync-header bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-800 dark:via-blue-900 dark:to-indigo-900 rounded-2xl p-8 mb-8 border border-slate-200 dark:border-slate-700 shadow-lg">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <div class="p-4 bg-white dark:bg-slate-800 rounded-2xl shadow-md">
                  <svg class="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                  </svg>
                </div>
                <div>
                  <h3 class="text-2xl font-bold text-slate-800 dark:text-slate-100">
                    Smart Sync Intelligence
                  </h3>
                  <p class="text-slate-600 dark:text-slate-300 mt-1">
                    AI-powered synchronization between Icarium and Talaria systems
                  </p>
                </div>
              </div>
              
              <!-- Status Indicator -->
              <div class="hidden sm:flex items-center space-x-3">
                <div class="flex items-center space-x-2">
                  <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                  <span class="text-sm font-medium text-slate-600 dark:text-slate-300">Active</span>
                </div>
              </div>
            </div>
            
            <!-- Feature Highlights -->
            <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div class="flex items-center space-x-3 p-3 bg-white/60 dark:bg-slate-800/60 rounded-xl">
                <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                  <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                  </svg>
                </div>
                <div>
                  <p class="font-semibold text-slate-800 dark:text-slate-200">Smart Recommendations</p>
                  <p class="text-xs text-slate-600 dark:text-slate-400">AI-powered suggestions</p>
                </div>
              </div>
              
              <div class="flex items-center space-x-3 p-3 bg-white/60 dark:bg-slate-800/60 rounded-xl">
                <div class="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                  <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.31 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.31 4 8 4s8-1.79 8-4M4 7c0-2.21 3.31-4 8-4s8 1.79 8 4"></path>
                  </svg>
                </div>
                <div>
                  <p class="font-semibold text-slate-800 dark:text-slate-200">Batch Operations</p>
                  <p class="text-xs text-slate-600 dark:text-slate-400">Efficient bulk processing</p>
                </div>
              </div>
              
              <div class="flex items-center space-x-3 p-3 bg-white/60 dark:bg-slate-800/60 rounded-xl">
                <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                  <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v6a2 2 0 01-2 2H9z"></path>
                  </svg>
                </div>
                <div>
                  <p class="font-semibold text-slate-800 dark:text-slate-200">Real-time Analytics</p>
                  <p class="text-xs text-slate-600 dark:text-slate-400">Live performance metrics</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Smart Sync Dashboard Container -->
          <div id="smart-sync-dashboard" class="min-h-96 bg-white dark:bg-slate-800 rounded-2xl border border-slate-200 dark:border-slate-700 shadow-sm">
            <!-- Dashboard will be loaded here by JavaScript -->
          </div>
        </div>

        <!-- Action Buttons with improved styling and hover effects -->
        <div class="flex flex-wrap justify-end gap-4 mt-8">
          <button type="button" id="eigerCsvBtn" class="btn btn-success">
            <i class="fas fa-file-csv mr-2"></i>
            Generate Eiger CSV
          </button>
          {% if check_permission('modify') %}
          <button type="button" id="sendEmailBtn" class="btn btn-info">
            <i class="fas fa-envelope mr-2"></i>
            Send Email
          </button>
          <button type="button" id="testEmailBtn" class="btn btn-primary">
            <i class="fas fa-vial mr-2"></i>
            Test Email
          </button>
          {% endif %}
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-arrow-right mr-2"></i>
            Continue
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
{% endblock %} {% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="{{ url_for('static', filename='js/smart-sync-dashboard.js') }}"></script>
<script src="{{ url_for('static', filename='js/select_lot.js') }}"></script>
{% endblock %}

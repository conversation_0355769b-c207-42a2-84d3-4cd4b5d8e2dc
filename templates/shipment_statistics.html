<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shipment Statistics</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        html {
            position: relative;
            min-height: 100%;
        }

        body {
            background-color: #f3f4f6;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            margin-bottom: 60px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            padding-bottom: 80px;
        }

        .dashboard-header {
            text-align: center;
            margin-bottom: 2rem;
            animation: fadeIn 1s ease-in;
        }

        .dashboard-header h1 {
            color: #1f2937;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .dashboard-header p {
            color: #6b7280;
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            animation: slideInUp 0.5s ease-out;
            cursor: pointer;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 12px rgba(0, 0, 0, 0.1);
        }

        .chart-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 30px;
            animation: slideInUp 0.5s ease-out;

        }

        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            height: 400px;
            animation: fadeIn 1s ease-out;
            cursor: pointer;
        }

        .stat-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: #374151;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            text-align: center;
            color: #1F2937;
            transition: color 0.3s ease;
        }

        .stat-change {
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            margin-top: 8px;
            padding: 4px 8px;
            border-radius: 12px;
            width: fit-content;
            margin: 8px auto 0;
        }

        .stat-change.positive {
            color: #10B981;
            background-color: rgba(16, 185, 129, 0.1);
        }

        .stat-change.negative {
            color: #EF4444;
            background-color: rgba(239, 68, 68, 0.1);
        }

        .stat-change i {
            font-size: 12px;
        }

        .footer {
            position: absolute;
            bottom: 0;
            width: 100%;
            height: 60px;
            background-color: #1f2937;
            color: #ffffff;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        }

        .footer p {
            margin: 0;
            padding: 0;
            font-size: 0.9rem;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #3B82F6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .refresh-button {
            position: fixed;
            bottom: 80px;
            right: 20px;
            background: #3B82F6;
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .refresh-button:hover {
            transform: rotate(180deg);
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }

            to {
                opacity: 1;
            }
        }

        @keyframes slideInUp {
            from {
                transform: translateY(20px);
                opacity: 0;
            }

            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        @media (max-width: 768px) {

            .stats-grid,
            .chart-grid {
                grid-template-columns: 1fr;
            }

            body {
                margin-bottom: 80px;
            }

            .footer {
                height: 80px;
                padding: 10px;
            }

            .refresh-button {
                bottom: 100px;
            }
        }

        .error-message {
            background-color: #FEE2E2;
            border-left: 4px solid #EF4444;
            color: #B91C1C;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.375rem;
            display: none;
        }

        .actions-bar {
            background: white;
            padding: 1rem;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .date-filters {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-select,
        .date-input {
            padding: 0.5rem;
            border: 1px solid #e5e7eb;
            border-radius: 0.375rem;
            background-color: white;
        }

        .export-options {
            display: flex;
            gap: 0.5rem;
        }

        .export-btn,
        .share-btn,
        .apply-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 0.375rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
        }

        .export-btn {
            background-color: #3B82F6;
            color: white;
        }

        .share-btn {
            background-color: #10B981;
            color: white;
        }

        .apply-btn {
            background-color: #6B7280;
            color: white;
        }

        .export-btn:hover,
        .share-btn:hover,
        .apply-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* Modal styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 2rem;
            border-radius: 0.5rem;
            max-width: 500px;
            position: relative;
        }

        .close-modal {
            position: absolute;
            right: 1rem;
            top: 1rem;
            cursor: pointer;
        }

        #customDateRange {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .share-modal-content {
            padding: 1rem;
        }

        .share-options {
            margin-top: 1rem;
            text-align: left;
        }

        .share-option {
            display: flex;
            align-items: center;
            margin: 0.5rem 0;
            cursor: pointer;
        }

        .share-option input[type="checkbox"] {
            margin-right: 0.5rem;
        }

        .swal2-popup {
            font-size: 0.9rem;
        }

        .swal2-input {
            margin: 1rem 0;
        }
    </style>
</head>

<body>
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
    </div>

    <div class="container">
        <div class="dashboard-header">
            <h1>Shipment Analytics Dashboard</h1>
            <p>Real-time overview of shipment operations</p>
        </div>

        <div class="error-message" id="errorMessage">
            An error occurred while loading the data. Please try again.
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-title">
                    <i class="fas fa-paper-plane"></i>
                    Weekly Sent
                </div>
                <div class="stat-value" id="weeklySent">0</div>
                <div class="stat-change" id="weeklySentChange">
                    <i class="fas fa-arrow-up"></i>
                    <span>0%</span>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-title">
                    <i class="fas fa-box"></i>
                    Monthly Delivered
                </div>
                <div class="stat-value" id="monthlyDelivered">0</div>
                <div class="stat-change" id="monthlyDeliveredChange">
                    <i class="fas fa-arrow-up"></i>
                    <span>0%</span>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-title">
                    <i class="fas fa-chart-line"></i>
                    Yearly Summary
                </div>
                <div class="stat-value">
                    <div id="yearlyDelivered">Delivered: 0</div>
                    <div id="yearlyIncoming">Incoming: 0</div>
                </div>
            </div>
        </div>

        <div class="chart-grid">
            <div class="chart-container">
                <canvas id="sectionChart"></canvas>
            </div>
            <div class="chart-container">
                <canvas id="monthlyChart"></canvas>
            </div>
        </div>
        <div class="actions-bar">
            <div class="date-filters">
                <select id="dateRange" class="filter-select" title="bar"">
                    <option value=" 7">Last 7 days</option>
                    <option value="30" selected>Last 30 days</option>
                    <option value="90">Last 90 days</option>
                    <option value="180">Last 180 days</option>
                    <option value="365">Last year</option>
                    <option value="custom">Custom range</option>
                </select>
                <div id="customDateRange">
                    <input type="date" id="startDate" class="date-input" title="Start Date">
                    <input type="date" id="endDate" class="date-input" title="End Date">
                    <button onclick="applyCustomRange()" class="apply-btn">Apply</button>
                </div>
            </div>
            <div class="export-options">
                <button onclick="exportToPDF()" class="export-btn">
                    <i class="fas fa-file-pdf"></i> Export PDF
                </button>
                <button onclick="exportToExcel()" class="export-btn">
                    <i class="fas fa-file-excel"></i> Export Excel
                </button>
                <button onclick="shareReport()" class="share-btn">
                    <i class="fas fa-share-alt"></i> Share
                </button>
            </div>
        </div>


    </div>

    <footer class="footer">
        <div>
            <p>&copy; 2024 Ligentec. All rights reserved.</p>
        </div>
    </footer>

    <button class="refresh-button" onclick="refreshData()" title="Refresh Data">
        <i class="fas fa-sync-alt"></i>
    </button>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.17.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <script>
        // Utility Functions
        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }

        function showError(message) {
            const errorElement = document.getElementById('errorMessage');
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }

        // Animation Function
        function animateValue(element, start, end, duration) {
            const range = end - start;
            const increment = range / (duration / 16);
            let current = start;
            const timer = setInterval(() => {
                current += increment;
                if ((increment > 0 && current >= end) || (increment < 0 && current <= end)) {
                    clearInterval(timer);
                    current = end;
                }
                element.textContent = Math.round(current);
            }, 16);
        }

        // Date range handling
        document.getElementById('dateRange').addEventListener('change', function (e) {
            const customRange = document.getElementById('customDateRange');
            if (e.target.value === 'custom') {
                customRange.style.display = 'flex';
            } else {
                customRange.style.display = 'none';
                updateCharts(e.target.value);
            }
        });

        function applyCustomRange() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            if (!startDate || !endDate) {
                alert('Please select both start and end dates');
                return;
            }

            const loadingOverlay = document.getElementById('loadingOverlay');
            loadingOverlay.style.display = 'flex';

            window.location.href = `/shipment_statistics?range=custom&start=${startDate}&end=${endDate}`;
        }
        document.getElementById('dateRange').addEventListener('change', function () {
            const customDateRange = document.getElementById('customDateRange');
            if (this.value === 'custom') {
                customDateRange.style.display = 'flex';
            } else {
                customDateRange.style.display = 'none';
                const loadingOverlay = document.getElementById('loadingOverlay');
                loadingOverlay.style.display = 'flex';
                window.location.href = `/shipment_statistics?range=${this.value}`;
            }
        });

        function updateCharts(range, startDate = null, endDate = null) {
            showLoading();
            let url = `/shipment_statistics?range=${range}`;
            if (range === 'custom') {
                url += `&start=${startDate}&end=${endDate}`;
            }

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    initializeCharts(data.section_data, data.monthly_data);
                    updateStats(data.stats);
                })
                .catch(error => {
                    showError('Failed to update data');
                    console.error('Update error:', error);
                })
                .finally(() => {
                    hideLoading();
                });
        }

        function updateStats(stats) {
            animateValue(document.getElementById('weeklySent'), 0, stats.weekly_sent, 1000);
            animateValue(document.getElementById('monthlyDelivered'), 0, stats.monthly_delivered, 1000);

            updatePercentageChange('weeklySentChange', stats.weekly_sent_change);
            updatePercentageChange('monthlyDeliveredChange', stats.monthly_delivered_change);

            document.getElementById('yearlyDelivered').textContent = `Delivered: ${stats.yearly.delivered}`;
            document.getElementById('yearlyIncoming').textContent = `Incoming: ${stats.yearly.incoming}`;
        }

        function updatePercentageChange(elementId, changeValue) {
            const element = document.getElementById(elementId);
            element.innerHTML = `
                <i class="fas fa-arrow-${changeValue >= 0 ? 'up' : 'down'}"></i>
                <span>${Math.abs(changeValue)}%</span>
            `;
            element.className = `stat-change ${changeValue >= 0 ? 'positive' : 'negative'}`;
        }

        // Export Functions
        function exportToPDF() {
            fetch('/export_statistics/pdf')
                .then(response => {
                    if (!response.ok) throw new Error('Export failed');
                    return response.blob();
                })
                .then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `shipment_statistics_${new Date().toISOString().split('T')[0]}.pdf`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                })
                .catch(error => {
                    console.error('Export error:', error);
                    alert('Failed to export PDF. Please try again.');
                });
        }
        function exportToExcel() {
            fetch('/export_statistics/excel')
                .then(response => {
                    if (!response.ok) throw new Error('Export failed');
                    return response.blob();
                })
                .then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `shipment_statistics_${new Date().toISOString().split('T')[0]}.xlsx`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                })
                .catch(error => {
                    console.error('Export error:', error);
                    alert('Failed to export Excel. Please try again.');
                });
        }

        function shareReport() {
            const Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true
            });

            Swal.fire({
                title: 'Share Report',
                html: `
            <div class="share-modal-content">
                <input type="email" id="shareEmail" class="swal2-input" placeholder="Enter email address">
                <div class="share-options">
                    <label class="share-option">
                        <input type="checkbox" id="includeCharts" checked>
                        Include charts
                    </label>
                    <label class="share-option">
                        <input type="checkbox" id="includeSummary" checked>
                        Include summary
                    </label>
                </div>
            </div>
        `,
                showCancelButton: true,
                confirmButtonText: 'Share',
                showLoaderOnConfirm: true,
                preConfirm: () => {
                    const email = document.getElementById('shareEmail').value;
                    const includeCharts = document.getElementById('includeCharts').checked;
                    const includeSummary = document.getElementById('includeSummary').checked;

                    if (!email) {
                        Swal.showValidationMessage('Please enter an email address');
                        return false;
                    }

                    if (!email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
                        Swal.showValidationMessage('Please enter a valid email address');
                        return false;
                    }

                    return fetch('/share_statistics', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
                        },
                        body: JSON.stringify({
                            email: email,
                            includeCharts: includeCharts,
                            includeSummary: includeSummary
                        })
                    })
                        .then(response => {
                            if (!response.ok) throw new Error('Share failed');
                            return response.json();
                        })
                        .catch(error => {
                            console.error('Share error:', error);
                            throw new Error('Failed to share report. Please try again.');
                        });
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    Toast.fire({
                        icon: 'success',
                        title: 'Report shared successfully'
                    });
                }
            }).catch(error => {
                Toast.fire({
                    icon: 'error',
                    title: error.message || 'Failed to share report'
                });
            });
        }

        // Chart Initialization
        let sectionChart = null;
        let monthlyChart = null;

        function initializeCharts(sectionData, monthlyData) {
            // Destroy existing charts if they exist
            if (sectionChart) {
                sectionChart.destroy();
            }
            if (monthlyChart) {
                monthlyChart.destroy();
            }

            // Section Chart
            const sectionCtx = document.getElementById('sectionChart').getContext('2d');
            sectionChart = new Chart(sectionCtx, {
                type: 'bar',
                data: sectionData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: {
                        duration: 2000,
                        easing: 'easeInOutQuart'
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        title: {
                            display: true,
                            text: 'Section Statistics',
                            font: {
                                size: 16,
                                weight: 'bold'
                            }
                        },
                        tooltip: {
                            backgroundColor: '#374151',
                            titleFont: { size: 14 },
                            bodyFont: { size: 12 },
                            padding: 12
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0,
                                font: { size: 12 }
                            },
                            grid: {
                                color: 'rgba(0,0,0,0.05)'
                            }
                        },
                        x: {
                            ticks: {
                                maxRotation: 45,
                                minRotation: 45,
                                font: { size: 12 }
                            },
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });

            // Monthly Chart
            const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
            monthlyChart = new Chart(monthlyCtx, {
                type: 'line',
                data: monthlyData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: {
                        duration: 2000,
                        easing: 'easeInOutQuart'
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Monthly Deliveries',
                            font: {
                                size: 16,
                                weight: 'bold'
                            }
                        },
                        tooltip: {
                            backgroundColor: '#374151',
                            titleFont: { size: 14 },
                            bodyFont: { size: 12 },
                            padding: 12
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0,
                                font: { size: 12 }
                            },
                            grid: {
                                color: 'rgba(0,0,0,0.05)'
                            }
                        },
                        x: {
                            ticks: {
                                maxRotation: 45,
                                minRotation: 45,
                                font: { size: 12 }
                            },
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }
        // Data Refresh
        async function refreshData() {
            showLoading();
            try {
                const response = await fetch(window.location.href);
                if (!response.ok) throw new Error('Failed to fetch data');

                // Destroy existing charts before reload
                if (sectionChart) {
                    sectionChart.destroy();
                }
                if (monthlyChart) {
                    monthlyChart.destroy();
                }

                location.reload();
            } catch (error) {
                showError('Failed to refresh data. Please try again.');
                console.error('Refresh error:', error);
            } finally {
                hideLoading();
            }
        }

        // Initialize everything when the page loads
        document.addEventListener('DOMContentLoaded', function () {
            showLoading();
            try {
                const sectionData = {{ section_data| tojson | safe
            }};
        const monthlyData = {{ monthly_data| tojson | safe }};
        const stats = {{ stats| tojson | safe }};

        updateStats(stats);
        initializeCharts(sectionData, monthlyData);

        // Set current date range
        const today = new Date();
        const startDate = document.getElementById('startDate');
        const endDate = document.getElementById('endDate');
        if (startDate && endDate) {
            endDate.valueAsDate = today;
            startDate.valueAsDate = new Date(today.setDate(today.getDate() - 30));
        }

        // Initialize date range handler
        const dateRange = document.getElementById('dateRange');
        const customRange = document.getElementById('customDateRange');
        if (dateRange && customRange) {
            dateRange.addEventListener('change', function (e) {
                if (e.target.value === 'custom') {
                    customRange.style.display = 'flex';
                } else {
                    customRange.style.display = 'none';
                    updateCharts(e.target.value);
                }
            });
        }
    } catch (error) {
            showError('An error occurred while initializing the dashboard.');
            console.error('Initialization error:', error);
        } finally {
            hideLoading();
        }
});

        // Update the updateCharts function to handle errors better
        function updateCharts(range, startDate = null, endDate = null) {
            showLoading();
            let url = `/shipment_statistics?range=${range}`;
            if (range === 'custom') {
                url += `&start=${startDate}&end=${endDate}`;
            }

            fetch(url, {
                headers: {
                    'Accept': 'application/json'
                }
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.error) {
                        throw new Error(data.error);
                    }
                    initializeCharts(data.section_data, data.monthly_data);
                    updateStats(data.stats);
                })
                .catch(error => {
                    showError('Failed to update data: ' + error.message);
                    console.error('Update error:', error);
                })
                .finally(() => {
                    hideLoading();
                });
        }
    </script>
</body>

</html>
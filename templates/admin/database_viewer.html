{% extends "base.html" %}

{% block title %}Database Viewer{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-2xl shadow-xl p-6">
        <!-- Header -->
        <div class="mb-6">
            <h3 class="text-2xl font-bold text-gray-900">Database Viewer</h3>
            <p class="text-sm text-gray-600">View database tables and their contents</p>
        </div>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
        {% for category, message in messages %}
        <div
            class="mb-4 p-4 rounded-lg {% if category == 'error' %}bg-red-50 text-red-800{% else %}bg-green-50 text-green-800{% endif %} text-sm">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    {% if category == 'error' %}
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    {% else %}
                    <i class="fas fa-check-circle mr-2"></i>
                    {% endif %}
                </div>
                <div>{{ message }}</div>
            </div>
        </div>
        {% endfor %}
        {% endif %}
        {% endwith %}

        <!-- Table Selection -->
        <div class="mb-6">
            <form method="GET" action="{{ url_for('users.database_viewer') }}">
                <div class="flex gap-4 items-end">
                    <div class="flex-grow">
                        <label for="table_name" class="block text-sm font-medium text-gray-700 mb-1">Select Table</label>
                        <select id="table_name" name="table_name" 
                            class="w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            {% for table in available_tables %}
                            <option value="{{ table }}" {% if table == current_table %}selected{% endif %}>{{ table }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div>
                        <button type="submit"
                            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            View Table
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Table View -->
        {% if rows %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        {% for column in columns %}
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ column }}
                        </th>
                        {% endfor %}
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for row in rows %}
                    <tr class="hover:bg-gray-50">
                        {% for column in columns %}
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ row[column] if row[column] is not none else "NULL" }}
                        </td>
                        {% endfor %}
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if total_pages > 1 %}
        <div class="flex justify-between items-center mt-4">
            <div>
                Showing page {{ current_page }} of {{ total_pages }} ({{ total_rows }} total rows)
            </div>
            <div class="flex gap-2">
                {% if current_page > 1 %}
                <a href="{{ url_for('users.database_viewer', table_name=current_table, page=current_page-1) }}"
                    class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">
                    Previous
                </a>
                {% endif %}
                
                {% if current_page < total_pages %}
                <a href="{{ url_for('users.database_viewer', table_name=current_table, page=current_page+1) }}"
                    class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">
                    Next
                </a>
                {% endif %}
            </div>
        </div>
        {% endif %}
        {% else %}
        <div class="text-center py-8 text-gray-500">
            {% if current_table %}
            <p>No data found in the selected table.</p>
            {% else %}
            <p>Please select a table to view.</p>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
{% extends "base.html" %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto bg-white rounded-2xl shadow-xl p-6">
        <!-- Header -->
        <div class="mb-6">
            <h3 class="text-2xl font-bold text-gray-900">Add New User</h3>
            <p class="text-sm text-gray-600">Create a new user account</p>
        </div>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
        {% for category, message in messages %}
        <div
            class="mb-4 p-4 rounded-lg {% if category == 'error' %}bg-red-50 text-red-800{% else %}bg-green-50 text-green-800{% endif %} text-sm">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    {% if category == 'error' %}
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    {% else %}
                    <i class="fas fa-check-circle mr-2"></i>
                    {% endif %}
                </div>
                <div>{{ message }}</div>
            </div>
        </div>
        {% endfor %}
        {% endif %}
        {% endwith %}

        <!-- Add User Form -->
        <form method="POST" action="{{ url_for('users.add_user') }}">
            <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
            <div class="space-y-6">
                <!-- Email -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-envelope text-gray-400"></i>
                        </div>
                        <input id="email" name="email" type="email" required
                            class="pl-10 block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            placeholder="Enter email address">
                    </div>
                </div>

                <!-- Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-user text-gray-400"></i>
                        </div>
                        <input id="name" name="name" type="text" required
                            class="pl-10 block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            placeholder="Enter full name">
                    </div>
                </div>

                <!-- Role -->
                <div>
                    <label for="role" class="block text-sm font-medium text-gray-700 mb-1">Role</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-user-tag text-gray-400"></i>
                        </div>
                        <select id="role" name="role" onchange="togglePasswordType()"
                            class="pl-10 block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            <option value="colleague">Colleague</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                </div>

                <!-- Password Type -->
                <div>
                    <label for="password_type" class="block text-sm font-medium text-gray-700 mb-1">Password
                        Type</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-key text-gray-400"></i>
                        </div>
                        <select id="password_type" name="password_type" onchange="toggleCustomPassword()"
                            class="pl-10 block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            <option value="default">Use Default Password</option>
                            <option value="custom">Set Custom Password</option>
                        </select>
                    </div>
                    <p class="mt-1 text-xs text-gray-500" id="password_help_text">
                        User will use the standard colleague password
                    </p>
                </div>

                <!-- Custom Password (Hidden by default) -->
                <div id="custom_password_div" style="display: none;">
                    <label for="custom_password" class="block text-sm font-medium text-gray-700 mb-1">Custom
                        Password</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-lock text-gray-400"></i>
                        </div>
                        <input id="custom_password" name="custom_password" type="password"
                            class="pl-10 block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            placeholder="Enter custom password">
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-3">
                    <a href="{{ url_for('users.manage_users') }}"
                        class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Cancel
                    </a>
                    <button type="submit"
                        class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Add User
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
    function togglePasswordType() {
        const roleSelect = document.getElementById('role');
        const passwordTypeSelect = document.getElementById('password_type');
        const helpText = document.getElementById('password_help_text');

        // Reset password type when role changes
        passwordTypeSelect.value = 'default';

        // Update help text based on role
        if (roleSelect.value === 'admin') {
            helpText.textContent = 'User will use the admin master password';
        } else {
            helpText.textContent = 'User will use the standard colleague password';
        }

        toggleCustomPassword();
    }

    function toggleCustomPassword() {
        const passwordType = document.getElementById('password_type').value;
        const customPasswordDiv = document.getElementById('custom_password_div');

        if (passwordType === 'custom') {
            customPasswordDiv.style.display = 'block';
            document.getElementById('custom_password').required = true;
        } else {
            customPasswordDiv.style.display = 'none';
            document.getElementById('custom_password').required = false;
        }
    }
</script>
{% endblock %}
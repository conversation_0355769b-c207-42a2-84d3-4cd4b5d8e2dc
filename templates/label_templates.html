{% extends "base.html" %}

{% block title %}Label Templates - Ligentec Labeller{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header Section -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Document Templates</h1>
        <p class="mt-2 text-sm text-gray-600 dark:text-gray-300">Manage your shipping labels and packing slips</p>
    </div>

    <!-- Labels Section -->
    <div class="mb-12">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Shipping Labels</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            {% for template_id, template in templates.labels.items() %}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ template.name }}</h3>
                    <button onclick="previewTemplate('{{ template_id }}', 'label')"
                        class="text-blue-600 hover:text-blue-700 text-sm">
                        <i class="fas fa-eye mr-1"></i> Preview
                    </button>
                </div>

                <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">{{ template.description }}</p>

                <div class="space-y-2">
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">Required Fields:</h4>
                    <ul class="text-sm text-gray-600 dark:text-gray-400">
                        {% for field in template.fields %}
                        <li class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            {{ field.replace('_', ' ').title() }}
                        </li>
                        {% endfor %}
                    </ul>
                </div>

                <div class="mt-6 flex space-x-3">
                    <button onclick="editTemplate('{{ template_id }}', 'label')"
                        class="flex-1 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                        <i class="fas fa-edit mr-1"></i> Edit
                    </button>
                    <button onclick="downloadTemplate('{{ template_id }}', 'label')"
                        class="flex-1 px-4 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200">
                        <i class="fas fa-download mr-1"></i> Download
                    </button>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Packing Slips Section -->
    <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Packing Slips</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            {% for template_id, template in templates.packing_slips.items() %}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ template.name }}</h3>
                    <button onclick="previewTemplate('{{ template_id }}', 'packing_slip')"
                        class="text-blue-600 hover:text-blue-700 text-sm">
                        <i class="fas fa-eye mr-1"></i> Preview
                    </button>
                </div>

                <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">{{ template.description }}</p>

                <div class="space-y-2">
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">Required Fields:</h4>
                    <ul class="text-sm text-gray-600 dark:text-gray-400">
                        {% for field in template.fields %}
                        <li class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            {{ field.replace('_', ' ').title() }}
                        </li>
                        {% endfor %}
                    </ul>
                </div>

                <div class="mt-6 flex space-x-3">
                    <button onclick="editTemplate('{{ template_id }}', 'packing_slip')"
                        class="flex-1 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                        <i class="fas fa-edit mr-1"></i> Edit
                    </button>
                    <button onclick="downloadTemplate('{{ template_id }}', 'packing_slip')"
                        class="flex-1 px-4 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200">
                        <i class="fas fa-download mr-1"></i> Download
                    </button>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Preview Modal -->
    <div id="previewModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
        <div class="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full mx-4">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium" id="previewTitle">Template Preview</h3>
                    <button onclick="closePreview()" class="text-gray-500 hover:text-gray-700" title="Close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div id="previewContent" class="border rounded p-4">
                    <!-- Preview content will be inserted here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for template management -->
<script>
    function previewTemplate(templateId, type) {
        const modal = document.getElementById('previewModal');
        const content = document.getElementById('previewContent');
        modal.classList.remove('hidden');
        content.innerHTML = '<div class="flex justify-center"><i class="fas fa-spinner fa-spin text-2xl"></i></div>';

        fetch(`/api/templates/${type}/${templateId}/preview`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    content.innerHTML = data.html;
                } else {
                    content.innerHTML = `<div class="text-red-500">Error loading preview: ${data.error}</div>`;
                }
            })
            .catch(error => {
                content.innerHTML = `<div class="text-red-500">Error: ${error.message}</div>`;
            });
    }

    function closePreview() {
        document.getElementById('previewModal').classList.add('hidden');
    }

    function editTemplate(templateId, type) {
        window.location.href = `/label_templates/edit/${type}/${templateId}`;
    }

    function downloadTemplate(templateId, type) {
        window.location.href = `/api/templates/${type}/${templateId}/download`;
    }

    // Close modal when clicking outside
    document.getElementById('previewModal').addEventListener('click', function (e) {
        if (e.target === this) {
            closePreview();
        }
    });

    // Prevent modal content clicks from closing the modal
    document.querySelector('#previewModal > div').addEventListener('click', function (e) {
        e.stopPropagation();
    });
</script>
{% endblock %}
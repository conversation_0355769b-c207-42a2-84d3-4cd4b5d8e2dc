{% extends "base.html" %}

{% block title %}Error{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div>
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Error
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                {{ error }}
            </p>
        </div>
        <div class="mt-8 text-center">
            <a href="{{ url_for('home') }}"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Return to Home
            </a>
        </div>
    </div>
</div>
{% endblock %}
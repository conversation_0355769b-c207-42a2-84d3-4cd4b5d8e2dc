{% extends "base.html" %}

{% block title %}Shipment Management{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/intel-parsing.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/shipment_management.css') }}">
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-800 dark:text-white">Shipment Management</h1>
        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">Manage and track your wafer shipments</p>
    </div>

    <!-- Top Search Bar -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Asana Link</label>
                <input type="text" id="asana-link" class="w-full px-3 py-2 border border-gray-300 rounded-md"
                    placeholder="Paste Asana link">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">LGT Lot IDs</label>
                <input type="text" id="lot-ids" class="w-full px-3 py-2 border border-gray-300 rounded-md"
                    placeholder="Comma separated">
            </div>
            <div class="flex space-x-2">
                <button class="p-2 rounded-full bg-gray-200" title="Search"><i class="fas fa-search"></i></button>
                <button class="p-2 rounded-full bg-gray-200" title="eraser"><i class="fas fa-eraser"></i></button>
            </div>
            <div class="flex justify-end space-x-2">
                <button class="bg-blue-600 text-white px-4 py-2 rounded-md">Upload</button>
                <!--<button class="bg-red-600 text-white px-4 py-2 rounded-md">Delete</button>-->
                <button class="bg-green-600 text-white px-4 py-2 rounded-md">Create</button>
                <button class="bg-yellow-600 text-white px-4 py-2 rounded-md">Modify</button>
            </div>
        </div>
    </div>
    <!-- Main Form -->
    <form id="shipment-form" class="space-y-6">
        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">

        <!-- Shipment Task Info section -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold mb-4">Shipment Task Info</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                    <label for="type" class="block text-sm font-medium mb-2">Type</label>
                    <select id="type" name="type" class="w-full border rounded-md p-2" required>
                        <option value="standard">Standard</option>
                        <option value="critical">Critical</option>
                    </select>
                </div>
                <div>
                    <label for="title" class="block text-sm font-medium mb-2">Title</label>
                    <input type="text" id="title" name="title" class="w-full border rounded-md p-2" required>
                </div>
                <div>
                    <label for="shipment_date" class="block text-sm font-medium mb-2">Shipment Date</label>
                    <input type="date" id="shipment_date" name="shipment_date" class="w-full border rounded-md p-2"
                        required>
                </div>
                <div>
                    <label for="priority" class="block text-sm font-medium mb-2">Priority</label>
                    <select id="priority" name="priority" class="w-full border rounded-md p-2" required>
                        <option value="low">Low</option>
                        <option value="medium">Medium</option>
                        <option value="high">High</option>
                    </select>
                </div>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                <div class="flex items-center">
                    <input type="checkbox" id="need_reviewing" name="need_reviewing" class="mr-2">
                    <label for="need_reviewing">Need Reviewing?</label>
                </div>
                <div class="flex items-center">
                    <input type="checkbox" id="label_free" name="label_free" class="mr-2">
                    <label for="label_free">Label Free?</label>
                </div>
                <div>
                    <select id="wafer_choice" name="wafer_choice" class="w-full border rounded-md p-2"
                        title="Wafer Choice" required>
                        <option value="">Choose wafer type</option>
                        <option value="random">Random</option>
                        <option value="not_random">Not Random</option>
                    </select>
                </div>
                <div class="flex items-center">
                    <input type="checkbox" id="keep_cassette_closed" name="keep_cassette_closed" class="mr-2">
                    <label for="keep_cassette_closed">Keep Cassette Closed</label>
                </div>
            </div>
        </div>

        <!-- For Erfurt -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold mb-4">For Erfurt</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label for="xfab_po" class="block text-sm font-medium mb-2">XFAB PO</label>
                    <input type="text" id="xfab_po" name="xfab_po" class="w-full border rounded-md p-2">
                </div>
                <div>
                    <label for="xfab_device_id" class="block text-sm font-medium mb-2">XFAB Device ID</label>
                    <input type="text" id="xfab_device_id" name="xfab_device_id" class="w-full border rounded-md p-2">
                </div>
                <div>
                    <label for="project_id" class="block text-sm font-medium mb-2">Project ID</label>
                    <input type="text" id="project_id" name="project_id" class="w-full border rounded-md p-2">
                </div>
            </div>
        </div>

        <!-- For Eiger -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold mb-4">For Eiger</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                    <label for="eiger_number" class="block text-sm font-medium mb-2">Eiger Number</label>
                    <input type="text" id="eiger_number" name="eiger_number" class="w-full border rounded-md p-2">
                </div>
                <div>
                    <label for="tapeout" class="block text-sm font-medium mb-2">Tapeout</label>
                    <input type="text" id="tapeout" name="tapeout" class="w-full border rounded-md p-2">
                </div>
                <div>
                    <label for="vendor_lot" class="block text-sm font-medium mb-2">Vendor Lot</label>
                    <input type="text" id="vendor_lot" name="vendor_lot" class="w-full border rounded-md p-2">
                </div>
                <div>
                    <label for="customer_lot" class="block text-sm font-medium mb-2">Customer Lot</label>
                    <input type="text" id="customer_lot" name="customer_lot" class="w-full border rounded-md p-2">
                </div>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                <div>
                    <label for="rib" class="block text-sm font-medium mb-2">Rib</label>
                    <select id="rib" name="rib" class="w-full border rounded-md p-2">
                        <option value="">Rib?</option>
                        <option value="true">True</option>
                        <option value="false">False</option>
                    </select>
                </div>
                <div>
                    <label for="tox_target_sin" class="block text-sm font-medium mb-2">TOX Target SiN</label>
                    <input type="text" id="tox_target_sin" name="tox_target_sin" class="w-full border rounded-md p-2">
                </div>
                <div>
                    <label for="heater" class="block text-sm font-medium mb-2">Heater</label>
                    <select id="heater" name="heater" class="w-full border rounded-md p-2">
                        <option value="">Heater?</option>
                        <option value="true">True</option>
                        <option value="false">False</option>
                    </select>
                </div>
                <div>
                    <label for="undercut" class="block text-sm font-medium mb-2">Undercut</label>
                    <select id="undercut" name="undercut" class="w-full border rounded-md p-2">
                        <option value="">Undercut?</option>
                        <option value="true">True</option>
                        <option value="false">False</option>
                    </select>
                </div>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                <div>
                    <label for="sin_tube_position" class="block text-sm font-medium mb-2">SiN Tube Position</label>
                    <input type="number" id="sin_tube_position" name="sin_tube_position"
                        class="w-full border rounded-md p-2" placeholder="1-6">
                </div>
                <div>
                    <label for="mask" class="block text-sm font-medium mb-2">Mask</label>
                    <select id="mask" name="mask" class="w-full border rounded-md p-2">
                        <option value="">Mask</option>
                        <option value="e-beam">E-beam</option>
                        <option value="laser">Laser</option>
                    </select>
                </div>
            </div>
        </div>


        <!-- Carrier Info -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold mb-4">Carrier Info</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label for="location_id" class="block text-sm font-medium mb-2">Location</label>
                    <select id="location_id" name="location_id" class="w-full border rounded-md p-2" required>
                        <!-- Options will be populated dynamically -->
                    </select>
                </div>
                <div>
                    <label for="contact_person" class="block text-sm font-medium mb-2">Contact Person</label>
                    <input type="text" id="contact_person" name="contact_person" class="w-full border rounded-md p-2"
                        required>
                </div>
                <div>
                    <label for="email" class="block text-sm font-medium mb-2">Email</label>
                    <input type="email" id="email" name="email" class="w-full border rounded-md p-2" required>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <div>
                    <label for="address" class="block text-sm font-medium mb-2">Address</label>
                    <textarea id="address" name="address" class="w-full border rounded-md p-2" required></textarea>
                </div>
                <div>
                    <label for="destination_label" class="block text-sm font-medium mb-2">Destination Label</label>
                    <input type="text" id="destination_label" name="destination_label"
                        class="w-full border rounded-md p-2" required>
                </div>
                <div>
                    <label for="telephone" class="block text-sm font-medium mb-2">Telephone</label>
                    <input type="tel" id="telephone" name="telephone" class="w-full border rounded-md p-2" required>
                </div>
            </div>

            <div class="mt-4">
                <label for="parcel_size" class="block text-sm font-medium mb-2">Parcel Size</label>
                <select id="parcel_size" name="parcel_size" class="w-full border rounded-md p-2" required>
                    <option value="">Parcel Size/Weight</option>
                    <option value="40x40x35">40x40x35cm (1 Cassette) 5kg</option>
                    <option value="40x20x35">40x20x35cm (1 Cassette) 8kg</option>
                </select>
            </div>
        </div>

        <!-- Label Info Section -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold mb-4">Label Info</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label for="label_title" class="block text-sm font-medium mb-2">Label Title</label>
                    <input type="text" id="label_title" name="label_title" class="w-full border rounded-md p-2"
                        required>
                </div>
                <div>
                    <label for="number_of_wafers" class="block text-sm font-medium mb-2">Number of Wafers</label>
                    <input type="number" id="number_of_wafers" name="number_of_wafers"
                        class="w-full border rounded-md p-2" min="1" required>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="wafer_ids" class="block text-sm font-medium mb-2">
                        Wafer IDs
                        <span class="text-xs text-gray-500">(Comma separated or one per line)</span>
                    </label>
                    <div class="relative">
                        <textarea id="wafer_ids" name="wafer_ids" rows="6"
                            class="w-full border rounded-md p-2 pr-8 font-mono text-sm resize-vertical"
                            required></textarea>
                        <button type="button" id="clear-wafers-btn"
                            class="absolute top-2 right-2 text-gray-400 hover:text-gray-600" title="Clear Selection">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div>
                    <label for="comments" class="block text-sm font-medium mb-2">
                        Comments
                        <span class="text-xs text-gray-500">(Additional information)</span>
                    </label>
                    <textarea id="comments" name="comments" rows="6"
                        class="w-full border rounded-md p-2 resize-vertical"></textarea>
                </div>
            </div>
        </div>
    </form>
    <!-- Selection message container -->
    <div id="selection-message" class="mt-4 p-4 bg-blue-50 text-blue-700 rounded-md hidden">
        <!-- Selection message will be dynamically inserted here -->
    </div>
    <!-- Wafer Inventory Section -->
    <div class="mt-8 bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
        <!-- Enhanced Header Area -->
        <div class="border-b border-gray-200 dark:border-gray-700">
            <div class="px-6 py-4 flex items-center justify-between">
                <div class="flex items-center gap-4">
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-gray-100">Wafer Inventory</h2>
                    <div class="flex items-center gap-2">
                        <!-- Wafer count badge -->
                        <span class="status-badge bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200">
                            <i class="fas fa-microchip"></i>
                            <span id="total-wafer-count">Available Wafers</span>
                        </span>
                        <!-- Selected count badge -->
                        <span class="status-badge bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200">
                            <i class="fas fa-check-circle"></i>
                            <span id="selected-wafer-count">0 Selected</span>
                        </span>
                    </div>
                </div>

                <div class="flex items-center gap-3">
                    <!-- Toggle Button -->
                    <button id="toggle-table"
                        class="flex items-center px-4 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200">
                        <span class="mr-2 text-sm font-medium text-gray-700 dark:text-gray-200" id="toggle-text">Show
                            Table</span>
                        <i class="fas fa-chevron-down transition-transform duration-200" id="toggle-icon"></i>
                    </button>

                    <!-- Action Buttons -->
                    <div class="flex items-center gap-2">
                        <button id="refresh-table"
                            class="p-2 text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/50 transition-colors"
                            title="Refresh Table">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button id="clear-selection"
                            class="p-2 text-gray-500 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/50 transition-colors"
                            title="Clear Selection">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Filter Row -->
            <div class="px-6 py-3 bg-gray-50 dark:bg-gray-700/50 border-t border-gray-200 dark:border-gray-700"
                id="filter-row">
                <div class="flex items-center gap-4">
                    <div class="relative flex-1">
                        <input type="text" id="table-search"
                            class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                            placeholder="Search wafers...">
                        <div class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>
                    <button
                        class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:text-blue-600 dark:hover:text-blue-400">
                        <i class="fas fa-filter mr-2"></i>
                        Filters
                    </button>
                </div>
            </div>
        </div>

        <!-- Table Container -->
        <div id="table-wrapper" class="transition-all duration-300">
            <div class="overflow-x-auto custom-scrollbar">
                <table class="min-w-full divide-y divide-gray-200">
                    <colgroup>
                        <col class="w-16"> <!-- Checkbox -->
                        <col class="w-48"> <!-- Wafer ID -->
                        <col class="w-48"> <!-- LGT Lot ID -->
                        <col class="w-48"> <!-- XFAB Lot ID -->
                        <col class="w-48"> <!-- Mask Set ID -->
                        <col class="w-48"> <!-- Module Name -->
                        <col class="w-48"> <!-- Cassette ID -->
                        <col class="w-32"> <!-- Slot ID -->
                        <col class="w-48"> <!-- Location -->
                        <col class="w-48"> <!-- Arrived At -->
                        <col class="w-48"> <!-- Sent At -->
                        <col class="w-32"> <!-- History -->
                    </colgroup>

                    <!-- Table Header -->
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th class="px-6 py-3">
                                    <input type="checkbox" id="select-all"
                                        class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                        title="Select All">
                                </th>
                                <th class="px-6 py-3">
                                    <div class="flex items-center justify-between">
                                        <span
                                            class="text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Wafer
                                            ID</span>
                                        <button class="sort-button text-gray-400 hover:text-gray-600"
                                            onclick="SortingAndFilters.handleSort('wafer_id')" title="Sort by Wafer ID">
                                            <i class="fas fa-sort"></i>
                                        </button>
                                    </div>
                                </th>
                                <th class="px-6 py-3">
                                    <div class="flex items-center justify-between">
                                        <span
                                            class="text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">LGT
                                            Lot
                                            ID</span>
                                        <button class="sort-button text-gray-400 hover:text-gray-600"
                                            onclick="SortingAndFilters.handleSort('lot_id')" title="Sort by LGT Lot ID">
                                            <i class="fas fa-sort"></i>
                                        </button>
                                    </div>
                                </th>
                                <th class="px-6 py-3">
                                    <div class="flex items-center justify-between">
                                        <span
                                            class="text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">XFAB
                                            Lot
                                            ID</span>
                                        <button class="sort-button text-gray-400 hover:text-gray-600"
                                            onclick="SortingAndFilters.handleSort('xfab_id')"
                                            title="Sort by XFAB Lot ID">
                                            <i class="fas fa-sort"></i>
                                        </button>
                                    </div>
                                </th>
                                <th class="px-6 py-3">
                                    <div class="flex items-center justify-between">
                                        <span
                                            class="text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Mask
                                            Set
                                            ID</span>
                                        <button class="sort-button text-gray-400 hover:text-gray-600"
                                            onclick="SortingAndFilters.handleSort('mask_set_id')"
                                            title="Sort by Mask Set ID">
                                            <i class="fas fa-sort"></i>
                                        </button>
                                    </div>
                                </th>
                                <th class="px-6 py-3">
                                    <div class="flex items-center justify-between">
                                        <span
                                            class="text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Module
                                            Name</span>
                                        <button class="sort-button text-gray-400 hover:text-gray-600"
                                            onclick="SortingAndFilters.handleSort('module_name')"
                                            title="Sort by Module Name">
                                            <i class="fas fa-sort"></i>
                                        </button>
                                    </div>
                                </th>
                                <th class="px-6 py-3">
                                    <div class="flex items-center justify-between">
                                        <span
                                            class="text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Cassette
                                            ID</span>
                                        <button class="sort-button text-gray-400 hover:text-gray-600"
                                            onclick="SortingAndFilters.handleSort('cassette_id')"
                                            title="Sort by Cassette ID">
                                            <i class="fas fa-sort"></i>
                                        </button>
                                    </div>
                                </th>
                                <th class="px-6 py-3">
                                    <div class="flex items-center justify-between">
                                        <span
                                            class="text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Slot
                                            ID</span>
                                        <button class="sort-button text-gray-400 hover:text-gray-600"
                                            onclick="SortingAndFilters.handleSort('slot_id')" title="Sort by Slot ID">
                                            <i class="fas fa-sort"></i>
                                        </button>
                                    </div>
                                </th>
                                <th class="px-6 py-3">
                                    <div class="flex items-center justify-between">
                                        <span
                                            class="text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Location</span>
                                        <button class="sort-button text-gray-400 hover:text-gray-600"
                                            onclick="SortingAndFilters.handleSort('location_id')"
                                            title="Sort by Location">
                                            <i class="fas fa-sort"></i>
                                        </button>
                                    </div>
                                </th>
                                <th class="px-6 py-3">
                                    <div class="flex items-center justify-between">
                                        <span
                                            class="text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Arrived
                                            At</span>
                                        <button class="sort-button text-gray-400 hover:text-gray-600"
                                            onclick="SortingAndFilters.handleSort('arrived_at')"
                                            title="Sort by Arrived At">
                                            <i class="fas fa-sort"></i>
                                        </button>
                                    </div>
                                </th>
                                <th class="px-6 py-3">
                                    <div class="flex items-center justify-between">
                                        <span
                                            class="text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Sent
                                            At</span>
                                        <button class="sort-button text-gray-400 hover:text-gray-600"
                                            onclick="SortingAndFilters.handleSort('sent_at')" title="Sort by Sent At">
                                            <i class="fas fa-sort"></i>
                                        </button>
                                    </div>
                                </th>
                                <th class="px-6 py-3">
                                    <span
                                        class="text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">History</span>
                                </th>
                            </tr>
                        </thead>
                    </thead>

                    <!-- Table Body -->
                    <tbody id="inventory-table-body"
                        class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        <!-- Dynamic content will be inserted here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Pagination -->
        <div
            class="mt-4 flex items-center justify-between px-4 py-3 bg-white dark:bg-gray-800 border-t border-gray-200 sm:px-6">
            <div class="flex items-center">
                <select id="per-page-select"
                    class="rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                    title="Items per page">
                    <option value="10">10 per page</option>
                    <option value="25">25 per page</option>
                    <option value="50">50 per page</option>
                    <option value="100">100 per page</option>
                </select>
                <span class="ml-3 text-sm text-gray-700 dark:text-gray-300" id="pagination-info">
                    Showing <span class="font-medium" id="showing-start">1</span>
                    to <span class="font-medium" id="showing-end">10</span>
                    of <span class="font-medium" id="total-items">20</span> results
                </span>
            </div>
            <div class="flex items-center space-x-2">
                <button id="prev-page"
                    class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                    Previous
                </button>
                <div id="page-numbers" class="flex items-center space-x-1">
                    <!-- Page numbers will be inserted here -->
                </div>
                <button id="next-page"
                    class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                    Next
                </button>
            </div>
        </div>
    </div>
    {% endblock %}
    {% block extra_js %}
    <script src="{{ url_for('static', filename='js/shipment_management.js') }}"></script>

    {% endblock %}
{% extends "base.html" %} {% block title %}Manual Label Generator - Talaria
Dashboard{% endblock %} {% block head %} {{ super() }}
<meta name="csrf-token" content="{{ csrf_token }}" />
{% endblock %} {% block extra_css %}
<style>
  .form-section {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
  }

  .section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid #e5e7eb;
    display: flex;
    align-items: center;
  }

  .section-title i {
    margin-right: 8px;
    color: #3b82f6;
  }

  .form-group {
    margin-bottom: 20px;
  }

  .form-label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 6px;
    font-size: 0.875rem;
  }

  .form-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.875rem;
    transition: all 0.2s;
    background-color: #ffffff;
  }

  .form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .form-select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.875rem;
    background-color: #ffffff;
    transition: all 0.2s;
  }

  .form-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  .btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    border: none;
    padding: 14px 28px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
  }

  .btn-primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }

  .erfurt-fields {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
  }

  .erfurt-fields.show {
    display: block;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .grid-2 {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
  }

  .grid-3 {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
  }

  @media (max-width: 768px) {
    .grid-2,
    .grid-3 {
      grid-template-columns: 1fr;
    }
  }

  .help-text {
    font-size: 0.75rem;
    color: #6b7280;
    margin-top: 4px;
  }

  .loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
  }

  .loading-overlay.hidden {
    display: none !important;
  }

  .loading-content {
    background: white;
    padding: 32px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  }

  .spinner {
    border: 4px solid #f3f4f6;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  /* Origin selection styling */
  .origin-option.active {
    border-color: #3b82f6 !important;
    background-color: #eff6ff;
  }

  .origin-option:hover {
    background-color: #f8fafc;
  }

  .origin-option.active:hover {
    background-color: #dbeafe;
  }
</style>
{% endblock %} {% block content %}
<div class="max-w-6xl mx-auto">
  <!-- Header -->
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
      <i class="fas fa-tags mr-3 text-blue-600"></i>Manual Label Generator
    </h1>
    <p class="text-gray-600 dark:text-gray-400">
      Create and print labels and packing slips for your shipments without
      database dependencies.
      <span class="text-red-600 font-semibold"></span>
    </p>
  </div>

  <!-- Error Display -->
  <div
    id="errorAlert"
    class="hidden mb-6 bg-red-50 border border-red-200 rounded-lg p-4"
  >
    <div class="flex items-center">
      <i class="fas fa-exclamation-circle text-red-500 mr-2"></i>
      <span id="errorMessage" class="text-red-700"></span>
    </div>
  </div>

  <!-- Success Display -->
  <div
    id="successAlert"
    class="hidden mb-6 bg-green-50 border border-green-200 rounded-lg p-4"
  >
    <div class="flex items-center">
      <i class="fas fa-check-circle text-green-500 mr-2"></i>
      <span id="successMessage" class="text-green-700"></span>
    </div>
  </div>

  <!-- Label Generation Form -->
  <form id="labelForm">
    <!-- Label Information Section -->
    <div class="form-section">
      <h3 class="section-title"><i class="fas fa-tag"></i>Label Information</h3>

      <div class="grid-2">
        <div class="form-group">
          <label for="label_type" class="form-label">Label Type *</label>
          <select
            id="label_type"
            name="label_type"
            class="form-select"
            required
          >
            <option value="">Select Label Type</option>
            <option value="1">1 - Label free (Without Ligentec logo)</option>
            <option value="2">2 - Erfurt label</option>
            <option value="3">3 - Standard label</option>
          </select>
        </div>

        <div class="form-group">
          <label for="shipment_date" class="form-label">Shipment Date *</label>
          <input
            type="date"
            id="shipment_date"
            name="shipment_date"
            class="form-input"
            required
          />
        </div>
      </div>

      <!-- Packing Slip Origin Selection -->
      <div class="form-group">
        <label class="form-label">
          <i class="fas fa-map-marker-alt mr-2 text-blue-600"></i>Packing Slip
          Address
        </label>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3">
          <div class="relative">
            <input
              type="radio"
              id="origin_france"
              name="shipment_origin"
              value="france"
              class="sr-only"
              checked
            />
            <label
              for="origin_france"
              class="flex items-center p-3 border-2 border-blue-200 rounded-lg cursor-pointer hover:border-blue-400 transition-colors duration-200 origin-option active"
            >
              <div class="flex items-center">
                <span class="text-2xl mr-3">🇫🇷</span>
                <div>
                  <div class="font-medium text-gray-900 dark:text-white">
                    France
                  </div>
                  <div class="text-sm text-gray-600 dark:text-gray-400">
                    LIGENTEC France SAS
                  </div>
                  <div class="text-xs text-gray-500 dark:text-gray-500">
                    Corbeil-Essonnes
                  </div>
                </div>
              </div>
            </label>
          </div>
          <div class="relative">
            <input
              type="radio"
              id="origin_switzerland"
              name="shipment_origin"
              value="switzerland"
              class="sr-only"
            />
            <label
              for="origin_switzerland"
              class="flex items-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover:border-blue-400 transition-colors duration-200 origin-option"
            >
              <div class="flex items-center">
                <span class="text-2xl mr-3">🇨🇭</span>
                <div>
                  <div class="font-medium text-gray-900 dark:text-white">
                    Switzerland
                  </div>
                  <div class="text-sm text-gray-600 dark:text-gray-400">
                    LIGENTEC SA
                  </div>
                  <div class="text-xs text-gray-500 dark:text-gray-500">
                    EPFL Innovation Park, Ecublens
                  </div>
                </div>
              </div>
            </label>
          </div>
        </div>
        <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
          <i class="fas fa-info-circle mr-1"></i>
          Select the company address to appear on the packing slip
        </p>
      </div>

      <div class="form-group">
        <label for="project_name" class="form-label">Label Title *</label>
        <textarea
          id="project_name"
          name="project_name"
          rows="2"
          class="form-input"
          placeholder="Enter project name or label title"
          required
        ></textarea>
        <div class="help-text">
          This will be the main title displayed on the label
        </div>
      </div>

      <div class="grid-2">
        <div class="form-group">
          <label for="wafer_number" class="form-label"
            >Number of Wafers *</label
          >
          <input
            type="number"
            id="wafer_number"
            name="wafer_number"
            min="0"
            class="form-input"
            required
          />
        </div>

        <div class="form-group">
          <label for="wafer_list" class="form-label"
            >SlotID,WaferID List *</label
          >
          <textarea
            id="wafer_list"
            name="wafer_list"
            rows="3"
            class="form-input"
            placeholder="Format: 1,xxxxxxx;2,xxxxxxx;"
            required
          ></textarea>
          <div class="help-text">Use semicolon (;) to separate entries</div>
        </div>
      </div>
    </div>

    <!-- Erfurt Specific Information (Hidden by default) -->
    <div id="erfurt-fields" class="form-section erfurt-fields">
      <h3 class="section-title">
        <i class="fas fa-industry"></i>Erfurt Specific Information
      </h3>

      <div class="grid-3">
        <div class="form-group">
          <label for="po" class="form-label">Purchase Order *</label>
          <input
            type="text"
            id="po"
            name="po"
            class="form-input"
            placeholder="PO number"
          />
        </div>

        <div class="form-group">
          <label for="device_id" class="form-label">Project ID *</label>
          <input
            type="text"
            id="device_id"
            name="device_id"
            class="form-input"
            placeholder="Project ID"
          />
        </div>

        <div class="form-group">
          <label for="xfab_lot_id" class="form-label">X-FAB LOT ID *</label>
          <input
            type="text"
            id="xfab_lot_id"
            name="xfab_lot_id"
            class="form-input"
            placeholder="LOT ID"
          />
        </div>
      </div>
    </div>

    <!-- Additional Information Section -->
    <div class="form-section">
      <h3 class="section-title">
        <i class="fas fa-file-alt"></i>Additional Information
      </h3>

      <div class="form-group">
        <label for="comments" class="form-label"
          >Comments for Packing Slip</label
        >
        <textarea
          id="comments"
          name="comments"
          rows="3"
          class="form-input"
          placeholder="Enter any additional comments to include on the packing slip"
        ></textarea>
      </div>
    </div>

    <!-- Printing Options Section -->
    <div class="form-section">
      <h3 class="section-title">
        <i class="fas fa-print"></i>Printing Options
      </h3>

      <div class="grid-2">
        <div class="form-group">
          <label for="printer_ip" class="form-label">Printer IP Address</label>
          <input
            type="text"
            id="printer_ip"
            name="printer_ip"
            class="form-input"
            value="*************"
            placeholder="e.g. *************"
          />
          <div class="help-text">Leave empty to skip printing</div>
        </div>

        <div class="form-group">
          <label for="copy_number" class="form-label"
            >Number of Copies to Print</label
          >
          <input
            type="number"
            id="copy_number"
            name="copy_number"
            min="0"
            value="2"
            class="form-input"
          />
          <div class="help-text">Set to 0 to generate without printing</div>
        </div>
      </div>
    </div>

    <!-- Submit Button -->
    <div class="text-center">
      <button type="submit" id="generateBtn" class="btn-primary">
        <i class="fas fa-file-pdf mr-2"></i>Generate Label
      </button>
    </div>
  </form>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="loading-overlay hidden">
  <div class="loading-content">
    <div class="spinner"></div>
    <h3 class="text-lg font-semibold text-gray-900 mb-2">
      Generating Label...
    </h3>
    <p class="text-gray-600">
      Please wait while we create your label and packing slip.
    </p>
  </div>
</div>
{% endblock %} {% block extra_js %}
<script>
  document.addEventListener("DOMContentLoaded", function () {
    console.log("Manual Label Generator: DOM loaded");

    // DOM Elements
    const labelForm = document.getElementById("labelForm");
    const labelTypeSelect = document.getElementById("label_type");
    const erfurtFields = document.getElementById("erfurt-fields");
    const shipmentDateInput = document.getElementById("shipment_date");
    const generateBtn = document.getElementById("generateBtn");
    const loadingOverlay = document.getElementById("loadingOverlay");
    const errorAlert = document.getElementById("errorAlert");
    const successAlert = document.getElementById("successAlert");
    const errorMessage = document.getElementById("errorMessage");
    const successMessage = document.getElementById("successMessage");

    // Ensure loading overlay is hidden on page load
    if (loadingOverlay) {
      loadingOverlay.classList.add("hidden");
      console.log("Manual Label Generator: Loading overlay hidden");
    }

    // Set today's date as default
    if (shipmentDateInput) {
      shipmentDateInput.valueAsDate = new Date();
    }

    // Handle origin selection styling
    const originOptions = document.querySelectorAll(".origin-option");
    const originRadios = document.querySelectorAll(
      'input[name="shipment_origin"]'
    );

    originRadios.forEach((radio) => {
      radio.addEventListener("change", function () {
        // Remove active class from all options
        originOptions.forEach((option) => {
          option.classList.remove("active");
          option.classList.remove("border-blue-500");
          option.classList.add("border-gray-200");
        });

        // Add active class to selected option
        const selectedLabel = document.querySelector(`label[for="${this.id}"]`);
        if (selectedLabel) {
          selectedLabel.classList.add("active");
          selectedLabel.classList.remove("border-gray-200");
          selectedLabel.classList.add("border-blue-500");
        }
      });
    });

    // Handle label type change
    if (labelTypeSelect) {
      labelTypeSelect.addEventListener("change", function () {
        if (this.value === "2") {
          erfurtFields.classList.add("show");
          // Make Erfurt fields required
          document.getElementById("po").required = true;
          document.getElementById("device_id").required = true;
          document.getElementById("xfab_lot_id").required = true;
        } else {
          erfurtFields.classList.remove("show");
          // Remove required from Erfurt fields
          document.getElementById("po").required = false;
          document.getElementById("device_id").required = false;
          document.getElementById("xfab_lot_id").required = false;
        }
      });
    }

    // Handle form submission
    if (labelForm) {
      labelForm.addEventListener("submit", function (e) {
        e.preventDefault();
        generateLabel();
      });

      function generateLabel() {
        // Hide previous alerts
        hideAlerts();

        // Show loading
        showLoading();

        // Disable submit button
        generateBtn.disabled = true;
        generateBtn.innerHTML =
          '<i class="fas fa-spinner fa-spin mr-2"></i>Generating...';

        // Prepare form data
        const formData = new FormData(labelForm);

        // Add CSRF token to form data
        const csrfTokenMeta = document.querySelector('meta[name="csrf-token"]');
        if (csrfTokenMeta) {
          const csrfToken = csrfTokenMeta.getAttribute("content");
          formData.append("csrf_token", csrfToken);
          console.log("Manual Label Generator: CSRF token added to request");
        } else {
          console.warn("Manual Label Generator: CSRF token meta tag not found");
        }

        // Send request
        fetch("/label-generator/generate", {
          method: "POST",
          body: formData,
        })
          .then((response) => response.json())
          .then((data) => {
            hideLoading();

            if (data.success) {
              showSuccess("Label generated successfully!");

              // Show download options
              showDownloadOptions(data);
            } else {
              showError(
                data.message || "An error occurred while generating the label."
              );
            }
          })
          .catch((error) => {
            hideLoading();
            showError("Network error: " + error.message);
          })
          .finally(() => {
            // Re-enable submit button
            generateBtn.disabled = false;
            generateBtn.innerHTML =
              '<i class="fas fa-file-pdf mr-2"></i>Generate Label';
          });
      }
    }

    function showDownloadOptions(data) {
      // Create download section
      const downloadSection = document.createElement("div");
      downloadSection.id = "downloadSection";
      downloadSection.className = "form-section mt-6";
      downloadSection.innerHTML = `
      <h3 class="section-title">
        <i class="fas fa-download"></i>Download Generated Files
      </h3>

      <div class="grid-2">
        <a href="/label-generator/download/label?session_id=${data.session_id}"
           class="btn-primary text-center text-decoration-none"
           onclick="trackDownload('label', '${data.session_id}')">
          <i class="fas fa-download mr-2"></i>Download Label
        </a>
        <a href="/label-generator/download/packing?session_id=${
          data.session_id
        }"
           class="btn-primary text-center text-decoration-none"
           onclick="trackDownload('packing', '${data.session_id}')">
          <i class="fas fa-file-pdf mr-2"></i>Download Packing Slip
        </a>
      </div>

      <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
        <div class="flex items-start space-x-2">
          <i class="fas fa-info-circle text-blue-600 mt-0.5"></i>
          <div class="text-sm text-blue-800">
            <strong>Note:</strong> Download links are valid for 2 hours.
            If you encounter any download issues, please generate a new label.
            <br>
            <strong>Session ID:</strong> <code class="bg-blue-100 px-1 rounded">${
              data.session_id
            }</code>
          </div>
        </div>
      </div>

      ${
        data.copies > 0
          ? `
        <div class="mt-4 p-4 rounded-lg ${
          data.print_success
            ? "bg-green-50 border border-green-200"
            : "bg-red-50 border border-red-200"
        }">
          <h4 class="font-semibold ${
            data.print_success ? "text-green-800" : "text-red-800"
          }">
            <i class="fas fa-${
              data.print_success ? "check" : "exclamation-triangle"
            } mr-2"></i>
            ${data.print_success ? "Printing Successful" : "Printing Failed"}
          </h4>
          <p class="${data.print_success ? "text-green-700" : "text-red-700"}">
            ${
              data.print_success
                ? `${data.copies} copies have been sent to the printer.`
                : "Could not connect to the printer. Please check the printer IP address and try again."
            }
          </p>
        </div>
      `
          : ""
      }
    `;

      // Remove existing download section if any
      const existingSection = document.getElementById("downloadSection");
      if (existingSection) {
        existingSection.remove();
      }

      // Add download section after the form
      labelForm.parentNode.insertBefore(downloadSection, labelForm.nextSibling);
    }

    function showLoading() {
      loadingOverlay.classList.remove("hidden");
    }

    function hideLoading() {
      loadingOverlay.classList.add("hidden");
    }

    function showError(message) {
      errorMessage.textContent = message;
      errorAlert.classList.remove("hidden");
      successAlert.classList.add("hidden");

      // Scroll to error
      errorAlert.scrollIntoView({ behavior: "smooth", block: "center" });
    }

    function showSuccess(message) {
      successMessage.textContent = message;
      successAlert.classList.remove("hidden");
      errorAlert.classList.add("hidden");

      // Scroll to success
      successAlert.scrollIntoView({ behavior: "smooth", block: "center" });
    }

    function hideAlerts() {
      errorAlert.classList.add("hidden");
      successAlert.classList.add("hidden");
    }

    // Track download attempts for debugging
    function trackDownload(fileType, sessionId) {
      console.log(`Download attempt: ${fileType}, Session: ${sessionId}`);

      // Add a small delay to allow the download to start, then check if it failed
      setTimeout(() => {
        // You could add additional error checking here if needed
        console.log(`Download initiated for ${fileType}`);
      }, 1000);
    }

    // Add a debug function to check session status
    window.debugSessions = function () {
      fetch("/label-generator/debug/sessions")
        .then((response) => response.json())
        .then((data) => {
          console.log("Current sessions:", data);
        })
        .catch((error) => {
          console.error("Error fetching session debug info:", error);
        });
    };
  });
</script>
{% endblock %}

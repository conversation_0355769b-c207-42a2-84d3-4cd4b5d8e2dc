{% extends "base.html" %} {% block title %}Inventory Management{% endblock %} {%
block breadcrumb %}
<li class="flex items-center">
  <span class="mx-2"><i class="fas fa-chevron-right text-gray-400"></i></span>
  <span class="text-gray-900 font-medium flex items-center">
    <i class="fas fa-boxes mr-1"></i>Inventory Management
  </span>
</li>
{% endblock %} {% block extra_css %}
<link
  rel="stylesheet"
  href="{{ url_for('static', filename='css/inventory_modal.css') }}"
/>

<style>
  /* Date cell styling for better user interaction */
  .date-cell {
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    position: relative;
  }

  .date-cell:hover {
    background-color: #f3f4f6 !important;
    transform: scale(1.02);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .date-cell:hover::after {
    content: "Click to refresh";
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    background: #374151;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    white-space: nowrap;
    z-index: 10;
  }

  /* Dark mode support */
  .dark .date-cell:hover {
    background-color: #374151 !important;
  }

  .dark .date-cell:hover::after {
    background: #f3f4f6;
    color: #374151;
  }

  /* Refresh animation */
  .date-refreshing {
    animation: pulse 0.5s ease-in-out;
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
    100% {
      transform: scale(1);
    }
  }

  /* Enhanced Search Styles */
  .preset-btn {
    @apply px-4 py-2 text-sm font-medium rounded-2xl transition-all duration-200 cursor-pointer border-2 shadow-sm;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-color: #e2e8f0;
    color: #475569;
    border-radius: 16px;
  }

  .preset-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .preset-btn.active {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  /* Individual preset button styles */
  .preset-btn[data-preset="recent"] {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    border-color: #60a5fa;
    color: #1e40af;
  }

  .preset-btn[data-preset="recent"]:hover {
    background: linear-gradient(135deg, #bfdbfe 0%, #93c5fd 100%);
    border-color: #3b82f6;
  }

  .preset-btn[data-preset="recent"].active {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    border-color: #1d4ed8;
    color: white;
  }

  .preset-btn[data-preset="shipped"] {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    border-color: #34d399;
    color: #065f46;
  }

  .preset-btn[data-preset="shipped"]:hover {
    background: linear-gradient(135deg, #a7f3d0 0%, #6ee7b7 100%);
    border-color: #10b981;
  }

  .preset-btn[data-preset="shipped"].active {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border-color: #047857;
    color: white;
  }

  .preset-btn[data-preset="unshipped"] {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border-color: #f59e0b;
    color: #92400e;
  }

  .preset-btn[data-preset="unshipped"]:hover {
    background: linear-gradient(135deg, #fde68a 0%, #fcd34d 100%);
    border-color: #d97706;
  }

  .preset-btn[data-preset="unshipped"].active {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    border-color: #92400e;
    color: white;
  }

  .preset-btn[data-preset="location-issues"] {
    background: linear-gradient(135deg, #fecaca 0%, #fca5a5 100%);
    border-color: #f87171;
    color: #991b1b;
  }

  .preset-btn[data-preset="location-issues"]:hover {
    background: linear-gradient(135deg, #fca5a5 0%, #f87171 100%);
    border-color: #ef4444;
  }

  .preset-btn[data-preset="location-issues"].active {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border-color: #b91c1c;
    color: white;
  }

  .smart-input {
    position: relative;
  }

  .field-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #d1d5db;
    border-top: none;
    border-radius: 0 0 0.375rem 0.375rem;
    max-height: 200px;
    overflow-y: auto;
    z-index: 10;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .suggestion-item {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f3f4f6;
    transition: background-color 0.2s;
  }

  .suggestion-item:hover {
    background-color: #f3f4f6;
  }

  .suggestion-item:last-child {
    border-bottom: none;
  }

  /* Action Button Styles */
  .action-btn {
    @apply px-3 py-2 text-sm font-medium rounded-md transition-all duration-200 flex items-center;
  }

  .action-btn:hover {
    @apply transform scale-105 shadow-md;
  }

  .action-btn:active {
    @apply transform scale-95;
  }

  /* Keyboard Shortcuts Indicator */
  .keyboard-shortcut {
    @apply text-xs text-gray-400 ml-2;
  }

  /* Modern animations and effects */
  @keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: calc(200px + 100%) 0; }
  }

  .preset-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: left 0.5s;
  }

  .preset-btn:hover::before {
    left: 100%;
  }

  .preset-btn {
    position: relative;
    overflow: hidden;
  }

  /* Pulse animation for active state */
  .preset-btn.active {
    animation: pulse-glow 2s infinite;
  }

  @keyframes pulse-glow {
    0%, 100% { box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); }
    50% { box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25), 0 0 0 3px rgba(59, 130, 246, 0.1); }
  }

  /* Enhanced focus states for accessibility */
  .preset-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
  }

  /* Responsive design improvements */
  @media (max-width: 640px) {
    .preset-btn {
      @apply px-3 py-1.5 text-xs;
    }

    .preset-btn .ml-2 {
      @apply ml-1;
    }
  }
</style>
{% endblock %} {% block content %}
<div class="container mx-auto px-4 py-6">
  <!-- Header -->
  <div class="mb-6">
    <h1 class="text-2xl font-bold text-gray-800 dark:text-white">
      Inventory Management
    </h1>
    <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
      Manage your wafer inventory and track locations
    </p>
  </div>

  <!-- Enhanced Search Section -->
  <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg mb-6">
    <!-- Smart Search Header -->
    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between">
        <h2
          class="text-lg font-semibold text-gray-900 dark:text-white flex items-center"
        >
          <i class="fas fa-search mr-2 text-blue-500"></i>
          Smart Inventory Search
        </h2>
        <div class="flex items-center space-x-2">
          <button
            id="toggle-advanced-search"
            class="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors"
          >
            <i class="fas fa-cog mr-1"></i>Advanced
          </button>
          <button
            id="save-search-preset"
            class="px-3 py-1 text-sm bg-green-100 text-green-700 rounded-md hover:bg-green-200 transition-colors"
          >
            <i class="fas fa-bookmark mr-1"></i>Save Preset
          </button>
        </div>
      </div>
    </div>

    <!-- Quick Search Bar -->
    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
      <div class="relative">
        <div
          class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
        >
          <i class="fas fa-search text-gray-400"></i>
        </div>
        <input
          type="text"
          id="smart-search"
          class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-2xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white text-lg"
          placeholder="Search by wafer ID, lot ID, location, or any field... (Press Ctrl+K)"
          autocomplete="off"
        />
        <div class="absolute inset-y-0 right-0 pr-3 flex items-center space-x-2">
          <span class="text-xs text-gray-400 hidden sm:block">Press Enter to search</span>
          <kbd
            class="px-2 py-1 text-xs font-semibold text-gray-500 bg-gray-100 border border-gray-200 rounded-lg dark:bg-gray-600 dark:text-gray-300 dark:border-gray-500"
          >
            Ctrl+K
          </kbd>
        </div>
      </div>
      <!-- Search Suggestions Dropdown -->
      <div
        id="search-suggestions"
        class="hidden absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-2xl shadow-lg max-h-64 overflow-y-auto"
      >
        <!-- Suggestions will be populated dynamically -->
      </div>
    </div>

    <!-- Search Presets -->
    <div class="p-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-700">
      <div class="flex items-center space-x-3 mb-3">
        <div class="flex items-center">
          <div class="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse"></div>
          <span class="text-sm font-semibold text-gray-700 dark:text-gray-300">Quick Filters</span>
        </div>
        <div class="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-600 px-3 py-1.5 rounded-full">
          One-click search presets
        </div>
      </div>
      <div class="flex flex-wrap gap-3" id="search-presets">
        <button class="preset-btn" data-preset="recent" title="Show items that arrived in the last 7 days">
          <div class="flex items-center">
            <div class="w-5 h-5 mr-2 flex items-center justify-center">
              <i class="fas fa-clock-rotate-left text-sm"></i>
            </div>
            <span>Recent Arrivals</span>
            <div class="ml-2 px-1.5 py-0.5 bg-white bg-opacity-30 rounded-full text-xs font-medium">7d</div>
          </div>
        </button>
        <button class="preset-btn" data-preset="shipped" title="Show items shipped in the last 7 days">
          <div class="flex items-center">
            <div class="w-5 h-5 mr-2 flex items-center justify-center">
              <i class="fas fa-truck-fast text-sm"></i>
            </div>
            <span>Recently Shipped</span>
            <div class="ml-2 px-1.5 py-0.5 bg-white bg-opacity-30 rounded-full text-xs font-medium">7d</div>
          </div>
        </button>
        <button class="preset-btn" data-preset="unshipped" title="Show items currently available in inventory">
          <div class="flex items-center">
            <div class="w-5 h-5 mr-2 flex items-center justify-center">
              <i class="fas fa-boxes-stacked text-sm"></i>
            </div>
            <span>Available Stock</span>
            <div class="ml-2 px-1.5 py-0.5 bg-white bg-opacity-30 rounded-full text-xs font-medium">Now</div>
          </div>
        </button>
        <button class="preset-btn" data-preset="location-issues" title="Identify items with potential location problems">
          <div class="flex items-center">
            <div class="w-5 h-5 mr-2 flex items-center justify-center">
              <i class="fas fa-triangle-exclamation text-sm"></i>
            </div>
            <span>Location Issues</span>
            <div class="ml-2 px-1.5 py-0.5 bg-white bg-opacity-30 rounded-full text-xs font-medium">!</div>
          </div>
        </button>
      </div>
    </div>

    <!-- Advanced Search Fields -->
    <div id="advanced-search-fields" class="p-6 hidden">
      <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
        <!-- Basic Info Fields -->
        <div class="smart-field-container">
          <label
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
          >
            Wafer ID
          </label>
          <div class="relative">
            <input
              type="text"
              id="wafer-id"
              class="smart-input w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              placeholder="Enter Wafer ID"
              data-field="wafer_id"
            />
            <div class="field-suggestions hidden"></div>
          </div>
        </div>

        <div>
          <label
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >LGT Lot ID</label
          >
          <input
            type="text"
            id="lot-id"
            class="w-full px-3 py-2 border border-gray-300 rounded-xl shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            placeholder="Enter LGT Lot ID"
          />
        </div>

        <div>
          <label
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >Xfab Lot ID</label
          >
          <input
            type="text"
            id="xfab-id"
            class="w-full px-3 py-2 border border-gray-300 rounded-xl shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            placeholder="Enter Xfab Lot ID"
          />
        </div>

        <div>
          <label
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >Mask Set ID</label
          >
          <input
            type="text"
            id="mask-set-id"
            class="w-full px-3 py-2 border border-gray-300 rounded-xl shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            placeholder="Enter Mask Set ID"
          />
        </div>

        <div>
          <label
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >Module Name</label
          >
          <input
            type="text"
            id="module-name"
            class="w-full px-3 py-2 border border-gray-300 rounded-xl shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            placeholder="Enter Module Name"
          />
        </div>

        <!-- Location Fields -->
        <div>
          <label
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >Cassette ID</label
          >
          <input
            type="text"
            id="cassette-id"
            class="w-full px-3 py-2 border border-gray-300 rounded-xl shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            placeholder="Enter Cassette ID"
          />
        </div>

        <div>
          <label
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >Slot ID</label
          >
          <input
            type="number"
            id="slot-id"
            class="w-full px-3 py-2 border border-gray-300 rounded-xl shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            placeholder="Enter Slot ID"
          />
        </div>

        <div>
          <label
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >Location</label
          >
          <select
            id="location-id"
            class="w-full px-3 py-2 border border-gray-300 rounded-xl shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            title="Location"
          >
            <option value="">All Locations</option>
            <!-- Other options will be populated dynamically -->
          </select>
        </div>

        <!-- Date Range Fields -->
        <div
          class="col-span-full lg:col-span-4 grid grid-cols-1 md:grid-cols-2 gap-4"
        >
          <!-- Arrival Period -->
          <div class="space-y-2">
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300"
              >Arrival Period</label
            >
            <div class="flex items-center space-x-2">
              <div class="date-picker-wrapper flex-1">
                <input
                  type="date"
                  id="arrived-at-from"
                  class="w-full px-3 py-2 border border-gray-300 rounded-xl shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  title="Arrived At"
                />
              </div>
              <span class="text-gray-500">to</span>
              <div class="date-picker-wrapper flex-1">
                <input
                  type="date"
                  id="arrived-at-to"
                  class="w-full px-3 py-2 border border-gray-300 rounded-xl shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  title="Arrived At"
                />
              </div>
            </div>
          </div>

          <!-- Sent Period -->
          <div class="space-y-2">
            <label
              class="block text-sm font-medium text-gray-700 dark:text-gray-300"
              >Sent Period</label
            >
            <div class="flex items-center space-x-2">
              <div class="date-picker-wrapper flex-1">
                <input
                  type="date"
                  id="sent-at-from"
                  class="w-full px-3 py-2 border border-gray-300 rounded-xl shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  title="Sent At"
                />
              </div>
              <span class="text-gray-500">to</span>
              <div class="date-picker-wrapper flex-1">
                <input
                  type="date"
                  id="sent-at-to"
                  class="w-full px-3 py-2 border border-gray-300 rounded-xl shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  title="Sent At"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Floating Action Buttons -->
    <div class="fixed bottom-8 right-8 flex flex-col gap-3 z-40">
      <!-- Search Button -->
      <button
        onclick="searchInventory()"
        class="bg-blue-600 hover:bg-blue-700 text-white rounded-full p-3 shadow-lg transition-all duration-200 hover:scale-105 group"
        title="Execute search"
      >
        <i class="fas fa-search text-sm"></i>
        <span
          class="absolute right-full mr-3 top-1/2 transform -translate-y-1/2 bg-gray-800 text-white px-2 py-1 rounded-lg text-xs opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap"
        >
          Search
        </span>
      </button>

      <!-- Clear Button -->
      <button
        id="clear-form-btn"
        onclick="clearForm()"
        class="bg-gray-500 hover:bg-gray-600 text-white rounded-full p-3 shadow-lg transition-all duration-200 hover:scale-105 group"
        title="Clear all search filters"
      >
        <i class="fas fa-eraser text-sm"></i>
        <span
          class="absolute right-full mr-3 top-1/2 transform -translate-y-1/2 bg-gray-800 text-white px-2 py-1 rounded-lg text-xs opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap"
        >
          Clear Filters
        </span>
      </button>
    </div>

    <!-- Results Section -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
      <!-- Action Buttons -->
      <div
        class="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center"
      >
        <div class="text-sm text-gray-500 dark:text-gray-400">
          <span id="selected-count">0</span> items selected
        </div>
        <div class="flex space-x-3">
          <button
            onclick="syncWafersToInventory()"
            class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out"
            data-permission="sync"
          >
            <i class="fas fa-sync-alt mr-2"></i>Sync Wafers
          </button>
          <button
            onclick="addInventory()"
            class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out"
            data-permission="add"
          >
            <i class="fas fa-plus mr-2"></i>Add
          </button>
          <button
            onclick="modifyInventory()"
            class="bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out"
            data-permission="modify"
          >
            <i class="fas fa-edit mr-2"></i>Modify
          </button>
          <button
            onclick="bulkModifyInventory()"
            class="bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out"
            data-permission="modify"
            title="Bulk modify wafers with common attributes"
          >
            <i class="fas fa-layer-group mr-2"></i>Bulk Modify
          </button>
          <button
            onclick="deleteInventory()"
            class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out"
            data-permission="delete"
          >
            <i class="fas fa-trash mr-2"></i>Delete
          </button>
          <button
            onclick="exportToCSV()"
            class="bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out"
          >
            <i class="fas fa-file-export mr-2"></i>Export CSV
          </button>
          <button
            onclick="forceRefreshInventoryData()"
            class="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out"
          >
            <i class="fas fa-sync-alt mr-2"></i>Refresh Data
          </button>
        </div>
      </div>

      <!-- Table Headers -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div class="table-container custom-scrollbar">
          <table
            class="min-w-full divide-y divide-gray-200 dark:divide-gray-700"
          >
            <!-- Table Headers -->
            <thead class="sticky-header bg-gray-50 dark:bg-gray-700">
              <tr>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  <input
                    type="checkbox"
                    id="select-all"
                    class="rounded border-gray-300 text-blue-600 shadow-sm focus:ring"
                    title="Select All"
                  />
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onclick="handleSort('wafer_id')"
                >
                  WAFER ID
                  <span id="sort-wafer_id" class="sort-indicator"></span>
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onclick="handleSort('lot_id')"
                >
                  LGT LOT ID
                  <span id="sort-lot_id" class="sort-indicator"></span>
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onclick="handleSort('xfab_id')"
                >
                  XFAB LOT ID
                  <span id="sort-xfab_id" class="sort-indicator"></span>
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onclick="handleSort('mask_set_id')"
                >
                  MASK SET ID
                  <span id="sort-mask_set_id" class="sort-indicator"></span>
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onclick="handleSort('module_name')"
                >
                  MODULE NAME
                  <span id="sort-module_name" class="sort-indicator"></span>
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onclick="handleSort('cassette_id')"
                >
                  CASSETTE ID
                  <span id="sort-cassette_id" class="sort-indicator"></span>
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onclick="handleSort('slot_id')"
                >
                  SLOT ID
                  <span id="sort-slot_id" class="sort-indicator"></span>
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onclick="handleSort('location_id')"
                >
                  LOCATION
                  <span id="sort-location_id" class="sort-indicator"></span>
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onclick="handleSort('arrived_at')"
                >
                  ARRIVED AT
                  <span id="sort-arrived_at" class="sort-indicator"></span>
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onclick="handleSort('sent_at')"
                >
                  SENT AT
                  <span id="sort-sent_at" class="sort-indicator"></span>
                </th>
                <th
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  HISTORY
                </th>
              </tr>
            </thead>
            <tbody
              class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"
              id="inventory-table-body"
            >
              <!-- Table content will be dynamically populated -->
            </tbody>
          </table>
        </div>
      </div>
      <!-- Pagination -->
      <div
        class="mt-4 flex items-center justify-between px-4 py-3 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700"
      >
        <div class="flex items-center">
          <span class="text-sm text-gray-700 dark:text-gray-300">Show</span>
          <select
            id="page-size"
            class="mx-2 border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600"
            title="Items per page"
          >
            <option value="10">10</option>
            <option value="25">25</option>
            <option value="50">50</option>
            <option value="100">100</option>
          </select>
          <span class="text-sm text-gray-700 dark:text-gray-300">entries</span>
        </div>

        <div class="flex items-center space-x-4">
          <div class="text-sm text-gray-700 dark:text-gray-300">
            Showing <span id="page-start">0</span> to
            <span id="page-end">0</span> of <span id="total-items">0</span>
            entries
          </div>
          <div class="flex space-x-2">
            <button
              id="prev-page"
              class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              id="next-page"
              class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Batch Operations Panel -->
  <div id="batch-operations-panel" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
    <div class="flex items-center justify-center min-h-screen p-4">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <!-- Panel Header -->
        <div class="p-6 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
              <i class="fas fa-layer-group mr-2 text-indigo-500"></i>
              Batch Operations Center
            </h2>
            <button
              id="close-batch-panel"
              class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <i class="fas fa-times text-xl"></i>
            </button>
          </div>
          <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
            Perform operations on multiple inventory items with progress tracking
          </p>
        </div>

        <!-- Panel Content -->
        <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          <!-- Batch Operation Tabs -->
          <div class="mb-6">
            <div class="border-b border-gray-200 dark:border-gray-700">
              <nav class="-mb-px flex space-x-8">
                <button
                  class="batch-tab active py-2 px-1 border-b-2 border-indigo-500 text-indigo-600 font-medium text-sm"
                  data-tab="bulk-modify"
                >
                  <i class="fas fa-edit mr-1"></i>Bulk Modify
                </button>
                <button
                  class="batch-tab py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm"
                  data-tab="bulk-location"
                >
                  <i class="fas fa-map-marker-alt mr-1"></i>Location Update
                </button>
                <button
                  class="batch-tab py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm"
                  data-tab="analytics"
                >
                  <i class="fas fa-chart-bar mr-1"></i>Analytics
                </button>
              </nav>
            </div>
          </div>

          <!-- Bulk Modify Tab -->
          <div id="bulk-modify-tab" class="batch-tab-content">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <!-- Selection Summary -->
              <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-3">
                  Selection Summary
                </h3>
                <div class="space-y-2">
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Selected Items:</span>
                    <span id="batch-selected-count" class="text-sm font-medium">0</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Unique Lots:</span>
                    <span id="batch-unique-lots" class="text-sm font-medium">0</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-gray-600 dark:text-gray-400">Locations:</span>
                    <span id="batch-unique-locations" class="text-sm font-medium">0</span>
                  </div>
                </div>
              </div>

              <!-- Batch Operations -->
              <div class="space-y-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                  Batch Operations
                </h3>

                <!-- Common Field Updates -->
                <div class="space-y-3">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Update Location
                    </label>
                    <select id="batch-location" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600">
                      <option value="">Select new location...</option>
                    </select>
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Update Module Name
                    </label>
                    <input
                      type="text"
                      id="batch-module"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600"
                      placeholder="Enter new module name..."
                    />
                  </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex space-x-3 pt-4">
                  <button
                    id="execute-batch-operation"
                    class="flex-1 bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
                  >
                    <i class="fas fa-play mr-2"></i>Execute Batch Operation
                  </button>
                  <button
                    id="preview-batch-operation"
                    class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
                  >
                    <i class="fas fa-eye mr-2"></i>Preview
                  </button>
                </div>
              </div>
            </div>

            <!-- Progress Tracking -->
            <div id="batch-progress" class="hidden mt-6 bg-blue-50 dark:bg-blue-900 rounded-lg p-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-blue-900 dark:text-blue-100">
                  Processing batch operation...
                </span>
                <span id="batch-progress-text" class="text-sm text-blue-700 dark:text-blue-300">
                  0 / 0 completed
                </span>
              </div>
              <div class="w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2">
                <div
                  id="batch-progress-bar"
                  class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style="width: 0%"
                ></div>
              </div>
              <div id="batch-progress-details" class="mt-2 text-xs text-blue-700 dark:text-blue-300">
                <!-- Progress details will be shown here -->
              </div>
            </div>
          </div>

          <!-- Analytics Tab -->
          <div id="analytics-tab" class="batch-tab-content hidden">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <!-- Inventory Insights -->
              <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900 dark:to-indigo-900 rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                  <i class="fas fa-lightbulb mr-2 text-yellow-500"></i>
                  Smart Insights
                </h3>
                <div id="inventory-insights" class="space-y-3">
                  <!-- Insights will be populated dynamically -->
                </div>
              </div>

              <!-- Predictive Analytics -->
              <div class="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900 dark:to-emerald-900 rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                  <i class="fas fa-chart-line mr-2 text-green-500"></i>
                  Predictive Analytics
                </h3>
                <div id="predictive-analytics" class="space-y-3">
                  <!-- Analytics will be populated dynamically -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block extra_js %}
<!-- SweetAlert2 for enhanced notifications -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- Core inventory management -->
<script src="{{ url_for('static', filename='js/inventory_management.js') }}"></script>

<!-- Advanced inventory features -->
<script src="{{ url_for('static', filename='js/advanced-inventory.js') }}"></script>

<!-- Debug utilities -->
<script src="{{ url_for('static', filename='js/debug.js') }}"></script>

<!-- Enhanced functionality integration -->
<script>
// Extend the existing inventory management with advanced features
document.addEventListener('DOMContentLoaded', function() {
  // Override the original updateSelectedCount to work with advanced features
  const originalUpdateSelectedCount = window.updateSelectedCount;
  window.updateSelectedCount = function() {
    originalUpdateSelectedCount();

    // Update advanced features
    if (window.advancedInventoryManager) {
      window.advancedInventoryManager.updateBatchSummary();

      // Show/hide selection actions
      const selectedCount = document.querySelectorAll('input[name="inventory-item"]:checked').length;
      const selectionActions = document.getElementById('selection-actions');
      if (selectionActions) {
        selectionActions.style.display = selectedCount > 0 ? 'flex' : 'none';
      }
    }
  };

  // Add batch operation handlers
  const executeBatchBtn = document.getElementById('execute-batch-operation');
  if (executeBatchBtn) {
    executeBatchBtn.addEventListener('click', function() {
      const selectedItems = Array.from(document.querySelectorAll('input[name="inventory-item"]:checked'))
        .map(checkbox => ({
          wafer_id: checkbox.value,
          row: checkbox.closest('tr')
        }));

      if (selectedItems.length === 0) {
        Swal.fire({
          title: 'No Items Selected',
          text: 'Please select items to perform batch operations.',
          icon: 'warning'
        });
        return;
      }

      const batchLocation = document.getElementById('batch-location').value;
      const batchModule = document.getElementById('batch-module').value;

      if (!batchLocation && !batchModule) {
        Swal.fire({
          title: 'No Updates Specified',
          text: 'Please specify at least one field to update.',
          icon: 'warning'
        });
        return;
      }

      const updates = {};
      if (batchLocation) updates.location_id = batchLocation;
      if (batchModule) updates.module_name = batchModule;

      // Execute batch operation
      window.advancedInventoryManager.batchOperations.executeBatchOperation(
        'bulk-modify',
        selectedItems,
        updates
      );
    });
  }

  // Add preview functionality
  const previewBatchBtn = document.getElementById('preview-batch-operation');
  if (previewBatchBtn) {
    previewBatchBtn.addEventListener('click', function() {
      const selectedItems = document.querySelectorAll('input[name="inventory-item"]:checked');

      if (selectedItems.length === 0) {
        Swal.fire({
          title: 'No Items Selected',
          text: 'Please select items to preview batch operations.',
          icon: 'warning'
        });
        return;
      }

      const batchLocation = document.getElementById('batch-location').value;
      const batchModule = document.getElementById('batch-module').value;

      let previewText = `<div class="text-left">
        <p><strong>Items to update:</strong> ${selectedItems.length}</p>`;

      if (batchLocation) {
        previewText += `<p><strong>New Location:</strong> ${batchLocation}</p>`;
      }

      if (batchModule) {
        previewText += `<p><strong>New Module:</strong> ${batchModule}</p>`;
      }

      previewText += '</div>';

      Swal.fire({
        title: 'Batch Operation Preview',
        html: previewText,
        icon: 'info',
        showCancelButton: true,
        confirmButtonText: 'Execute',
        cancelButtonText: 'Cancel'
      }).then((result) => {
        if (result.isConfirmed) {
          document.getElementById('execute-batch-operation').click();
        }
      });
    });
  }

  // Load locations for batch operations
  fetch('/api/locations')
    .then(response => response.json())
    .then(data => {
      const batchLocationSelect = document.getElementById('batch-location');
      if (batchLocationSelect && data.success) {
        data.locations.forEach(location => {
          const option = document.createElement('option');
          option.value = location.location_id;
          option.textContent = location.label;
          batchLocationSelect.appendChild(option);
        });
      }
    })
    .catch(error => console.error('Error loading locations:', error));

  console.log('🎯 Advanced inventory features integrated successfully');
});
</script>

{% endblock %}
</div>

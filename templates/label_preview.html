<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Label Preview</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
        }

        h1,
        h2 {
            color: #333;
        }

        .wafer-list {
            white-space: pre-wrap;
            font-family: monospace;
        }
    </style>
</head>

<body>
    <h1>Label Preview</h1>
    <p>Project: {{ project_name }}</p>
    <p>Date: {{ shipment_date }}</p>
    <p>Wafer Count: {{ wafer_count }}</p>
    <p>Device ID: {{ device_id }}</p>
    <div>
        <h2>Wafer List</h2>
        <pre class="wafer-list">{% for wafer in wafer_list %}{{ wafer }}{% if loop.index is even and not loop.last %}
{% endif %}{% endfor %}</pre>
    </div>
</body>

</html>
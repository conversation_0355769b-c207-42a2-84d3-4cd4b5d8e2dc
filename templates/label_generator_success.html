{% extends "base.html" %} {% block title %}Manual Label Generated Successfully -
Talaria Dashboard{% endblock %} {% block extra_css %}
<style>
  .success-card {
    background: white;
    border-radius: 12px;
    padding: 32px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    text-align: center;
  }

  .success-header {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 24px;
    border-radius: 12px;
    margin-bottom: 24px;
  }

  .success-icon {
    font-size: 4rem;
    margin-bottom: 16px;
  }

  .download-btn {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    border: none;
    padding: 14px 28px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin: 8px;
    transition: all 0.2s;
  }

  .download-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
    color: white;
    text-decoration: none;
  }

  .download-btn.secondary {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  }

  .download-btn.secondary:hover {
    box-shadow: 0 8px 25px rgba(107, 114, 128, 0.3);
  }

  .print-status {
    margin-top: 24px;
    padding: 20px;
    border-radius: 12px;
    border: 2px solid;
  }

  .print-status.success {
    background-color: #f0fdf4;
    border-color: #22c55e;
    color: #15803d;
  }

  .print-status.error {
    background-color: #fef2f2;
    border-color: #ef4444;
    color: #dc2626;
  }

  .back-btn {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    margin-top: 24px;
    transition: all 0.2s;
  }

  .back-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(107, 114, 128, 0.3);
    color: white;
    text-decoration: none;
  }

  .grid-2 {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    max-width: 600px;
    margin: 0 auto;
  }

  @media (max-width: 768px) {
    .grid-2 {
      grid-template-columns: 1fr;
    }
  }
</style>
{% endblock %} {% block content %}
<div class="max-w-4xl mx-auto">
  <!-- Header -->
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
      <i class="fas fa-check-circle mr-3 text-green-600"></i>Label Generated
      Successfully
    </h1>
    <p class="text-gray-600 dark:text-gray-400">
      Your label and packing slip have been generated and are ready for
      download.
    </p>
  </div>

  <!-- Success Card -->
  <div class="success-card">
    <div class="success-header">
      <i class="fas fa-check-circle success-icon"></i>
      <h2 class="text-2xl font-bold mb-2">Generation Complete!</h2>
      <p class="text-green-100">Your files have been successfully created.</p>
    </div>

    <!-- File Information -->
    <div class="mb-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Generated Files</h3>
      <div class="grid-2">
        <div class="bg-gray-50 p-4 rounded-lg">
          <i class="fas fa-file-pdf text-red-500 text-2xl mb-2"></i>
          <h4 class="font-semibold text-gray-900">Label PDF</h4>
          <p class="text-sm text-gray-600">{{ label_filename }}</p>
        </div>
        <div class="bg-gray-50 p-4 rounded-lg">
          <i class="fas fa-file-pdf text-blue-500 text-2xl mb-2"></i>
          <h4 class="font-semibold text-gray-900">Packing Slip PDF</h4>
          <p class="text-sm text-gray-600">{{ packing_filename }}</p>
        </div>
      </div>
    </div>

    <!-- Download Buttons -->
    <div class="mb-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Download Files</h3>
      <div class="grid-2">
        <a
          href="{{ url_for('label_generator.download_file', file_type='label', session_id=session_id) }}"
          class="download-btn"
        >
          <i class="fas fa-download mr-2"></i>Download Label
        </a>
        <a
          href="{{ url_for('label_generator.download_file', file_type='packing', session_id=session_id) }}"
          class="download-btn secondary"
        >
          <i class="fas fa-file-pdf mr-2"></i>Download Packing Slip
        </a>
      </div>
    </div>

    <!-- Print Status -->
    {% if copies > 0 %}
    <div
      class="print-status {% if print_success %}success{% else %}error{% endif %}"
    >
      <h4 class="font-semibold text-lg mb-2">
        {% if print_success %}
        <i class="fas fa-check mr-2"></i>Printing Successful {% else %}
        <i class="fas fa-exclamation-triangle mr-2"></i>Printing Failed {% endif
        %}
      </h4>
      <p>
        {% if print_success %} {{ copies }} copies have been sent to the printer
        successfully. {% else %} Could not connect to the printer. Please check
        the printer IP address and try again. You can still download the files
        and print them manually. {% endif %}
      </p>
    </div>
    {% endif %}

    <!-- Back Button -->
    <a href="{{ url_for('label_generator.index') }}" class="back-btn">
      <i class="fas fa-arrow-left mr-2"></i>Generate Another Manual Label
    </a>
  </div>
</div>
{% endblock %} {% block extra_js %}
<script>
  document.addEventListener("DOMContentLoaded", function () {
    // Auto-focus on the first download button for keyboard navigation
    const firstDownloadBtn = document.querySelector(".download-btn");
    if (firstDownloadBtn) {
      firstDownloadBtn.focus();
    }

    // Add keyboard shortcuts
    document.addEventListener("keydown", function (e) {
      // Press 'L' to download label
      if (e.key.toLowerCase() === "l" && !e.ctrlKey && !e.metaKey) {
        const labelBtn = document.querySelector('a[href*="file_type=label"]');
        if (labelBtn) {
          labelBtn.click();
        }
      }

      // Press 'P' to download packing slip
      if (e.key.toLowerCase() === "p" && !e.ctrlKey && !e.metaKey) {
        const packingBtn = document.querySelector(
          'a[href*="file_type=packing"]'
        );
        if (packingBtn) {
          packingBtn.click();
        }
      }

      // Press 'N' to generate new label
      if (e.key.toLowerCase() === "n" && !e.ctrlKey && !e.metaKey) {
        const backBtn = document.querySelector(".back-btn");
        if (backBtn) {
          window.location.href = backBtn.href;
        }
      }
    });

    // Show keyboard shortcuts hint
    console.log(
      "Keyboard shortcuts: L = Download Label, P = Download Packing Slip, N = New Manual Label"
    );
  });
</script>
{% endblock %}

{% extends "base.html" %}
{% from "macros.html" import navigation_buttons %}
{% block content %}
<div class="min-h-screen bg-gray-50 py-8">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header Section with improved styling -->
    <div class="mb-8">
      <div class="text-center">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Wafer Details</h1>
        <div class="inline-flex items-center px-4 py-2 bg-indigo-50 rounded-full">
          <span class="text-lg text-gray-700">Lot: <span class="font-semibold text-indigo-700">{{ lot }}</span></span>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-200">
      <form id="initialForm" method="POST" action="{{ url_for('process_wafer_selection') }}"
        class="divide-y divide-gray-200">
        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
        <input type="hidden" id="wafer-pairs-input" name="wafer_pairs" value="">
        <input type="hidden" name="lot" value="{{ lot }}">

        <!-- Improved Table Header Design -->
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead>
              <tr class="bg-gray-100">
                <th scope="col" class="px-6 py-4 text-left">
                  <div class="flex items-center gap-2">
                    <input type="checkbox" id="select-all"
                      class="w-4 h-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 shadow-sm">
                    <label for="select-all" class="font-medium text-gray-700">SELECT ALL</label>
                  </div>
                </th>
                <th scope="col" class="px-6 py-4 text-left font-medium text-gray-700">CASSETTE ID</th>
                <th scope="col" class="px-6 py-4 text-left font-medium text-gray-700">SLOT ID</th>
                <th scope="col" class="px-6 py-4 text-left font-medium text-gray-700">LOT</th>
                <th scope="col" class="px-6 py-4 text-left font-medium text-gray-700">SCRIBE ID</th>
                <th scope="col" class="px-6 py-4 text-left font-medium text-gray-700">LOCATION</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {% for wafer in wafers %}
              <tr class="hover:bg-gray-50 transition-colors">
                <td class="px-6 py-4 whitespace-nowrap">
                  <input type="checkbox"
                    class="wafer-checkbox w-4 h-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 shadow-sm"
                    data-slot-id="{{ wafer['Slot ID'] }}" data-scribe-id="{{ wafer['Scribe ID'] }}"
                    title="Select Wafer">
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">{{ wafer['Cassette ID'] }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ wafer['Slot ID'] }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ wafer['Lot'] }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">{{ wafer['Scribe ID'] }}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full 
                                        {% if wafer['Location'] == 'Ligentec FR' %}
                                            bg-green-100 text-green-800
                                        {% else %}
                                            bg-gray-100 text-gray-800
                                        {% endif %}">
                    {{ wafer['Location'] }}
                  </span>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>

        <!-- Improved Selection Summary Design -->
        <div class="py-6 px-6">
          <div class="bg-gray-50 p-6 rounded-lg border border-gray-200 shadow-sm">
            <h3 class="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">Selection Summary</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label for="auto-filled-slot-id" class="block text-sm font-medium text-gray-700 mb-1">Selected Slot
                  IDs</label>
                <textarea id="auto-filled-slot-id" name="slot_ids" rows="3"
                  class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm bg-white"
                  readonly></textarea>
              </div>
              <div>
                <label for="auto-filled-scribe-id" class="block text-sm font-medium text-gray-700 mb-1">Selected Scribe
                  IDs</label>
                <textarea id="auto-filled-scribe-id" name="scribe_ids" rows="3"
                  class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm bg-white"
                  readonly></textarea>
              </div>
            </div>
          </div>
        </div>

        <!-- Improved Action Buttons Layout -->
        <div class="px-6 py-4 bg-gray-50 flex items-center">
          <div class="flex-grow">
            <button type="button" id="validateButton"
              class="inline-flex items-center px-5 py-2.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              Validate Selection
            </button>
          </div>
          <div>
            <button type="submit" id="nextButton"
              class="inline-flex items-center px-5 py-2.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors">
              Next
              <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </button>
          </div>
        </div>
      </form>
    </div>

    <!-- Improved Navigation Buttons -->
    <div class="mt-8 flex justify-between">
      <div>
        <a href="{{ url_for('select_lot') }}"
          class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
          Back to Lot Selection
        </a>
      </div>
    </div>
  </div>
</div>
{% endblock %}
{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script src="{{ url_for('static', filename='js/wafer_details.js') }}"></script>

{% endblock %}
{% extends "base.html" %}

{% block title %}UX Enhancements Demo{% endblock %}

{% block breadcrumb %}
<li class="flex items-center">
    <span class="mx-2"><i class="fas fa-chevron-right text-gray-400"></i></span>
    <a href="{{ url_for('settings') }}" class="hover:text-blue-600 transition-colors">
        <i class="fas fa-cog mr-1"></i>Settings
    </a>
</li>
<li class="flex items-center">
    <span class="mx-2"><i class="fas fa-chevron-right text-gray-400"></i></span>
    <span class="text-gray-900 font-medium flex items-center">
        <i class="fas fa-magic mr-1"></i>UX Demo
    </span>
</li>
{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-4">
            <i class="fas fa-magic mr-2 text-blue-600"></i>
            UX Enhancements Demo
        </h1>
        <p class="text-gray-600 mb-6">
            This page demonstrates the new User Experience enhancements implemented in Talaria Dashboard.
        </p>
    </div>

    <!-- Breadcrumb Demo -->
    <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">
            <i class="fas fa-map-signs mr-2 text-green-600"></i>
            Breadcrumb Navigation
        </h2>
        <p class="text-gray-600 mb-4">
            Notice the breadcrumb navigation at the top of the page showing: Dashboard → Settings → UX Demo
        </p>
        <div class="bg-gray-50 p-4 rounded-lg">
            <p class="text-sm text-gray-700">
                <strong>Features:</strong> Dynamic generation, clickable intermediate levels, icon support, mobile responsive
            </p>
        </div>
    </div>

    <!-- Progress Indicator Demo -->
    <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">
            <i class="fas fa-tasks mr-2 text-blue-600"></i>
            Progress Indicators
        </h2>
        <p class="text-gray-600 mb-4">
            Click the button below to see a progress indicator in action:
        </p>
        <button id="demo-progress" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            <i class="fas fa-play mr-2"></i>
            Demo Progress Indicator
        </button>
        <div class="bg-gray-50 p-4 rounded-lg mt-4">
            <p class="text-sm text-gray-700">
                <strong>Features:</strong> Multi-step visualization, error handling, completion states, automatic cleanup
            </p>
        </div>
    </div>

    <!-- Keyboard Shortcuts Demo -->
    <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">
            <i class="fas fa-keyboard mr-2 text-purple-600"></i>
            Keyboard Shortcuts
        </h2>
        <p class="text-gray-600 mb-4">
            Press <kbd class="px-2 py-1 bg-gray-100 rounded text-sm">?</kbd> or 
            <kbd class="px-2 py-1 bg-gray-100 rounded text-sm">Shift + /</kbd> to see all available shortcuts.
        </p>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
                <h4 class="font-medium mb-2">Quick Navigation</h4>
                <div class="space-y-1 text-sm">
                    <div class="flex justify-between">
                        <span>Dashboard</span>
                        <kbd class="px-2 py-1 bg-gray-100 rounded">Alt + H</kbd>
                    </div>
                    <div class="flex justify-between">
                        <span>Inventory</span>
                        <kbd class="px-2 py-1 bg-gray-100 rounded">Alt + I</kbd>
                    </div>
                    <div class="flex justify-between">
                        <span>Generate Labels</span>
                        <kbd class="px-2 py-1 bg-gray-100 rounded">Alt + L</kbd>
                    </div>
                </div>
            </div>
            <div>
                <h4 class="font-medium mb-2">Quick Actions</h4>
                <div class="space-y-1 text-sm">
                    <div class="flex justify-between">
                        <span>Search</span>
                        <kbd class="px-2 py-1 bg-gray-100 rounded">Ctrl + K</kbd>
                    </div>
                    <div class="flex justify-between">
                        <span>Save Form</span>
                        <kbd class="px-2 py-1 bg-gray-100 rounded">Ctrl + S</kbd>
                    </div>
                    <div class="flex justify-between">
                        <span>Toggle Sidebar</span>
                        <kbd class="px-2 py-1 bg-gray-100 rounded">Ctrl + B</kbd>
                    </div>
                </div>
            </div>
        </div>
        <div class="bg-gray-50 p-4 rounded-lg">
            <p class="text-sm text-gray-700">
                <strong>Features:</strong> Power user shortcuts, context-aware actions, help modal, smart input detection
            </p>
        </div>
    </div>

    <!-- Offline Mode Demo -->
    <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">
            <i class="fas fa-wifi mr-2 text-orange-600"></i>
            Offline Mode
        </h2>
        <p class="text-gray-600 mb-4">
            Check the connection status indicator in the top navigation bar. Try disconnecting your internet to see offline mode in action.
        </p>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
                <h4 class="font-medium mb-2">Online Features</h4>
                <ul class="text-sm text-gray-700 space-y-1">
                    <li><i class="fas fa-check text-green-600 mr-2"></i>Full functionality</li>
                    <li><i class="fas fa-check text-green-600 mr-2"></i>Real-time data</li>
                    <li><i class="fas fa-check text-green-600 mr-2"></i>Immediate sync</li>
                    <li><i class="fas fa-check text-green-600 mr-2"></i>All operations</li>
                </ul>
            </div>
            <div>
                <h4 class="font-medium mb-2">Offline Features</h4>
                <ul class="text-sm text-gray-700 space-y-1">
                    <li><i class="fas fa-check text-orange-600 mr-2"></i>Cached data viewing</li>
                    <li><i class="fas fa-check text-orange-600 mr-2"></i>Basic search</li>
                    <li><i class="fas fa-check text-orange-600 mr-2"></i>Action queuing</li>
                    <li><i class="fas fa-check text-orange-600 mr-2"></i>Auto-sync when online</li>
                </ul>
            </div>
        </div>
        <div class="bg-gray-50 p-4 rounded-lg">
            <p class="text-sm text-gray-700">
                <strong>Features:</strong> IndexedDB storage, action queuing, connection monitoring, graceful degradation
            </p>
        </div>
    </div>

    <!-- Test Form for Keyboard Shortcuts -->
    <div class="bg-white rounded-lg shadow-sm border p-6">
        <h2 class="text-xl font-semibold mb-4">
            <i class="fas fa-vial mr-2 text-indigo-600"></i>
            Test Form (Keyboard Shortcuts)
        </h2>
        <p class="text-gray-600 mb-4">
            Use this form to test keyboard shortcuts like <kbd class="px-2 py-1 bg-gray-100 rounded text-sm">Ctrl + S</kbd> to save.
        </p>
        <form id="test-form" class="space-y-4">
            <div>
                <label for="test-input" class="block text-sm font-medium text-gray-700 mb-1">Test Input</label>
                <input type="text" id="test-input" name="test-input" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       placeholder="Type something and press Ctrl+S to save">
            </div>
            <div>
                <label for="test-textarea" class="block text-sm font-medium text-gray-700 mb-1">Test Textarea</label>
                <textarea id="test-textarea" name="test-textarea" rows="3"
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Test keyboard shortcuts here"></textarea>
            </div>
            <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                <i class="fas fa-save mr-2"></i>
                Save Form (Ctrl+S)
            </button>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Demo progress indicator
    document.getElementById('demo-progress').addEventListener('click', function() {
        const progress = new ProgressIndicator();
        const steps = [
            'Initializing demo',
            'Loading components',
            'Processing data',
            'Finalizing demo'
        ];
        
        progress.init('Demo Progress', steps);
        
        // Simulate progress
        setTimeout(() => progress.nextStep(), 1000);
        setTimeout(() => progress.nextStep(), 2000);
        setTimeout(() => progress.nextStep(), 3000);
        setTimeout(() => progress.complete('Demo completed!'), 4000);
    });
    
    // Test form submission
    document.getElementById('test-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (window.Swal) {
            Swal.fire({
                icon: 'success',
                title: 'Form Saved!',
                text: 'This demonstrates the Ctrl+S keyboard shortcut functionality.',
                timer: 2000,
                showConfirmButton: false
            });
        } else {
            alert('Form saved! This demonstrates the Ctrl+S keyboard shortcut functionality.');
        }
    });
});
</script>
{% endblock %}

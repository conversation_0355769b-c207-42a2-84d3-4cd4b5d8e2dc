<!-- skeleton.html  -->
<!-- This file contains the skeleton loading templates that can be included in the home.html layout -->

<!-- Skeleton for main stats cards -->
{% macro stat_skeleton() %}
<div class="animate-pulse">
    <div class="h-8 w-16 bg-gray-200 dark:bg-gray-700 rounded mx-auto mb-2"></div>
    <div class="h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded mx-auto"></div>
</div>
{% endmacro %}

<!-- Skeleton for metric cards (with progress bar) -->
{% macro metric_skeleton() %}
<div class="animate-pulse">
    <div class="h-10 w-24 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
    <div class="h-4 w-16 bg-gray-200 dark:bg-gray-700 rounded text-right mb-2 ml-auto"></div>
    <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded-full mb-2"></div>
    <div class="h-4 w-32 bg-gray-200 dark:bg-gray-700 rounded mt-2"></div>
</div>
{% endmacro %}

<!-- Skeleton for donut chart -->
{% macro donut_chart_skeleton() %}
<div class="animate-pulse flex flex-col items-center">
    <div class="relative w-48 h-48 rounded-full bg-gray-200 dark:bg-gray-700 mb-4">
        <div class="absolute top-1/4 left-1/4 w-1/2 h-1/2 bg-white dark:bg-gray-800 rounded-full"></div>
    </div>
    <div class="grid grid-cols-2 gap-2 w-full max-w-sm">
        {% for i in range(6) %}
        <div class="flex items-center">
            <div class="w-3 h-3 rounded-full bg-gray-200 dark:bg-gray-700 mr-2"></div>
            <div class="h-4 w-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
        {% endfor %}
    </div>
</div>
{% endmacro %}

<!-- Skeleton for bar/line chart -->
{% macro bar_chart_skeleton() %}
<div class="animate-pulse">
    <div class="flex justify-center gap-4 mb-4">
        {% for i in range(3) %}
        <div class="h-4 w-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
        {% endfor %}
    </div>
    <div class="grid grid-cols-12 gap-1 h-40 items-end">
        {% for i in range(12) %}
        {% set height_classes = ['h-6', 'h-8', 'h-10', 'h-12', 'h-16', 'h-20', 'h-24', 'h-28', 'h-32', 'h-36', 'h-40',
        'h-44'] %}
        {% set index = i % 12 %}
        <div class="bg-gray-200 dark:bg-gray-700 rounded-t w-full {{ height_classes[index] }}"></div>
        {% endfor %}
    </div>
    <div class="mt-2 grid grid-cols-12 gap-1">
        {% for i in range(12) %}
        <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
        {% endfor %}
    </div>
</div>
{% endmacro %}

<!-- Skeleton for table rows -->
{% macro table_skeleton(rows=5) %}
<div class="animate-pulse">
    {% for i in range(rows) %}
    <div class="border-b border-gray-200 dark:border-gray-700 py-3 flex">
        <div class="w-16 h-4 bg-gray-200 dark:bg-gray-700 rounded mr-4"></div>
        <div class="w-20 h-6 bg-gray-200 dark:bg-gray-700 rounded mr-4"></div>
        <div class="w-24 h-4 bg-gray-200 dark:bg-gray-700 rounded mr-4"></div>
        <div class="w-20 h-4 bg-gray-200 dark:bg-gray-700 rounded mr-4"></div>
        <div class="w-8 h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
    </div>
    {% endfor %}
</div>
{% endmacro %}
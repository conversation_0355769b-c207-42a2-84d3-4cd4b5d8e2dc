{% extends "base.html" %} {% block title %}Workflow Automation - Talaria
Dashboard{% endblock %} {% block extra_css %}
<style>
  /* Workflow Automation Styles */
  .workflow-card {
    @apply bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 transition-all duration-200 hover:shadow-xl;
  }

  .workflow-card:hover {
    transform: translateY(-2px);
  }

  .workflow-status {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium;
  }

  .workflow-status.enabled {
    @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
  }

  .workflow-status.disabled {
    @apply bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200;
  }

  .trigger-badge {
    @apply inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-lg text-xs font-medium mr-2 mb-2;
  }

  .action-badge {
    @apply inline-flex items-center px-2 py-1 bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 rounded-lg text-xs font-medium mr-2 mb-2;
  }

  .stats-card {
    @apply bg-gradient-to-r rounded-2xl p-6 text-white shadow-lg;
  }

  .execution-log {
    @apply bg-gray-50 dark:bg-gray-900 rounded-xl p-4 border border-gray-200 dark:border-gray-700;
  }

  .execution-success {
    @apply text-green-600 dark:text-green-400;
  }

  .execution-failed {
    @apply text-red-600 dark:text-red-400;
  }

  /* Tutorial Modal Styling */
  .tutorial-popup {
    border-radius: 16px !important;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
  }

  .tutorial-close-btn {
    background: #ef4444 !important; /* Red background */
    color: #ffffff !important; /* White X icon */
    border-radius: 50% !important;
    width: 32px !important;
    height: 32px !important;
    font-size: 18px !important;
    font-weight: bold !important;
    transition: all 0.2s ease !important;
    box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3) !important; /* Red shadow for emphasis */
  }

  .tutorial-close-btn:hover {
    background: #dc2626 !important; /* Darker red on hover */
    color: #ffffff !important;
    transform: scale(1.15) !important; /* Slightly larger scale on hover */
    box-shadow: 0 4px 8px rgba(220, 38, 38, 0.4) !important; /* Enhanced shadow on hover */
  }

  .tutorial-popup .swal2-title {
    font-size: 1.5rem !important;
    font-weight: 600 !important;
    color: #1f2937 !important;
  }

  .tutorial-popup .swal2-content {
    font-size: 1rem !important;
    line-height: 1.6 !important;
  }
</style>
{% endblock %} {% block content %}
<div class="container mx-auto px-4 py-8">
  <!-- Header -->
  <div class="flex justify-between items-center mb-8">
    <div>
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
        <i class="fas fa-robot mr-3 text-blue-500"></i>
        Workflow Automation
      </h1>
      <p class="text-gray-600 dark:text-gray-400 mt-2">
        Automate your wafer inventory management with smart workflows
      </p>
    </div>

    <div class="flex space-x-3">
      {% if session.get('user_role') == 'admin' %}
      <button
        id="tutorial-btn"
        class="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-2xl shadow-lg transition-all duration-200 hover:scale-105"
      >
        <i class="fas fa-graduation-cap mr-2"></i>
        Interactive Tutorial
      </button>

      <button
        id="create-workflow-btn"
        class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-2xl shadow-lg transition-all duration-200 hover:scale-105"
      >
        <i class="fas fa-plus mr-2"></i>
        Create Workflow
      </button>

      <button
        id="automation-toggle"
        class="px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-2xl shadow-lg transition-all duration-200 hover:scale-105"
      >
        <i class="fas fa-power-off mr-2"></i>
        <span id="automation-status">Enabled</span>
      </button>
      {% else %}
      <button
        onclick="showAdminOnlyMessage()"
        class="px-6 py-3 bg-gray-400 cursor-not-allowed text-white rounded-2xl shadow-lg"
        disabled
      >
        <i class="fas fa-lock mr-2"></i>
        Tutorial (Admin Only)
      </button>

      <button
        onclick="showAdminOnlyMessage()"
        class="px-6 py-3 bg-gray-400 cursor-not-allowed text-white rounded-2xl shadow-lg"
        disabled
      >
        <i class="fas fa-lock mr-2"></i>
        Create Workflow (Admin Only)
      </button>

      <button
        onclick="showAdminOnlyMessage()"
        class="px-6 py-3 bg-gray-400 cursor-not-allowed text-white rounded-2xl shadow-lg"
        disabled
      >
        <i class="fas fa-lock mr-2"></i>
        Automation (Admin Only)
      </button>
      {% endif %}
    </div>
  </div>

  {% if session.get('user_role') != 'admin' %}
  <!-- Admin Only Notice -->
  <div class="mb-8">
    <div class="bg-amber-50 border border-amber-200 rounded-lg p-4">
      <div class="flex items-center">
        <i class="fas fa-shield-alt text-amber-600 mr-3"></i>
        <div class="text-left">
          <h3 class="text-lg font-semibold text-amber-800">
            Admin Access Required
          </h3>
          <p class="text-sm text-amber-700 mt-1">
            Workflow Automation features are restricted to administrators only.
            You can view this page but cannot access automation functions.
          </p>
        </div>
      </div>
    </div>
  </div>
  {% endif %}

  <!-- Enhanced Tutorial Guide Section -->
  <div id="tutorial-guide" class="hidden mb-8 bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 dark:from-purple-900 dark:via-blue-900 dark:to-indigo-900 rounded-2xl shadow-xl border border-purple-200 dark:border-purple-700">
    <!-- Tutorial Header -->
    <div class="p-6 border-b border-purple-200 dark:border-purple-700">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="p-3 bg-purple-100 dark:bg-purple-800 rounded-full">
            <i class="fas fa-graduation-cap text-2xl text-purple-600 dark:text-purple-300"></i>
          </div>
          <div>
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
              Workflow Automation Tutorial
            </h2>
            <p class="text-gray-600 dark:text-gray-300">
              Master workflow automation with our step-by-step interactive guide
            </p>
          </div>
        </div>
        <div class="flex items-center space-x-3">
          <div class="flex items-center space-x-2 bg-white dark:bg-gray-800 rounded-lg px-3 py-2">
            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300" id="tutorial-progress">Step 1 of 5</span>
          </div>
          <button id="close-tutorial" class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
            <i class="fas fa-times text-xl"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Tutorial Navigation -->
    <div class="px-6 py-4 bg-white/50 dark:bg-gray-800/50">
      <div class="flex items-center space-x-2">
        <button id="tutorial-prev" class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 disabled:opacity-50" disabled>
          <i class="fas fa-chevron-left mr-2"></i>Previous
        </button>
        <div class="flex-1">
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div id="tutorial-progress-bar" class="bg-purple-600 h-2 rounded-full transition-all duration-300" style="width: 20%"></div>
          </div>
        </div>
        <button id="tutorial-next" class="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg">
          Next<i class="fas fa-chevron-right ml-2"></i>
        </button>
      </div>
    </div>

    <!-- Tutorial Content Steps -->
    <div class="p-6">
      <!-- Step 1: Introduction -->
      <div id="tutorial-step-1" class="tutorial-step">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              <span class="inline-flex items-center justify-center w-8 h-8 bg-purple-100 dark:bg-purple-800 text-purple-600 dark:text-purple-300 rounded-full text-sm font-bold mr-3">1</span>
              Welcome to Workflow Automation
            </h3>
            <div class="space-y-4 text-gray-700 dark:text-gray-300">
              <p class="text-lg">
                🎯 <strong>What you'll learn:</strong> How to automate repetitive wafer inventory management tasks using our powerful workflow system.
              </p>
              <div class="bg-blue-50 dark:bg-blue-900/50 rounded-lg p-4">
                <h4 class="font-semibold text-blue-800 dark:text-blue-200 mb-2">Before we start:</h4>
                <ul class="space-y-2 text-sm text-blue-700 dark:text-blue-300">
                  <li>✅ You need admin privileges to create workflows</li>
                  <li>✅ Have your Asana account connected</li>
                  <li>✅ Understand your wafer inventory process</li>
                  <li>✅ Know which tasks you want to automate</li>
                </ul>
              </div>
              <div class="bg-green-50 dark:bg-green-900/50 rounded-lg p-4">
                <h4 class="font-semibold text-green-800 dark:text-green-200 mb-2">Common automation scenarios:</h4>
                <ul class="space-y-1 text-sm text-green-700 dark:text-green-300">
                  <li>• Auto-generate labels when Asana tasks are created</li>
                  <li>• Send notifications for low stock levels</li>
                  <li>• Sync wafer data with external systems</li>
                  <li>• Generate periodic inventory reports</li>
                </ul>
              </div>
            </div>
          </div>
          <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
            <h4 class="font-semibold text-gray-900 dark:text-white mb-4">🔄 Workflow Process Overview</h4>
            <div class="space-y-4">
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center">
                  <span class="text-sm font-bold text-blue-600 dark:text-blue-300">1</span>
                </div>
                <div>
                  <p class="font-medium text-gray-900 dark:text-white">Trigger Event</p>
                  <p class="text-xs text-gray-500 dark:text-gray-400">Something happens (e.g., new Asana task)</p>
                </div>
              </div>
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center">
                  <span class="text-sm font-bold text-purple-600 dark:text-purple-300">2</span>
                </div>
                <div>
                  <p class="font-medium text-gray-900 dark:text-white">Conditions Check</p>
                  <p class="text-xs text-gray-500 dark:text-gray-400">System validates if workflow should run</p>
                </div>
              </div>
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-green-100 dark:bg-green-800 rounded-full flex items-center justify-center">
                  <span class="text-sm font-bold text-green-600 dark:text-green-300">3</span>
                </div>
                <div>
                  <p class="font-medium text-gray-900 dark:text-white">Actions Execute</p>
                  <p class="text-xs text-gray-500 dark:text-gray-400">Automated tasks are performed</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 2: Understanding Triggers -->
      <div id="tutorial-step-2" class="tutorial-step hidden">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              <span class="inline-flex items-center justify-center w-8 h-8 bg-purple-100 dark:bg-purple-800 text-purple-600 dark:text-purple-300 rounded-full text-sm font-bold mr-3">2</span>
              Understanding Workflow Triggers
            </h3>
            <div class="space-y-4 text-gray-700 dark:text-gray-300">
              <p>
                Triggers are events that start your workflows. Think of them as the "when" in "when this happens, do that."
              </p>
              
              <div class="space-y-4">
                <div class="border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                  <div class="flex items-center mb-2">
                    <i class="fas fa-tasks text-blue-600 mr-2"></i>
                    <h4 class="font-semibold text-blue-800 dark:text-blue-200">Asana Integration Triggers</h4>
                  </div>
                  <ul class="text-sm space-y-1 text-blue-700 dark:text-blue-300">
                    <li>• New task created with wafer information</li>
                    <li>• Task marked as complete</li>
                    <li>• Task assigned to specific team member</li>
                    <li>• Custom field values updated</li>
                  </ul>
                </div>

                <div class="border border-green-200 dark:border-green-800 rounded-lg p-4">
                  <div class="flex items-center mb-2">
                    <i class="fas fa-clock text-green-600 mr-2"></i>
                    <h4 class="font-semibold text-green-800 dark:text-green-200">Time-based Triggers</h4>
                  </div>
                  <ul class="text-sm space-y-1 text-green-700 dark:text-green-300">
                    <li>• Every hour/day/week</li>
                    <li>• Specific dates and times</li>
                    <li>• Business hours only</li>
                    <li>• After X days of inactivity</li>
                  </ul>
                </div>

                <div class="border border-purple-200 dark:border-purple-800 rounded-lg p-4">
                  <div class="flex items-center mb-2">
                    <i class="fas fa-database text-purple-600 mr-2"></i>
                    <h4 class="font-semibold text-purple-800 dark:text-purple-200">Inventory Triggers</h4>
                  </div>
                  <ul class="text-sm space-y-1 text-purple-700 dark:text-purple-300">
                    <li>• Stock level falls below threshold</li>
                    <li>• New wafer batch received</li>
                    <li>• Quality control status changes</li>
                    <li>• Location transfer completed</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
            <h4 class="font-semibold text-gray-900 dark:text-white mb-4">💡 Pro Tips</h4>
            <div class="space-y-4">
              <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
                <h5 class="font-medium text-yellow-800 dark:text-yellow-200 mb-1">Choose the Right Trigger</h5>
                <p class="text-sm text-yellow-700 dark:text-yellow-300">
                  Start with simple triggers like "new Asana task" before moving to complex conditions.
                </p>
              </div>
              <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
                <h5 class="font-medium text-blue-800 dark:text-blue-200 mb-1">Test First</h5>
                <p class="text-sm text-blue-700 dark:text-blue-300">
                  Always test your triggers with sample data before enabling them in production.
                </p>
              </div>
              <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3">
                <h5 class="font-medium text-green-800 dark:text-green-200 mb-1">Monitor Performance</h5>
                <p class="text-sm text-green-700 dark:text-green-300">
                  Use the execution log to monitor how often your triggers fire and their success rate.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 3: Creating Your First Workflow -->
      <div id="tutorial-step-3" class="tutorial-step hidden">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              <span class="inline-flex items-center justify-center w-8 h-8 bg-purple-100 dark:bg-purple-800 text-purple-600 dark:text-purple-300 rounded-full text-sm font-bold mr-3">3</span>
              Creating Your First Workflow
            </h3>
            <div class="space-y-4 text-gray-700 dark:text-gray-300">
              <p>
                Let's create a simple workflow that automatically generates wafer labels when a new Asana task is created.
              </p>
              
              <div class="bg-indigo-50 dark:bg-indigo-900/50 rounded-lg p-4">
                <h4 class="font-semibold text-indigo-800 dark:text-indigo-200 mb-3">📋 Step-by-Step Instructions:</h4>
                <ol class="space-y-3 text-sm text-indigo-700 dark:text-indigo-300">
                  <li class="flex items-start">
                    <span class="inline-flex items-center justify-center w-6 h-6 bg-indigo-100 dark:bg-indigo-800 text-indigo-600 dark:text-indigo-300 rounded-full text-xs font-bold mr-3 mt-0.5">1</span>
                    <div>
                      <strong>Choose a Template:</strong> Click "Browse Templates" and select "Asana to Label Generation"
                    </div>
                  </li>
                  <li class="flex items-start">
                    <span class="inline-flex items-center justify-center w-6 h-6 bg-indigo-100 dark:bg-indigo-800 text-indigo-600 dark:text-indigo-300 rounded-full text-xs font-bold mr-3 mt-0.5">2</span>
                    <div>
                      <strong>Configure Trigger:</strong> Set it to watch for new tasks in your wafer project
                    </div>
                  </li>
                  <li class="flex items-start">
                    <span class="inline-flex items-center justify-center w-6 h-6 bg-indigo-100 dark:bg-indigo-800 text-indigo-600 dark:text-indigo-300 rounded-full text-xs font-bold mr-3 mt-0.5">3</span>
                    <div>
                      <strong>Set Conditions:</strong> Only trigger for tasks with "wafer" in the title
                    </div>
                  </li>
                  <li class="flex items-start">
                    <span class="inline-flex items-center justify-center w-6 h-6 bg-indigo-100 dark:bg-indigo-800 text-indigo-600 dark:text-indigo-300 rounded-full text-xs font-bold mr-3 mt-0.5">4</span>
                    <div>
                      <strong>Configure Actions:</strong> Set up label generation with your preferred template
                    </div>
                  </li>
                  <li class="flex items-start">
                    <span class="inline-flex items-center justify-center w-6 h-6 bg-indigo-100 dark:bg-indigo-800 text-indigo-600 dark:text-indigo-300 rounded-full text-xs font-bold mr-3 mt-0.5">5</span>
                    <div>
                      <strong>Test & Enable:</strong> Run a test with sample data, then enable the workflow
                    </div>
                  </li>
                </ol>
              </div>

              <div class="bg-red-50 dark:bg-red-900/50 rounded-lg p-4">
                <h4 class="font-semibold text-red-800 dark:text-red-200 mb-2">⚠️ Common Mistakes to Avoid:</h4>
                <ul class="space-y-1 text-sm text-red-700 dark:text-red-300">
                  <li>• Don't enable workflows without testing first</li>
                  <li>• Avoid overly broad trigger conditions</li>
                  <li>• Don't forget to set proper error handling</li>
                  <li>• Avoid creating duplicate workflows for the same task</li>
                </ul>
              </div>
            </div>
          </div>
          <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
            <h4 class="font-semibold text-gray-900 dark:text-white mb-4">🛠️ Workflow Builder Preview</h4>
            <div class="space-y-4">
              <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4 text-center">
                <i class="fas fa-plus-circle text-3xl text-gray-400 mb-2"></i>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  Interactive workflow builder will appear here when you start creating workflows
                </p>
              </div>
              <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                <h5 class="font-medium text-gray-900 dark:text-white mb-2">Sample Workflow Configuration:</h5>
                <div class="text-xs space-y-1 text-gray-600 dark:text-gray-400">
                  <div><strong>Name:</strong> Auto Label Generator</div>
                  <div><strong>Trigger:</strong> New Asana Task</div>
                  <div><strong>Condition:</strong> Task contains "wafer"</div>
                  <div><strong>Action:</strong> Generate shipping label</div>
                  <div><strong>Status:</strong> <span class="text-green-600">Active</span></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 4: Advanced Features -->
      <div id="tutorial-step-4" class="tutorial-step hidden">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              <span class="inline-flex items-center justify-center w-8 h-8 bg-purple-100 dark:bg-purple-800 text-purple-600 dark:text-purple-300 rounded-full text-sm font-bold mr-3">4</span>
              Advanced Workflow Features
            </h3>
            <div class="space-y-4 text-gray-700 dark:text-gray-300">
              <p>
                Once you're comfortable with basic workflows, these advanced features will help you create more sophisticated automations.
              </p>
              
              <div class="space-y-4">
                <div class="border border-orange-200 dark:border-orange-800 rounded-lg p-4">
                  <div class="flex items-center mb-2">
                    <i class="fas fa-code-branch text-orange-600 mr-2"></i>
                    <h4 class="font-semibold text-orange-800 dark:text-orange-200">Conditional Logic</h4>
                  </div>
                  <p class="text-sm text-orange-700 dark:text-orange-300 mb-2">
                    Create "if-then-else" logic to handle different scenarios:
                  </p>
                  <ul class="text-sm space-y-1 text-orange-700 dark:text-orange-300">
                    <li>• If wafer count > 100, send to Team A, else Team B</li>
                    <li>• If priority = "urgent", notify manager immediately</li>
                    <li>• If substrate type = "silicon", use specific template</li>
                  </ul>
                </div>

                <div class="border border-teal-200 dark:border-teal-800 rounded-lg p-4">
                  <div class="flex items-center mb-2">
                    <i class="fas fa-link text-teal-600 mr-2"></i>
                    <h4 class="font-semibold text-teal-800 dark:text-teal-200">Workflow Chaining</h4>
                  </div>
                  <p class="text-sm text-teal-700 dark:text-teal-300 mb-2">
                    Connect multiple workflows to create complex processes:
                  </p>
                  <ul class="text-sm space-y-1 text-teal-700 dark:text-teal-300">
                    <li>• Workflow A generates label → triggers Workflow B to send email</li>
                    <li>• Quality check → packaging → shipping notification</li>
                    <li>• Inventory update → stock alert → reorder automation</li>
                  </ul>
                </div>

                <div class="border border-pink-200 dark:border-pink-800 rounded-lg p-4">
                  <div class="flex items-center mb-2">
                    <i class="fas fa-stopwatch text-pink-600 mr-2"></i>
                    <h4 class="font-semibold text-pink-800 dark:text-pink-200">Delays & Scheduling</h4>
                  </div>
                  <p class="text-sm text-pink-700 dark:text-pink-300 mb-2">
                    Add timing controls to your workflows:
                  </p>
                  <ul class="text-sm space-y-1 text-pink-700 dark:text-pink-300">
                    <li>• Wait 2 hours before sending reminder</li>
                    <li>• Run only during business hours</li>
                    <li>• Schedule weekly inventory reports</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
            <h4 class="font-semibold text-gray-900 dark:text-white mb-4">🎯 Best Practices</h4>
            <div class="space-y-4">
              <div class="bg-emerald-50 dark:bg-emerald-900/20 border border-emerald-200 dark:border-emerald-800 rounded-lg p-3">
                <h5 class="font-medium text-emerald-800 dark:text-emerald-200 mb-1">Start Simple</h5>
                <p class="text-sm text-emerald-700 dark:text-emerald-300">
                  Begin with single-action workflows before adding complex logic.
                </p>
              </div>
              <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
                <h5 class="font-medium text-blue-800 dark:text-blue-200 mb-1">Use Descriptive Names</h5>
                <p class="text-sm text-blue-700 dark:text-blue-300">
                  Name workflows clearly: "Auto-Label-High-Priority-Wafers" not "Workflow1"
                </p>
              </div>
              <div class="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-3">
                <h5 class="font-medium text-amber-800 dark:text-amber-200 mb-1">Monitor & Optimize</h5>
                <p class="text-sm text-amber-700 dark:text-amber-300">
                  Regularly check execution logs and adjust workflows based on performance.
                </p>
              </div>
              <div class="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-3">
                <h5 class="font-medium text-purple-800 dark:text-purple-200 mb-1">Document Your Workflows</h5>
                <p class="text-sm text-purple-700 dark:text-purple-300">
                  Add clear descriptions so team members understand what each workflow does.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 5: Monitoring & Troubleshooting -->
      <div id="tutorial-step-5" class="tutorial-step hidden">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              <span class="inline-flex items-center justify-center w-8 h-8 bg-purple-100 dark:bg-purple-800 text-purple-600 dark:text-purple-300 rounded-full text-sm font-bold mr-3">5</span>
              Monitoring & Troubleshooting
            </h3>
            <div class="space-y-4 text-gray-700 dark:text-gray-300">
              <p>
                Learn how to monitor your workflows and troubleshoot common issues to ensure reliable automation.
              </p>
              
              <div class="bg-blue-50 dark:bg-blue-900/50 rounded-lg p-4">
                <h4 class="font-semibold text-blue-800 dark:text-blue-200 mb-3">📊 Monitoring Tools:</h4>
                <ul class="space-y-2 text-sm text-blue-700 dark:text-blue-300">
                  <li class="flex items-start">
                    <i class="fas fa-chart-line text-blue-600 mr-2 mt-0.5"></i>
                    <div>
                      <strong>Execution Dashboard:</strong> View real-time workflow performance and success rates
                    </div>
                  </li>
                  <li class="flex items-start">
                    <i class="fas fa-history text-blue-600 mr-2 mt-0.5"></i>
                    <div>
                      <strong>Execution Log:</strong> Detailed history of every workflow run with timestamps and outcomes
                    </div>
                  </li>
                  <li class="flex items-start">
                    <i class="fas fa-bell text-blue-600 mr-2 mt-0.5"></i>
                    <div>
                      <strong>Alerts System:</strong> Get notified when workflows fail or need attention
                    </div>
                  </li>
                  <li class="flex items-start">
                    <i class="fas fa-tachometer-alt text-blue-600 mr-2 mt-0.5"></i>
                    <div>
                      <strong>Performance Metrics:</strong> Track execution times and resource usage
                    </div>
                  </li>
                </ul>
              </div>

              <div class="bg-red-50 dark:bg-red-900/50 rounded-lg p-4">
                <h4 class="font-semibold text-red-800 dark:text-red-200 mb-3">🔧 Common Issues & Solutions:</h4>
                <div class="space-y-3 text-sm text-red-700 dark:text-red-300">
                  <div>
                    <strong>Issue:</strong> Workflow not triggering
                    <br>
                    <strong>Solution:</strong> Check trigger conditions and Asana connection status
                  </div>
                  <div>
                    <strong>Issue:</strong> Actions failing silently
                    <br>
                    <strong>Solution:</strong> Enable debug mode and check execution logs
                  </div>
                  <div>
                    <strong>Issue:</strong> Too many executions
                    <br>
                    <strong>Solution:</strong> Add more specific trigger conditions or cooldown periods
                  </div>
                  <div>
                    <strong>Issue:</strong> Email notifications not sending
                    <br>
                    <strong>Solution:</strong> Verify email settings and recipient addresses
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
            <h4 class="font-semibold text-gray-900 dark:text-white mb-4">✅ Tutorial Complete!</h4>
            <div class="space-y-4">
              <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                <h5 class="font-medium text-green-800 dark:text-green-200 mb-2">🎉 Congratulations!</h5>
                <p class="text-sm text-green-700 dark:text-green-300 mb-3">
                  You've completed the Workflow Automation tutorial. You now know how to:
                </p>
                <ul class="text-sm space-y-1 text-green-700 dark:text-green-300">
                  <li>✓ Understand workflow triggers and actions</li>
                  <li>✓ Create and configure workflows</li>
                  <li>✓ Use advanced features and conditions</li>
                  <li>✓ Monitor and troubleshoot workflows</li>
                </ul>
              </div>
              
              <div class="space-y-3">
                <h5 class="font-medium text-gray-900 dark:text-white">Next Steps:</h5>
                <div class="grid grid-cols-1 gap-2">
                  <button onclick="window.safeWorkflowCall('createWorkflow')" class="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm">
                    <i class="fas fa-plus mr-2"></i>Create Your First Workflow
                  </button>
                  <button onclick="window.safeWorkflowCall('showTemplates')" class="w-full px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg text-sm">
                    <i class="fas fa-template mr-2"></i>Browse Templates
                  </button>
                  <button id="tutorial-restart" class="w-full px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg text-sm">
                    <i class="fas fa-redo mr-2"></i>Restart Tutorial
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Start Guide (shown when no workflows exist) -->
  <div
    id="quick-start-guide"
    class="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900 dark:to-purple-900 rounded-2xl p-8 mb-8 hidden"
  >
    <div class="flex items-start space-x-6">
      <div class="flex-shrink-0">
        <div
          class="w-16 h-16 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center"
        >
          <i class="fas fa-magic text-2xl text-blue-600 dark:text-blue-300"></i>
        </div>
      </div>
      <div class="flex-1">
        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">
          Welcome to Workflow Automation!
        </h3>
        <p class="text-gray-600 dark:text-gray-300 mb-4">
          Automate repetitive tasks in your wafer inventory management. Here are
          some popular use cases:
        </p>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
          <div
            class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700"
          >
            <div class="flex items-center mb-2">
              <i class="fas fa-exclamation-triangle text-amber-500 mr-2"></i>
              <span class="font-medium text-gray-900 dark:text-white"
                >Low Stock Alerts</span
              >
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              Get notified when wafer quantities drop below threshold
            </p>
          </div>
          <div
            class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700"
          >
            <div class="flex items-center mb-2">
              <i class="fas fa-sync-alt text-blue-500 mr-2"></i>
              <span class="font-medium text-gray-900 dark:text-white"
                >Icarium Sync</span
              >
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              Auto-sync wafer data with Icarium system
            </p>
          </div>
          <div
            class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700"
          >
            <div class="flex items-center mb-2">
              <i class="fas fa-envelope text-green-500 mr-2"></i>
              <span class="font-medium text-gray-900 dark:text-white"
                >Email Reports</span
              >
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              Send daily/weekly inventory reports automatically
            </p>
          </div>
        </div>
        <div class="flex space-x-3">
          {% if session.get('user_role') == 'admin' %}
          <button
            onclick="window.safeWorkflowCall('startTutorial')"
            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm"
          >
            <i class="fas fa-play mr-2"></i>Start Tutorial
          </button>
          <button
            onclick="window.safeWorkflowCall('showTemplates')"
            class="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg text-sm"
          >
            <i class="fas fa-templates mr-2"></i>Browse Templates
          </button>
          {% else %}
          <button
            onclick="showAdminOnlyMessage()"
            class="px-4 py-2 bg-gray-400 cursor-not-allowed text-white rounded-lg text-sm"
            disabled
          >
            <i class="fas fa-lock mr-2"></i>Start Tutorial (Admin Only)
          </button>
          <button
            onclick="showAdminOnlyMessage()"
            class="px-4 py-2 bg-gray-400 cursor-not-allowed text-white rounded-lg text-sm"
            disabled
          >
            <i class="fas fa-lock mr-2"></i>Browse Templates (Admin Only)
          </button>
          {% endif %}
        </div>
      </div>
    </div>
  </div>

  <!-- Statistics Dashboard -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="stats-card from-blue-500 to-blue-600">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-blue-100 text-sm">Total Workflows</p>
          <p class="text-3xl font-bold" id="total-workflows">0</p>
        </div>
        <i class="fas fa-cogs text-4xl text-blue-200"></i>
      </div>
    </div>

    <div class="stats-card from-green-500 to-green-600">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-green-100 text-sm">Active Workflows</p>
          <p class="text-3xl font-bold" id="active-workflows">0</p>
        </div>
        <i class="fas fa-play-circle text-4xl text-green-200"></i>
      </div>
    </div>

    <div class="stats-card from-purple-500 to-purple-600">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-purple-100 text-sm">Total Executions</p>
          <p class="text-3xl font-bold" id="total-executions">0</p>
        </div>
        <i class="fas fa-bolt text-4xl text-purple-200"></i>
      </div>
    </div>

    <div class="stats-card from-amber-500 to-amber-600">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-amber-100 text-sm">Success Rate</p>
          <p class="text-3xl font-bold" id="success-rate">0%</p>
        </div>
        <i class="fas fa-chart-line text-4xl text-amber-200"></i>
      </div>
    </div>
  </div>

  <!-- Workflow Management Tabs -->
  <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg">
    <div class="border-b border-gray-200 dark:border-gray-700">
      <nav class="flex space-x-8 px-6" aria-label="Tabs">
        <button
          class="workflow-tab active py-4 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600 dark:text-blue-400"
          data-tab="workflows"
        >
          <i class="fas fa-list mr-2"></i>
          Workflows
        </button>
        <button
          class="workflow-tab py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          data-tab="templates"
        >
          <i class="fas fa-template mr-2"></i>
          Templates
        </button>
        <button
          class="workflow-tab py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          data-tab="execution-log"
        >
          <i class="fas fa-history mr-2"></i>
          Execution Log
        </button>
        <button
          class="workflow-tab py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          data-tab="settings"
        >
          <i class="fas fa-cog mr-2"></i>
          Settings
        </button>
      </nav>
    </div>

    <!-- Tab Content -->
    <div class="p-6">
      <!-- Workflows Tab -->
      <div id="workflows-tab" class="tab-content">
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            Configured Workflows
          </h2>

          <div class="flex space-x-3">
            <div class="relative">
              <input
                type="text"
                id="workflow-search"
                class="pl-10 pr-4 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                placeholder="Search workflows..."
              />
              <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
            </div>

            <select
              id="workflow-filter"
              class="px-4 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              <option value="all">All Workflows</option>
              <option value="enabled">Enabled Only</option>
              <option value="disabled">Disabled Only</option>
            </select>
          </div>
        </div>

        <div
          id="workflows-container"
          class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6"
        >
          <!-- Workflows will be populated here -->
        </div>

        <!-- Empty State -->
        <div id="workflows-empty" class="text-center py-12 hidden">
          <div
            class="w-24 h-24 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4"
          >
            <i class="fas fa-robot text-3xl text-gray-400"></i>
          </div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No Workflows Yet
          </h3>
          <p class="text-gray-500 dark:text-gray-400 mb-6">
            Create your first workflow to automate inventory management tasks
          </p>
          <button
            onclick="window.safeWorkflowCall('createWorkflow')"
            class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-2xl shadow-lg transition-all duration-200"
          >
            <i class="fas fa-plus mr-2"></i>
            Create First Workflow
          </button>
        </div>
      </div>

      <!-- Templates Tab -->
      <div id="templates-tab" class="tab-content hidden">
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            Workflow Templates for Wafer Management
          </h2>
          <div class="flex space-x-3">
            <select
              id="template-category-filter"
              class="px-4 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              <option value="all">All Categories</option>
              <option value="inventory">Inventory Management</option>
              <option value="quality">Quality Control</option>
              <option value="shipping">Shipping & Logistics</option>
              <option value="integration">System Integration</option>
            </select>
          </div>
        </div>

        <!-- Popular Templates Section -->
        <div class="mb-8">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
            <i class="fas fa-star text-yellow-500 mr-2"></i>
            Most Popular for Wafer Inventory
          </h3>
          <div
            class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6"
            id="popular-templates"
          >
            <!-- Popular templates will be populated here -->
          </div>
        </div>

        <!-- All Templates Section -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
            All Templates
          </h3>
          <div
            id="templates-container"
            class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6"
          >
            <!-- All templates will be populated here -->
          </div>
        </div>

        <!-- Custom Template Creation Hint -->
        <div
          class="mt-8 p-6 bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800 dark:to-blue-900 rounded-xl border border-gray-200 dark:border-gray-700"
        >
          <div class="flex items-center space-x-4">
            <div
              class="w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center"
            >
              <i class="fas fa-lightbulb text-blue-600 dark:text-blue-300"></i>
            </div>
            <div class="flex-1">
              <h4 class="font-medium text-gray-900 dark:text-white mb-1">
                Need a Custom Workflow?
              </h4>
              <p class="text-sm text-gray-600 dark:text-gray-400">
                Can't find what you're looking for? Create a custom workflow
                tailored to your specific needs.
              </p>
            </div>
            <button
              onclick="window.safeWorkflowCall('createCustomWorkflow')"
              class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm"
            >
              <i class="fas fa-plus mr-2"></i>Create Custom
            </button>
          </div>
        </div>
      </div>

      <!-- Execution Log Tab -->
      <div id="execution-log-tab" class="tab-content hidden">
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            Execution History
          </h2>

          <div class="flex space-x-3">
            <select
              id="log-filter"
              class="px-4 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              <option value="all">All Executions</option>
              <option value="success">Successful Only</option>
              <option value="failed">Failed Only</option>
            </select>

            <button
              id="clear-log-btn"
              class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-xl"
            >
              <i class="fas fa-trash mr-2"></i>
              Clear Log
            </button>
          </div>
        </div>

        <div id="execution-log-container" class="space-y-4">
          <!-- Execution log will be populated here -->
        </div>
      </div>

      <!-- Settings Tab -->
      <div id="settings-tab" class="tab-content hidden">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
          Automation Settings
        </h2>

        <div class="space-y-6">
          <div class="bg-gray-50 dark:bg-gray-900 rounded-xl p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
              General Settings
            </h3>

            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <div>
                  <label
                    class="text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Enable Workflow Automation
                  </label>
                  <p class="text-xs text-gray-500 dark:text-gray-400">
                    Master switch for all workflow automation
                  </p>
                </div>
                <input
                  type="checkbox"
                  id="global-automation-toggle"
                  class="toggle-switch"
                />
              </div>

              <div class="flex items-center justify-between">
                <div>
                  <label
                    class="text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Debug Mode
                  </label>
                  <p class="text-xs text-gray-500 dark:text-gray-400">
                    Enable detailed logging for troubleshooting
                  </p>
                </div>
                <input
                  type="checkbox"
                  id="debug-mode-toggle"
                  class="toggle-switch"
                />
              </div>
            </div>
          </div>

          <div class="bg-gray-50 dark:bg-gray-900 rounded-xl p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Notification Settings
            </h3>

            <div class="space-y-4">
              <div>
                <label
                  class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                >
                  Default Email Recipients
                </label>
                <input
                  type="email"
                  id="default-email"
                  class="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label
                  class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                >
                  Notification Frequency
                </label>
                <select
                  id="notification-frequency"
                  class="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                >
                  <option value="immediate">Immediate</option>
                  <option value="hourly">Hourly Digest</option>
                  <option value="daily">Daily Digest</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Include Workflow Automation Scripts -->
<script src="{{ url_for('static', filename='js/workflow-templates.js') }}"></script>
<script src="{{ url_for('static', filename='js/workflow-automation.js') }}"></script>
<script src="{{ url_for('static', filename='js/workflow-manager.js') }}"></script>
{% endblock %} {% block extra_js %}
<script>
  // Admin-only message function
  function showAdminOnlyMessage() {
    if (typeof Swal !== "undefined") {
      Swal.fire({
        title: "Admin Access Required",
        text: "This feature is restricted to administrators only. Please contact your system administrator for access.",
        icon: "warning",
        confirmButtonText: "Understood",
        confirmButtonColor: "#f59e0b",
      });
    } else {
      alert("This feature is restricted to administrators only.");
    }
  }

  // Make function globally accessible
  window.showAdminOnlyMessage = showAdminOnlyMessage;

  // Initialize Workflow Automation
  document.addEventListener("DOMContentLoaded", function () {
    try {
      // Initialize the workflow automation engine
      window.workflowEngine = new WorkflowAutomationEngine();
      window.workflowManager = new WorkflowManager(window.workflowEngine);

      // Make sure workflowManager is globally accessible
      window.workflowManager = window.workflowManager;

      console.log("🤖 Workflow Automation System initialized");
      console.log(
        "✅ workflowManager available globally:",
        typeof window.workflowManager
      );

      // Wait a bit for everything to load, then refresh the workflow list
      setTimeout(() => {
        if (window.workflowManager && window.workflowManager.loadWorkflows) {
          window.workflowManager.loadWorkflows();
        }
      }, 500);
    } catch (error) {
      console.error("❌ Failed to initialize Workflow Automation:", error);
    }
  });

  // Fallback function to ensure workflowManager is available
  function ensureWorkflowManager() {
    if (!window.workflowManager) {
      console.warn(
        "⚠️ workflowManager not found, attempting to reinitialize..."
      );
      try {
        if (!window.workflowEngine) {
          window.workflowEngine = new WorkflowAutomationEngine();
        }
        window.workflowManager = new WorkflowManager(window.workflowEngine);
        console.log("✅ workflowManager reinitialized");
      } catch (error) {
        console.error("❌ Failed to reinitialize workflowManager:", error);
        alert("Workflow system not ready. Please refresh the page.");
        return null;
      }
    }
    return window.workflowManager;
  }

  // Global helper functions for onclick handlers
  window.safeWorkflowCall = function (methodName, ...args) {
    try {
      const manager = window.workflowManager || ensureWorkflowManager();
      if (manager && typeof manager[methodName] === "function") {
        return manager[methodName](...args);
      } else {
        console.error(`❌ Method ${methodName} not found on workflowManager`);
        alert("Workflow function not available. Please refresh the page.");
      }
    } catch (error) {
      console.error(`❌ Error calling ${methodName}:`, error);
      alert("An error occurred. Please refresh the page.");
    }
  };

  // Tutorial System Implementation
  class TutorialSystem {
    constructor() {
      this.currentStep = 1;
      this.totalSteps = 5;
      this.tutorialGuide = document.getElementById('tutorial-guide');
      this.init();
    }

    init() {
      // Tutorial button click handler
      document.getElementById('tutorial-btn')?.addEventListener('click', () => {
        this.showTutorial();
      });

      // Tutorial navigation
      document.getElementById('tutorial-next')?.addEventListener('click', () => {
        this.nextStep();
      });

      document.getElementById('tutorial-prev')?.addEventListener('click', () => {
        this.prevStep();
      });

      document.getElementById('close-tutorial')?.addEventListener('click', () => {
        this.closeTutorial();
      });

      document.getElementById('tutorial-restart')?.addEventListener('click', () => {
        this.restartTutorial();
      });

      // Add startTutorial method to window for onclick access
      window.startTutorial = () => this.showTutorial();
    }

    showTutorial() {
      this.tutorialGuide.classList.remove('hidden');
      this.currentStep = 1;
      this.updateStep();
      
      // Scroll to tutorial
      this.tutorialGuide.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }

    closeTutorial() {
      this.tutorialGuide.classList.add('hidden');
    }

    restartTutorial() {
      this.currentStep = 1;
      this.updateStep();
    }

    nextStep() {
      if (this.currentStep < this.totalSteps) {
        this.currentStep++;
        this.updateStep();
      }
    }

    prevStep() {
      if (this.currentStep > 1) {
        this.currentStep--;
        this.updateStep();
      }
    }

    updateStep() {
      // Hide all steps
      for (let i = 1; i <= this.totalSteps; i++) {
        const step = document.getElementById(`tutorial-step-${i}`);
        if (step) {
          step.classList.add('hidden');
        }
      }

      // Show current step
      const currentStepElement = document.getElementById(`tutorial-step-${this.currentStep}`);
      if (currentStepElement) {
        currentStepElement.classList.remove('hidden');
      }

      // Update progress
      const progressText = document.getElementById('tutorial-progress');
      const progressBar = document.getElementById('tutorial-progress-bar');
      const prevBtn = document.getElementById('tutorial-prev');
      const nextBtn = document.getElementById('tutorial-next');

      if (progressText) {
        progressText.textContent = `Step ${this.currentStep} of ${this.totalSteps}`;
      }

      if (progressBar) {
        const progressPercent = (this.currentStep / this.totalSteps) * 100;
        progressBar.style.width = `${progressPercent}%`;
      }

      // Update button states
      if (prevBtn) {
        prevBtn.disabled = this.currentStep === 1;
        prevBtn.classList.toggle('opacity-50', this.currentStep === 1);
      }

      if (nextBtn) {
        if (this.currentStep === this.totalSteps) {
          nextBtn.textContent = 'Finish Tutorial';
          nextBtn.innerHTML = 'Finish Tutorial<i class="fas fa-check ml-2"></i>';
          nextBtn.onclick = () => this.finishTutorial();
        } else {
          nextBtn.innerHTML = 'Next<i class="fas fa-chevron-right ml-2"></i>';
          nextBtn.onclick = () => this.nextStep();
        }
      }
    }

    finishTutorial() {
      // Show completion message
      if (typeof Swal !== 'undefined') {
        Swal.fire({
          title: '🎉 Tutorial Complete!',
          text: 'You\'ve successfully completed the Workflow Automation tutorial. Ready to create your first workflow?',
          icon: 'success',
          showCancelButton: true,
          confirmButtonText: '<i class="fas fa-plus mr-2"></i>Create Workflow',
          cancelButtonText: 'Close Tutorial',
          confirmButtonColor: '#3b82f6',
          cancelButtonColor: '#6b7280',
        }).then((result) => {
          if (result.isConfirmed) {
            this.closeTutorial();
            window.safeWorkflowCall('createWorkflow');
          } else {
            this.closeTutorial();
          }
        });
      } else {
        const createWorkflow = confirm('Tutorial complete! Would you like to create your first workflow now?');
        this.closeTutorial();
        if (createWorkflow) {
          window.safeWorkflowCall('createWorkflow');
        }
      }
    }
  }

  // Initialize tutorial system
  document.addEventListener('DOMContentLoaded', function() {
    window.tutorialSystem = new TutorialSystem();
  });
</script>
{% endblock %}

{% extends "base.html" %} {% block title %}Workflow Automation - Talaria
Dashboard{% endblock %} {% block extra_css %}
<style>
  /* Workflow Automation Styles */
  .workflow-card {
    @apply bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6 transition-all duration-200 hover:shadow-xl;
  }

  .workflow-card:hover {
    transform: translateY(-2px);
  }

  .workflow-status {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium;
  }

  .workflow-status.enabled {
    @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
  }

  .workflow-status.disabled {
    @apply bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200;
  }

  .trigger-badge {
    @apply inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-lg text-xs font-medium mr-2 mb-2;
  }

  .action-badge {
    @apply inline-flex items-center px-2 py-1 bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 rounded-lg text-xs font-medium mr-2 mb-2;
  }

  .stats-card {
    @apply bg-gradient-to-r rounded-2xl p-6 text-white shadow-lg;
  }

  .execution-log {
    @apply bg-gray-50 dark:bg-gray-900 rounded-xl p-4 border border-gray-200 dark:border-gray-700;
  }

  .execution-success {
    @apply text-green-600 dark:text-green-400;
  }

  .execution-failed {
    @apply text-red-600 dark:text-red-400;
  }
</style>
{% endblock %} {% block content %}
<div class="container mx-auto px-4 py-8">
  <!-- Header -->
  <div class="flex justify-between items-center mb-8">
    <div>
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
        <i class="fas fa-robot mr-3 text-blue-500"></i>
        Workflow Automation
      </h1>
      <p class="text-gray-600 dark:text-gray-400 mt-2">
        Automate your wafer inventory management with smart workflows
      </p>
    </div>

    <div class="flex space-x-3">
      <button
        id="tutorial-btn"
        class="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-2xl shadow-lg transition-all duration-200 hover:scale-105"
      >
        <i class="fas fa-graduation-cap mr-2"></i>
        Tutorial
      </button>
      
      <button
        id="create-workflow-btn"
        class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-2xl shadow-lg transition-all duration-200 hover:scale-105"
      >
        <i class="fas fa-plus mr-2"></i>
        Create Workflow
      </button>

      <button
        id="automation-toggle"
        class="px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-2xl shadow-lg transition-all duration-200 hover:scale-105"
      >
        <i class="fas fa-power-off mr-2"></i>
        <span id="automation-status">Enabled</span>
      </button>
    </div>
  </div>

  <!-- Quick Start Guide (shown when no workflows exist) -->
  <div id="quick-start-guide" class="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900 dark:to-purple-900 rounded-2xl p-8 mb-8 hidden">
    <div class="flex items-start space-x-6">
      <div class="flex-shrink-0">
        <div class="w-16 h-16 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center">
          <i class="fas fa-magic text-2xl text-blue-600 dark:text-blue-300"></i>
        </div>
      </div>
      <div class="flex-1">
        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-3">
          Welcome to Workflow Automation!
        </h3>
        <p class="text-gray-600 dark:text-gray-300 mb-4">
          Automate repetitive tasks in your wafer inventory management. Here are some popular use cases:
        </p>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
          <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center mb-2">
              <i class="fas fa-exclamation-triangle text-amber-500 mr-2"></i>
              <span class="font-medium text-gray-900 dark:text-white">Low Stock Alerts</span>
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-400">Get notified when wafer quantities drop below threshold</p>
          </div>
          <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center mb-2">
              <i class="fas fa-sync-alt text-blue-500 mr-2"></i>
              <span class="font-medium text-gray-900 dark:text-white">Icarium Sync</span>
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-400">Auto-sync wafer data with Icarium system</p>
          </div>
          <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
            <div class="flex items-center mb-2">
              <i class="fas fa-envelope text-green-500 mr-2"></i>
              <span class="font-medium text-gray-900 dark:text-white">Email Reports</span>
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-400">Send daily/weekly inventory reports automatically</p>
          </div>
        </div>
        <div class="flex space-x-3">
          <button onclick="window.safeWorkflowCall('startTutorial')" 
                  class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm">
            <i class="fas fa-play mr-2"></i>Start Tutorial
          </button>
          <button onclick="window.safeWorkflowCall('showTemplates')" 
                  class="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg text-sm">
            <i class="fas fa-templates mr-2"></i>Browse Templates
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Statistics Dashboard -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="stats-card from-blue-500 to-blue-600">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-blue-100 text-sm">Total Workflows</p>
          <p class="text-3xl font-bold" id="total-workflows">0</p>
        </div>
        <i class="fas fa-cogs text-4xl text-blue-200"></i>
      </div>
    </div>

    <div class="stats-card from-green-500 to-green-600">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-green-100 text-sm">Active Workflows</p>
          <p class="text-3xl font-bold" id="active-workflows">0</p>
        </div>
        <i class="fas fa-play-circle text-4xl text-green-200"></i>
      </div>
    </div>

    <div class="stats-card from-purple-500 to-purple-600">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-purple-100 text-sm">Total Executions</p>
          <p class="text-3xl font-bold" id="total-executions">0</p>
        </div>
        <i class="fas fa-bolt text-4xl text-purple-200"></i>
      </div>
    </div>

    <div class="stats-card from-amber-500 to-amber-600">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-amber-100 text-sm">Success Rate</p>
          <p class="text-3xl font-bold" id="success-rate">0%</p>
        </div>
        <i class="fas fa-chart-line text-4xl text-amber-200"></i>
      </div>
    </div>
  </div>

  <!-- Workflow Management Tabs -->
  <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg">
    <div class="border-b border-gray-200 dark:border-gray-700">
      <nav class="flex space-x-8 px-6" aria-label="Tabs">
        <button
          class="workflow-tab active py-4 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600 dark:text-blue-400"
          data-tab="workflows"
        >
          <i class="fas fa-list mr-2"></i>
          Workflows
        </button>
        <button
          class="workflow-tab py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          data-tab="templates"
        >
          <i class="fas fa-template mr-2"></i>
          Templates
        </button>
        <button
          class="workflow-tab py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          data-tab="execution-log"
        >
          <i class="fas fa-history mr-2"></i>
          Execution Log
        </button>
        <button
          class="workflow-tab py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          data-tab="settings"
        >
          <i class="fas fa-cog mr-2"></i>
          Settings
        </button>
      </nav>
    </div>

    <!-- Tab Content -->
    <div class="p-6">
      <!-- Workflows Tab -->
      <div id="workflows-tab" class="tab-content">
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            Configured Workflows
          </h2>

          <div class="flex space-x-3">
            <div class="relative">
              <input
                type="text"
                id="workflow-search"
                class="pl-10 pr-4 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                placeholder="Search workflows..."
              />
              <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
            </div>

            <select
              id="workflow-filter"
              class="px-4 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              <option value="all">All Workflows</option>
              <option value="enabled">Enabled Only</option>
              <option value="disabled">Disabled Only</option>
            </select>
          </div>
        </div>

        <div
          id="workflows-container"
          class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6"
        >
          <!-- Workflows will be populated here -->
        </div>

        <!-- Empty State -->
        <div id="workflows-empty" class="text-center py-12 hidden">
          <div
            class="w-24 h-24 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4"
          >
            <i class="fas fa-robot text-3xl text-gray-400"></i>
          </div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No Workflows Yet
          </h3>
          <p class="text-gray-500 dark:text-gray-400 mb-6">
            Create your first workflow to automate inventory management tasks
          </p>
          <button
            onclick="window.safeWorkflowCall('createWorkflow')"
            class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-2xl shadow-lg transition-all duration-200"
          >
            <i class="fas fa-plus mr-2"></i>
            Create First Workflow
          </button>
        </div>
      </div>

      <!-- Templates Tab -->
      <div id="templates-tab" class="tab-content hidden">
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            Workflow Templates for Wafer Management
          </h2>
          <div class="flex space-x-3">
            <select id="template-category-filter" class="px-4 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
              <option value="all">All Categories</option>
              <option value="inventory">Inventory Management</option>
              <option value="quality">Quality Control</option>
              <option value="shipping">Shipping & Logistics</option>
              <option value="integration">System Integration</option>
            </select>
          </div>
        </div>

        <!-- Popular Templates Section -->
        <div class="mb-8">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
            <i class="fas fa-star text-yellow-500 mr-2"></i>
            Most Popular for Wafer Inventory
          </h3>
          <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6" id="popular-templates">
            <!-- Popular templates will be populated here -->
          </div>
        </div>

        <!-- All Templates Section -->
        <div>
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
            All Templates
          </h3>
          <div
            id="templates-container"
            class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6"
          >
            <!-- All templates will be populated here -->
          </div>
        </div>

        <!-- Custom Template Creation Hint -->
        <div class="mt-8 p-6 bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800 dark:to-blue-900 rounded-xl border border-gray-200 dark:border-gray-700">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center">
              <i class="fas fa-lightbulb text-blue-600 dark:text-blue-300"></i>
            </div>
            <div class="flex-1">
              <h4 class="font-medium text-gray-900 dark:text-white mb-1">Need a Custom Workflow?</h4>
              <p class="text-sm text-gray-600 dark:text-gray-400">Can't find what you're looking for? Create a custom workflow tailored to your specific needs.</p>
            </div>
            <button onclick="window.safeWorkflowCall('createCustomWorkflow')" 
                    class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm">
              <i class="fas fa-plus mr-2"></i>Create Custom
            </button>
          </div>
        </div>
      </div>

      <!-- Execution Log Tab -->
      <div id="execution-log-tab" class="tab-content hidden">
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            Execution History
          </h2>

          <div class="flex space-x-3">
            <select
              id="log-filter"
              class="px-4 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              <option value="all">All Executions</option>
              <option value="success">Successful Only</option>
              <option value="failed">Failed Only</option>
            </select>

            <button
              id="clear-log-btn"
              class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-xl"
            >
              <i class="fas fa-trash mr-2"></i>
              Clear Log
            </button>
          </div>
        </div>

        <div id="execution-log-container" class="space-y-4">
          <!-- Execution log will be populated here -->
        </div>
      </div>

      <!-- Settings Tab -->
      <div id="settings-tab" class="tab-content hidden">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
          Automation Settings
        </h2>

        <div class="space-y-6">
          <div class="bg-gray-50 dark:bg-gray-900 rounded-xl p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
              General Settings
            </h3>

            <div class="space-y-4">
              <div class="flex items-center justify-between">
                <div>
                  <label
                    class="text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Enable Workflow Automation
                  </label>
                  <p class="text-xs text-gray-500 dark:text-gray-400">
                    Master switch for all workflow automation
                  </p>
                </div>
                <input
                  type="checkbox"
                  id="global-automation-toggle"
                  class="toggle-switch"
                />
              </div>

              <div class="flex items-center justify-between">
                <div>
                  <label
                    class="text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Debug Mode
                  </label>
                  <p class="text-xs text-gray-500 dark:text-gray-400">
                    Enable detailed logging for troubleshooting
                  </p>
                </div>
                <input
                  type="checkbox"
                  id="debug-mode-toggle"
                  class="toggle-switch"
                />
              </div>
            </div>
          </div>

          <div class="bg-gray-50 dark:bg-gray-900 rounded-xl p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Notification Settings
            </h3>

            <div class="space-y-4">
              <div>
                <label
                  class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                >
                  Default Email Recipients
                </label>
                <input
                  type="email"
                  id="default-email"
                  class="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label
                  class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                >
                  Notification Frequency
                </label>
                <select
                  id="notification-frequency"
                  class="w-full px-3 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                >
                  <option value="immediate">Immediate</option>
                  <option value="hourly">Hourly Digest</option>
                  <option value="daily">Daily Digest</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Include Workflow Automation Scripts -->
<script src="{{ url_for('static', filename='js/workflow-templates.js') }}"></script>
<script src="{{ url_for('static', filename='js/workflow-automation.js') }}"></script>
<script src="{{ url_for('static', filename='js/workflow-manager.js') }}"></script>
{% endblock %} {% block extra_js %}
<script>
  // Initialize Workflow Automation
  document.addEventListener("DOMContentLoaded", function () {
    try {
      // Initialize the workflow automation engine
      window.workflowEngine = new WorkflowAutomationEngine();
      window.workflowManager = new WorkflowManager(window.workflowEngine);

      // Make sure workflowManager is globally accessible
      window.workflowManager = window.workflowManager;

      console.log("🤖 Workflow Automation System initialized");
      console.log(
        "✅ workflowManager available globally:",
        typeof window.workflowManager
      );

      // Wait a bit for everything to load, then refresh the workflow list
      setTimeout(() => {
        if (window.workflowManager && window.workflowManager.loadWorkflows) {
          window.workflowManager.loadWorkflows();
        }
      }, 500);
    } catch (error) {
      console.error("❌ Failed to initialize Workflow Automation:", error);
    }
  });

  // Fallback function to ensure workflowManager is available
  function ensureWorkflowManager() {
    if (!window.workflowManager) {
      console.warn(
        "⚠️ workflowManager not found, attempting to reinitialize..."
      );
      try {
        if (!window.workflowEngine) {
          window.workflowEngine = new WorkflowAutomationEngine();
        }
        window.workflowManager = new WorkflowManager(window.workflowEngine);
        console.log("✅ workflowManager reinitialized");
      } catch (error) {
        console.error("❌ Failed to reinitialize workflowManager:", error);
        alert("Workflow system not ready. Please refresh the page.");
        return null;
      }
    }
    return window.workflowManager;
  }

  // Global helper functions for onclick handlers
  window.safeWorkflowCall = function (methodName, ...args) {
    try {
      const manager = window.workflowManager || ensureWorkflowManager();
      if (manager && typeof manager[methodName] === "function") {
        return manager[methodName](...args);
      } else {
        console.error(`❌ Method ${methodName} not found on workflowManager`);
        alert("Workflow function not available. Please refresh the page.");
      }
    } catch (error) {
      console.error(`❌ Error calling ${methodName}:`, error);
      alert("An error occurred. Please refresh the page.");
    }
  };
</script>
{% endblock %}

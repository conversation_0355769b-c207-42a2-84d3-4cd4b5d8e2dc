{% extends "base.html" %} {% block title %}Download Files{% endblock %} {% block
extra_css %}
<link
  rel="stylesheet"
  href="{{ url_for('static', filename='css/download_files.css') }}"
/>
{% endblock %} {% block head %} {{ super() }}
<meta name="csrf-token" content="{{ csrf_token }}" />
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
{% endblock %} {% block content %} {% with messages = get_flashed_messages() %}
{% if messages %}
<div class="flash-messages">
  {% for message in messages %}
  <div class="alert alert-info">{{ message }}</div>
  {% endfor %}
</div>
{% endif %} {% endwith %}

<div class="min-h-screen bg-gray-50 py-8">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="bg-white rounded-xl shadow-lg overflow-hidden">
      <div class="p-6">
        <h2 class="text-2xl font-bold text-gray-900 mb-4 text-center">
          Generated Files
        </h2>

        <div
          class="grid grid-cols-1 {% if session.get('label_type') != '4' %}md:grid-cols-2{% endif %} gap-6"
        >
          <!-- Label File Section -->
          <div
            class="border rounded-lg p-4 {% if session.get('label_type') == '4' %}mx-auto max-w-md{% endif %}"
          >
            <h3 class="text-lg font-medium mb-4">Label File</h3>
            <div class="flex flex-col gap-4">
              <div class="text-sm text-gray-600">
                <label
                  for="printer-ip"
                  class="block text-sm font-medium text-gray-700"
                  >Printer IP</label
                >
                <input
                  type="text"
                  id="printer-ip"
                  value="*************"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
              </div>

              <div class="text-sm text-gray-600">
                <label
                  for="copies"
                  class="block text-sm font-medium text-gray-700"
                  >Number of Copies</label
                >
                <input
                  type="number"
                  id="copies"
                  value="1"
                  min="1"
                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                />
              </div>

              <div class="flex flex-col sm:flex-row gap-2">
                <a
                  href="{{ url_for('download_label_route') }}"
                  class="flex-1 inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700"
                >
                  Download Label
                </a>

                <button
                  id="printButton"
                  class="flex-1 inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Print Label
                </button>
              </div>

              <button
                id="testConnectionButton"
                class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Test Printer Connection
              </button>
            </div>
          </div>

          <!-- Packing Slip Section - Only show for non-Substrate-wafer labels -->
          {% if session.get('label_type') != '4' %}
          <div class="border rounded-lg p-4">
            <h3 class="text-lg font-medium mb-4">Packing Slip</h3>
            <div class="flex flex-col gap-4">
              <div class="mb-2">
                <label class="block text-sm font-medium text-gray-700"
                  >Select Address:</label
                >
                <div class="mt-2 flex gap-2">
                  <button
                    id="france-address"
                    class="flex-1 inline-flex items-center justify-center px-4 py-2 border rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 address-btn active"
                  >
                    France
                  </button>
                  <button
                    id="switzerland-address"
                    class="flex-1 inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 address-btn"
                  >
                    Switzerland
                  </button>
                </div>
              </div>
              <a
                id="download-packing-slip"
                href="{{ url_for('download_packing_slip') }}"
                class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700"
              >
                Download Packing Slip
              </a>
            </div>
          </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>

  <div
    class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg"
  >
    <div class="max-w-7xl mx-auto px-4 py-3 sm:px-6 lg:px-8">
      <div class="flex flex-col sm:flex-row justify-end items-center gap-3">
        <div class="flex gap-3 w-full sm:w-auto">
          <button
            id="eigerCsvBtn"
            class="flex-1 sm:flex-initial inline-flex items-center justify-center px-4 py-2 border border-green-600 rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-2"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                clip-rule="evenodd"
              />
            </svg>
            Generate Eiger CSV
          </button>

          {% if check_permission('modify') %}
          <button
            id="sendEmailBtn"
            type="button"
            class="flex-1 sm:flex-initial inline-flex items-center justify-center px-4 py-2 border border-indigo-600 rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-2"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"
              />
              <path
                d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"
              />
            </svg>
            Send Email
          </button>
          {% endif %} {% if check_permission('modify') %}
          <button
            id="testEmailBtn"
            type="button"
            class="flex-1 sm:flex-initial inline-flex items-center justify-center px-4 py-2 border border-blue-600 rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4 mr-2"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clip-rule="evenodd"
              />
            </svg>
            Test Email
          </button>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block extra_js %}
<script>
  // Store the task_gid in sessionStorage when the page loads
  document.addEventListener("DOMContentLoaded", function () {
    const taskGid = "{{ session.get('current_task_gid', '') }}";
    if (taskGid) {
      sessionStorage.setItem("current_task_gid", taskGid);
      console.log("Task GID stored in sessionStorage:", taskGid);
    } else {
      console.log("No task GID found in session");
    }
  });
</script>
<script src="{{ url_for('static', filename='js/download_files.js') }}"></script>
{% endblock %}

{% macro navigation_buttons(prev_url, next_url, prev_text='Previous', next_text='Next') %}
<div id="navigation-buttons"></div>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const navButtons = createNavigationButtons('{{ prev_url }}', '{{ next_url }}', '{{ prev_text }}', '{{ next_text }}');
        document.getElementById('navigation-buttons').appendChild(navButtons);
    });
</script>
{% endmacro %}
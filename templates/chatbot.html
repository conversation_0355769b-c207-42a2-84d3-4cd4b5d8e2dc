<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="csrf-token" content="{{ csrf_token }}" />
    <title>Chat with <PERSON><PERSON><PERSON> Assistant</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"
    />
    <style>
      body {
        font-family: "Roboto", sans-serif;
        margin: 0;
        padding: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        color: #333;
        background: linear-gradient(135deg, #f5f7fa 0%, #e4edf5 100%);
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
      }

      #chatbox {
        width: 90%;
        max-width: 800px;
        background-color: #fff;
        border-radius: 16px;
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        overflow: hidden;
        display: flex;
        flex-direction: column;
        height: 80vh;
        transition: all 0.3s ease;
      }

      #chatbox:hover {
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        transform: translateY(-5px);
      }

      #header {
        background: linear-gradient(90deg, #3498db, #2c3e50);
        color: white;
        padding: 20px;
        text-align: center;
        font-weight: 500;
        font-size: 1.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
      }

      #header i {
        margin-right: 10px;
        font-size: 1.8rem;
      }

      #closeButton {
        position: absolute;
        right: 20px;
        background: none;
        border: none;
        color: white;
        font-size: 1.2rem;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      #closeButton:hover {
        transform: scale(1.2);
      }

      #messages {
        flex-grow: 1;
        padding: 20px;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        background-color: #f8fafc;
        background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%233498db' fill-opacity='0.03' fill-rule='evenodd'/%3E%3C/svg%3E");
      }

      .message {
        max-width: 80%;
        margin-bottom: 15px;
        padding: 12px 18px;
        border-radius: 18px;
        font-size: 0.95rem;
        line-height: 1.5;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        animation-duration: 0.5s;
      }

      .user {
        align-self: flex-end;
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        border-bottom-right-radius: 4px;
        animation-name: fadeInRight;
      }

      .bot {
        align-self: flex-start;
        background-color: white;
        color: #333;
        border-bottom-left-radius: 4px;
        border-left: 4px solid #3498db;
        animation-name: fadeInLeft;
      }

      .typing-indicator {
        align-self: flex-start;
        margin-bottom: 15px;
        animation: pulse 1.5s infinite;
      }

      .typing-dot {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #3498db;
        margin-right: 3px;
        animation: bounce 1.3s infinite;
      }

      .typing-dot:nth-child(2) {
        animation-delay: 0.2s;
      }

      .typing-dot:nth-child(3) {
        animation-delay: 0.4s;
      }

      @keyframes bounce {
        0%,
        80%,
        100% {
          transform: translateY(0);
        }
        40% {
          transform: translateY(-8px);
        }
      }

      @keyframes pulse {
        0% {
          opacity: 0.6;
        }
        50% {
          opacity: 1;
        }
        100% {
          opacity: 0.6;
        }
      }

      .timestamp {
        font-size: 0.7rem;
        margin-top: 5px;
        opacity: 0.7;
        text-align: right;
      }

      #inputContainer {
        display: flex;
        padding: 20px;
        background-color: #fff;
        border-top: 1px solid #e1e4e8;
        position: relative;
      }

      #userInput {
        flex-grow: 1;
        padding: 14px 18px;
        border: 2px solid #e1e4e8;
        border-radius: 24px;
        font-size: 0.95rem;
        outline: none;
        transition: all 0.3s ease;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
      }

      #userInput:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
      }

      #sendButton {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        border: none;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        margin-left: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        justify-content: center;
        align-items: center;
        box-shadow: 0 3px 10px rgba(52, 152, 219, 0.3);
      }

      #sendButton:hover {
        transform: scale(1.05);
        box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
      }

      #sendButton:active {
        transform: scale(0.95);
      }

      #suggestionChips {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 15px;
        padding: 0 10px;
      }

      .suggestionChip {
        background-color: #e9f5fe;
        color: #3498db;
        border: 1px solid #c5e4fd;
        border-radius: 16px;
        padding: 6px 12px;
        font-size: 0.85rem;
        cursor: pointer;
        transition: all 0.2s ease;
        white-space: nowrap;
      }

      .suggestionChip:hover {
        background-color: #d0e8fc;
        transform: translateY(-2px);
      }

      footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(5px);
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        padding: 15px 0;
        text-align: center;
        z-index: 10;
      }

      footer p {
        margin: 0;
        color: #4a5568;
        font-size: 0.9rem;
      }

      .social-links {
        margin-top: 10px;
      }

      .social-links a {
        color: #4a5568;
        margin: 0 10px;
        font-size: 1.2rem;
        transition: all 0.3s ease;
      }

      .social-links a:hover {
        color: #3498db;
        transform: translateY(-3px);
      }

      /* Emoji styles */
      .emoji {
        font-size: 1.2em;
        margin-right: 5px;
        display: inline-block;
        animation: wave 1.5s infinite;
      }

      @keyframes wave {
        0%,
        100% {
          transform: rotate(0deg);
        }
        25% {
          transform: rotate(10deg);
        }
        75% {
          transform: rotate(-10deg);
        }
      }

      /* Dark mode toggle */
      #darkModeToggle {
        position: absolute;
        top: 20px;
        right: 20px;
        background: none;
        border: none;
        color: white;
        font-size: 1.2rem;
        cursor: pointer;
        z-index: 100;
        transition: transform 0.3s ease;
      }

      #darkModeToggle:hover {
        transform: rotate(30deg);
      }

      /* Dark mode styles */
      body.dark-mode {
        background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
      }

      body.dark-mode #chatbox {
        background-color: #2d3748;
      }

      body.dark-mode #messages {
        background-color: #1a202c;
        background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%233498db' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
      }

      body.dark-mode .bot {
        background-color: #4a5568;
        color: #e2e8f0;
      }

      body.dark-mode #inputContainer {
        background-color: #2d3748;
        border-top: 1px solid #4a5568;
      }

      body.dark-mode #userInput {
        background-color: #4a5568;
        border-color: #4a5568;
        color: #e2e8f0;
      }

      body.dark-mode #userInput::placeholder {
        color: #a0aec0;
      }

      body.dark-mode footer {
        background-color: rgba(26, 32, 44, 0.9);
      }

      body.dark-mode footer p,
      body.dark-mode .social-links a {
        color: #e2e8f0;
      }

      body.dark-mode .suggestionChip {
        background-color: #4a5568;
        border-color: #2d3748;
        color: #e2e8f0;
      }
    </style>
  </head>

  <body>
    <!-- CSRF Token -->
    <input type="hidden" name="csrf_token" value="{{ csrf_token }}" />

    <button id="darkModeToggle" title="Toggle Dark Mode">
      <i class="fas fa-moon"></i>
    </button>

    <div id="chatbox">
      <div id="header">
        <i class="fas fa-robot"></i>
        Chat with Talaria Assistant
        <button id="closeButton" title="Close Chat">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div id="messages"></div>
      <div id="suggestionChips">
        <div class="suggestionChip" onclick="sendSuggestion('Help')">Help</div>
        <div
          class="suggestionChip"
          onclick="sendSuggestion('What can you do?')"
        >
          What can you do?
        </div>
        <div
          class="suggestionChip"
          onclick="sendSuggestion('Tell me about inventory')"
        >
          Inventory
        </div>
        <div
          class="suggestionChip"
          onclick="sendSuggestion('How do I track shipments?')"
        >
          Shipments
        </div>
        <div class="suggestionChip" onclick="sendSuggestion('Tell me a joke')">
          Tell me a joke
        </div>
      </div>
      <div id="inputContainer">
        <input
          type="text"
          id="userInput"
          placeholder="Type your message here..."
        />
        <button id="sendButton" onclick="sendMessage()" title="Send Message">
          <i class="fas fa-paper-plane"></i>
        </button>
      </div>
    </div>

    <footer>
      <p>&copy; 2024 Ligentec. All rights reserved.</p>
      <div class="social-links">
        <a
          href="https://www.linkedin.com"
          target="_blank"
          rel="noopener noreferrer"
        >
          <i class="fab fa-linkedin"></i>
        </a>
        <a
          href="https://www.youtube.com"
          target="_blank"
          rel="noopener noreferrer"
        >
          <i class="fab fa-youtube"></i>
        </a>
        <a
          href="https://www.twitter.com"
          target="_blank"
          rel="noopener noreferrer"
        >
          <i class="fab fa-twitter"></i>
        </a>
      </div>
    </footer>

    <script>
      // Setup CSRF token for all AJAX requests
      $.ajaxSetup({
        beforeSend: function (xhr, settings) {
          if (
            !/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) &&
            !this.crossDomain
          ) {
            xhr.setRequestHeader(
              "X-CSRFToken",
              $('meta[name="csrf-token"]').attr("content")
            );
          }
        },
      });

      // Initialize the chat with a welcome message
      document.addEventListener("DOMContentLoaded", function () {
        setTimeout(() => {
          showTypingIndicator();
          setTimeout(() => {
            removeTypingIndicator();
            appendMessage(
              "bot",
              "Hello! 👋 I'm your Talaria Assistant. How can I help you today?"
            );
          }, 1500);
        }, 500);

        // Add close button event listener
        document
          .getElementById("closeButton")
          .addEventListener("click", function () {
            // Redirect to the dashboard
            window.location.href = "/";
          });
      });

      // Dark mode toggle
      document
        .getElementById("darkModeToggle")
        .addEventListener("click", function () {
          document.body.classList.toggle("dark-mode");
          const icon = this.querySelector("i");
          if (document.body.classList.contains("dark-mode")) {
            icon.classList.remove("fa-moon");
            icon.classList.add("fa-sun");
          } else {
            icon.classList.remove("fa-sun");
            icon.classList.add("fa-moon");
          }
        });

      function sendSuggestion(text) {
        const userInput = $("#userInput");
        userInput.val(text);
        sendMessage();
      }

      function sendMessage() {
        const userInput = $("#userInput");
        const userMessage = userInput.val().trim();
        if (userMessage === "") return;

        // Add timestamp to user message
        const timestamp = new Date().toLocaleTimeString([], {
          hour: "2-digit",
          minute: "2-digit",
        });
        appendMessage("user", userMessage, timestamp);
        userInput.val("");

        // Show typing indicator
        showTypingIndicator();

        // Send message to server
        $.ajax({
          url: "/chat",
          type: "POST",
          contentType: "application/json",
          headers: {
            "X-CSRFToken": $('meta[name="csrf-token"]').attr("content"),
          },
          data: JSON.stringify({ message: userMessage }),
          success: function (response) {
            // Remove typing indicator and show response
            setTimeout(() => {
              removeTypingIndicator();
              const botTimestamp = new Date().toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              });
              appendMessage("bot", response.response, botTimestamp);
            }, Math.min(1000, userMessage.length * 50)); // Dynamic delay based on message length
          },
          error: function () {
            removeTypingIndicator();
            appendMessage(
              "bot",
              "Sorry, I encountered an error while processing your request. Please try again later."
            );
          },
        });
      }

      function showTypingIndicator() {
        const typingIndicator = $("<div>").addClass("typing-indicator");
        typingIndicator.html(`
                <div class="bg-gray-200 rounded-lg px-4 py-2 max-w-xs">
                    <div class="flex space-x-1">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            `);
        $("#messages").append(typingIndicator);
        $("#messages").scrollTop($("#messages")[0].scrollHeight);
      }

      function removeTypingIndicator() {
        $(".typing-indicator").remove();
      }

      function appendMessage(sender, message, timestamp) {
        // Process message to add emoji animations
        message = processEmojis(message);

        // Create message element with timestamp
        const messageElement = $("<div>").addClass("message").addClass(sender);
        messageElement.html(message);

        if (timestamp) {
          const timestampElement = $("<div>")
            .addClass("timestamp")
            .text(timestamp);
          messageElement.append(timestampElement);
        }

        $("#messages").append(messageElement);
        $("#messages").scrollTop($("#messages")[0].scrollHeight);
      }

      function processEmojis(message) {
        // Add animation class to emojis
        const emojiRegex =
          /[\u{1F300}-\u{1F6FF}\u{1F900}-\u{1F9FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/gu;
        return message.replace(
          emojiRegex,
          (match) => `<span class="emoji">${match}</span>`
        );
      }

      $("#userInput").keypress(function (e) {
        if (e.which == 13) {
          sendMessage();
          return false;
        }
      });
    </script>
  </body>
</html>

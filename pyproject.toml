[tool.black]
line-length = 88
target-version = ['py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
    \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  | talaria_env
)/
'''

[tool.ruff]
line-length = 88
target-version = "py312"
select = ["E", "F", "W", "I", "N", "B"]
ignore = ["E203"]
exclude = [
    ".git",
    ".venv",
    "__pycache__",
    "talaria_env",
    "build",
    "dist",
]

[tool.ruff.isort]
known-first-party = ["app", "api", "config", "core", "database", "integrations", "utils"]

#!/usr/bin/env python3
# WSGI entry point for the Talaria Dashboard application

try:
    from app import app

    print("Successfully imported app from app")
except ImportError as e:
    print(f"Error importing app from app: {e}")
    raise

# Create the application instance
application = app

try:
    from database.auth_schema import setup_auth_tables

    print("Successfully imported setup_auth_tables from database.auth_schema")
except ImportError as e:
    print(f"Error importing setup_auth_tables from database.auth_schema: {e}")
    raise

# Ensure auth tables exist
with application.app_context():
    setup_auth_tables()

if __name__ == "__main__":
    application.run()

from database.auth_db_config import get_auth_db_connection

def check_tables():
    try:
        conn = get_auth_db_connection()
        cursor = conn.cursor()
        
        # Check if support_tickets table exists
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'support_tickets'
            );
        """)
        support_tickets_exists = cursor.fetchone()[0]
        print(f"Support tickets table exists: {support_tickets_exists}")
        
        # List all tables
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public';
        """)
        tables = cursor.fetchall()
        print("Tables in auth database:")
        for table in tables:
            print(f"- {table[0]}")
        
        # Create support_tickets table if it doesn't exist
        if not support_tickets_exists:
            print("Creating support_tickets table...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS support_tickets (
                    id SERIAL PRIMARY KEY,
                    user_id INTEGER,
                    user_email VARCHAR(255),
                    user_name VARCHAR(255),
                    issue_type VARCHAR(50) NOT NULL,
                    priority VARCHAR(20) NOT NULL,
                    subject VARCHAR(255) NOT NULL,
                    message TEXT NOT NULL,
                    status VARCHAR(20) NOT NULL DEFAULT 'open',
                    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
                );
                
                CREATE INDEX IF NOT EXISTS idx_support_tickets_user_email ON support_tickets(user_email);
                CREATE INDEX IF NOT EXISTS idx_support_tickets_status ON support_tickets(status);
                CREATE INDEX IF NOT EXISTS idx_support_tickets_issue_type ON support_tickets(issue_type);
            """)
            conn.commit()
            print("Support tickets table created successfully")
            
            # Verify table was created
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = 'support_tickets'
                );
            """)
            support_tickets_exists = cursor.fetchone()[0]
            print(f"Support tickets table exists after creation: {support_tickets_exists}")
        
        # Create support_attachments table if it doesn't exist
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'support_attachments'
            );
        """)
        support_attachments_exists = cursor.fetchone()[0]
        print(f"Support attachments table exists: {support_attachments_exists}")
        
        if not support_attachments_exists:
            print("Creating support_attachments table...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS support_attachments (
                    id SERIAL PRIMARY KEY,
                    ticket_id INTEGER NOT NULL REFERENCES support_tickets(id) ON DELETE CASCADE,
                    file_name VARCHAR(255) NOT NULL,
                    file_path VARCHAR(255) NOT NULL,
                    file_size INTEGER NOT NULL,
                    file_type VARCHAR(100) NOT NULL,
                    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
                );
                
                CREATE INDEX IF NOT EXISTS idx_support_attachments_ticket_id ON support_attachments(ticket_id);
            """)
            conn.commit()
            print("Support attachments table created successfully")
        
        conn.close()
        return True
    except Exception as e:
        print(f"Error checking/creating tables: {str(e)}")
        return False

if __name__ == "__main__":
    check_tables()

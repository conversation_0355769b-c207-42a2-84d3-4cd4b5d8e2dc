FROM python:3.11-alpine

# Create a non-root user for security
RUN addgroup -S appuser && adduser -S -G appuser appuser

# Set working directory
WORKDIR /app

# Install system dependencies including fonts with improved font support
RUN apk add --no-cache \
    build-base \
    postgresql-dev \
    curl \
    tini \
    libffi-dev \
    fontconfig \
    ttf-dejavu \
    ttf-liberation \
    msttcorefonts-installer \
    && update-ms-fonts \
    && fc-cache -fv

# Create fonts directory and download additional fonts
RUN mkdir -p /usr/share/fonts/truetype/custom && \
    curl -L -o /usr/share/fonts/truetype/custom/Arial.ttf https://github.com/matomo-org/travis-scripts/raw/master/fonts/Arial.ttf && \
    # Create symlinks to make fonts available in expected paths
    mkdir -p /usr/share/fonts/dejavu && \
    ln -sf /usr/share/fonts/ttf-dejavu/DejaVuSans.ttf /usr/share/fonts/dejavu/DejaVuSans.ttf && \
    # Create additional symlinks for common paths
    mkdir -p /usr/share/fonts/truetype/dejavu && \
    ln -sf /usr/share/fonts/ttf-dejavu/DejaVuSans.ttf /usr/share/fonts/truetype/dejavu/DejaVuSans.ttf && \
    # Ensure proper permissions
    chmod 644 /usr/share/fonts/truetype/custom/Arial.ttf && \
    # Update font cache with verbose output for debugging
    fc-cache -fv

# Additional font diagnostic commands for build logs
RUN echo "Available fonts:" && \
    fc-list && \
    echo "DejaVu fonts:" && \
    find /usr/share -name "*DejaVu*.ttf" -type f

# Set environment variables for fonts - use DejaVuSans as primary font
ENV FONT_PATH=/usr/share/fonts/ttf-dejavu/DejaVuSans.ttf
ENV DEJAVU_FONT_PATH=/usr/share/fonts/ttf-dejavu/DejaVuSans.ttf
ENV FORCE_FONT=DejaVuSans

# Copy requirements and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install --no-cache-dir gunicorn pillow reportlab

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DOWNLOAD_FOLDER=/app/downloads

# Create necessary directories and set proper permissions
RUN mkdir -p /app/logs /app/flask_session /app/data /app/downloads /home/<USER>/Downloads && \
    # Make sure the downloads directory is writable by the app user
    chmod 777 /app/downloads && \
    # Set environment variable to indicate we're in a Docker container
    echo "DOCKER_CONTAINER=true" >> /etc/environment

# Set ownership of application directories
RUN chown -R appuser:appuser /app /home/<USER>

# Copy application code
COPY --chown=appuser:appuser . .

# This section creates lowercase aliases for image files and ensures specific files have both case variants
RUN find /app/static/img -type f -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" -o -name "*.gif" | while read file; do \
    filename=$(basename "$file"); \
    lowercase=$(echo "$filename" | tr '[:upper:]' '[:lower:]'); \
    if [ "$filename" != "$lowercase" ]; then \
        cp "$file" "$(dirname "$file")/$lowercase"; \
    fi; \
done && \
# Create specific symlinks for critical images to ensure they work with both case variants
if [ -f "/app/static/img/logoTalaria.jpeg" ]; then \
    ln -sf /app/static/img/logoTalaria.jpeg /app/static/img/logotalaria.jpeg; \
fi && \
if [ -f "/app/static/img/logo-talaria.jpeg" ]; then \
    ln -sf /app/static/img/logo-talaria.jpeg /app/static/img/Logo-Talaria.jpeg; \
fi

# Create diagnostic script for font debugging
RUN echo '#!/bin/sh\necho "Available fonts:"\nfc-list\necho "\nDejaVu fonts:"\nfind /usr/share -name "*DejaVu*.ttf" -type f\necho "\nFont registration test:"\npython -c "from reportlab.pdfbase import pdfmetrics; from reportlab.pdfbase.ttfonts import TTFont; print(\"Registered fonts:\", pdfmetrics.getRegisteredFontNames()); pdfmetrics.registerFont(TTFont(\"DejaVuSans\", \"'"$DEJAVU_FONT_PATH"'\")); print(\"After registration:\", pdfmetrics.getRegisteredFontNames())"' > /app/check_fonts.sh && \
    chmod +x /app/check_fonts.sh && \
    chown appuser:appuser /app/check_fonts.sh

# Switch to non-root user for security
USER appuser

# Expose port
EXPOSE 5000

# Use tini as init system to handle signals properly
ENTRYPOINT ["/sbin/tini", "--"]

# Run the application with gunicorn
CMD ["gunicorn", "--bind", "0.0.0.0:5000", "--workers=4", "--threads=2", "--timeout=60", "wsgi:app"]
# test_dashboard.py
from flask import render_template, Blueprint, current_app, jsonify
import os
from datetime import datetime
import sqlite3

test_dashboard = Blueprint("test_dashboard", __name__)


@test_dashboard.route("/storage-test")
def storage_test_dashboard():
    return render_template("storage_test.html")


@test_dashboard.route("/toggle-offline-mode")
def toggle_offline_mode():
    current_app.config["ONLINE_MODE"] = not current_app.config.get("ONLINE_MODE", True)
    mode = "Offline" if not current_app.config["ONLINE_MODE"] else "Online"

    # Log mode change
    current_app.logger.info(f"Storage mode changed to: {mode}")

    # Add status button to your storage_test.html template
    return jsonify({"success": True, "mode": mode})


@test_dashboard.route("/test-connection")
def test_connection():
    try:
        with current_app.offline_storage.get_db() as db:
            cursor = db.cursor()
            cursor.execute("SELECT 1")
            current_app.logger.info("Connection test successful")
            return jsonify(
                {"success": True, "message": "Database connection successful"}
            )
    except Exception as e:
        current_app.logger.error(f"Connection test failed: {str(e)}")
        return (
            jsonify({"success": False, "message": f"Connection failed: {str(e)}"}),
            500,
        )


@test_dashboard.route("/backup-offline-data")
def backup_offline_data():
    try:
        backup_dir = os.path.join(current_app.root_path, "backups")
        if not os.path.exists(backup_dir):
            current_app.logger.info(f"Creating backup directory: {backup_dir}")
            os.makedirs(backup_dir)

        # Generate backup filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = os.path.join(backup_dir, f"offline_db_backup_{timestamp}.db")

        # Copy the database file
        with current_app.offline_storage.get_db() as source_db:
            backup_db = sqlite3.connect(backup_path)
            source_db.backup(backup_db)
            backup_db.close()

        return jsonify(
            {
                "success": True,
                "message": f"Backup created successfully at {backup_path}",
                "backup_path": backup_path,
            }
        )
    except Exception as e:
        current_app.logger.error(f"Backup failed: {str(e)}")
        return jsonify({"success": False, "message": f"Backup failed: {str(e)}"}), 500

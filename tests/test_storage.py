import sqlite3
from datetime import datetime

from flask import current_app, jsonify


def init_test_routes(app):
    @app.route("/test_storage")
    def test_storage():
        """Test basic storage functionality"""
        try:
            # Check online/offline status
            is_online = current_app.config.get("ONLINE_MODE", False)

            # Get storage info
            storage_info = {
                "mode": "Online" if is_online else "Offline",
                "db_path": current_app.config.get("OFFLINE_DATABASE"),
                "timestamp": datetime.now().isoformat(),
                "tables": [],
            }

            # Get table information
            with current_app.offline_storage.get_db() as db:
                cursor = db.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                storage_info["tables"] = [row[0] for row in cursor.fetchall()]

            return jsonify({"success": True, "storage_info": storage_info})

        except Exception as e:
            current_app.logger.error(f"Storage test error: {str(e)}")
            return jsonify({"success": False, "error": str(e)}), 500

    @app.route("/test_offline_crud")
    def test_offline_crud():
        """Test CRUD operations in offline storage"""
        try:
            results = {}

            # Test data
            test_data = {
                "wafer_id": "TEST001",
                "data": '{"lot": "TEST_LOT", "status": "testing"}',
                "last_synced": datetime.now().isoformat(),
            }

            with current_app.offline_storage.get_db() as db:
                cursor = db.cursor()

                # Test CREATE
                try:
                    cursor.execute(
                        """
                        INSERT INTO offline_inventory (wafer_id, data, last_synced)
                        VALUES (?, ?, ?)
                    """,
                        (
                            test_data["wafer_id"],
                            test_data["data"],
                            test_data["last_synced"],
                        ),
                    )
                    results["create"] = "Success"
                except sqlite3.IntegrityError:
                    results["create"] = "Already exists"

                # Test READ
                cursor.execute(
                    "SELECT * FROM offline_inventory WHERE wafer_id = ?",
                    (test_data["wafer_id"],),
                )
                read_result = cursor.fetchone()
                results["read"] = "Found" if read_result else "Not found"

                # Test UPDATE
                cursor.execute(
                    """
                    UPDATE offline_inventory 
                    SET data = ? 
                    WHERE wafer_id = ?
                """,
                    ('{"lot": "TEST_LOT", "status": "updated"}', test_data["wafer_id"]),
                )
                results["update"] = "Success" if cursor.rowcount > 0 else "Failed"

                # Test DELETE
                cursor.execute(
                    "DELETE FROM offline_inventory WHERE wafer_id = ?",
                    (test_data["wafer_id"],),
                )
                results["delete"] = "Success" if cursor.rowcount > 0 else "Failed"

                db.commit()

            return jsonify({"success": True, "test_results": results})

        except Exception as e:
            current_app.logger.error(f"CRUD test error: {str(e)}")
            return jsonify({"success": False, "error": str(e)}), 500

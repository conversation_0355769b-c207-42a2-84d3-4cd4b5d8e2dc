import asana
from pprint import pprint

import traceback


def test_asana_integration():
    """Test Asana integration and custom fields handling"""
    try:
        # 1. Configure Asana client
        configuration = asana.Configuration()
        configuration.access_token = (
            "1/1204968822616977:ed9cf542d701d8a0809f0155bb7bd306"
        )
        api_client = asana.ApiClient(configuration)
        tasks_api_instance = asana.TasksApi(api_client)
        sections_api_instance = asana.SectionsApi(api_client)

        # Define opts with all needed fields
        opts = {
            "opt_fields": "actual_time_minutes,approval_status,assignee,assignee_section,assignee_status,"
            "completed,completed_at,completed_by,created_at,created_by,custom_fields,dependencies,"
            "dependents,due_at,due_on,external,followers,gid,hearted,hearts,html_notes,"
            "is_rendered_as_separator,liked,likes,memberships,modified_at,name,notes,num_hearts,"
            "num_likes,num_subtasks,parent,permalink_url,projects,resource_subtype,resource_type,"
            "start_at,start_on,tags,workspace"
        }

        print("\n=== Retrieving Section GID ===")
        shipment_project_gid = "1206397258493005"

        # Retrieve all sections in the project
        sections = sections_api_instance.get_sections_for_project(
            project_gid=shipment_project_gid, opts={}
        )
        elisee_test_section_gid = None
        for section in sections:
            print(f"Section Name: {section['name']}, GID: {section['gid']}")
            if section["name"] == "Elisee Test":
                elisee_test_section_gid = section["gid"]

        if not elisee_test_section_gid:
            raise Exception("Section 'Elisee Test' not found in the project.")

        print(f"Found 'Elisee Test' section with GID: {elisee_test_section_gid}")

        print("\n=== Testing Task Creation ===")

        # Create basic task with title
        body = {
            "data": {
                "name": "Test Shipment Task",  # This is the title field
                "projects": [shipment_project_gid],
                "notes": "TEST SHIPMENT - PLEASE IGNORE",
            }
        }

        try:
            print("Creating initial task...")
            new_task = tasks_api_instance.create_task(body=body, opts=opts)
            task_gid = new_task.gid if hasattr(new_task, "gid") else new_task.get("gid")
            print(f"Successfully created task with GID: {task_gid}")

            # Add task to section
            print(
                f"\nAssigning task to section 'Elisee Test' with GID: {elisee_test_section_gid}"
            )
            try:
                add_task_body = {"data": {"task": task_gid}}
                sections_api_instance.add_task_for_section(
                    section_gid=elisee_test_section_gid, body=add_task_body, opts={}
                )
                print("Task successfully added to the 'Elisee Test' section.")
            except Exception as e:
                print(f"Error adding task to section: {str(e)}")
                print(f"Full error: {traceback.format_exc()}")

            # Get task details with custom fields
            print("\nRetrieving task details...")
            task_details = tasks_api_instance.get_task(task_gid=task_gid, opts=opts)
            if hasattr(task_details, "to_dict"):
                task_details = task_details.to_dict()

            custom_fields = task_details.get("custom_fields", [])
            print(f"\nFound {len(custom_fields)} custom fields")

            # Debug: Print ALL custom fields and their properties
            print("\n=== ALL Custom Fields ===")
            field_gid_map = {}  # Map to store field name to GID mapping

            for field in custom_fields:
                print(f"\nField Name: {field.get('name')}")
                print(f"Field GID: {field.get('gid')}")
                print(f"Field Type: {field.get('type')}")
                print(f"Resource Type: {field.get('resource_type')}")

                # Store in our map for easier access later
                field_name = field.get("name").strip()
                field_gid_map[field_name] = {
                    "gid": field.get("gid"),
                    "type": field.get("type"),
                    "enum_options": field.get("enum_options"),
                }

                if field.get("enum_options"):
                    print("Enum Options:")
                    for option in field.get("enum_options"):
                        print(f"  - {option.get('name')} (GID: {option.get('gid')})")
                print("Current Value:", field.get("display_value"))
                print("-" * 50)

            # Now we have a complete map of all fields and their GIDs
            print("\n=== Field GID Map ===")
            for name, info in field_gid_map.items():
                print(f"{name}: {info['gid']} (Type: {info['type']})")

            # Define field updates with accurate information from our map
            field_updates = {
                "Shipment task title": {
                    "value": "Test Shipment From Integration",
                    "field_name": "Shipment task title",
                },
                "Need Reviewing?": {"value": "Yes", "field_name": "Need Reviewing?"},
                "label-free shipment ?": {
                    "value": "Yes",
                    "field_name": "label-free shipment ?",
                },
                "Keep Cassette closed": {
                    "value": "Yes",
                    "field_name": "Keep Cassette closed",
                },
                "Wafer choice type": {
                    "value": "Not Random",
                    "field_name": "Wafer choice type",
                },
                "Number of Wafers": {"value": "4", "field_name": "Number of Wafers"},
                "Mask": {"value": "e-beam", "field_name": "Mask"},
                "RIB ?": {"value": "TRUE", "field_name": "RIB ?"},
                "HEATERS?": {"value": "FALSE", "field_name": "HEATERS?"},
                "Undercuts?": {"value": "FALSE", "field_name": "Undercuts?"},
            }

            # Update fields with enhanced debugging
            print("\n=== Testing Field Updates ===")
            for field_key, field_info in field_updates.items():
                field_name = field_info["field_name"].strip()
                print(f"\nProcessing field: {field_name}")

                # Get field info from our map
                if field_name in field_gid_map:
                    field_data = field_gid_map[field_name]
                    field_gid = field_data["gid"]
                    field_type = field_data["type"]

                    print(f"Found field in map: GID={field_gid}, Type={field_type}")

                    if field_type == "enum":
                        # Handle enum fields (dropdowns, yes/no fields)
                        enum_options = field_data["enum_options"]
                        target_value = field_info["value"]
                        print(f"Looking for enum option with value: {target_value}")
                        print(
                            f"Available options: {[opt.get('name') for opt in enum_options]}"
                        )

                        # Find matching option (case insensitive)
                        matching_option = next(
                            (
                                opt
                                for opt in enum_options
                                if opt.get("name").lower() == target_value.lower()
                            ),
                            None,
                        )

                        if matching_option:
                            print(
                                f"Found matching option with GID: {matching_option.get('gid')}"
                            )
                            update_body = {
                                "data": {
                                    "custom_fields": {field_gid: matching_option["gid"]}
                                }
                            }

                            try:
                                print(f"Updating {field_name} to {target_value}")
                                tasks_api_instance.update_task(
                                    task_gid=task_gid, body=update_body, opts=opts
                                )
                                print("Update successful")
                            except Exception as e:
                                print(f"Error updating {field_name}: {str(e)}")
                                print(f"Full error: {traceback.format_exc()}")
                        else:
                            print(
                                f"No matching option found for value '{target_value}'"
                            )
                            print(
                                f"Available options: {[opt.get('name') for opt in enum_options]}"
                            )

                    elif field_type == "number":
                        # Handle number fields
                        try:
                            update_body = {
                                "data": {
                                    "custom_fields": {
                                        field_gid: float(field_info["value"])
                                    }
                                }
                            }
                            print(
                                f"Updating number field {field_name} to {field_info['value']}"
                            )
                            tasks_api_instance.update_task(
                                task_gid=task_gid, body=update_body, opts=opts
                            )
                            print("Update successful")
                        except Exception as e:
                            print(f"Error updating {field_name}: {str(e)}")

                    elif field_type == "text":
                        # Handle text fields
                        try:
                            update_body = {
                                "data": {
                                    "custom_fields": {field_gid: field_info["value"]}
                                }
                            }
                            print(
                                f"Updating text field {field_name} to {field_info['value']}"
                            )
                            tasks_api_instance.update_task(
                                task_gid=task_gid, body=update_body, opts=opts
                            )
                            print("Update successful")
                        except Exception as e:
                            print(f"Error updating {field_name}: {str(e)}")
                else:
                    print(f"Field not found in map: {field_name}")
                    print("Available fields:", list(field_gid_map.keys()))

            # Update the task name if it's not in the custom fields
            if "Shipment task title" not in field_gid_map:
                try:
                    print("\nUpdating task name directly...")
                    update_name_body = {
                        "data": {"name": "Test Shipment From Integration"}
                    }
                    tasks_api_instance.update_task(
                        task_gid=task_gid, body=update_name_body, opts=opts
                    )
                    print("Task name update successful")
                except Exception as e:
                    print(f"Error updating task name: {str(e)}")

            # Verify the updates
            print("\n=== Verifying Field Updates ===")
            updated_task = tasks_api_instance.get_task(task_gid=task_gid, opts=opts)
            if hasattr(updated_task, "to_dict"):
                updated_task = updated_task.to_dict()

            updated_fields = updated_task.get("custom_fields", [])
            for field in updated_fields:
                field_name = field.get("name", "").strip()
                if field_name in [
                    info["field_name"].strip() for info in field_updates.values()
                ]:
                    print(f"{field_name}: {field.get('display_value')}")

            # Clean up
            print("\nCleaning up test task...")
            tasks_api_instance.delete_task(task_gid=task_gid)
            print("Test task deleted successfully")

            return True

        except Exception as e:
            print(f"Error in task operations: {str(e)}")
            try:
                if "task_gid" in locals() and task_gid:
                    tasks_api_instance.delete_task(task_gid=task_gid)
                    print("Cleaned up test task despite error")
            except:
                pass
            return False

    except Exception as e:
        print(f"Test failed: {str(e)}")
        return False


if __name__ == "__main__":
    print("Starting Asana integration test...")
    success = test_asana_integration()
    print(f"\nTest {'succeeded' if success else 'failed'}")
    print("Test completed")

    if not success:
        exit(1)
    exit(0)

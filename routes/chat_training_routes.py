"""
Routes for chat training interface (admin only).
"""

import logging
from flask import (
    Blueprint,
    render_template,
    request,
    jsonify,
    session,
    redirect,
    url_for,
    flash,
)
from core.auth.auth import login_required, admin_required
from database.chat_models import (
    add_training_data,
    get_training_data,
    update_training_data,
    delete_training_data,
    get_training_data_by_id,
)
from database.chat_schema import setup_chat_tables

# Configure logging
logger = logging.getLogger(__name__)

# Create blueprint
chat_training_bp = Blueprint("chat_training", __name__)

# Ensure chat tables exist
setup_chat_tables()


@chat_training_bp.route("/training", methods=["GET"])
@login_required
@admin_required
def training_interface():
    """Admin-only interface for training the chatbot."""
    # Get category filter from query parameters
    category = request.args.get("category", "")
    
    # Get training data, filtered by category if provided
    if category:
        training_data = get_training_data(category=category, active_only=False)
    else:
        training_data = get_training_data(active_only=False)
    
    return render_template(
        "chat_training.html", 
        training_data=training_data,
        selected_category=category
    )


@chat_training_bp.route("/training/add", methods=["POST"])
@login_required
@admin_required
def add_training():
    """Add new training data."""
    pattern = request.form.get("pattern", "").strip()
    response = request.form.get("response", "").strip()
    category = request.form.get("category", "general").strip()
    priority = request.form.get("priority", 5, type=int)
    
    # Validate inputs
    if not pattern or not response:
        flash("Pattern and response are required.", "error")
        return redirect(url_for("chat_training.training_interface"))
    
    # Get current user as creator
    created_by = session.get("user_email", "admin")
    
    # Add the training data
    success = add_training_data(
        pattern=pattern,
        response=response,
        category=category,
        priority=priority,
        created_by=created_by
    )
    
    if success:
        flash("Training data added successfully.", "success")
    else:
        flash("Error adding training data.", "error")
    
    return redirect(url_for("chat_training.training_interface"))


@chat_training_bp.route("/training/get", methods=["GET"])
@login_required
@admin_required
def get_training_item():
    """Get a single training data item by ID."""
    try:
        item_id = request.args.get("id", 0, type=int)
        if not item_id:
            return jsonify({"success": False, "message": "ID is required"})
        
        item = get_training_data_by_id(item_id)
        if not item:
            return jsonify({"success": False, "message": "Item not found"})
        
        return jsonify({"success": True, "data": item})
    except Exception as e:
        logger.error(f"Error getting training item: {str(e)}")
        return jsonify({"success": False, "message": str(e)})


@chat_training_bp.route("/training/update", methods=["POST"])
@login_required
@admin_required
def update_training():
    """Update existing training data."""
    try:
        item_id = request.form.get("id", 0, type=int)
        pattern = request.form.get("pattern", "").strip()
        response = request.form.get("response", "").strip()
        category = request.form.get("category", "").strip()
        priority = request.form.get("priority", 0, type=int)
        active = request.form.get("active") == "on"
        
        if not item_id:
            return jsonify({"success": False, "message": "ID is required"})
        
        if not pattern or not response:
            return jsonify({"success": False, "message": "Pattern and response are required"})
        
        success = update_training_data(
            id=item_id,
            pattern=pattern,
            response=response,
            category=category,
            priority=priority,
            active=active
        )
        
        if success:
            return jsonify({"success": True, "message": "Training data updated successfully"})
        else:
            return jsonify({"success": False, "message": "Error updating training data"})
    except Exception as e:
        logger.error(f"Error updating training data: {str(e)}")
        return jsonify({"success": False, "message": str(e)})


@chat_training_bp.route("/training/delete", methods=["POST"])
@login_required
@admin_required
def delete_training():
    """Delete training data."""
    try:
        item_id = request.form.get("id", 0, type=int)
        if not item_id:
            return jsonify({"success": False, "message": "ID is required"})
        
        success = delete_training_data(item_id)
        if success:
            return jsonify({"success": True, "message": "Training data deleted successfully"})
        else:
            return jsonify({"success": False, "message": "Error deleting training data"})
    except Exception as e:
        logger.error(f"Error deleting training data: {str(e)}")
        return jsonify({"success": False, "message": str(e)})

"""
UPS Routes Module
This module provides API routes for UPS shipping functionality.
"""

import os
import re
import logging
import dotenv
import pathlib
from flask import (
    Blueprint,
    redirect,
    request,
    jsonify,
    current_app,
    send_file,
    url_for,
    flash,
    session,
)
import traceback
import time


from integrations.ups.ups_service import UPSService
from integrations.ups.ups_config import get_ups_config
from integrations.ups.ups_client import UPSClient
from integrations.asana.asana_client import get_asana_client
from database.db_operations import extract_task_gid, get_asana_task_info

# Configure logging
logger = logging.getLogger(__name__)


# Load environment variables from database/.env file
def load_db_env_vars():
    """Load environment variables from database/.env file"""
    project_root = pathlib.Path(os.path.dirname(os.path.abspath(__file__))).parent
    db_env_path = project_root / "database" / ".env"

    if db_env_path.exists():
        logger.info(f"Loading environment variables from {db_env_path}")
        dotenv.load_dotenv(db_env_path)
    else:
        logger.warning(f"Database .env file not found at {db_env_path}")


# Load environment variables from database/.env file
load_db_env_vars()

# Create a Blueprint for UPS routes
ups_bp = Blueprint("ups", __name__, url_prefix="/ups")

# Initialize UPS service
ups_service = UPSService()


@ups_bp.route("/api/create-label", methods=["POST"])
def create_shipping_label():
    """Create a UPS shipping label from form submission"""
    try:
        # Get JSON data from request
        data = request.get_json()
        logger.info(f"Received shipping label request: {data}")

        # Extract Asana task GID if provided
        asana_task_gid = None
        asana_url = data.get("asana_url")
        if asana_url:
            asana_task_gid = extract_task_gid(asana_url)

        # Build recipient address
        ship_to_address = {
            "name": data.get("recipient_name", ""),
            "attention_name": data.get("attention_name", ""),
            "phone": data.get("phone", ""),
            "address_line1": data.get("address_line1", ""),
            "address_line2": data.get("address_line2", ""),
            "city": data.get("city", ""),
            "state_province": data.get("state_province", ""),
            "postal_code": data.get("postal_code", ""),
            "country_code": data.get("country_code", ""),
        }

        # Build package dimensions
        package_dimensions = {
            "length": data.get("length", ""),
            "width": data.get("width", ""),
            "height": data.get("height", ""),
            "weight": data.get("weight", ""),
            "unit_of_measurement": data.get("dimension_unit", "CM"),
            "weight_unit": data.get("weight_unit", "KGS"),
        }

        # Get service and package type
        service_code = data.get("service_code", "")
        package_type = data.get("package_type", "02")  # Default to small box

        # Build shipment request
        shipment_request = ups_service.build_shipment_request(
            ship_to_address=ship_to_address,
            package_dimensions=package_dimensions,
            service_code=service_code,
            package_type=package_type,
            description=data.get("description", "Wafer Shipment"),
            reference_number=data.get("reference_number", ""),
            is_return=data.get("is_return", False),
        )

        # Create shipment
        result = ups_service.create_shipment(shipment_request, asana_task_gid)

        if not result.get("success"):
            return (
                jsonify(
                    {
                        "success": False,
                        "message": result.get(
                            "message", "Failed to create shipping label"
                        ),
                    }
                ),
                400,
            )

        # Return success response
        return jsonify(
            {
                "success": True,
                "message": "Shipping label created successfully",
                "tracking_number": result.get("tracking_number", ""),
                "label_path": result.get("label_path", ""),
                "label_name": result.get("label_name", ""),
                "invoice_path": result.get("invoice_path", ""),
                "invoice_name": result.get("invoice_name", ""),
                "is_international": result.get("is_international", False),
                "asana_uploads": result.get("asana_uploads", []),
            }
        )

    except Exception as e:
        logger.error(f"Error creating UPS shipping label: {str(e)}")
        logger.error(traceback.format_exc())
        return (
            jsonify(
                {
                    "success": False,
                    "message": f"Error creating shipping label: {str(e)}",
                }
            ),
            500,
        )


@ups_bp.route("/api/track/<tracking_number>", methods=["GET"])
def track_package(tracking_number):
    """Track a UPS package by tracking number"""
    try:
        # Log the tracking request
        logger.info(f"Received tracking request for: {tracking_number}")

        # Track package
        result = ups_service.track_shipment(tracking_number)

        if not result.get("success"):
            status_code = result.get("status", 400)
            logger.warning(
                f"Tracking failed with status {status_code}: {result.get('message')}"
            )
            return (
                jsonify(
                    {
                        "success": False,
                        "message": result.get("message", "Failed to track package"),
                        "error_details": result.get("raw_response", {}),
                    }
                ),
                status_code,
            )

        # Return tracking information
        logger.info(f"Successfully tracked package: {tracking_number}")
        return jsonify(
            {
                "success": True,
                "message": "Package tracked successfully",
                "tracking_info": result.get("tracking_info", {}),
            }
        )

    except Exception as e:
        logger.error(f"Error tracking UPS package: {str(e)}")
        logger.error(traceback.format_exc())
        return (
            jsonify({"success": False, "message": f"Error tracking package: {str(e)}"}),
            500,
        )


@ups_bp.route("/api/rates", methods=["POST"])
def get_shipping_rates():
    """Get UPS shipping rates"""
    try:
        # Get JSON data from request
        data = request.get_json()
        logger.info(f"Received shipping rate request: {data}")

        # Build recipient address
        ship_to_address = {
            "name": data.get("recipient_name", ""),
            "attention_name": data.get("attention_name", ""),
            "phone": data.get("phone", ""),
            "address_line1": data.get("address_line1", ""),
            "address_line2": data.get("address_line2", ""),
            "city": data.get("city", ""),
            "state_province": data.get("state_province", ""),
            "postal_code": data.get("postal_code", ""),
            "country_code": data.get("country_code", ""),
        }

        # Build package dimensions
        package_dimensions = {
            "length": data.get("length", ""),
            "width": data.get("width", ""),
            "height": data.get("height", ""),
            "weight": data.get("weight", ""),
            "unit_of_measurement": data.get("dimension_unit", "CM"),
            "weight_unit": data.get("weight_unit", "KGS"),
        }

        # Get package type
        package_type = data.get("package_type", "02")  # Default to small box

        # Get UPS configuration
        ups_config = get_ups_config()

        # Use a simplified rate request structure
        rate_request = {
            "RateRequest": {
                "Request": {
                    "RequestOption": "Shop",
                    "TransactionReference": {
                        "CustomerContext": "Talaria Dashboard Rate Request"
                    },
                },
                "Shipment": {
                    "Shipper": {
                        "Name": ups_config.get("default_shipper", {}).get("name", ""),
                        "ShipperNumber": ups_config.get("account_number", ""),
                        "Address": {
                            "AddressLine": ups_config.get("default_shipper", {}).get(
                                "address_line1", ""
                            ),
                            "City": ups_config.get("default_shipper", {}).get(
                                "city", ""
                            ),
                            "StateProvinceCode": ups_config.get(
                                "default_shipper", {}
                            ).get("state_province", ""),
                            "PostalCode": ups_config.get("default_shipper", {}).get(
                                "postal_code", ""
                            ),
                            "CountryCode": ups_config.get("default_shipper", {}).get(
                                "country_code", ""
                            ),
                        },
                    },
                    "ShipTo": {
                        "Name": ship_to_address.get("name", ""),
                        "Address": {
                            "AddressLine": ship_to_address.get("address_line1", ""),
                            "City": ship_to_address.get("city", ""),
                            "StateProvinceCode": ship_to_address.get(
                                "state_province", ""
                            ),
                            "PostalCode": ship_to_address.get("postal_code", ""),
                            "CountryCode": ship_to_address.get("country_code", ""),
                        },
                    },
                    "Package": {
                        "PackagingType": {
                            "Code": package_type,
                            "Description": "Package",
                        },
                        "Dimensions": {
                            "UnitOfMeasurement": {
                                "Code": package_dimensions.get(
                                    "unit_of_measurement", "CM"
                                )
                            },
                            "Length": str(package_dimensions.get("length", "")),
                            "Width": str(package_dimensions.get("width", "")),
                            "Height": str(package_dimensions.get("height", "")),
                        },
                        "PackageWeight": {
                            "UnitOfMeasurement": {
                                "Code": package_dimensions.get("weight_unit", "KGS")
                            },
                            "Weight": str(package_dimensions.get("weight", "")),
                        },
                    },
                },
            }
        }

        # Get rates
        result = ups_service.get_shipping_rates(rate_request)

        if not result.get("success"):
            return (
                jsonify(
                    {
                        "success": False,
                        "message": result.get(
                            "message", "Failed to get shipping rates"
                        ),
                    }
                ),
                400,
            )

        # Return rates
        return jsonify(
            {
                "success": True,
                "message": "Shipping rates retrieved successfully",
                "rates": result.get("rates", []),
            }
        )

    except Exception as e:
        logger.error(f"Error getting UPS shipping rates: {str(e)}")
        logger.error(traceback.format_exc())
        return (
            jsonify(
                {"success": False, "message": f"Error getting shipping rates: {str(e)}"}
            ),
            500,
        )


@ups_bp.route("/api/validate-address", methods=["POST"])
def validate_address():
    """Validate an address using UPS Address Validation API"""
    try:
        # Get JSON data from request
        data = request.get_json()
        logger.info(f"Received address validation request: {data}")

        # Build address for validation
        address = {
            "AddressLine": data.get("address_line1", ""),
            "AddressLine2": data.get("address_line2", ""),
            "PoliticalDivision2": data.get("city", ""),
            "PoliticalDivision1": data.get("state_province", ""),
            "PostcodePrimaryLow": data.get("postal_code", ""),
            "CountryCode": data.get("country_code", ""),
        }

        # Validate address
        result = ups_service.validate_address(address)

        if not result.get("success"):
            status_code = result.get("status", 400)
            logger.warning(
                f"Address validation failed with status {status_code}: {result.get('message')}"
            )
            return (
                jsonify(
                    {
                        "success": False,
                        "message": result.get("message", "Failed to validate address"),
                        "error_details": result.get("raw_response", {}),
                    }
                ),
                status_code,
            )

        # Return validation results
        logger.info(f"Address validation completed: {result.get('is_valid')}")
        return jsonify(
            {
                "success": True,
                "message": "Address validation completed",
                "is_valid": result.get("is_valid", False),
                "validation_result": result.get("validation_result", ""),
                "suggested_addresses": result.get("suggested_addresses", []),
            }
        )

    except Exception as e:
        logger.error(f"Error validating address with UPS: {str(e)}")
        logger.error(traceback.format_exc())
        return (
            jsonify(
                {"success": False, "message": f"Error validating address: {str(e)}"}
            ),
            500,
        )


@ups_bp.route("/api/download/<file_type>/<tracking_number>", methods=["GET"])
def download_document(file_type, tracking_number):
    """Download a UPS shipping document"""
    try:
        # Get upload folder
        upload_folder = current_app.config.get("UPLOAD_FOLDER", "uploads")

        # Find the file
        file_prefix = "UPS_Label" if file_type == "label" else "UPS_Invoice"

        # List files in upload folder
        files = os.listdir(upload_folder)
        matching_files = [
            f for f in files if f.startswith(f"{file_prefix}_{tracking_number}_")
        ]

        if not matching_files:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": f"No {file_type} found for tracking number {tracking_number}",
                    }
                ),
                404,
            )

        # Get the most recent file
        matching_files.sort(reverse=True)
        file_name = matching_files[0]
        file_path = os.path.join(upload_folder, file_name)

        # Send the file
        return send_file(
            file_path,
            as_attachment=True,
            download_name=file_name,
            mimetype="image/gif" if file_type == "label" else "application/pdf",
        )

    except Exception as e:
        logger.error(f"Error downloading UPS document: {str(e)}")
        logger.error(traceback.format_exc())
        return (
            jsonify(
                {"success": False, "message": f"Error downloading document: {str(e)}"}
            ),
            500,
        )


@ups_bp.route("/api/get-asana-info", methods=["POST"])
def get_asana_info():
    """Get recipient information from an Asana task"""
    try:
        # Get JSON data from request
        data = request.get_json()
        asana_url = data.get("asana_url")

        if not asana_url:
            return jsonify({"success": False, "message": "Asana URL is required"}), 400

        # Extract task GID from URL
        task_gid = extract_task_gid(asana_url)
        if not task_gid:
            return (
                jsonify({"success": False, "message": "Invalid Asana URL format"}),
                400,
            )

        # Get task information from Asana
        task_info = get_asana_task_info(task_gid)
        if not task_info:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "Failed to retrieve Asana task information",
                    }
                ),
                400,
            )

        # Extract recipient information from task
        recipient_info = {}

        # Log the task info for debugging
        logger.info(f"Processing Asana task info: {task_info}")

        # Extract name from task name or custom fields
        recipient_info["recipient_name"] = task_info.get("Task Name", "")

        # Check for specific fields we know exist in the Asana tasks
        if "Contact person" in task_info:
            recipient_info["attention_name"] = task_info.get("Contact person", "")

        if "Telephone number (delivery contact)" in task_info:
            recipient_info["phone"] = task_info.get(
                "Telephone number (delivery contact)", ""
            )

        if (
            "Email (delivery contact)" in task_info
            and task_info.get("Email (delivery contact)") != "N/A"
        ):
            recipient_info["email"] = task_info.get("Email (delivery contact)", "")

        if "Shipping address" in task_info:
            # Parse the shipping address
            address = task_info.get("Shipping address", "")
            if address and address != "N/A":
                # Try to parse the address
                address_parts = address.split(",")
                if len(address_parts) >= 1:
                    recipient_info["address_line1"] = address_parts[0].strip()

                # Try to extract city, state, postal code, country
                if len(address_parts) >= 2:
                    # Last part might contain postal code and country
                    location_parts = address_parts[-1].strip().split()

                    # Try to find postal code (usually numeric or alphanumeric)
                    for part in location_parts:
                        if any(c.isdigit() for c in part):
                            recipient_info["postal_code"] = part
                            break

                    # Country is usually the last word
                    if location_parts:
                        recipient_info["country_code"] = location_parts[-1]

                    # City is usually in the second part
                    if len(address_parts) >= 2:
                        city_part = address_parts[1].strip()
                        # Remove postal code if it's in the city part
                        if (
                            recipient_info.get("postal_code")
                            and recipient_info["postal_code"] in city_part
                        ):
                            city_part = city_part.replace(
                                recipient_info["postal_code"], ""
                            ).strip()
                        recipient_info["city"] = city_part

        # Extract from task description if available
        description = task_info.get("Task Description", "")
        if description:
            # Look for email
            email_match = re.search(r"[\w.+-]+@[\w-]+\.[\w.-]+", description)
            if email_match and not recipient_info.get("email"):
                recipient_info["email"] = email_match.group(0)

        # If we have a tracking number, add it
        if "Tracking number" in task_info and task_info.get("Tracking number") != "N/A":
            recipient_info["tracking_number"] = task_info.get("Tracking number", "")

        # Extract information from task description
        description = task_info.get("notes", "")
        if description and not recipient_info.get("address_line1"):
            # Try to parse address from description
            lines = description.split("\n")
            address_lines = []
            for line in lines:
                if any(
                    keyword in line.lower()
                    for keyword in ["address", "street", "avenue", "road", "blvd"]
                ):
                    address_lines.append(line)

            if address_lines:
                recipient_info["address_line1"] = address_lines[0]
                if len(address_lines) > 1:
                    recipient_info["address_line2"] = address_lines[1]

        # Log the extracted information
        logger.info(f"Extracted recipient info: {recipient_info}")

        # Return recipient information
        return jsonify(
            {
                "success": True,
                "message": "Asana task information retrieved successfully",
                "recipient_info": recipient_info,
            }
        )

    except Exception as e:
        logger.error(f"Error getting Asana task information: {str(e)}")
        logger.error(traceback.format_exc())
        return (
            jsonify(
                {
                    "success": False,
                    "message": f"Error getting Asana task information: {str(e)}",
                }
            ),
            500,
        )


@ups_bp.route("/api/upload-to-asana", methods=["POST"])
def upload_to_asana_task():
    """Upload UPS documents to an Asana task"""
    try:
        # Get JSON data from request
        data = request.get_json()
        logger.info(f"Received Asana upload request: {data}")

        # Extract required parameters
        tracking_number = data.get("tracking_number")
        asana_task_gid = data.get("asana_task_gid")

        if not tracking_number or not asana_task_gid:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "Tracking number and Asana task GID are required",
                    }
                ),
                400,
            )

        # Get upload folder
        upload_folder = current_app.config.get("UPLOAD_FOLDER", "uploads")

        # Find the label file
        label_files = [
            f
            for f in os.listdir(upload_folder)
            if f.startswith(f"UPS_Label_{tracking_number}_")
        ]

        # Find the invoice file (if any)
        invoice_files = [
            f
            for f in os.listdir(upload_folder)
            if f.startswith(f"UPS_Invoice_{tracking_number}_")
        ]

        if not label_files:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": (
                            f"No label found for tracking number {tracking_number}"
                        ),
                    }
                ),
                404,
            )

        # Get Asana client
        asana_client = get_asana_client()

        # Upload files to Asana
        uploads = []

        # Upload label
        label_file = label_files[0]
        label_path = os.path.join(upload_folder, label_file)

        from integrations.ups.ups_utils import upload_to_asana

        label_uploaded = upload_to_asana(label_path, asana_task_gid, asana_client)
        uploads.append({"file_name": label_file, "uploaded": label_uploaded})

        # Upload invoice if available
        if invoice_files:
            invoice_file = invoice_files[0]
            invoice_path = os.path.join(upload_folder, invoice_file)
            invoice_uploaded = upload_to_asana(
                invoice_path, asana_task_gid, asana_client
            )
            uploads.append({"file_name": invoice_file, "uploaded": invoice_uploaded})

        # Return results
        return jsonify(
            {
                "success": True,
                "message": "Documents uploaded to Asana successfully",
                "uploads": uploads,
            }
        )

    except Exception as e:
        logger.error(f"Error uploading UPS documents to Asana: {str(e)}")
        logger.error(traceback.format_exc())
        return (
            jsonify(
                {
                    "success": False,
                    "message": f"Error uploading documents to Asana: {str(e)}",
                }
            ),
            500,
        )


@ups_bp.route("/auth/login")
def auth_login():
    """Get UPS access token using client credentials flow"""
    try:
        # Create UPS client
        client = UPSClient(
            use_sandbox=os.environ.get("UPS_USE_SANDBOX", "True").lower() == "true"
        )

        # Get a new token
        token = client._get_auth_token()

        if token:
            logger.info("Successfully obtained UPS access token")
            flash("Successfully connected to UPS", "success")
        else:
            logger.warning("Failed to obtain UPS access token")
            flash("Failed to connect to UPS. Please check your credentials.", "error")

        return redirect(url_for("ups_views.shipping_page"))

    except Exception as e:
        logger.error(f"Error authenticating with UPS: {str(e)}")
        flash(f"UPS authentication error: {str(e)}", "error")
        return redirect(url_for("ups_views.shipping_page"))


@ups_bp.route("/api/check-auth", methods=["GET"])
def check_auth():
    """Check if UPS OAuth is authenticated"""

    # Check if we have a valid token
    if (
        session.get("ups_access_token")
        and session.get("ups_token_expires", 0) > time.time()
    ):
        logger.info("Valid UPS access token found in session")
        return jsonify({"authenticated": True})

    # Try to get a new token using client credentials
    try:
        # Create UPS client
        client = UPSClient(
            use_sandbox=os.environ.get("UPS_USE_SANDBOX", "True").lower() == "true"
        )

        # Get a new token
        token = client._get_auth_token()

        if token:
            logger.info("Successfully obtained new UPS access token")
            return jsonify({"authenticated": True})
        else:
            logger.warning("Failed to obtain UPS access token")
            return jsonify({"authenticated": False}), 401

    except Exception as e:
        logger.error(f"Error checking UPS authentication: {str(e)}")
        # Clear invalid tokens
        session.pop("ups_access_token", None)
        session.pop("ups_token_expires", None)

    logger.info("No valid UPS authentication found")
    return jsonify({"authenticated": False}), 401

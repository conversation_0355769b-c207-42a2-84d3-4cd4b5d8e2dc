"""RFQ Routes for Talaria Dashboard.

This module handles the web routes for RFQ (Request for Quotation) email automation.
Provides endpoints for file upload, email sending, and status monitoring.

Developed for Ligentec SA - RFQ Email Automation Feature
"""

import logging
import os
from io import BytesIO

import pandas as pd
from flask import (
    Blueprint,
    current_app,
    flash,
    jsonify,
    redirect,
    render_template,
    request,
    send_file,
    session,
    url_for,
)
from flask_mail import Mail
from werkzeug.utils import secure_filename

from core.auth.auth import check_permission, login_required
from core.services.rfq_email_service import RFQEmailService

# Configure logging
logger = logging.getLogger(__name__)

# Create blueprint
rfq_bp = Blueprint("rfq", __name__, url_prefix="/rfq")

# Initialize mail instance (will be set when app starts)
mail = None


def init_rfq_service():
    """Initialize RFQ service with mail instance."""
    global mail
    if mail is None:
        mail = Mail(current_app)
    return RFQEmailService(mail)


@rfq_bp.route("/")
@login_required
def rfq_automation():
    """Display the RFQ automation interface."""
    try:
        # Check if user has permission to use RFQ automation
        if not check_permission("modify"):
            flash("You do not have permission to access RFQ automation.", "error")
            return redirect(url_for("home"))

        # Check if there's existing RFQ data in session
        existing_data = None
        if session.get("rfq_file_path") and session.get("rfq_records_count"):
            try:
                # Verify file still exists
                file_path = session["rfq_file_path"]
                if os.path.exists(file_path):
                    # Get fresh data from file
                    rfq_service = init_rfq_service()
                    rfq_records = rfq_service.parse_excel_file(file_path)

                    if rfq_records:
                        # Prepare preview data
                        preview_data = []
                        for record in rfq_records[
                            :5
                        ]:  # Show first 5 records as preview
                            preview_data.append(
                                {
                                    "order_id": record["order_id"],
                                    "project_name": record["project_name"],
                                    "links_count": len(record["links"]),
                                    "priority": record["priority"],
                                }
                            )

                        existing_data = {
                            "file_name": os.path.basename(file_path),
                            "records_count": len(rfq_records),
                            "preview_data": preview_data,
                            "file_size": round(
                                os.path.getsize(file_path) / (1024 * 1024), 2
                            ),  # Size in MB
                        }
            except Exception as e:
                logger.warning(f"Error loading existing RFQ data: {str(e)}")
                # Clear invalid session data
                session.pop("rfq_file_path", None)
                session.pop("rfq_records_count", None)

        return render_template("rfq_automation.html", existing_data=existing_data)

    except Exception as e:
        logger.error(f"Error loading RFQ automation page: {str(e)}")
        flash("Error loading RFQ automation page.", "error")
        return redirect(url_for("home"))


@rfq_bp.route("/upload", methods=["POST"])
@login_required
def upload_rfq_file():
    """Handle RFQ Excel file upload and validation."""
    try:
        # Check permissions
        if not check_permission("modify"):
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "You do not have permission to upload RFQ files.",
                    }
                ),
                403,
            )

        # Check if file was uploaded
        if "rfq_file" not in request.files:
            return jsonify({"success": False, "message": "No file uploaded."}), 400

        file = request.files["rfq_file"]

        # Initialize RFQ service
        rfq_service = init_rfq_service()

        # Validate file
        is_valid, error_message = rfq_service.validate_file(file)
        if not is_valid:
            return jsonify({"success": False, "message": error_message}), 400

        # Save file
        file_path = rfq_service.save_uploaded_file(file)

        # Parse Excel file
        rfq_records = rfq_service.parse_excel_file(file_path)

        if not rfq_records:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "No valid RFQ records found in the uploaded file.",
                    }
                ),
                400,
            )

        # Store file path in session for later use
        from flask import session

        session["rfq_file_path"] = file_path
        session["rfq_records_count"] = len(rfq_records)

        # Return preview data
        preview_data = []
        for record in rfq_records[:5]:  # Show first 5 records as preview
            preview_data.append(
                {
                    "order_id": record["order_id"],
                    "project_name": record["project_name"],
                    "links_count": len(record["links"]),
                    "priority": record["priority"],
                }
            )

        return jsonify(
            {
                "success": True,
                "message": f"File uploaded successfully. Found {len(rfq_records)} RFQ records.",
                "records_count": len(rfq_records),
                "preview_data": preview_data,
            }
        )

    except Exception as e:
        logger.error(f"Error uploading RFQ file: {str(e)}")
        return (
            jsonify({"success": False, "message": f"Error processing file: {str(e)}"}),
            500,
        )


@rfq_bp.route("/send-emails", methods=["POST"])
@login_required
def send_rfq_emails():
    """Send RFQ emails based on uploaded file."""
    try:
        # Check permissions
        if not check_permission("modify"):
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "You do not have permission to send RFQ emails.",
                    }
                ),
                403,
            )

        # Get request data
        data = request.get_json()
        if data is None:
            data = {}
        test_mode = data.get("test_mode", True)  # Default to test mode for safety

        # Get file path from session
        from flask import session

        file_path = session.get("rfq_file_path")

        if not file_path or not os.path.exists(file_path):
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "No uploaded file found. Please upload a file first.",
                    }
                ),
                400,
            )

        # Initialize RFQ service
        rfq_service = init_rfq_service()

        # Parse file again to get fresh data
        rfq_records = rfq_service.parse_excel_file(file_path)

        if not rfq_records:
            return (
                jsonify({"success": False, "message": "No valid RFQ records found."}),
                400,
            )

        # Send emails
        results = rfq_service.send_rfq_emails(
            rfq_records, file_path, test_mode=test_mode
        )

        # Note: Session data is NOT automatically cleared after sending emails
        # This allows users to navigate away and return without re-uploading
        # Users can manually clear session data using the "Clear Session" button

        # Prepare response
        if results["errors"]:
            status_message = f"Partially completed: {results['emails_sent']} emails sent, {len(results['errors'])} errors"
            success = results["emails_sent"] > 0
        else:
            status_message = f"Successfully sent {results['emails_sent']} RFQ emails"
            success = True

        mode_text = "TEST MODE" if test_mode else "PRODUCTION MODE"

        return jsonify(
            {
                "success": success,
                "message": f"{status_message} ({mode_text})",
                "results": {
                    "total_records": results["total_records"],
                    "emails_sent": results["emails_sent"],
                    "errors_count": len(results["errors"]),
                    "errors": results["errors"][:5],  # Show first 5 errors
                    "test_mode": test_mode,
                },
            }
        )

    except Exception as e:
        logger.error(f"Error sending RFQ emails: {str(e)}")
        return (
            jsonify({"success": False, "message": f"Error sending emails: {str(e)}"}),
            500,
        )


@rfq_bp.route("/test-email", methods=["POST"])
@login_required
def send_test_email():
    """Send a test RFQ email to verify configuration."""
    try:
        # Check permissions
        if not check_permission("modify"):
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "You do not have permission to send test emails.",
                    }
                ),
                403,
            )

        # Initialize RFQ service
        rfq_service = init_rfq_service()

        # Create test record
        test_record = {
            "order_id": "TEST001",
            "project_name": "Test Project - Email Configuration",
            "links": [
                "https://example.com/test-link-1",
                "https://example.com/test-link-2",
            ],
            "priority": "Test",
            "notes": "This is a test email to verify RFQ automation configuration.",
        }

        # Create a temporary test Excel file for the test email
        test_data = [test_record]
        test_df = pd.DataFrame(test_data)

        # Create temporary file
        import tempfile

        with tempfile.NamedTemporaryFile(suffix=".xlsx", delete=False) as tmp_file:
            test_df.to_excel(tmp_file.name, index=False)
            temp_file_path = tmp_file.name

        try:
            # Send test email (always in test mode)
            rfq_service._send_single_rfq_email(
                test_record, temp_file_path, test_mode=True
            )
        finally:
            # Clean up temporary file
            try:
                os.remove(temp_file_path)
            except:
                pass

        return jsonify(
            {
                "success": True,
                "message": "Test email sent <NAME_EMAIL>",
            }
        )

    except Exception as e:
        logger.error(f"Error sending test email: {str(e)}")
        return (
            jsonify(
                {"success": False, "message": f"Error sending test email: {str(e)}"}
            ),
            500,
        )


@rfq_bp.route("/status")
@login_required
def rfq_status():
    """Get current RFQ automation status."""
    try:
        from flask import session

        status = {
            "file_uploaded": "rfq_file_path" in session,
            "records_count": session.get("rfq_records_count", 0),
            "upload_folder_exists": os.path.exists(
                os.path.join(current_app.root_path, "uploads/rfq")
            ),
        }

        return jsonify({"success": True, "status": status})

    except Exception as e:
        logger.error(f"Error getting RFQ status: {str(e)}")
        return (
            jsonify({"success": False, "message": f"Error getting status: {str(e)}"}),
            500,
        )


@rfq_bp.route("/send_test", methods=["POST"])
@login_required
def send_form_test_email():
    """Send test RFQ email using form data."""
    try:
        # Check permissions
        if not check_permission("modify"):
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "You do not have permission to send test emails.",
                    }
                ),
                403,
            )

        # Get form data
        form_data = {
            "sifoProject": request.form.get("sifoProject", "").strip(),
            "talosLotId": request.form.get("talosLotId", "").strip(),
            "lotProject": request.form.get("lotProject", "").strip(),
            "xfabCloudUrl": request.form.get("xfabCloudUrl", "").strip(),
        }

        # Validate required fields
        required_fields = [
            "sifoProject",
            "talosLotId",
            "lotProject",
            "xfabCloudUrl",
        ]
        missing_fields = [field for field in required_fields if not form_data[field]]

        if missing_fields:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": f"Missing required fields: {', '.join(missing_fields)}",
                    }
                ),
                400,
            )

        # Handle PDF file if uploaded
        pdf_file_path = None
        if "pdfFile" in request.files:
            pdf_file = request.files["pdfFile"]
            if pdf_file and pdf_file.filename:
                # Save PDF file temporarily
                pdf_filename = secure_filename(pdf_file.filename)
                pdf_file_path = os.path.join(
                    current_app.root_path, "uploads", "rfq", f"temp_{pdf_filename}"
                )
                os.makedirs(os.path.dirname(pdf_file_path), exist_ok=True)
                pdf_file.save(pdf_file_path)

        # Initialize RFQ service and send email
        rfq_service = init_rfq_service()
        results = rfq_service.send_form_based_rfq_email(
            form_data, pdf_file_path, test_mode=True
        )

        # Clean up temporary PDF file
        if pdf_file_path and os.path.exists(pdf_file_path):
            try:
                os.remove(pdf_file_path)
            except:
                pass

        if results["errors"]:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": f"Error sending test email: {results['errors'][0]}",
                    }
                ),
                500,
            )

        return jsonify(
            {
                "success": True,
                "message": "Test email sent <NAME_EMAIL>",
                "results": results,
            }
        )

    except Exception as e:
        logger.error(f"Error sending form-based test email: {str(e)}")
        return (
            jsonify(
                {"success": False, "message": f"Error sending test email: {str(e)}"}
            ),
            500,
        )


@rfq_bp.route("/send", methods=["POST"])
@login_required
def send_form_rfq_email():
    """Send RFQ email using form data."""
    try:
        # Check permissions
        if not check_permission("modify"):
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "You do not have permission to send RFQ emails.",
                    }
                ),
                403,
            )

        # Get form data
        form_data = {
            "sifoProject": request.form.get("sifoProject", "").strip(),
            "talosLotId": request.form.get("talosLotId", "").strip(),
            "lotProject": request.form.get("lotProject", "").strip(),
            "xfabCloudUrl": request.form.get("xfabCloudUrl", "").strip(),
        }

        # Get mode (test or production) - default to production
        test_mode = request.form.get("mode", "production") == "test"

        # Validate required fields
        required_fields = [
            "sifoProject",
            "talosLotId",
            "lotProject",
            "xfabCloudUrl",
        ]
        missing_fields = [field for field in required_fields if not form_data[field]]

        if missing_fields:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": f"Missing required fields: {', '.join(missing_fields)}",
                    }
                ),
                400,
            )

        # Handle PDF file if uploaded
        pdf_file_path = None
        if "pdfFile" in request.files:
            pdf_file = request.files["pdfFile"]
            if pdf_file and pdf_file.filename:
                # Save PDF file temporarily
                pdf_filename = secure_filename(pdf_file.filename)
                pdf_file_path = os.path.join(
                    current_app.root_path, "uploads", "rfq", f"temp_{pdf_filename}"
                )
                os.makedirs(os.path.dirname(pdf_file_path), exist_ok=True)
                pdf_file.save(pdf_file_path)

        # Initialize RFQ service and send email
        rfq_service = init_rfq_service()
        results = rfq_service.send_form_based_rfq_email(
            form_data, pdf_file_path, test_mode=test_mode
        )

        # Clean up temporary PDF file
        if pdf_file_path and os.path.exists(pdf_file_path):
            try:
                os.remove(pdf_file_path)
            except:
                pass

        if results["errors"]:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": f"Error sending RFQ email: {results['errors'][0]}",
                    }
                ),
                500,
            )

        mode_text = "TEST MODE" if test_mode else "PRODUCTION MODE"
        return jsonify(
            {
                "success": True,
                "message": f"RFQ email sent successfully ({mode_text})",
                "results": results,
            }
        )

    except Exception as e:
        logger.error(f"Error sending form-based RFQ email: {str(e)}")
        return (
            jsonify(
                {"success": False, "message": f"Error sending RFQ email: {str(e)}"}
            ),
            500,
        )


@rfq_bp.route("/clear-session", methods=["POST"])
@login_required
def clear_rfq_session():
    """Clear RFQ session data."""
    try:
        from flask import session

        # Remove RFQ-related session data
        session.pop("rfq_file_path", None)
        session.pop("rfq_records_count", None)

        return jsonify({"success": True, "message": "Session cleared successfully"})

    except Exception as e:
        logger.error(f"Error clearing RFQ session: {str(e)}")
        return (
            jsonify({"success": False, "message": f"Error clearing session: {str(e)}"}),
            500,
        )


@rfq_bp.route("/download-template")
@login_required
def download_template():
    """Download RFQ Excel template file."""
    try:
        # Create sample data for the template
        sample_data = [
            {
                "Project_Name": "Sample Project Alpha",
                "Links": "https://cloud.xfab.com/index.php/s/A3on9tSzbQ2ctkt?path=%2F1-Operations%2FRFQ_SoW_batch_ID%2FAN350_05-114_femto_icomb_run1&openfile=30220756, https://cloud.xfab.com/index.php/s/B4po0uTzcR3dukt?path=%2F2-Operations%2FRFQ_SoW_batch_ID%2FAN350_05-115_femto_icomb_run2&openfile=30220757",
                "Order_ID": "RFQ001",
                "Priority": "High",
                "Notes": "Urgent project requiring immediate quotation",
            },
            {
                "Project_Name": "Sample Project Beta",
                "Links": "https://cloud.xfab.com/index.php/s/C5qp1vUadS4evlt?path=%2F3-Operations%2FRFQ_SoW_batch_ID%2FAN350_05-116_femto_icomb_run3&openfile=30220758",
                "Order_ID": "RFQ002",
                "Priority": "Medium",
                "Notes": "Standard project timeline",
            },
            {
                "Project_Name": "Sample Project Gamma",
                "Links": "https://cloud.xfab.com/index.php/s/D6rq2wVbeT5fwmu?path=%2F4-Operations%2FRFQ_SoW_batch_ID%2FAN350_05-117_femto_icomb_run4&openfile=30220759, https://cloud.xfab.com/index.php/s/E7sr3xWcfU6gxnv?path=%2F5-Operations%2FRFQ_SoW_batch_ID%2FAN350_05-118_femto_icomb_run5&openfile=30220760, https://cloud.xfab.com/index.php/s/F8ts4yXdgV7hyow?path=%2F6-Operations%2FRFQ_SoW_batch_ID%2FAN350_05-119_femto_icomb_run6&openfile=30220761",
                "Order_ID": "RFQ003",
                "Priority": "Low",
                "Notes": "Future planning project with multiple links",
            },
        ]

        # Create DataFrame
        df = pd.DataFrame(sample_data)

        # Create Excel file in memory
        output = BytesIO()
        with pd.ExcelWriter(output, engine="openpyxl") as writer:
            df.to_excel(writer, sheet_name="RFQ_Data", index=False)

        output.seek(0)

        return send_file(
            output,
            mimetype="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            as_attachment=True,
            download_name="RFQ_Template.xlsx",
        )

    except Exception as e:
        logger.error(f"Error creating template: {str(e)}")
        flash("Error creating template file.", "error")
        return redirect(url_for("rfq.rfq_automation"))


# Error handlers for the blueprint
@rfq_bp.errorhandler(413)
def file_too_large(error):
    """Handle file too large error."""
    return (
        jsonify({"success": False, "message": "File too large. Maximum size is 10MB."}),
        413,
    )


@rfq_bp.errorhandler(500)
def internal_error(error):
    """Handle internal server errors."""
    logger.error(f"Internal error in RFQ routes: {str(error)}")
    return (
        jsonify(
            {
                "success": False,
                "message": "Internal server error. Please try again later.",
            }
        ),
        500,
    )

"""
UPS Views Module
This module provides view routes for UPS shipping pages.
"""

from flask import Blueprint, render_template, current_app
import logging

# Configure logging
logger = logging.getLogger(__name__)

# Create a Blueprint for UPS views
ups_views_bp = Blueprint("ups_views", __name__, url_prefix="/ups")


@ups_views_bp.route("/shipping")
def shipping_page():
    """Render the UPS shipping page"""
    return render_template("ups/shipping.html")


@ups_views_bp.route("/tracking")
def tracking_page():
    """Render the UPS tracking page"""
    return render_template("ups/shipping.html", active_tab="tracking")

"""
Helper functions for inventory search operations.
"""

from flask import jsonify, current_app


def get_text_search_fields():
    """
    Return a dictionary of field names and their corresponding SQL query parts.
    Keeps lines within flake8 limits.
    """
    return {
        "wafer_id": "wi.wafer_id ILIKE %s",
        "lot_id": "w.lot_id ILIKE %s",
        "xfab_id": (
            "(xfl.xfab_fr_lot_id ILIKE %s OR "
            "wi.metadata->'lgt'->>'xfab_lot_id' ILIKE %s)"
        ),
        "mask_set_id": (
            "(l.mask_set_id ILIKE %s OR " "wi.metadata->'lgt'->>'mask_set_id' ILIKE %s)"
        ),
        "module_name": (
            "(wi.metadata->'lgt'->>'Modules' ILIKE %s OR "
            "wi.metadata->>'Modules' ILIKE %s)"
        ),
        "cassette_id": "wi.cassette_id ILIKE %s",
    }


def build_inventory_search_query(data):
    """Build base query and where clause for inventory search."""
    # Base query
    base_query = """
        FROM wafer_inventory wi
        LEFT JOIN wafers w ON wi.wafer_id = w.wafer_id
        LEFT JOIN lots l ON w.lot_id = l.lot_id
        LEFT JOIN locations loc ON wi.location_id = loc.location_id
        LEFT JOIN xfab_fr_lots xfl ON w.lot_id = xfl.lot_id
        WHERE 1=1
    """

    # Build where clause and params
    where_clause = []
    params = []

    # Get and process text search fields
    text_search_fields = get_text_search_fields()
    for field, query in text_search_fields.items():
        if data.get(field):
            search_value = f"%{data[field].strip()}%"
            if field in ["xfab_id", "mask_set_id", "module_name"]:
                # These fields need to search in both locations
                where_clause.append(query)
                params.extend([search_value, search_value])
            else:
                where_clause.append(query)
                params.append(search_value)

    # Add exact match fields
    add_exact_match_fields(data, where_clause, params)

    # Add date range fields
    add_date_range_fields(data, where_clause, params)

    return base_query, where_clause, params


def add_exact_match_fields(data, where_clause, params):
    """
    Add exact match fields to where clause and params.

    Args:
        data: Request data dictionary
        where_clause: List to append where clause conditions
        params: List to append query parameters
    """
    if data.get("slot_id"):
        where_clause.append("wi.slot_id = %s")
        params.append(data["slot_id"])

    if data.get("location_id"):
        where_clause.append("wi.location_id = %s")
        params.append(data["location_id"])


def add_date_range_fields(data, where_clause, params):
    """
    Add date range fields to where clause and params.

    Args:
        data: Request data dictionary
        where_clause: List to append where clause conditions
        params: List to append query parameters
    """
    date_ranges = [
        ("arrived_at_from", "wi.arrived_at >= %s::timestamp"),
        ("arrived_at_to", "wi.arrived_at <= %s::timestamp"),
        ("sent_at_from", "wi.sent_at >= %s::timestamp"),
        ("sent_at_to", "wi.sent_at <= %s::timestamp"),
    ]

    for field, query in date_ranges:
        if data.get(field):
            where_clause.append(query)
            time_suffix = "00:00:00" if field.endswith("_from") else "23:59:59"
            params.append(f"{data[field]} {time_suffix}")


def build_search_data_query(base_query, where_clause, sort_clause, sort_direction):
    """
    Build the SQL query for fetching search results.

    Args:
        base_query: Base SQL query string
        where_clause: List of where conditions
        sort_clause: Field to sort by
        sort_direction: Sort direction (ASC or DESC)

    Returns:
        str: Complete SQL query
    """
    # Add debug information to track the query being executed
    current_app.logger.info(
        f"Building search query with sort by {sort_clause} {sort_direction}"
    )

    query = f"""
        WITH sorted_data AS (
            SELECT
                wi.wafer_id,
                wi.cassette_id,
                wi.slot_id,
                wi.arrived_at,
                wi.sent_at,
                wi.location_id,
                COALESCE(w.lot_id, '') as lot_id,
                COALESCE(
                    wi.metadata->'lgt'->>'xfab_lot_id',
                    xfl.xfab_fr_lot_id,
                    ''
                ) as xfab_id,
                COALESCE(
                    wi.metadata->'lgt'->>'Modules',
                    wi.metadata->>'Modules',
                    ''
                ) as module_name,
                COALESCE(
                    wi.metadata->'lgt'->>'mask_set_id',
                    l.mask_set_id,
                    ''
                ) as mask_set_id,
                COALESCE(loc.label, wi.location_id) as location_label,
                -- Include the raw metadata for debugging
                wi.metadata,
                ROW_NUMBER() OVER (
                    ORDER BY {sort_clause} {sort_direction}
                ) as row_num
            {base_query}
            {' AND ' + ' AND '.join(where_clause) if where_clause else ''}
        )
        SELECT * FROM sorted_data
        LIMIT %s OFFSET %s
    """

    # Log the final query for debugging
    current_app.logger.info(f"Generated search query: {query}")

    return query


def create_search_response(results, page, page_size, total, offset, mode):
    """
    Create a standardized search response.

    Args:
        results: List of formatted results
        page: Current page number
        page_size: Number of items per page
        total: Total number of records
        offset: Offset for pagination
        mode: Search mode ("online" or "offline")

    Returns:
        flask.Response: JSON response
    """
    return jsonify(
        {
            "success": True,
            "data": results,
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total": total,
                "start": offset + 1,
                "end": min(offset + page_size, total),
                "total_pages": (total + page_size - 1) // page_size,
            },
            "mode": mode,
        }
    )


def perform_offline_search_with_response(data, page, page_size, offset):
    """
    Perform offline search and format response.

    Args:
        data: Request data dictionary
        page: Current page number
        page_size: Number of items per page
        offset: Offset for pagination

    Returns:
        flask.Response: JSON response with search results
    """
    try:
        paginated_results, total_records = perform_offline_search(
            data, page, page_size, offset
        )

        return create_search_response(
            paginated_results, page, page_size, total_records, offset, "offline"
        )
    except Exception as cache_error:
        current_app.logger.error(f"Offline search failed: {str(cache_error)}")
        raise cache_error


def build_sort_clause(data):
    """Build sorting clause based on user preferences."""
    sort_field = data.get("sort_field")
    sort_direction = data.get("sort_direction", "asc").upper()

    valid_sort_fields = get_valid_sort_fields()

    sort_clause = valid_sort_fields.get(sort_field, "wi.arrived_at")
    sort_direction = sort_direction if sort_direction in ["ASC", "DESC"] else "DESC"

    return sort_clause, sort_direction


def get_valid_sort_fields():
    """
    Return a dictionary of valid sort fields for inventory queries.

    Returns:
        dict: Mapping of API field names to database field names
    """
    return {
        "wafer_id": "wi.wafer_id",
        "lot_id": "w.lot_id",
        "xfab_id": "xfl.xfab_fr_lot_id",
        "module_name": "wi.metadata->>'Modules'",
        "cassette_id": "wi.cassette_id",
        "mask_set_id": "l.mask_set_id",
        "slot_id": "wi.slot_id",
        "location_id": "wi.location_id",
        "arrived_at": "wi.arrived_at",
        "sent_at": "wi.sent_at",
    }


def format_inventory_results(results):
    """Format inventory search results for API response with better metadata."""
    formatted_results = []
    for row in results:
        formatted_row = create_formatted_row(row)
        formatted_results.append(formatted_row)

    # Log a sample result for debugging
    if formatted_results:
        current_app.logger.info(f"Sample formatted result: {formatted_results[0]}")

    return formatted_results


def create_formatted_row(row):
    """
    Create a formatted row for API response with improved metadata extraction.

    Args:
        row: Database result row

    Returns:
        dict: Formatted row
    """
    # Debug log to see what's in the row
    metadata = row.get("metadata", {})
    current_app.logger.debug(f"Formatting row with metadata: {metadata}")

    # Extract metadata fields from the 'lgt' section if available
    if metadata and "lgt" in metadata:
        lgt = metadata["lgt"]

        # Get fields from lgt section primarily
        module_name = lgt.get("Modules", row.get("module_name", ""))
        xfab_id = lgt.get("xfab_lot_id", row.get("xfab_id", ""))
        mask_set_id = lgt.get("mask_set_id", row.get("mask_set_id", ""))
    else:
        # Get from row if metadata isn't available
        module_name = row.get("module_name", "")
        xfab_id = row.get("xfab_id", "")
        mask_set_id = row.get("mask_set_id", "")

    # Log the extracted values for debugging
    current_app.logger.debug(
        f"Extracted values - module_name: {module_name}, "
        f"xfab_id: {xfab_id}, mask_set_id: {mask_set_id}"
    )

    # Format timestamps
    arrived_at = format_timestamp(row.get("arrived_at"))
    sent_at = format_timestamp(row.get("sent_at"))

    return {
        "row_number": row["row_num"],
        "wafer_id": row["wafer_id"],
        "lot_id": row["lot_id"],
        "xfab_id": xfab_id,
        "module_name": module_name,
        "cassette_id": row["cassette_id"],
        "mask_set_id": mask_set_id,
        "slot_id": row["slot_id"],
        "location_id": row["location_id"],
        "location_label": row["location_label"],
        "arrived_at": arrived_at,
        "sent_at": sent_at,
    }


def format_timestamp(timestamp):
    """
    Format a timestamp to ISO format or None.

    Args:
        timestamp: Datetime object or None

    Returns:
        str or None: ISO formatted timestamp or None
    """
    if timestamp:
        return timestamp.isoformat()
    return None


def perform_offline_search(data, page, page_size, offset):
    """
    Perform offline search using cached data.

    Args:
        data: Request data dictionary
        page: Current page number
        page_size: Number of items per page
        offset: Offset for pagination

    Returns:
        tuple: (paginated results, total records)
    """
    if not hasattr(current_app, "inventory_storage"):
        raise Exception("Offline storage not available")

    # Get cached results
    cached_results = current_app.inventory_storage.get_inventory(data)

    # Apply filters to cached results
    filtered_results = filter_cached_results(cached_results, data)

    # Apply pagination
    total_records = len(filtered_results)
    paginated_results = filtered_results[offset : offset + page_size]

    return paginated_results, total_records


def filter_cached_results(results, filters):
    """
    Apply filters to cached results.

    Args:
        results: List of cached results
        filters: Dictionary of filters to apply

    Returns:
        list: Filtered results
    """
    filtered = results.copy()

    for key, value in filters.items():
        if value and key in [
            "wafer_id",
            "lot_id",
            "xfab_id",
            "module_name",
            "cassette_id",
        ]:
            filtered = [
                r for r in filtered if value.lower() in str(r.get(key, "")).lower()
            ]
        elif key == "slot_id" and value:
            filtered = [r for r in filtered if r.get("slot_id") == value]
        elif key == "location_id" and value:
            filtered = [r for r in filtered if r.get("location_id") == value]
        # Handle date filters
        elif key.endswith("_from") and value:
            base_key = key.replace("_from", "")
            filtered = [r for r in filtered if r.get(base_key) and r[base_key] >= value]
        elif key.endswith("_to") and value:
            base_key = key.replace("_to", "")
            filtered = [r for r in filtered if r.get(base_key) and r[base_key] <= value]

    return filtered

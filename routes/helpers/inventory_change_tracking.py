"""
Helper functions for tracking changes to inventory items.
"""

import json
import traceback
from flask import current_app
from datetime import datetime, timezone
from typing import Dict


def track_wafer_change(
    cursor, wafer_id: str, change_type: str, old_values: Dict, new_values: Dict
) -> None:
    """
    Track changes to wafer inventory with improved change detection and storage.
    """
    try:
        # Get current metadata
        cursor.execute(
            """
            SELECT metadata FROM wafer_inventory WHERE wafer_id = %s
        """,
            [wafer_id],
        )
        result = cursor.fetchone()
        if not result:
            current_app.logger.warning(
                f"No wafer found with ID {wafer_id} for change tracking"
            )
            return

        # When using DictCursor, we access by column name instead of index
        current_metadata = result["metadata"] if result["metadata"] else {}

        current_app.logger.info(f"Current metadata for {wafer_id}: {current_metadata}")

        # Initialize change history if it doesn't exist
        if "change_history" not in current_metadata:
            current_metadata["change_history"] = []

        # Create change record and identify specific changes
        change_record = create_change_record(change_type, old_values, new_values)

        # Add new change record to history
        current_metadata["change_history"] = [change_record] + current_metadata.get(
            "change_history", []
        )[:9]

        # Update metadata in database
        current_app.logger.info(f"Updating metadata for {wafer_id} with change history")
        cursor.execute(
            """
            UPDATE wafer_inventory
            SET metadata = %s::jsonb
            WHERE wafer_id = %s
        """,
            [json.dumps(current_metadata), wafer_id],
        )

    except Exception as e:
        current_app.logger.error(f"Error tracking change: {str(e)}")
        current_app.logger.error(traceback.format_exc())


def create_change_record(change_type, old_values, new_values):
    """
    Create a change record with detailed change information.

    Args:
        change_type: Type of change (e.g., "UPDATE", "INSERT")
        old_values: Dictionary of original values
        new_values: Dictionary of updated values

    Returns:
        dict: Change record
    """
    # Identify specific changes
    changes = {}
    if old_values and new_values:
        # Compare all fields for changes
        all_fields = set(old_values.keys()) | set(new_values.keys())
        for field in all_fields:
            old_val = old_values.get(field)
            new_val = new_values.get(field)

            # Skip comparing large objects
            if field in ["metadata", "custom_properties"]:
                continue

            # Convert datetime objects to ISO format strings
            if isinstance(old_val, datetime):
                old_val = old_val.isoformat()
            if isinstance(new_val, datetime):
                new_val = new_val.isoformat()

            # Only record if values are different
            if old_val != new_val:
                changes[field] = {"old": old_val, "new": new_val}

    # Create change record with detailed changes
    change_record = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "change_type": change_type,
        "changed_by": "operations_fr_inventory",
        "changes": changes,
    }

    # Store references to old/new values but filter out large fields to save space
    change_record["old_values"] = convert_values_for_json(
        old_values, ["metadata", "custom_properties"]
    )
    change_record["new_values"] = convert_values_for_json(
        new_values, ["metadata", "custom_properties"]
    )

    return change_record


def convert_values_for_json(values_dict, exclude_keys=None):
    """
    Convert values to JSON-compatible format.

    Args:
        values_dict: Dictionary with values to convert
        exclude_keys: List of keys to exclude

    Returns:
        dict: Converted values dictionary
    """
    if not values_dict:
        return {}

    exclude_keys = exclude_keys or []
    converted_values = {}

    for key, value in values_dict.items():
        if key not in exclude_keys:
            if isinstance(value, datetime):
                converted_values[key] = value.isoformat()
            else:
                converted_values[key] = value

    return converted_values

"""
Helper functions for Eiger CSV generation.
"""

import re
from datetime import datetime
from io import BytesIO
from flask import send_file


def get_task_name_from_info(task_info):
    """Extract task name from different possible fields in task_info."""
    # Debug what fields are available
    # print(f"Available fields in task_info: {list(task_info.keys())}")

    # Try to get the task name from different possible fields
    # Add case-insensitive matching
    task_info_lower = {k.lower(): v for k, v in task_info.items()}

    fields_to_check = [
        "name",
        "task name",
        "ligentec label title",
        "shipment task title",
    ]

    # First try exact matches
    for field in fields_to_check:
        if field in task_info and task_info[field]:
            # print(f"Found title in field '{field}': {task_info[field]}")
            return task_info[field]

    # Then try case-insensitive matches
    for field in fields_to_check:
        if field.lower() in task_info_lower and task_info_lower[field.lower()]:
            # print(f"Found title in case-insensitive field '{field}': "
            #       f"{task_info_lower[field.lower()]}")
            return task_info_lower[field.lower()]

    # If nothing is found, log it
    # print("No task name found in any of the expected fields")
    return ""


def extract_project_info(task_name):
    """Extract only the project relevant part from the task name.
    Excludes Lot IDs, label free info, etc."""
    if not task_name:
        return ""

    # Try to extract the project info using regex patterns
    # Looking for project names that often follow format like "FAU WP1.9 RI_Oxide"

    # Pattern 1: Try to find sequences with capital letters followed by numbers
    # and words
    match = re.search(
        r"([A-Z]+\s*W?P?\.?\d+\.?\d*\s*[A-Z][A-Za-z0-9_]+"
        r"(?:\s*[-]\s*\d+\s*[A-Z]+\s*)*)",
        task_name,
    )
    if match:
        return match.group(1).strip()

    # Pattern 2: Try to find any part after a comma that might contain the project info
    match = re.search(r",\s*([^,]+)(?:,|$)", task_name)
    if match:
        return match.group(1).strip()

    # If no patterns match, just return the last part of the title, limited to 30 chars
    parts = task_name.split(",")
    if len(parts) > 1:
        return parts[1].strip()

    # Last resort, just use the full name but without "Eiger" prefix if present
    if task_name.lower().startswith("eiger"):
        return task_name[5:].strip()

    return task_name.strip()


def clean_filename(task_name, max_length=50):
    """Clean and format task name for use in a filename."""
    if not task_name:
        return ""

    # Extract just the project part
    project_part = extract_project_info(task_name)
    # print(f"Extracted project part: '{project_part}'")

    # Replace special characters with underscore
    cleaned_name = re.sub(r"[^a-zA-Z0-9]", "_", project_part)
    # Replace multiple underscores with a single one
    cleaned_name = re.sub(r"_+", "_", cleaned_name)
    # Remove leading/trailing underscores
    cleaned_name = cleaned_name.strip("_")
    # Limit length
    cleaned_name = cleaned_name[:max_length]

    # print(f"Cleaned name: '{cleaned_name}'")
    return cleaned_name


"""
def create_eiger_filename(task_info):
    #Create a filename for the Eiger CSV file based on task information.
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

    # Hard-code a specific project title if you have one constant project
    # Or just return a simple Eiger_timestamp format
    return f"Eiger_{timestamp}.csv"
"""

# If you want to include some task info, use this simpler version instead:


def create_eiger_filename(task_info):
    timestamp_date = datetime.now().strftime("%Y-%m-%d")
    timestamp_hour = datetime.now().strftime("%H-%M-%S")

    # GIDs for the custom fields
    LIGENTEC_LABEL_TITLE_GID = "1205906456345261"
    SHIPMENT_TASK_TITLE_GID = "1205906644617294"

    ligentec_label = ""

    # Debug log all the keys in task_info
    print(f"Available keys in task_info: {list(task_info.keys())}")

    # First try directly by field GID - this is most reliable
    if LIGENTEC_LABEL_TITLE_GID in task_info and task_info[LIGENTEC_LABEL_TITLE_GID]:
        ligentec_label = task_info[LIGENTEC_LABEL_TITLE_GID]
        print(f"Found title by GID {LIGENTEC_LABEL_TITLE_GID}: {ligentec_label}")

    # Try second field GID if first not found
    elif SHIPMENT_TASK_TITLE_GID in task_info and task_info[SHIPMENT_TASK_TITLE_GID]:
        ligentec_label = task_info[SHIPMENT_TASK_TITLE_GID]
        print(f"Found title by GID {SHIPMENT_TASK_TITLE_GID}: {ligentec_label}")

    # Fall back to field name (usual approach) if GIDs not found
    else:
        # Create case-insensitive lookup
        task_info_lower = {k.lower(): v for k, v in task_info.items()}

        # Try by field names in order of preference
        field_names = [
            "ligentec label title",
            "Ligentec label title",
            "shipment task title",
            "Shipment task title",
        ]

        # Check exact field names
        for field in field_names:
            if field in task_info and task_info[field]:
                ligentec_label = task_info[field]
                print(f"Found title by field name '{field}': {ligentec_label}")
                break

        # If not found, try case-insensitive lookup
        if not ligentec_label:
            for field in field_names:
                if field.lower() in task_info_lower and task_info_lower[field.lower()]:
                    ligentec_label = task_info_lower[field.lower()]
                    print(
                        f"Found title by lowercase field name '{field.lower()}': {ligentec_label}"
                    )
                    break

    # Last resort - use task name
    if not ligentec_label and "name" in task_info and task_info["name"]:
        ligentec_label = task_info["name"]
        print(f"Using task name as fallback: {ligentec_label}")
    elif not ligentec_label and "Task Name" in task_info and task_info["Task Name"]:
        ligentec_label = task_info["Task Name"]
        print(f"Using Task Name as fallback: {ligentec_label}")

    if ligentec_label:
        # Clean the label for filename use
        simple_name = ligentec_label[:30].replace(" ", "_").replace(",", "")
        simple_name = re.sub(r"[^a-zA-Z0-9_]", "", simple_name)
        filename = f"Eiger_{simple_name}_{timestamp_date}_{timestamp_hour}.csv"
        print(f"Generated filename: {filename}")
        return filename
    else:
        filename = f"Eiger_{timestamp_date}_{timestamp_hour}.csv"
        print(f"No title found. Using default filename: {filename}")
        return filename


def get_paired_slots_wafers(task_info, direct_generation, session_data=None):
    """Process wafer IDs from task info or session data."""
    if direct_generation:
        wafer_ids_raw = task_info.get("Wafers IDs", "")
        return [
            (str(i + 1), wid.strip())
            for i, wid in enumerate(re.findall(r"\d+[A-Z]+[A-Z0-9]+", wafer_ids_raw))
        ]
    else:
        return session_data


def prepare_csv_response(string_output, filename):
    """Prepare the HTTP response with the CSV file."""
    byte_output = BytesIO()
    byte_output.write(string_output.getvalue().encode("utf-8-sig"))
    byte_output.seek(0)

    return send_file(
        byte_output, mimetype="text/csv", as_attachment=True, download_name=filename
    )

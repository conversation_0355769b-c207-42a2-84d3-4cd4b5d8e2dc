from flask import current_app
from typing import Dict, Any, List, Optional, <PERSON><PERSON>
import traceback
from datetime import datetime, timezone
import asana

from integrations.asana.asana_client import (
    get_asana_client, 
    search_project_gid, 
    get_requester_gid_by_name,
    get_pi_task_gid,
    OPTS,
    SHIPMENT_PROJECT_GID
)
from integrations.asana.field_utils import (
    map_package_size,
    handle_boolean_enum,
    handle_mask_type,
    get_field_by_name
)
from integrations.asana.custom_fields import add_custom_field
from database.db_config import get_db_cursor, get_sqlalchemy_connection
from core.models.models import (
    Shipment, 
    ShipmentStatus, 
    ShipmentWafer
)
from sqlalchemy import select


def create_shipment_task(data: Dict[str, Any]) -> Tuple[bool, str, Optional[str]]:
    """Create a new shipment task in Asana
    
    Args:
        data: The shipment data from the request
    
    Returns:
        Tuple containing (success, message, task_id)
    """
    try:
        current_app.logger.info(f"Received shipment data: {data}")
        
        # Configure Asana client
        api_client = get_asana_client()
        tasks_api_instance = asana.TasksApi(api_client)
        
        # Get LGT lot IDs from the input field (comma-separated)
        lot_ids_raw = data.get("lot_ids", "")
        lgt_lot_ids = []
        if isinstance(lot_ids_raw, str) and lot_ids_raw.strip():
            lgt_lot_ids = [lot.strip() for lot in lot_ids_raw.split(",") if lot.strip()]
            
        current_app.logger.info(f"Processing LGT lot IDs: {lgt_lot_ids}")
        
        # Set default project ID and additional projects
        project_gids = [SHIPMENT_PROJECT_GID]
        
        # Search for project GIDs for all lot IDs at once
        if lgt_lot_ids:
            lot_project_gids = search_project_gid(api_client, lgt_lot_ids)
            for gid in lot_project_gids:
                if gid and gid not in project_gids:
                    project_gids.append(gid)
        
        # Get PI task and extract fields from it
        pi_task_gid = None
        pi_task = None
        if len(project_gids) > 1:
            try:
                pi_task_gid = get_pi_task_gid(api_client, project_gids[1], OPTS)
                if pi_task_gid:
                    pi_task = tasks_api_instance.get_task(pi_task_gid, opts=OPTS)
            except Exception as e:
                current_app.logger.error(f"Error getting PI task: {str(e)}")
        
        # Create initial task with basic information
        body = {
            "data": {
                "name": f"Additional-shipment: {data.get('title')}",
                "projects": project_gids,
            }
        }
        
        # Add start/due dates if available
        if data.get("shipment_date"):
            try:
                formatted_date = datetime.strptime(
                    data.get("shipment_date"), "%Y-%m-%d"
                ).strftime("%Y-%m-%d")
                body["data"]["start_on"] = formatted_date
                body["data"]["due_on"] = formatted_date
            except Exception as e:
                current_app.logger.error(f"Error formatting date: {str(e)}")
        
        # Add requester if available
        if data.get("contact_person"):
            try:
                requester_gid = get_requester_gid_by_name(
                    api_client, data["contact_person"]
                )
                if requester_gid:
                    body["data"]["followers"] = [requester_gid]
            except Exception as e:
                current_app.logger.error(f"Error getting requester GID: {str(e)}")
        
        # Create task
        try:
            new_task = tasks_api_instance.create_task(body=body, opts=OPTS)
            task_data = new_task.to_dict() if hasattr(new_task, "to_dict") else new_task
            task_gid = task_data.get("gid")
            
            if not task_gid:
                raise ValueError("Failed to get task GID from response")
        except Exception as e:
            current_app.logger.error(f"Error creating Asana task: {str(e)}")
            return False, f"Error creating Asana task: {str(e)}", None
        
        # Get task details to get custom fields
        try:
            task_details = tasks_api_instance.get_task(task_gid=task_gid, opts=OPTS)
            task_details_data = (
                task_details.to_dict()
                if hasattr(task_details, "to_dict")
                else task_details
            )
        except Exception as e:
            current_app.logger.error(f"Error getting task details: {str(e)}")
            return True, "Task created but could not update custom fields", task_gid
        
        # Build field map for easier access
        field_map = {}
        for field in task_details_data.get("custom_fields", []):
            if isinstance(field, dict) and "name" in field:
                field_name = field.get("name", "").strip()
                field_map[field_name] = field
        
        # Log all available fields for debugging
        current_app.logger.info(f"Available Asana fields: {[name for name in field_map.keys()]}")
        
        # Build custom fields update
        custom_fields = {}
        
        # LGT Lot ID
        lgt_lot_field = field_map.get("LGT Lot ID")
        if lgt_lot_field and isinstance(lgt_lot_field, dict) and "gid" in lgt_lot_field:
            custom_fields[lgt_lot_field["gid"]] = ", ".join(lgt_lot_ids)
        
        # Process text fields
        text_fields = {
            "Shipment task title": data.get("title", ""),
            "Ligentec label title": data.get("label_title", ""),
            "Wafers IDs": ", ".join(data.get("wafer_ids", [])),
            "Number of Wafers": data.get("number_of_wafers", ""),
            "XFAB purchase order": data.get("xfab_po", ""),
            "XFAB Device ID": data.get("xfab_device_id", ""),
            "Project ID": data.get("project_id", ""),
            "Contact person": data.get("contact_person", ""),
            "Email (delivery contact)": data.get("email", ""),
            "Shipping address": data.get("address", ""),
            "Telephone number (delivery contact)": data.get("telephone", ""),
            "Shipment comments": data.get("comments", ""),
            "Eiger Number": data.get("eiger_number", ""),
            "Tapeout (Eiger)": data.get("tapeout", ""),
            "Vendor Lot": data.get("vendor_lot", ""),
            "Customer Lot (Eiger)": data.get("customer_lot", ""),
            "TOX Target nm": data.get("tox_target_sin", ""),
            "SiN Tube Position (1-6)": data.get("sin_tube_position", ""),
        }
        
        # Add PI fields if available
        if pi_task:
            try:
                # Add sales order, customer ID, etc. from PI task
                sales_order_field = get_field_by_name("Sales order", pi_task["custom_fields"])
                if sales_order_field and "display_value" in sales_order_field:
                    text_fields["Sales order"] = sales_order_field["display_value"]
                
                customer_id_field = get_field_by_name("Customer ID", pi_task["custom_fields"])
                if customer_id_field and "display_value" in customer_id_field:
                    text_fields["Customer ID"] = customer_id_field["display_value"]
                
                # Handle Lot reservation (enum field)
                lot_reservation_field = get_field_by_name("Lot reservation", pi_task["custom_fields"])
                if lot_reservation_field and lot_reservation_field.get("enum_value"):
                    lot_reservation_value = lot_reservation_field["enum_value"]
                    if isinstance(lot_reservation_value, dict) and "gid" in lot_reservation_value:
                        custom_fields[field_map.get("Lot reservation", {}).get("gid")] = lot_reservation_value["gid"]
                
                # Handle Account/Project manager (people field)
                account_manager_field = get_field_by_name("Account/Project manager", pi_task["custom_fields"])
                if account_manager_field and account_manager_field.get("people_value"):
                    people_value = account_manager_field["people_value"]
                    if isinstance(people_value, list) and len(people_value) > 0 and "gid" in people_value[0]:
                        custom_fields[field_map.get("Account/Project manager", {}).get("gid")] = people_value[0]["gid"]
                
                # Handle Corridor (enum field)
                corridor_field = get_field_by_name("Corridor", pi_task["custom_fields"])
                if corridor_field and corridor_field.get("enum_value"):
                    corridor_value = corridor_field["enum_value"]
                    if isinstance(corridor_value, dict) and "gid" in corridor_value:
                        custom_fields[field_map.get("Corridor", {}).get("gid")] = corridor_value["gid"]
                
                # Handle Lot project (enum field)
                lot_project_field = get_field_by_name("Lot project", pi_task["custom_fields"])
                if lot_project_field and lot_project_field.get("enum_value"):
                    lot_project_value = lot_project_field["enum_value"]
                    if isinstance(lot_project_value, dict) and "gid" in lot_project_value:
                        custom_fields[field_map.get("Lot project", {}).get("gid")] = lot_project_value["gid"]
                
            except Exception as e:
                current_app.logger.error(f"Error extracting PI fields: {str(e)}")
        
        for field_name, value in text_fields.items():
            field = field_map.get(field_name)
            if field and isinstance(field, dict) and "gid" in field and value:
                custom_fields[field["gid"]] = str(value)
                current_app.logger.info(f"Setting text field {field_name}: {value}")
        
        # Process standard Yes/No boolean fields
        yes_no_fields = {
            "Need Reviewing?": data.get("need_reviewing", False),
            "label-free shipment ?": data.get("label_free", False),
            "Keep Cassette closed": data.get("keep_cassette_closed", False),
        }
        
        for field_name, is_true in yes_no_fields.items():
            field = field_map.get(field_name)
            if (
                field
                and isinstance(field, dict)
                and "gid" in field
                and "enum_options" in field
            ):
                options = field.get("enum_options", [])
                
                yes_option = next(
                    (
                        opt
                        for opt in options
                        if isinstance(opt, dict) and opt.get("name") == "Yes"
                    ),
                    None,
                )
                no_option = next(
                    (
                        opt
                        for opt in options
                        if isinstance(opt, dict) and opt.get("name") == "No"
                    ),
                    None,
                )
                
                if is_true and yes_option and isinstance(yes_option, dict):
                    custom_fields[field["gid"]] = yes_option.get("gid")
                    current_app.logger.info(f"Setting {field_name} to Yes")
                elif not is_true and no_option and isinstance(no_option, dict):
                    custom_fields[field["gid"]] = no_option.get("gid")
                    current_app.logger.info(f"Setting {field_name} to No")
        
        # Process TRUE/FALSE boolean fields
        true_false_fields = {
            "RIB ?": data.get("rib", False),
            "HEATERS?": data.get("heater", False),
            "Undercuts?": data.get("undercut", False),
        }
        
        for field_name, is_true in true_false_fields.items():
            field = field_map.get(field_name)
            if field and "gid" in field and field.get("enum_options"):
                options = field.get("enum_options", [])
                
                true_option = next(
                    (opt for opt in options if opt.get("name") == "TRUE"), None
                )
                false_option = next(
                    (opt for opt in options if opt.get("name") == "FALSE"), None
                )
                
                # Convert string value to boolean if needed
                if isinstance(is_true, str):
                    is_true = is_true.lower() in ["true", "yes", "1", "on"]
                
                if is_true and true_option:
                    custom_fields[field["gid"]] = true_option.get("gid")
                    current_app.logger.info(f"Setting {field_name} to TRUE")
                elif not is_true and false_option:
                    custom_fields[field["gid"]] = false_option.get("gid")
                    current_app.logger.info(f"Setting {field_name} to FALSE")
        
        # Handle Priority field
        priority_field = field_map.get("Priority")
        if priority_field and "gid" in priority_field and priority_field.get("enum_options"):
            priority = data.get("priority", "low").capitalize()
            priority_option = next(
                (
                    opt
                    for opt in priority_field.get("enum_options", [])
                    if opt.get("name") == priority
                ),
                None,
            )
            if priority_option:
                custom_fields[priority_field["gid"]] = priority_option.get("gid")
                current_app.logger.info(f"Setting Priority to {priority}")
        
        # Handle Wafer choice type field
        wafer_choice_field = field_map.get("Wafer choice type")
        if (
            wafer_choice_field
            and "gid" in wafer_choice_field
            and "type" in wafer_choice_field
        ):
            wafer_choice_value = data.get("wafer_choice")
            if wafer_choice_value:
                if wafer_choice_field["type"] == "text":
                    # For text fields
                    display_value = "Not Random" if "not" in wafer_choice_value.lower() else "Random"
                    custom_fields[wafer_choice_field["gid"]] = display_value
                    current_app.logger.info(f"Setting Wafer choice type to {display_value}")
                elif wafer_choice_field["type"] == "enum" and wafer_choice_field.get("enum_options"):
                    # For enum fields
                    options = wafer_choice_field.get("enum_options", [])
                    display_value = "Not Random" if "not" in wafer_choice_value.lower() else "Random"
                    option = next(
                        (opt for opt in options if opt.get("name") == display_value), None
                    )
                    if option:
                        custom_fields[wafer_choice_field["gid"]] = option.get("gid")
                        current_app.logger.info(f"Setting Wafer choice type to {display_value}")
        
        # Handle Mask field
        mask_field = field_map.get("Mask")
        if mask_field and "gid" in mask_field:
            mask_value = data.get("mask")
            if mask_value:
                display_value = handle_mask_type(mask_value)
                if mask_field["type"] == "text":
                    custom_fields[mask_field["gid"]] = display_value
                    current_app.logger.info(f"Setting Mask to {display_value}")
                elif mask_field["type"] == "enum" and mask_field.get("enum_options"):
                    options = mask_field.get("enum_options", [])
                    option = next(
                        (opt for opt in options if opt.get("name") == display_value), None
                    )
                    if option:
                        custom_fields[mask_field["gid"]] = option.get("gid")
                        current_app.logger.info(f"Setting Mask to {display_value}")
        
        # Handle Package size field - try with different field names
        package_values = ["Package size & weight (200mm wafers)", "Package size & weight", "Package size"]
        package_field = None
        
        for package_name in package_values:
            package_field = field_map.get(package_name)
            if package_field and "gid" in package_field:
                current_app.logger.info(f"Found package field: {package_name}")
                break
        
        if package_field and "gid" in package_field:
            package_value = data.get("parcel_size")
            if package_value:
                display_value = map_package_size(package_value)
                current_app.logger.info(f"Mapped package size: {package_value} -> {display_value}")
                
                if display_value:
                    # Handle both enum and text types
                    if package_field.get("type") == "text":
                        custom_fields[package_field["gid"]] = display_value
                        current_app.logger.info(f"Setting Package size (text) to {display_value}")
                    elif package_field.get("enum_options"):
                        options = package_field.get("enum_options", [])
                        current_app.logger.info(f"Package size options: {[opt.get('name') for opt in options if isinstance(opt, dict)]}")
                        
                        option = next(
                            (opt for opt in options if isinstance(opt, dict) and opt.get("name") == display_value),
                            None,
                        )
                        if option:
                            custom_fields[package_field["gid"]] = option.get("gid")
                            current_app.logger.info(f"Setting Package size to {display_value}")
                        else:
                            # Try partial matching
                            for opt in options:
                                if isinstance(opt, dict) and opt.get("name") and display_value in opt.get("name"):
                                    custom_fields[package_field["gid"]] = opt.get("gid")
                                    current_app.logger.info(f"Setting Package size to {opt.get('name')} (partial match)")
                                    break
        
        # Handle Type of shipment field
        shipment_type_field = field_map.get("Type of shipment")
        if (
            shipment_type_field
            and "gid" in shipment_type_field
            and shipment_type_field.get("enum_options")
        ):
            options = shipment_type_field.get("enum_options", [])
            
            # Default to "ePO, Projects, Samples, RMA, R&D, External service"
            default_option = next(
                (opt for opt in options if "ePO" in opt.get("name")), None
            )
            
            if default_option:
                custom_fields[shipment_type_field["gid"]] = default_option.get("gid")
                current_app.logger.info(f"Setting Type of shipment to {default_option.get('name')}")
        
        # Handle Initial start date and Initial due date fields
        if data.get("shipment_date"):
            formatted_date_iso = datetime.strptime(
                data.get("shipment_date"), "%Y-%m-%d"
            ).strftime("%Y-%m-%dT%H:%M:%SZ")
            
            # Initial start date
            initial_start_date_field = field_map.get("Initial start date")
            if initial_start_date_field and "gid" in initial_start_date_field:
                custom_fields[initial_start_date_field["gid"]] = {
                    "date": formatted_date_iso
                }
                current_app.logger.info(f"Setting Initial start date to {formatted_date_iso}")
            
            # Initial due date
            initial_due_date_field = field_map.get("Initial due date")
            if initial_due_date_field and "gid" in initial_due_date_field:
                custom_fields[initial_due_date_field["gid"]] = {
                    "date": formatted_date_iso
                }
                current_app.logger.info(f"Setting Initial due date to {formatted_date_iso}")
        
        # Update the task with custom fields
        if custom_fields:
            current_app.logger.info(f"Updating Asana task with {len(custom_fields)} custom fields")
            update_body = {"data": {"custom_fields": custom_fields}}
            
            try:
                tasks_api_instance.update_task(
                    body=update_body, task_gid=task_gid, opts=OPTS
                )
            except Exception as e:
                current_app.logger.error(f"Error updating task with custom fields: {str(e)}")
                return True, "Task created but some custom fields could not be updated", task_gid
        
        return True, "Shipment task created successfully in Asana", task_gid
    
    except Exception as e:
        current_app.logger.error(f"Error in create_shipment: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return False, f"Error creating shipment task: {str(e)}", None


def get_available_locations():
    """Get all shipping locations"""
    try:
        with get_db_cursor() as cursor:
            query = """
                SELECT 
                    location_id, 
                    label, 
                    address, 
                    telephone, 
                    email, 
                    contact_person
                FROM locations 
                ORDER BY label
            """
            cursor.execute(query)
            locations = cursor.fetchall()
            
            return True, "Locations retrieved successfully", locations
    
    except Exception as e:
        current_app.logger.error(f"Error fetching locations: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return False, f"Error fetching locations: {str(e)}", None


def update_shipment_status(shipment_id: str, new_status: str, updated_by: str = "system") -> Tuple[bool, str]:
    """Update shipment status with proper validation"""
    try:
        with get_sqlalchemy_connection() as conn:
            # Get current shipment
            current_shipment = conn.execute(
                select([Shipment]).where(Shipment.shipment_id == shipment_id)
            ).fetchone()
            
            if not current_shipment:
                return False, "Shipment not found"
            
            # Validate status transition
            try:
                new_status_enum = ShipmentStatus(new_status)
                if not is_valid_status_transition(current_shipment.status, new_status_enum):
                    return False, f"Invalid status transition from {current_shipment.status} to {new_status_enum}"
            except ValueError as e:
                return False, f"Invalid status value: {str(e)}"
            
            # Update status
            update_data = {
                "status": new_status,
                "updated_at": datetime.now(timezone.utc),
                "updated_by": updated_by
            }
            
            # Add ship_date if transitioning to SHIPPED
            if new_status_enum == ShipmentStatus.SHIPPED and not current_shipment.ship_date:
                update_data["ship_date"] = datetime.now(timezone.utc)
            
            # Add delivery_date if transitioning to DELIVERED
            if new_status_enum == ShipmentStatus.DELIVERED and not current_shipment.delivery_date:
                update_data["delivery_date"] = datetime.now(timezone.utc)
            
            # Perform update
            conn.execute(
                Shipment.__table__.update()
                .where(Shipment.shipment_id == shipment_id)
                .values(update_data)
            )
            
            return True, f"Shipment status updated to {new_status}"
    
    except Exception as e:
        current_app.logger.error(f"Error updating shipment status: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return False, f"Error updating shipment status: {str(e)}"


def is_valid_status_transition(current_status: ShipmentStatus, new_status: ShipmentStatus) -> bool:
    """Check if a status transition is valid"""
    # Define valid transitions
    valid_transitions = {
        ShipmentStatus.DRAFT: [
            ShipmentStatus.PENDING,
            ShipmentStatus.CANCELLED
        ],
        ShipmentStatus.PENDING: [
            ShipmentStatus.PROCESSING,
            ShipmentStatus.CANCELLED
        ],
        ShipmentStatus.PROCESSING: [
            ShipmentStatus.PACKED,
            ShipmentStatus.CANCELLED
        ],
        ShipmentStatus.PACKED: [
            ShipmentStatus.SHIPPED,
            ShipmentStatus.CANCELLED
        ],
        ShipmentStatus.SHIPPED: [
            ShipmentStatus.DELIVERED,
            ShipmentStatus.RETURNED
        ],
        ShipmentStatus.DELIVERED: [
            ShipmentStatus.RETURNED
        ]
        # No transitions from CANCELLED, RETURNED, or ERROR
    }
    
    # Allow transition to same status and transitions defined above
    return (
        current_status == new_status or
        current_status in valid_transitions and new_status in valid_transitions[current_status]
    )
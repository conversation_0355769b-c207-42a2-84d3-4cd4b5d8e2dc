from flask import current_app
from typing import Dict, Any, List, Optional, <PERSON>ple
import traceback
from datetime import datetime

from database.db_config import get_db_cursor


def get_available_wafers(
    filters: Dict[str, Any], pagination: Dict[str, int]
) -> Tuple[bool, str, Dict[str, Any]]:
    """Get available wafers for shipment with enhanced filtering and pagination

    Args:
        filters: Dictionary containing filter parameters
        pagination: Dictionary containing pagination parameters

    Returns:
        Tuple containing (success, message, data)
    """
    try:
        # Get pagination parameters with defaults
        page = pagination.get("page", 1)
        per_page = pagination.get("per_page", 10)

        # Validate pagination parameters
        if page < 1:
            page = 1
        if per_page not in [10, 25, 50, 100]:
            per_page = 10

        with get_db_cursor() as cursor:
            # Define available locations
            available_locations = [
                "Ligentec France",
                "Xfab FR",
                "Ligentec FR Khalil",
                "Ligentec FR Elisee",
            ]

            # Build WHERE clause dynamically
            where_conditions = ["l.label = ANY(%s)", "wi.sent_at IS NULL"]
            query_params = [available_locations]

            # Add search filter
            if filters.get("search"):
                search_term = f"%{filters['search']}%"
                where_conditions.append(
                    """
                    (w.wafer_id ILIKE %s
                    OR w.lot_id ILIKE %s
                    OR wi.cassette_id ILIKE %s)
                """
                )
                query_params.extend([search_term, search_term, search_term])

            # Add lot ID filter
            if filters.get("lot_ids"):
                where_conditions.append("w.lot_id = ANY(%s)")
                query_params.append(filters["lot_ids"])

            # Add wafer ID filter
            if filters.get("wafer_ids"):
                where_conditions.append("w.wafer_id = ANY(%s)")
                query_params.append(filters["wafer_ids"])

            # Add cassette filter
            if filters.get("cassette_id"):
                where_conditions.append("wi.cassette_id = %s")
                query_params.append(filters["cassette_id"])

            # Add location filter
            if filters.get("location_id"):
                where_conditions.append("wi.location_id = %s")
                query_params.append(filters["location_id"])

            # Add date filters
            if filters.get("date_from"):
                where_conditions.append("wi.arrived_at >= %s")
                query_params.append(filters["date_from"])
            if filters.get("date_to"):
                where_conditions.append("wi.arrived_at <= %s")
                query_params.append(filters["date_to"])

            where_clause = " AND ".join(where_conditions)

            # Get total count with filters
            count_query = f"""
                SELECT COUNT(DISTINCT w.wafer_id) as count
                FROM wafers w
                JOIN wafer_inventory wi ON w.wafer_id = wi.wafer_id
                JOIN locations l ON wi.location_id = l.location_id
                WHERE {where_clause}
            """

            cursor.execute(count_query, query_params)
            result = cursor.fetchone()
            total_count = result["count"] if result else 0

            # Calculate offset and total pages
            offset = (page - 1) * per_page
            total_pages = (total_count + per_page - 1) // per_page

            # Add sorting
            sort_column = {
                "lot_id": "w.lot_id",
                "wafer_id": "w.wafer_id",
                "cassette_id": "wi.cassette_id",
                "location": "l.label",
                "arrived_at": "wi.arrived_at",
            }.get(filters.get("sort_by"), "w.lot_id")

            sort_order = (
                "DESC" if filters.get("sort_order", "").upper() == "DESC" else "ASC"
            )

            # Main query with pagination and sorting
            query = f"""
                WITH RankedWafers AS (
                    SELECT
                        w.wafer_id,
                        w.lot_id,
                        w.size,
                        wi.cassette_id,
                        wi.slot_id,
                        wi.arrived_at,
                        wi.sent_at,
                        wi.location_id,
                        wi.metadata,
                        l.label as location_label,
                        ROW_NUMBER() OVER (ORDER BY {sort_column} {sort_order}) as rn
                    FROM wafers w
                    JOIN wafer_inventory wi ON w.wafer_id = wi.wafer_id
                    JOIN locations l ON wi.location_id = l.location_id
                    WHERE {where_clause}
                )
                SELECT * FROM RankedWafers
                WHERE rn > %s AND rn <= %s
                ORDER BY rn
            """

            cursor.execute(query, query_params + [offset, offset + per_page])
            wafers = cursor.fetchall()

            # Group wafers by lot_id
            grouped_wafers = {}
            for wafer in wafers:
                lot_id = wafer["lot_id"]
                if lot_id not in grouped_wafers:
                    grouped_wafers[lot_id] = {"lot_id": lot_id, "wafers": []}

                # Convert datetime objects to ISO format
                wafer_data = {
                    "wafer_id": wafer["wafer_id"],
                    "cassette_id": wafer["cassette_id"],
                    "slot_id": wafer["slot_id"],
                    "location": wafer["location_label"],
                    "arrived_at": (
                        wafer["arrived_at"].isoformat() if wafer["arrived_at"] else None
                    ),
                    "sent_at": (
                        wafer["sent_at"].isoformat() if wafer["sent_at"] else None
                    ),
                    "metadata": wafer["metadata"],
                }
                grouped_wafers[lot_id]["wafers"].append(wafer_data)

            return (
                True,
                "Available wafers retrieved successfully",
                {
                    "data": list(grouped_wafers.values()),
                    "pagination": {
                        "total_items": total_count,
                        "total_pages": total_pages,
                        "current_page": page,
                        "per_page": per_page,
                        "has_next": page < total_pages,
                        "has_prev": page > 1,
                    },
                    "filters": filters,  # Return current filters for state management
                },
            )
    except Exception as e:
        current_app.logger.error(f"Error fetching available wafers: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return False, f"Error fetching available wafers: {str(e)}", {}

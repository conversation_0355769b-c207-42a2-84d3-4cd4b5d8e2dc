from flask import current_app

from routes.helpers.inventory_metadata_helpers import update_metadata_fields

"""
Helper functions for inventory update operations.
"""


def build_inventory_update_query(data, wafer_id=None):
    """Build update query and values based on provided fields."""
    update_fields = []
    update_values = []

    # Check each field and add to update if provided
    field_mappings = [
        ("cassette_id", "cassette_id"),
        ("location_id", "location_id"),
        ("arrived_at", "arrived_at"),
        ("sent_at", "sent_at"),
    ]

    for api_field, db_field in field_mappings:
        if data.get(api_field) is not None:
            update_fields.append(f"{db_field} = %s")
            update_values.append(data[api_field])

    # Handle slot_id with specific logic
    add_slot_id_to_update(data, update_fields, update_values, wafer_id)

    # Always update timestamp
    update_fields.extend(["updated_at = NOW()", "updated_by = %s"])

    return update_fields, update_values


def add_slot_id_to_update(data, update_fields, update_values, wafer_id):
    """
    Add slot_id to update fields and values if needed.

    Args:
        data: Request data dictionary
        update_fields: List to append update fields
        update_values: List to append update values
        wafer_id: ID of the wafer to update
    """
    wafer_updates = data.get("wafer_updates", {})

    if wafer_id and wafer_id in wafer_updates and "slot_id" in wafer_updates[wafer_id]:
        # Individual slot_id for this wafer
        slot_id = wafer_updates[wafer_id]["slot_id"]
        current_app.logger.info(
            f"Using individual slot_id {slot_id} for wafer {wafer_id}"
        )
        update_fields.append("slot_id = %s")
        update_values.append(slot_id)
    elif data.get("slot_id") is not None:
        # Common slot_id for all wafers
        update_fields.append("slot_id = %s")
        update_values.append(data["slot_id"])


def execute_inventory_update(cursor, update_fields, update_values, wafer_id):
    """
    Execute inventory update query.

    Args:
        cursor: Database cursor
        update_fields: List of fields to update
        update_values: List of values for the update
        wafer_id: ID of the wafer to update

    Returns:
        dict: Updated wafer record
    """
    if not update_fields:
        # If no fields to update, just get the current record
        cursor.execute(
            """
            SELECT * FROM wafer_inventory WHERE wafer_id = %s
            """,
            [wafer_id],
        )
        return dict(cursor.fetchone())

    update_query = f"""
        UPDATE wafer_inventory
        SET {", ".join(update_fields)}
        WHERE wafer_id = %s
        RETURNING *
    """
    update_values.append(wafer_id)
    cursor.execute(update_query, update_values)
    return dict(cursor.fetchone())


def update_single_wafer(cursor, wafer_id, data):
    """
    Update a single wafer in inventory.

    Args:
        cursor: Database cursor
        wafer_id: ID of the wafer to update
        data: Request data dictionary

    Returns:
        dict or None: New values if successful, None if wafer not found
    """
    # Get old values before update
    old_values = get_wafer_inventory_values(cursor, wafer_id)
    if not old_values:
        return None

    # Update metadata fields
    update_metadata_fields(cursor, wafer_id, data)

    # Build update query for standard fields
    update_fields, update_values = build_inventory_update_query(data, wafer_id)

    # Add updated_by value
    update_values.append(data.get("updated_by", "system"))

    # Execute standard field updates
    new_values = execute_inventory_update(
        cursor, update_fields, update_values, wafer_id
    )

    # Track the change
    from routes.helpers.inventory_change_tracking import track_wafer_change

    track_wafer_change(cursor, wafer_id, "UPDATE", old_values, new_values)

    return new_values


def process_bulk_inventory_updates(bulk_groups, individual_updates):
    """
    Process bulk inventory updates for groups of wafers with common attributes.

    Args:
        bulk_groups: List of group definitions with wafer IDs and common values
        individual_updates: Dict of individual wafer updates

    Returns:
        dict: Result of the bulk update operation
    """
    from database.db_config import get_db_cursor

    try:
        with get_db_cursor() as cursor:
            updated_wafers = []
            errors = []

            # Process bulk groups
            for group in bulk_groups:
                wafer_ids = group.get("wafer_ids", [])
                group_updates = group.get("updates", {})

                if not wafer_ids or not group_updates:
                    continue

                # Apply the same updates to all wafers in the group
                for wafer_id in wafer_ids:
                    try:
                        # Get current wafer values
                        old_values = get_wafer_inventory_values(cursor, wafer_id)
                        if not old_values:
                            errors.append(f"Wafer {wafer_id} not found in inventory")
                            continue

                        # Apply group updates
                        update_data = {**group_updates, "updated_by": "bulk_operations"}
                        new_values = update_single_wafer(cursor, wafer_id, update_data)
                        updated_wafers.append(new_values)

                    except Exception as e:
                        errors.append(f"Error updating wafer {wafer_id}: {str(e)}")

            # Process individual updates (these override group updates)
            for wafer_id, updates in individual_updates.items():
                try:
                    # Get current wafer values
                    old_values = get_wafer_inventory_values(cursor, wafer_id)
                    if not old_values:
                        errors.append(f"Wafer {wafer_id} not found in inventory")
                        continue

                    # Apply individual updates
                    update_data = {**updates, "updated_by": "individual_operations"}
                    new_values = update_single_wafer(cursor, wafer_id, update_data)

                    # Replace or add to updated wafers list
                    existing_index = next(
                        (
                            i
                            for i, w in enumerate(updated_wafers)
                            if w.get("wafer_id") == wafer_id
                        ),
                        None,
                    )
                    if existing_index is not None:
                        updated_wafers[existing_index] = new_values
                    else:
                        updated_wafers.append(new_values)

                except Exception as e:
                    errors.append(f"Error updating wafer {wafer_id}: {str(e)}")

            # Prepare response
            success = len(updated_wafers) > 0
            message = f"Successfully updated {len(updated_wafers)} wafer(s)"

            if errors:
                message += f". {len(errors)} error(s) occurred: {'; '.join(errors[:3])}"
                if len(errors) > 3:
                    message += f" and {len(errors) - 3} more..."

            return {
                "success": success,
                "message": message,
                "updated_wafers": updated_wafers,
                "errors": errors,
                "total_updated": len(updated_wafers),
                "total_errors": len(errors),
            }

    except Exception as e:
        return {
            "success": False,
            "message": f"Bulk update failed: {str(e)}",
            "updated_wafers": [],
            "errors": [str(e)],
            "total_updated": 0,
            "total_errors": 1,
        }


def validate_inventory_update(data):
    """Validate inventory update data."""
    # Log the data for debugging
    current_app.logger.info(f"Validating update data: {data}")

    wafer_ids = data.get("wafer_ids", [])
    if not wafer_ids:
        return False, "No wafer IDs provided"

    # Check for individual wafer updates
    wafer_updates = data.get("wafer_updates", {})
    if wafer_updates:
        current_app.logger.info(f"Found individual wafer updates: {wafer_updates}")

    return True, ""


def get_wafer_inventory_values(cursor, wafer_id):
    """Get current inventory values for a wafer."""
    cursor.execute(
        """
        SELECT
            wi.*,
            w.lot_id,
            wi.metadata->'lgt'->>'xfab_lot_id' as xfab_id,
            wi.metadata->'lgt'->>'Modules' as module_name,
            wi.metadata->'lgt'->>'mask_set_id' as mask_set_id,
            l.mask_set_id as wafer_mask_set_id
        FROM wafer_inventory wi
        LEFT JOIN wafers w ON wi.wafer_id = w.wafer_id
        LEFT JOIN lots l ON w.lot_id = l.lot_id
        WHERE wi.wafer_id = %s
        """,
        [wafer_id],
    )
    result = cursor.fetchone()
    if not result:
        return None

    # Convert to dict for easier handling
    values = dict(result)

    # Extract and validate metadata fields
    extract_metadata_fields(values)

    current_app.logger.info(f"Retrieved current values for wafer {wafer_id}: {values}")
    return values


def extract_metadata_fields(values):
    """
    Extract and validate metadata fields from inventory values.

    Args:
        values: Dictionary of wafer values
    """
    # If mask_set_id is not in metadata, try to get it from wafers table
    if not values.get("mask_set_id") and values.get("wafer_mask_set_id"):
        values["mask_set_id"] = values["wafer_mask_set_id"]

    # Extract values from nested metadata if not already present
    if values.get("metadata") and "lgt" in values["metadata"]:
        # Make sure we have the module_name from metadata
        if not values.get("module_name"):
            values["module_name"] = values["metadata"]["lgt"].get("Modules", "")

        # Make sure we have the xfab_id from metadata
        if not values.get("xfab_id"):
            values["xfab_id"] = values["metadata"]["lgt"].get("xfab_lot_id", "")

        # Make sure we have the mask_set_id from metadata
        if not values.get("mask_set_id"):
            values["mask_set_id"] = values["metadata"]["lgt"].get("mask_set_id", "")

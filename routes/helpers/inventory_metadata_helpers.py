import json

from flask import current_app

"""
Helper functions for managing inventory metadata updates.
"""


def update_module_name(cursor, wafer_id, module_name):
    """
    Update the module_name in metadata.

    Args:
        cursor: Database cursor
        wafer_id: ID of the wafer to update
        module_name: New module name value
    """
    if module_name is None:
        return False

    cursor.execute(
        """
        UPDATE wafer_inventory
        SET metadata = jsonb_set(
            COALESCE(metadata, '{}'::jsonb),
            '{lgt,Modules}',
            %s::jsonb
        )
        WHERE wafer_id = %s
        """,
        [json.dumps(module_name), wafer_id],
    )
    current_app.logger.info(
        f"Updated module_name to '{module_name}' for wafer {wafer_id}"
    )

    # Also update the top-level 'Modules' field if it exists
    cursor.execute(
        """
        UPDATE wafer_inventory
        SET metadata = jsonb_set(
            metadata,
            '{Modules}',
            %s::jsonb,
            true
        )
        WHERE wafer_id = %s AND metadata ? 'Modules'
        """,
        [json.dumps(module_name), wafer_id],
    )

    return True


def update_xfab_id(cursor, wafer_id, xfab_id):
    """
    Update the xfab_id in metadata.

    Args:
        cursor: Database cursor
        wafer_id: ID of the wafer to update
        xfab_id: New XFAB ID value
    """
    if xfab_id is None:
        return False

    # Debug log to check the value
    current_app.logger.info(f"Updating xfab_id to '{xfab_id}' for wafer {wafer_id}")

    # First, make sure lgt structure exists
    cursor.execute(
        """
        UPDATE wafer_inventory
        SET metadata = jsonb_set(
            COALESCE(metadata, '{}'::jsonb),
            '{lgt}',
            COALESCE(metadata->'lgt', '{}'::jsonb)
        )
        WHERE wafer_id = %s
        """,
        [wafer_id],
    )

    # Now update the xfab_lot_id field
    cursor.execute(
        """
        UPDATE wafer_inventory
        SET metadata = jsonb_set(
            metadata,
            '{lgt,xfab_lot_id}',
            %s::jsonb
        )
        WHERE wafer_id = %s
        """,
        [json.dumps(xfab_id), wafer_id],
    )

    # Verify the update
    cursor.execute(
        """
        SELECT metadata->'lgt'->>'xfab_lot_id' AS xfab_id
        FROM wafer_inventory
        WHERE wafer_id = %s
        """,
        [wafer_id],
    )
    result = cursor.fetchone()
    current_app.logger.info(
        f"After update, xfab_id = {result['xfab_id'] if result else 'None'}"
    )

    return True


def update_mask_set_id(cursor, wafer_id, mask_set_id):
    """
    Update the mask_set_id in metadata.

    Args:
        cursor: Database cursor
        wafer_id: ID of the wafer to update
        mask_set_id: New mask set ID value
    """
    if mask_set_id is None:
        return False

    # Debug log to check the value
    current_app.logger.info(
        f"Updating mask_set_id to '{mask_set_id}' for wafer {wafer_id}"
    )

    # First, make sure lgt structure exists
    cursor.execute(
        """
        UPDATE wafer_inventory
        SET metadata = jsonb_set(
            COALESCE(metadata, '{}'::jsonb),
            '{lgt}',
            COALESCE(metadata->'lgt', '{}'::jsonb)
        )
        WHERE wafer_id = %s
        """,
        [wafer_id],
    )

    # Now update the mask_set_id field
    cursor.execute(
        """
        UPDATE wafer_inventory
        SET metadata = jsonb_set(
            metadata,
            '{lgt,mask_set_id}',
            %s::jsonb
        )
        WHERE wafer_id = %s
        """,
        [json.dumps(mask_set_id), wafer_id],
    )

    # Also update the wafer_mask_set_id field to keep it in sync
    cursor.execute(
        """
        UPDATE wafer_inventory
        SET metadata = jsonb_set(
            metadata,
            '{lgt,wafer_mask_set_id}',
            %s::jsonb
        )
        WHERE wafer_id = %s
        """,
        [json.dumps(mask_set_id), wafer_id],
    )

    # Force update the mask_set_id in the lots table if we have a lot_id
    cursor.execute(
        """
        UPDATE lots l
        SET mask_set_id = %s
        FROM wafers w
        WHERE w.lot_id = l.lot_id AND w.wafer_id = %s
        """,
        [mask_set_id, wafer_id],
    )

    # Verify the update
    cursor.execute(
        """
        SELECT
            metadata->'lgt'->>'mask_set_id' AS mask_set_id,
            metadata->'lgt'->>'wafer_mask_set_id' AS wafer_mask_set_id
        FROM wafer_inventory
        WHERE wafer_id = %s
        """,
        [wafer_id],
    )
    result = cursor.fetchone()
    current_app.logger.info(
        f"After update, mask_set_id = {result['mask_set_id'] if result else 'None'}, "
        f"wafer_mask_set_id = {result['wafer_mask_set_id'] if result else 'None'}"
    )

    return True


def update_lgt_lot_id(cursor, wafer_id, lot_id, mask_set_id=None, xfab_id=None):
    if lot_id is None:
        return False

    current_app.logger.info(f"Updating lgt_lot_id to '{lot_id}' for wafer {wafer_id}")

    # Check if lot_id exists in lots table
    cursor.execute("SELECT lot_id, mask_set_id FROM lots WHERE lot_id = %s", [lot_id])
    lot_result = cursor.fetchone()

    # If mask_set_id is not provided, try to get it from the wafer's metadata
    if not mask_set_id and not lot_result:
        cursor.execute(
            """
            SELECT l.mask_set_id
            FROM wafers w
            JOIN lots l ON w.lot_id = l.lot_id
            WHERE w.wafer_id = %s
            """,
            [wafer_id],
        )
        mask_result = cursor.fetchone()
        if mask_result:
            mask_set_id = mask_result["mask_set_id"]
            current_app.logger.info(
                f"Retrieved mask_set_id {mask_set_id} for wafer {wafer_id}"
            )

    # Create lot_id if it doesn't exist and we have a mask_set_id
    if not lot_result and mask_set_id:
        current_app.logger.info(
            f"Creating new lot {lot_id} with mask_set_id {mask_set_id}"
        )
        cursor.execute(
            """
            INSERT INTO lots (lot_id, metadata, mask_set_id, updated_at, updated_by,
                             pending_mapping)
            VALUES (%s, %s, %s, NOW(), %s, %s)
            """,
            [lot_id, json.dumps({}), mask_set_id, "system", False],
        )
    elif lot_result and mask_set_id and lot_result["mask_set_id"] != mask_set_id:
        current_app.logger.warning(
            f"Mask set mismatch: lot {lot_id} has mask_set_id "
            f"{lot_result['mask_set_id']} but {mask_set_id} was provided"
        )

    # Update xfab_fr_lots if xfab_id is provided
    if xfab_id:
        # Check if xfab_id exists
        cursor.execute(
            "SELECT lot_id FROM xfab_fr_lots WHERE xfab_fr_lot_id = %s", [xfab_id]
        )
        xfab_result = cursor.fetchone()

        if xfab_result:
            # Update existing xfab mapping if different
            if xfab_result["lot_id"] != lot_id:
                cursor.execute(
                    "UPDATE xfab_fr_lots SET lot_id = %s WHERE xfab_fr_lot_id = %s",
                    [lot_id, xfab_id],
                )
                current_app.logger.info(f"Updated xfab mapping: {xfab_id} -> {lot_id}")
        else:
            current_app.logger.warning(
                f"XFAB ID {xfab_id} not found in xfab_fr_lots table"
            )

    # Update the wafers table lot_id
    cursor.execute(
        "UPDATE wafers SET lot_id = %s, updated_at = NOW() WHERE wafer_id = %s",
        [lot_id, wafer_id],
    )

    # Ensure lgt structure exists in metadata
    cursor.execute(
        """
        UPDATE wafer_inventory
        SET metadata = jsonb_set(
            COALESCE(metadata, '{}'::jsonb),
            '{lgt}',
            COALESCE(metadata->'lgt', '{}'::jsonb)
        )
        WHERE wafer_id = %s
        """,
        [wafer_id],
    )

    # Update the lgt_lot_id in metadata
    cursor.execute(
        """
        UPDATE wafer_inventory
        SET metadata = jsonb_set(
            metadata,
            '{lgt,lgt_lot_id}',
            %s::jsonb
        )
        WHERE wafer_id = %s
        """,
        [json.dumps(lot_id), wafer_id],
    )

    # If mask_set_id is provided, also update the wafer_mask_set_id field
    if mask_set_id:
        cursor.execute(
            """
            UPDATE wafer_inventory
            SET metadata = jsonb_set(
                metadata,
                '{lgt,wafer_mask_set_id}',
                %s::jsonb
            )
            WHERE wafer_id = %s
            """,
            [json.dumps(mask_set_id), wafer_id],
        )
        current_app.logger.info(
            f"Updated wafer_mask_set_id to '{mask_set_id}' for wafer {wafer_id}"
        )

    return True


def update_wafer_mask_set_id(cursor, wafer_id, wafer_mask_set_id):
    """
    Update the wafer_mask_set_id in metadata.

    Args:
        cursor: Database cursor
        wafer_id: ID of the wafer to update
        wafer_mask_set_id: New wafer mask set ID value
    """
    if wafer_mask_set_id is None:
        return False

    # Debug log to check the value
    current_app.logger.info(
        f"Explicitly updating wafer_mask_set_id to '{wafer_mask_set_id}' for wafer {wafer_id}"
    )

    # First, make sure lgt structure exists
    cursor.execute(
        """
        UPDATE wafer_inventory
        SET metadata = jsonb_set(
            COALESCE(metadata, '{}'::jsonb),
            '{lgt}',
            COALESCE(metadata->'lgt', '{}'::jsonb)
        )
        WHERE wafer_id = %s
        """,
        [wafer_id],
    )

    # Now update the wafer_mask_set_id field
    cursor.execute(
        """
        UPDATE wafer_inventory
        SET metadata = jsonb_set(
            metadata,
            '{lgt,wafer_mask_set_id}',
            %s::jsonb
        )
        WHERE wafer_id = %s
        """,
        [json.dumps(wafer_mask_set_id), wafer_id],
    )

    # Verify the update
    cursor.execute(
        """
        SELECT metadata->'lgt'->>'wafer_mask_set_id' AS wafer_mask_set_id
        FROM wafer_inventory
        WHERE wafer_id = %s
        """,
        [wafer_id],
    )
    result = cursor.fetchone()
    current_app.logger.info(
        f"After update, wafer_mask_set_id = {result['wafer_mask_set_id'] if result else 'None'}"
    )

    return True


def update_metadata_fields(cursor, wafer_id, data):
    """
    Update metadata fields for a wafer.

    Args:
        cursor: Database cursor
        wafer_id: ID of the wafer to update
        data: Request data dictionary with field values

    Returns:
        bool: True if at least one field was updated
    """
    updated = False

    # Get all relevant fields that might be needed for updates
    module_name = data.get("module_name")
    xfab_id = data.get("xfab_id")
    mask_set_id = data.get("mask_set_id")
    wafer_mask_set_id = data.get(
        "wafer_mask_set_id", mask_set_id
    )  # Default to mask_set_id if not provided
    lot_id = data.get("lot_id")

    # Update module_name if provided
    if module_name is not None:
        if update_module_name(cursor, wafer_id, module_name):
            updated = True

    # Update xfab_id if provided
    if xfab_id is not None:
        if update_xfab_id(cursor, wafer_id, xfab_id):
            updated = True

    # Update mask_set_id if provided
    if mask_set_id is not None:
        if update_mask_set_id(cursor, wafer_id, mask_set_id):
            updated = True

    # Update wafer_mask_set_id if provided or if mask_set_id was provided
    if wafer_mask_set_id is not None:
        if update_wafer_mask_set_id(cursor, wafer_id, wafer_mask_set_id):
            updated = True

    # Update lot_id if provided, passing mask_set_id and xfab_id for lot creation
    if lot_id is not None:
        if update_lgt_lot_id(cursor, wafer_id, lot_id, mask_set_id, xfab_id):
            updated = True

    return updated

"""
Chat routes module for handling chat functionality.
"""

import logging
import random
import time
import uuid
from datetime import datetime

from flask import Blueprint, jsonify, request, session

from database.chat_models import (
    find_best_response,
    log_conversation,
)
from database.chat_schema import setup_chat_tables

# Create blueprint
chat_bp = Blueprint("chat", __name__)

# Configure logging
logger = logging.getLogger(__name__)

# Ensure chat tables exist
setup_chat_tables()

# List of emojis to make responses more lively
EMOJIS = ["😊", "👍", "✨", "🚀", "💡", "🌟", "🔍", "📊", "🎯", "🙌"]

# Fallback responses when no match is found
FALLBACK_RESPONSES = [
    f"I'm not sure I understand. Could you rephrase that? {random.choice(EMOJIS)}",
    f"That's an interesting question! Let me think about it... {random.choice(EMOJIS)}",
    f"I'm still learning about that topic. Can you tell me more? {random.choice(EMOJIS)}",
    f"I don't have that information yet, but I'm learning every day! {random.choice(EMOJIS)}",
    f"Let me connect you with a human who can help better. Please check the Support section! {random.choice(EMOJIS)}",
]


def get_dynamic_response(message):
    """Generate dynamic responses based on the current time and message content"""
    current_hour = datetime.now().hour

    # Time-based greetings
    if "morning" in message.lower():
        if 5 <= current_hour < 12:
            return f"Good morning to you too! It's a beautiful morning. {random.choice(EMOJIS)}"
        else:
            return f"It's not morning here, but good morning to you! {random.choice(EMOJIS)}"

    if "afternoon" in message.lower():
        if 12 <= current_hour < 17:
            return (
                f"Good afternoon! How's your day going so far? {random.choice(EMOJIS)}"
            )
        else:
            return f"It's not afternoon here, but good afternoon to you! {random.choice(EMOJIS)}"

    if "evening" in message.lower() or "night" in message.lower():
        if current_hour >= 17 or current_hour < 5:
            return f"Good evening! Hope you're having a pleasant evening. {random.choice(EMOJIS)}"
        else:
            return f"It's not evening here yet, but good evening to you! {random.choice(EMOJIS)}"

    return None


@chat_bp.route("/chat", methods=["POST"])
def chat():
    """Handle chat messages and return responses"""
    try:
        # Get message from request
        data = request.get_json()
        if not data:
            # Handle form data if JSON is not available
            message = request.form.get("message", "").strip()
        else:
            message = data.get("message", "").strip()

        if not message:
            return jsonify({"success": False, "message": "No message provided"}), 400

        # Log the received message
        logger.info(f"Received chat message: {message}")

        # Add a small delay to simulate thinking (makes the chat feel more natural)
        time.sleep(0.5)

        # Check for dynamic responses first (time-based greetings)
        dynamic_response = get_dynamic_response(message)
        if dynamic_response:
            # Log the conversation
            user_id = session.get("user_id", "anonymous")
            session_id = session.get("chat_session_id")
            if not session_id:
                session_id = str(uuid.uuid4())
                session["chat_session_id"] = session_id

            log_conversation(user_id, message, dynamic_response, session_id)
            return jsonify({"success": True, "response": dynamic_response})

        # Try to find a response from the training data
        db_response = find_best_response(message)
        if db_response:
            # Log the conversation
            user_id = session.get("user_id", "anonymous")
            session_id = session.get("chat_session_id")
            if not session_id:
                session_id = str(uuid.uuid4())
                session["chat_session_id"] = session_id

            log_conversation(user_id, message, db_response, session_id)
            return jsonify({"success": True, "response": db_response})

        # If no match found, provide a generic response
        fallback_response = random.choice(FALLBACK_RESPONSES)

        # Log the conversation
        user_id = session.get("user_id", "anonymous")
        session_id = session.get("chat_session_id")
        if not session_id:
            session_id = str(uuid.uuid4())
            session["chat_session_id"] = session_id

        log_conversation(user_id, message, fallback_response, session_id)
        return jsonify({"success": True, "response": fallback_response})

    except Exception as e:
        logger.error(f"Error in chat endpoint: {str(e)}")
        return (
            jsonify(
                {
                    "success": False,
                    "message": "An error occurred processing your message",
                }
            ),
            500,
        )

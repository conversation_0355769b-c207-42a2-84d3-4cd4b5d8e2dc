# Description: API routes for dashboard_routes.py module. This module
# contains the API endpoints for fetching dashboard statistics. The
# get_dashboard_stats function retrieves inventory statistics from the
# database and Asana statistics using the dashboard service. The function
# combines the data and returns it as a JSON response. If an error occurs
# during the process, an error message is returned with a 500 status code.

from flask import Blueprint, jsonify, current_app
import traceback
from datetime import datetime
from database.db_operations import get_shipment_stats_from_asana  # noqa: F401
from database.db_operations import get_inventory_stats

dashboard_bp = Blueprint("dashboard", __name__, url_prefix="/api/dashboard")


@dashboard_bp.route("/stats", methods=["GET"])
def get_dashboard_stats():
    """API endpoint for getting all dashboard statistics"""
    try:
        # Get dashboard service from app config
        dashboard_service = current_app.config.get("DASHBOARD_SERVICE")

        if not dashboard_service:
            return (
                jsonify(
                    {"success": False, "message": "Dashboard service not available"}
                ),
                500,
            )

        # Get project GID for shipments
        project_gid = "1206397258493005"  # Shipment project from your code

        # Get inventory stats from database using your existing function
        inventory_stats = get_inventory_stats()

        # Get Asana stats using the dashboard service
        asana_stats = dashboard_service.get_shipment_stats(project_gid)
        section_data = dashboard_service.get_section_distribution(project_gid)
        monthly_data = dashboard_service.get_monthly_deliveries(project_gid)
        recent_shipments = dashboard_service.get_recent_shipments(project_gid, limit=5)

        # Log detailed stats for debugging
        current_app.logger.info(f"Asana stats: {asana_stats}")
        current_app.logger.info(
            f"Active shipments: {asana_stats.get('active_shipments', 0)}"
        )
        current_app.logger.info(f"On-time rate: {asana_stats.get('on_time_rate', 0)}")

        # Combine all data with dynamic calculations
        result = {
            "success": True,
            "stats": {
                # Database stats with dynamic values
                "available_lots": inventory_stats.get("available_lots", 0),
                "total_lots": inventory_stats.get("total_lots", 0),
                "capacity_percentage": inventory_stats.get("capacity_percentage", 0),
                "lot_change": inventory_stats.get("lot_change", 0),
                "available_wafers": inventory_stats.get("available_wafers", 0),
                "wafer_percentage": inventory_stats.get("wafer_percentage", 0),
                "shipped_wafers": inventory_stats.get("shipped_wafers", 0),
                "quarterly_target": inventory_stats.get("quarterly_target", 0),
                "quarterly_progress": inventory_stats.get("quarterly_progress", 0),
                "monthly_change": inventory_stats.get("monthly_change", 0),
                # Asana stats
                "total_shipments": asana_stats.get("total_shipments", 0),
                "active_shipments": asana_stats.get("active_shipments", 0),
                "on_time_rate": asana_stats.get("on_time_rate", 0),
                # Chart data
                "section_data": section_data,
                "monthly_data": monthly_data,
                # Last updated timestamp
                "last_updated": datetime.now().strftime("%b %d, %Y, %I:%M %p"),
            },
            "recent_shipments": recent_shipments.get("shipments", []),
        }

        return jsonify(result)

    except Exception as e:
        current_app.logger.error(f"Error getting dashboard stats: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return (
            jsonify(
                {
                    "success": False,
                    "message": f"Error getting dashboard stats: {str(e)}",
                }
            ),
            500,
        )


@dashboard_bp.route("/debug", methods=["GET"])
def debug_dashboard():
    """Debug endpoint for checking Asana connection and data"""
    try:
        # Get dashboard service from app config
        dashboard_service = current_app.config.get("DASHBOARD_SERVICE")

        if not dashboard_service:
            return jsonify(
                {
                    "success": False,
                    "message": "Dashboard service not available",
                    "status": "error",
                }
            )

        # Check if client is initialized
        client_status = "initialized" if dashboard_service.client else "not initialized"

        # Get project GID for shipments
        project_gid = "1206397258493005"  # Shipment project from your code

        # Collect debug information
        debug_info = {
            "success": True,
            "status": "ok",
            "client_status": client_status,
            "asana_token_provided": bool(dashboard_service.asana_token),
            "asana_token_length": (
                len(dashboard_service.asana_token)
                if dashboard_service.asana_token
                else 0
            ),
            "project_gid": project_gid,
            "cache_status": {
                "ttl": dashboard_service.cache.ttl,
                "cache_size": len(dashboard_service.cache.cache),
                "cached_keys": list(dashboard_service.cache.cache.keys()),
            },
            "last_success": {
                "shipment_stats": bool(
                    dashboard_service.last_success["shipment_stats"]
                ),
                "section_distribution": bool(
                    dashboard_service.last_success["section_distribution"]
                ),
                "monthly_deliveries": bool(
                    dashboard_service.last_success["monthly_deliveries"]
                ),
                "recent_shipments": bool(
                    dashboard_service.last_success["recent_shipments"]
                ),
            },
        }

        # Try to get basic stats if client is initialized
        if dashboard_service.client:
            try:
                # Test connection
                connection_test = dashboard_service.verify_connection()
                debug_info["connection_test"] = connection_test

                # Get basic stats
                asana_stats = dashboard_service.get_shipment_stats(project_gid)
                debug_info["asana_stats"] = asana_stats

                # Get section GIDs
                section_gids = dashboard_service.get_section_gids(project_gid)
                debug_info["section_gids"] = section_gids

            except Exception as e:
                debug_info["api_error"] = str(e)
                debug_info["status"] = "error"

        return jsonify(debug_info)

    except Exception as e:
        current_app.logger.error(f"Error in debug endpoint: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify(
            {
                "success": False,
                "message": f"Error in debug endpoint: {str(e)}",
                "status": "error",
            }
        )

#!/bin/bash

# This script updates Docker secret files with values from the database/.env file

# Path to the .env file
ENV_FILE="database/.env"

# Path to the secrets directory
SECRETS_DIR="secrets"

# Check if the .env file exists
if [ ! -f "$ENV_FILE" ]; then
    echo "Error: $ENV_FILE not found"
    exit 1
fi

# Check if the secrets directory exists
if [ ! -d "$SECRETS_DIR" ]; then
    echo "Creating secrets directory..."
    mkdir -p "$SECRETS_DIR"
fi

# Function to convert environment variable name to secret file name
# e.g., SECRET_KEY -> secret_key
to_secret_file() {
    echo "$1" | tr '[:upper:]' '[:lower:]'
}

# Read the .env file and update secret files
echo "Updating secret files..."
while IFS='=' read -r key value || [ -n "$key" ]; do
    # Skip comments and empty lines
    if [[ $key == \#* ]] || [[ -z $key ]]; then
        continue
    fi
    
    # Remove leading/trailing whitespace
    key=$(echo "$key" | xargs)
    value=$(echo "$value" | xargs)
    
    # Skip if key or value is empty
    if [[ -z $key ]] || [[ -z $value ]]; then
        continue
    fi
    
    # Convert key to secret file name
    secret_file=$(to_secret_file "$key")
    
    # Create or update the secret file
    echo -n "$value" > "$SECRETS_DIR/$secret_file"
    
    # Set proper permissions (only owner can read/write)
    chmod 600 "$SECRETS_DIR/$secret_file"
    
    echo "Updated $secret_file"
done < "$ENV_FILE"

echo "Done! All secret files have been updated."

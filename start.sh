#!/bin/bash

# Activate virtual environment
source ./talaria_env/bin/activate

# Set environment variables from .env file if available
if [ -f .env ]; then
    eval "$(grep -v '^#' .env | sed 's/^/export /')"
fi

# Override environment variables if provided as arguments
if [ ! -z "$1" ]; then
    export FLASK_ENV="$1"
fi

# Define the python path explicitly
PYTHON_PATH="./talaria_env/bin/python"

# Start the application
echo "FLASK_ENV is set to: $FLASK_ENV"
if [ "$FLASK_ENV" = "development" ]; then
    # Development mode with auto-reload
    echo "Starting in development mode with auto-reload..."
    
    # Kill any processes that might be using our ports
    for port in 5000 8080; do
        if command -v lsof >/dev/null 2>&1; then
            # lsof is available
            if lsof -i:$port >/dev/null 2>&1; then
                echo "Port $port is in use. Attempting to free it..."
                lsof -i:$port -t | xargs kill -9 2>/dev/null || true
                sleep 1
            fi
        fi
    done
    
    # Set environment variables
    export FLASK_APP=app.py
    export FLASK_DEBUG=1
    export FLASK_ENV=development
    export PYTHONUNBUFFERED=1
    
    # Run the application directly
    echo "Starting Flask with auto-reload..."
    $PYTHON_PATH app.py
else
    # Production mode
    echo "Starting in production mode..."
    echo "Using gunicorn with 4 workers on port ${PORT:-8000}"
    ./talaria_env/bin/gunicorn --worker-class=gthread --workers 4 --bind 0.0.0.0:${PORT:-8000} app:app
fi

# Run The Dashboard
#
#   1. ./start.sh - For development mode, uses Flask's built-in server
#   2. ./start_production.sh - For production mode, uses Gunicorn with 4 workers
#
#   To run the app in production mode, simply use:
#   ./start_production.sh
#
#   This approach is more reliable and avoids environment variable issues. The production script
#   explicitly sets FLASK_ENV=production and uses the gunicorn binary from your virtual environment.

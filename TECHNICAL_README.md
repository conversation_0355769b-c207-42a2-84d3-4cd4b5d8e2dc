# Talaria Dashboard

A comprehensive web application for tracking wafer inventory, shipments, and location management for Ligentec SA.

## Overview

Talaria Dashboard is a specialized inventory management system designed for the semiconductor industry. It provides real-time monitoring of wafer inventory, shipment tracking, and location management. The dashboard includes data visualization, integration with Asana for workflow management, and interfacing with LGT Icarium database.

## Core Technologies

### Backend

- **Python 3.12**: Core programming language
- **Flask**: Web framework for handling HTTP requests and responses
- **PostgreSQL**: Primary database for production
- **Flask-SQLAlchemy**: ORM for database interactions
- **Flask-Login**: User authentication and session management
- **ReportLab/Canvas**: PDF generation for packing slips and labels
- **Gunicorn**: WSGI HTTP server for production deployment

### Frontend

- **HTML/CSS/JavaScript**: Core frontend technologies
- **Tailwind CSS**: Utility-first CSS framework
- **Chart.js/Plotly**: Data visualization libraries
- **SweetAlert2**: Enhanced alert dialogs

### Integrations

- **Asana API**: For project management integration
- **Google Drive API**: For document storage and retrieval
- **SMTP/Email Service**: For sending notifications

## Features

- **User Authentication**: Secure login and role-based access control
- **Wafer Inventory Management**: Track available lots, wafers, and their locations
- **Shipment Tracking**: Monitor shipments and generate shipping labels
- **Location Management**: Organize and track storage locations
- **Real-time Dashboard**: Visualize inventory and shipment statistics
- **Asana Integration**: Sync tasks with Asana for workflow management
- **Google Drive Integration**: Store and retrieve files from Google Drive
- **Label Printing**: Generate and print wafer and shipment labels
- **Packing Slip Generation**: Create packing slips with selectable addresses (France or Switzerland)

## Local Development Setup

1. **Clone the repository**:

   ```bash
   git clone <repository-url>
   cd Talaria_Dashboard
   ```

2. **Set up a virtual environment**:

   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

   Alternatively, using Pipenv:

   ```bash
   pipenv install
   pipenv shell
   ```

3. **Install dependencies**:

   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**:
   Create a `.env` file in the project root:

   ```env
   DB_HOST=localhost
   DB_NAME=icarium_test
   DB_USER=operations_fr_inventory
   DB_PASSWORD=your_password
   MAIL_USERNAME=<EMAIL>
   MAIL_PASSWORD=your_email_password
   ASANA_ACCESS_TOKEN=your_asana_token
   GOOGLE_DRIVE_CREDENTIALS=path/to/credentials.json
   ```

5. **Initialize the development database**:

   ```bash
   # For PostgreSQL
   psql -U your_username -d your_database < database/schema.sql
   ```

6. **Start the development server**:

   ```bash
   # For development mode
   ./start.sh

   # For production mode
   ./start_production.sh
   ```

   The app will be available at [http://localhost:5000](http://localhost:5000)

## User Guide

### Authentication

1. **Login**: Access the application by navigating to the application URL and entering your credentials
2. **Registration**: New users can register with approval from administrators
3. **Permissions**: Different user roles have different access levels:
   - **Admin**: Full access to all features
   - **Operator**: Can manage inventory and shipments
   - **Viewer**: Read-only access to data

### Wafer Inventory Management

1. **Viewing Inventory**: Navigate to the Inventory tab to see all wafers in stock
2. **Adding Wafers**:
   - Click "Add New Wafer"
   - Fill out the wafer details form
   - Upload any relevant files
   - Submit the form

3. **Searching and Filtering**:
   - Use the search bar to find specific wafers
   - Apply filters by date, lot ID, or status

4. **Wafer Details**:
   - Click on any wafer to view detailed information
   - View history and location information
   - Generate QR codes for tracking

### Shipment Management

1. **Creating Shipments**:
   - Go to Shipment Management
   - Click "New Shipment"
   - Select wafers to include
   - Add shipment details (destination, courier, etc.)
   - Submit to create the shipment

2. **Generating Documentation**:
   - From a shipment record, click "Generate Documents"
   - Choose to generate:
     - Shipping labels
     - Packing slips (with option for France or Switzerland address)
     - Manifest documents

3. **Printing Labels**:
   - Select the network printer
   - Choose number of copies
   - Click "Print Label"

4. **Tracking**:
   - View all shipments in the Shipments dashboard
   - Filter by status, date, or destination
   - Export shipment data to CSV

### Location Management

1. **Managing Storage Locations**:
   - Navigate to Location Management
   - View hierarchy of storage locations
   - Add new locations with "Add Location"
   - Assign wafers to specific locations

2. **Location History**:
   - Track movement history of wafers
   - Generate reports on location utilization

### Asana Integration

1. **Linking Tasks**:
   - Connect shipments to Asana tasks by entering the task ID or URL
   - View task details directly in the dashboard

2. **Task Updates**:
   - Update Asana tasks from within the application
   - Automatic status updates when shipments are processed

### Email Notifications

1. **Automated Emails**:
   - System sends automated emails when:
     - Shipments are created
     - Shipment status changes
     - Inventory reaches low thresholds

2. **Manual Notifications**:
   - Send test emails to verify configuration
   - Send custom notifications to stakeholders

### Generating Reports

1. **Inventory Reports**:
   - Go to Reports section
   - Select report type and parameters
   - Generate PDF or CSV reports

2. **Shipment Statistics**:
   - View charts of shipment activity
   - Analyze trends by time period
   - Export statistics in various formats

## Troubleshooting

### Common Issues

1. **Login Problems**:
   - Verify your username and password
   - Check if your account has been activated by an administrator
   - Clear browser cookies and cache

2. **Printer Issues**:
   - Verify printer IP addresses are correct in the printer field (default is *************)
   - Check network connectivity to printers
   - Use the "Test Printer Connection" button to verify connectivity

3. **Email Notifications Not Sending**: Only for EIGER
   - Verify email addresses are formatted correctly
   - Use the "Test Email" button to verify email functionality

4. **Asana Integration Issues**:
   - Verify Asana task links are correctly formatted
   - Check task URLs or IDs for accuracy

5. **Data Not Displaying**:
   - Refresh the browser
   - Check your user permissions
   - Contact administrator if the issue persists

### Developer: Elisee Kajingu

### Directory Structure

```bash
Talaria_Dashboard/
├── app.py                      # Main application entry point
├── wsgi.py                     # WSGI entry point for production
├── config/                     # Configuration files
├── core/                       # Core application code
│   ├── auth/                   # Authentication related code
│   ├── models/                 # Data models
│   └── services/               # Business logic services
├── api/                        # API endpoints
├── routes/                     # Web routes/controllers
├── database/                   # Database-related code
├── utils/                      # Utility functions
├── integrations/               # Third-party integrations
├── static/                     # Static assets
│   ├── css/                    # CSS files
│   ├── js/                     # JavaScript files
│   └── img/                    # Image assets
├── templates/                  # HTML templates
├── tests/                      # Test files
├── backups/                    # Database backups
├── start.sh                   # Development startup script
└── start_production.sh        # Production startup script
```

## Contact and Support

For support, please contact:

- Email: [<EMAIL>](mailto:<EMAIL>) or [<EMAIL>](mailto:<EMAIL>)
- Internal Documentation: Please check the documentation section within the app sidebar menu.

{"python.formatting.provider": "black", "editor.formatOnSave": true, "python.linting.enabled": true, "python.linting.flake8Enabled": true, "python.linting.flake8Args": ["--max-line-length=88", "--extend-ignore=E203"], "[python]": {"editor.defaultFormatter": "ms-python.black-formatter", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}}, "python.testing.unittestArgs": ["-v", "-s", "./tests", "-p", "test_*.py"], "python.testing.pytestEnabled": false, "python.testing.unittestEnabled": true}
// skeleton-loader.js
// Add this to your static/js directory

class SkeletonLoader {
  constructor() {
    this.skeletonElements = {};
    this.originalElements = {};
  }

  // Create skeleton for stats cards
  createStatSkeleton(element) {
    const value = element.textContent;
    this.originalElements[element.dataset.stat] = value;

    // Create and store the skeleton structure
    element.innerHTML = `
      <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-12 mx-auto"></div>
    `;

    // Track which elements have skeletons
    this.skeletonElements[element.dataset.stat] = true;
  }

  // Create skeleton for progress bars
  createProgressSkeleton(container) {
    const progressBar = container.querySelector(".progress-bar-fill");
    const progressText = container.querySelector(".progress-text");

    if (progressBar) {
      this.originalElements[container.id + "-bar"] = progressBar.style.width;
      progressBar.style.width = "0%";
      progressBar.classList.add(
        "bg-gray-200",
        "dark:bg-gray-700",
        "animate-pulse"
      );
    }

    if (progressText) {
      this.originalElements[container.id + "-text"] = progressText.textContent;
      progressText.innerHTML = `<div class="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-16"></div>`;
    }

    this.skeletonElements[container.id] = true;
  }

  // Create skeleton for charts
  createChartSkeleton(chartContainer) {
    const chartId = chartContainer.querySelector("canvas")?.id;
    if (!chartId) return;

    // Save chart visibility state
    const canvas = document.getElementById(chartId);
    if (canvas) {
      canvas.style.display = "none";
      this.originalElements[chartId + "-visible"] = true;
    }

    // Create skeleton UI for chart
    const skeleton = document.createElement("div");
    skeleton.id = `${chartId}-skeleton`;
    skeleton.className =
      "w-full h-full flex flex-col justify-center items-center";

    if (chartId === "sectionChart") {
      // Donut chart skeleton
      skeleton.innerHTML = `
        <div class="relative w-48 h-48 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse">
          <div class="absolute top-1/4 left-1/4 w-1/2 h-1/2 bg-white dark:bg-gray-800 rounded-full"></div>
        </div>
        <div class="mt-4 grid grid-cols-2 gap-2 w-full">
          ${Array(6)
            .fill()
            .map(
              () => `
            <div class="flex items-center">
              <div class="w-3 h-3 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse mr-2"></div>
              <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-16"></div>
            </div>
          `
            )
            .join("")}
        </div>
      `;
    } else {
      // Bar/line chart skeleton
      skeleton.innerHTML = `
        <div class="w-full h-full flex flex-col">
          <div class="h-8 mb-4 flex justify-center gap-4">
            ${Array(3)
              .fill()
              .map(
                () => `
              <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-16"></div>
            `
              )
              .join("")}
          </div>
          <div class="flex-1 grid grid-cols-12 gap-1 items-end px-4">
            ${Array(12)
              .fill()
              .map(() => {
                const height = Math.floor(Math.random() * 70) + 10;
                return `<div class="bg-gray-200 dark:bg-gray-700 rounded-t animate-pulse" style="height: ${height}%"></div>`;
              })
              .join("")}
          </div>
          <div class="h-8 mt-2 grid grid-cols-12 gap-1">
            ${Array(12)
              .fill()
              .map(
                () => `
              <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-full"></div>
            `
              )
              .join("")}
          </div>
        </div>
      `;
    }

    chartContainer.appendChild(skeleton);
    this.skeletonElements[chartId] = true;
  }

  // Create skeleton for shipments table
  createTableSkeleton(tableContainer) {
    const tableBody = tableContainer.querySelector("tbody");
    if (!tableBody) return;

    // Save original table content
    this.originalElements["shipments-table"] = tableBody.innerHTML;

    // Generate skeleton rows
    let skeletonRows = "";
    for (let i = 0; i < 5; i++) {
      skeletonRows += `
        <tr class="border-b border-gray-200 dark:border-gray-700">
          <td class="px-4 py-3"><div class="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-16"></div></td>
          <td class="px-4 py-3"><div class="h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-20"></div></td>
          <td class="px-4 py-3"><div class="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-24"></div></td>
          <td class="px-4 py-3"><div class="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-20"></div></td>
          <td class="px-4 py-3"><div class="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-8"></div></td>
        </tr>
      `;
    }

    tableBody.innerHTML = skeletonRows;
    this.skeletonElements["shipments-table"] = true;
  }

  // Show skeleton loaders for all dashboard elements
  showSkeletons() {
    // Stats
    document.querySelectorAll("[data-stat]").forEach((el) => {
      this.createStatSkeleton(el);
    });

    // Progress bars
    document.querySelectorAll(".metric-card").forEach((el) => {
      this.createProgressSkeleton(el);
    });

    // Charts
    document.querySelectorAll(".chart-container").forEach((el) => {
      this.createChartSkeleton(el);
    });

    // Table
    const tableContainer = document.querySelector(".shipments-table");
    if (tableContainer) {
      this.createTableSkeleton(tableContainer);
    }
  }

  // Remove all skeletons and restore original content
  removeSkeletons() {
    // Restore stat values
    for (const [stat, value] of Object.entries(this.originalElements)) {
      const element = document.querySelector(`[data-stat="${stat}"]`);
      if (element) {
        element.textContent = value;
      }

      // Handle progress bars
      if (stat.endsWith("-bar")) {
        const id = stat.replace("-bar", "");
        const progressBar = document.querySelector(`#${id} .progress-bar-fill`);
        if (progressBar) {
          progressBar.style.width = value;
          progressBar.classList.remove(
            "bg-gray-200",
            "dark:bg-gray-700",
            "animate-pulse"
          );
        }
      }

      // Handle progress text
      if (stat.endsWith("-text")) {
        const id = stat.replace("-text", "");
        const progressText = document.querySelector(`#${id} .progress-text`);
        if (progressText) {
          progressText.textContent = value;
        }
      }

      // Handle chart visibility
      if (stat.endsWith("-visible")) {
        const chartId = stat.replace("-visible", "");
        const canvas = document.getElementById(chartId);
        if (canvas) {
          canvas.style.display = "block";
        }

        // Remove chart skeleton
        const skeleton = document.getElementById(`${chartId}-skeleton`);
        if (skeleton) {
          skeleton.remove();
        }
      }
    }

    // Remove table skeleton
    if (this.skeletonElements["shipments-table"]) {
      const tableBody = document.querySelector(".shipments-table tbody");
      if (tableBody && this.originalElements["shipments-table"]) {
        tableBody.innerHTML = this.originalElements["shipments-table"];
      }
    }

    // Reset tracking
    this.skeletonElements = {};
    this.originalElements = {};
  }
}

// Create a global instance
window.skeletonLoader = new SkeletonLoader();

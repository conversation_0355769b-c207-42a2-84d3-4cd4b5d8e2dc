/**
 * permissions.js - Handles UI elements based on user role and permissions
 */

document.addEventListener("DOMContentLoaded", function () {
  // Get user role from data attribute on body
  const userRole = document.body.getAttribute("data-user-role");
  console.log("Applying permission restrictions for role:", userRole);

  if (!userRole) {
    console.log("No user role detected, skipping permission checks");
    return;
  }

  console.log(`User role detected: ${userRole}`);

  // Define permissions based on role
  const permissions = {
    admin: ["view", "modify", "create", "delete", "sync"],
    colleague: ["view"],
  };

  // Get allowed permissions for current user - FIXED LINE
  const userPermissions = permissions[userRole] || [];
  console.log("User permissions:", userPermissions);

  /**
   * Check if the current user has a specific permission
   */
  function hasPermission(permission) {
    return userPermissions.includes(permission);
  }

  /**
   * Apply restrictions to UI elements based on permissions
   */
  function applyPermissionRestrictions() {
    // Process modify permission buttons
    if (!hasPermission("modify")) {
      const modifyButtons = document.querySelectorAll(
        '.btn-modify, [data-permission="modify"]'
      );
      modifyButtons.forEach(disableElement);
    }

    // Process add permission buttons
    if (!hasPermission("create")) {
      const addButtons = document.querySelectorAll(
        '.btn-add, [data-permission="create"]'
      );
      addButtons.forEach(disableElement);
    }

    // Process delete permission buttons
    if (!hasPermission("delete")) {
      const deleteButtons = document.querySelectorAll(
        '.btn-delete, [data-permission="delete"]'
      );
      deleteButtons.forEach(disableElement);
    }

    // Process sync permission buttons
    if (!hasPermission("sync")) {
      const syncButtons = document.querySelectorAll(
        '.btn-sync, [data-permission="sync"]'
      );
      syncButtons.forEach(disableElement);
    }

    // Handle forms with permission requirements
    const restrictedForms = document.querySelectorAll(
      "form[data-required-permission]"
    );
    restrictedForms.forEach((form) => {
      const requiredPermission = form.getAttribute("data-required-permission");
      if (!hasPermission(requiredPermission)) {
        const submitButtons = form.querySelectorAll(
          'button[type="submit"], input[type="submit"]'
        );
        submitButtons.forEach(disableElement);

        // Add warning message to form
        const warningMsg = document.createElement("div");
        warningMsg.className =
          "mt-4 p-2 bg-red-50 text-red-700 rounded text-sm";
        warningMsg.innerHTML =
          '<i class="fas fa-lock mr-1"></i> You do not have permission to submit this form';
        form.appendChild(warningMsg);
      }
    });
  }

  /**
   * Disable an element and add a visual indication
   */
  function disableElement(element) {
    // Check if element is already processed
    if (element.classList.contains("permission-processed")) {
      return;
    }

    // Mark as processed to avoid double processing
    element.classList.add("permission-processed");

    // Save original title
    const originalTitle = element.getAttribute("title") || "";

    // Disable element
    element.disabled = true;
    element.classList.add("opacity-50", "cursor-not-allowed");
    element.setAttribute(
      "title",
      (originalTitle ? originalTitle + " - " : "") +
        "You do not have permission for this action"
    );

    // If it's a link, prevent default
    if (element.tagName.toLowerCase() === "a") {
      element.addEventListener("click", function (e) {
        e.preventDefault();
        showPermissionAlert();
      });
    }

    // Add lock icon if it doesn't already have one
    if (!element.querySelector(".fa-lock")) {
      const lockIcon = document.createElement("i");
      lockIcon.className = "fas fa-lock ml-1";
      element.appendChild(lockIcon);
    }
  }

  /**
   * Show permission denied alert
   */
  function showPermissionAlert() {
    if (typeof Swal !== "undefined") {
      Swal.fire({
        title: "Permission Denied",
        text: "You do not have permission to perform this action.",
        icon: "error",
        confirmButtonText: "Ok",
      });
    } else {
      alert(
        "Permission Denied: You do not have permission to perform this action."
      );
    }
  }

  // Apply permission restrictions
  applyPermissionRestrictions();

  // Make functions globally available
  window.PermissionManager = {
    hasPermission: hasPermission,
    showPermissionAlert: showPermissionAlert,
  };
});

/**
 * Handle API requests with permission checks
 */
class ApiPermission {
  /**
   * Make API request if user has permission
   * @param {string} permission - Required permission
   * @param {string} url - API endpoint
   * @param {Object} options - Fetch options
   * @param {Function} onSuccess - Success callback
   * @param {Function} onError - Error callback
   */
  static async request(
    permission,
    url,
    options = {},
    onSuccess = null,
    onError = null
  ) {
    // Check if user has permission
    if (
      !window.PermissionManager ||
      !window.PermissionManager.hasPermission(permission)
    ) {
      window.PermissionManager?.showPermissionAlert();
      return;
    }

    try {
      // Add CSRF token
      if (!options.headers) {
        options.headers = {};
      }

      const csrfToken =
        document
          .querySelector('meta[name="csrf-token"]')
          ?.getAttribute("content") ||
        document.querySelector('input[name="csrf_token"]')?.value;

      if (csrfToken) {
        options.headers["X-CSRFToken"] = csrfToken;
      }

      // Make request
      const response = await fetch(url, options);
      const data = await response.json();

      if (response.ok) {
        if (onSuccess && typeof onSuccess === "function") {
          onSuccess(data);
        }
        return data;
      } else {
        if (onError && typeof onError === "function") {
          onError(data);
        }
        throw new Error(data.message || "API request failed");
      }
    } catch (error) {
      if (onError && typeof onError === "function") {
        onError({ success: false, message: error.message });
      }
      console.error("API request error:", error);
      throw error;
    }
  }
}

// Example usage:
/*
// Post request with permission check
document.getElementById('addButton').addEventListener('click', async () => {
    try {
        const data = await ApiPermission.request(
            'add',                         // Required permission
            '/api/inventory/add',          // API endpoint
            {                              // Request options
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({
                    wafer_ids: ['123ABC', '456DEF'],
                    slot_ids: [1, 2]
                })
            },
            (data) => {                    // Success callback
                console.log('Success:', data);
                showSuccessMessage('Items added successfully');
            },
            (error) => {                   // Error callback
                console.error('Error:', error);
                showErrorMessage(error.message || 'Failed to add items');
            }
        );
    } catch (error) {
        // Handle any uncaught errors
        console.error('Unhandled error:', error);
    }
});
*/

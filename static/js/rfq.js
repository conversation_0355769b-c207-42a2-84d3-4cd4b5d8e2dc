
document.addEventListener("DOMContentLoaded", function () {
  // DOM Elements - Form Fields
  const sifoProjectInput = document.getElementById("sifoProject");
  const talosLotIdInput = document.getElementById("talosLotId");
  const lotProjectInput = document.getElementById("lotProject");
  const xfabCloudUrlInput = document.getElementById("xfabCloudUrl");

  // DOM Elements - PDF Upload
  const pdfUploadArea = document.getElementById("pdfUploadArea");
  const pdfFileInput = document.getElementById("pdfFile");
  const pdfFileInfo = document.getElementById("pdfFileInfo");
  const pdfFileName = document.getElementById("pdfFileName");
  const pdfFileSize = document.getElementById("pdfFileSize");
  const removePdfFileBtn = document.getElementById("removePdfFile");

  // DOM Elements - Form Actions
  const validateFormBtn = document.getElementById("validateFormBtn");
  const clearFormBtn = document.getElementById("clearFormBtn");

  // DOM Elements - Email Sending
  const sendSection = document.getElementById("sendSection");
  const sendAllBtn = document.getElementById("sendAllBtn");
  const clearSessionBtn = document.getElementById("clearSessionBtn");
  const resultsSection = document.getElementById("resultsSection");
  const loadingModal = document.getElementById("loadingModal");
  const loadingText = document.getElementById("loadingText");

  // State variables
  let currentMode = "production";
  let uploadedPdfFile = null;

  // Check for existing data on page load
  let existingData = null;
  try {
    const existingDataElement = document.getElementById('existing-data');
    if (existingDataElement && existingDataElement.textContent.trim()) {
      existingData = JSON.parse(existingDataElement.textContent);
      console.log("Found existing RFQ data:", existingData);
      restoreFormData(existingData);
    }
  } catch (error) {
    console.log("No existing data found or error parsing:", error);
  }

  // PDF Upload handling
  pdfUploadArea.addEventListener("click", () => pdfFileInput.click());
  pdfFileInput.addEventListener("change", handlePdfSelect);

  // PDF Drag and drop
  pdfUploadArea.addEventListener("dragover", (e) => {
    e.preventDefault();
    pdfUploadArea.classList.add("dragover");
  });

  pdfUploadArea.addEventListener("dragleave", () => {
    pdfUploadArea.classList.remove("dragover");
  });

  pdfUploadArea.addEventListener("drop", (e) => {
    e.preventDefault();
    pdfUploadArea.classList.remove("dragover");
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      pdfFileInput.files = files;
      handlePdfSelect();
    }
  });

  removePdfFileBtn.addEventListener("click", removePdfFile);

  // Form event listeners
  validateFormBtn.addEventListener("click", validateAndPreview);
  clearFormBtn.addEventListener("click", clearForm);

  // Real-time validation - add event listeners to all form inputs
  sifoProjectInput.addEventListener("input", checkFormCompletion);
  talosLotIdInput.addEventListener("input", checkFormCompletion);
  lotProjectInput.addEventListener("input", checkFormCompletion);
  xfabCloudUrlInput.addEventListener("input", checkFormCompletion);

  // Initial check on page load
  checkFormCompletion();

    // Email sending
    sendAllBtn.addEventListener("click", sendAllEmails);
    clearSessionBtn.addEventListener("click", clearSession);

  // PDF File handling functions
  function handlePdfSelect() {
    const file = pdfFileInput.files[0];
    if (!file) return;

    // Validate PDF file
    if (file.type !== 'application/pdf') {
      showError('Please select a PDF file only.');
      pdfFileInput.value = '';
      return;
    }

    // Check file size (25MB limit)
    const maxSize = 25 * 1024 * 1024; // 25MB in bytes
    if (file.size > maxSize) {
      showError('PDF file size must be less than 25MB.');
      pdfFileInput.value = '';
      return;
    }

    uploadedPdfFile = file;

    // Show file info
    pdfFileName.textContent = file.name;
    pdfFileSize.textContent = `Size: ${(file.size / 1024 / 1024).toFixed(2)} MB`;
    pdfFileInfo.classList.remove("hidden");
  }

  function removePdfFile() {
    uploadedPdfFile = null;
    pdfFileInput.value = '';
    pdfFileInfo.classList.add("hidden");
  }

  // Real-time form validation
  function checkFormCompletion() {
    // Collect current form data
    const data = {
      sifoProject: sifoProjectInput.value.trim(),
      talosLotId: talosLotIdInput.value.trim(),
      lotProject: lotProjectInput.value.trim(),
      xfabCloudUrl: xfabCloudUrlInput.value.trim()
    };

    // Check which fields are missing
    const missingFields = [];
    if (!data.sifoProject) missingFields.push('Sifo Project');
    if (!data.talosLotId) missingFields.push('Talos Lot ID');
    if (!data.lotProject) missingFields.push('Lot Project');
    if (!data.xfabCloudUrl) missingFields.push('XFab Cloud URL');

    // Validate URL format if provided
    let urlError = false;
    if (data.xfabCloudUrl && !isValidUrl(data.xfabCloudUrl)) {
      urlError = true;
    }

    // Update form status
    if (missingFields.length === 0 && !urlError) {
      // All fields filled and valid - show send section
      showFormComplete();
      hideFormWarning();
    } else {
      // Missing fields or invalid URL - hide send section and show warning
      hideFormComplete();
      if (missingFields.length > 0) {
        showFormWarning(`Please fill in: ${missingFields.join(', ')}`);
      } else if (urlError) {
        showFormWarning('Please enter a valid XFab Cloud URL');
      }
    }
  }

  function showFormComplete() {
    if (sendSection) {
      sendSection.classList.remove("hidden");
    }
  }

  function hideFormComplete() {
    if (sendSection) {
      sendSection.classList.add("hidden");
    }
  }

  function showFormWarning(message) {
    // Create or update warning message
    let warningDiv = document.getElementById('formWarning');
    if (!warningDiv) {
      warningDiv = document.createElement('div');
      warningDiv.id = 'formWarning';
      warningDiv.className = 'bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-4';

      // Insert after the form
      const formContainer = document.querySelector('.bg-white.dark\\:bg-gray-800.rounded-lg.shadow-md.p-6');
      if (formContainer) {
        formContainer.appendChild(warningDiv);
      }
    }

    warningDiv.innerHTML = `
      <div class="flex items-center">
        <i class="fas fa-exclamation-triangle text-yellow-500 mr-2"></i>
        <span class="text-sm text-yellow-700">${message}</span>
      </div>
    `;
    warningDiv.classList.remove('hidden');
  }

  function hideFormWarning() {
    const warningDiv = document.getElementById('formWarning');
    if (warningDiv) {
      warningDiv.classList.add('hidden');
    }
  }

  // Form validation and preview functions (keep for manual validation if needed)
  function validateAndPreview() {
    // Just trigger the real-time validation
    checkFormCompletion();

    // Collect form data
    const data = {
      sifoProject: sifoProjectInput.value.trim(),
      talosLotId: talosLotIdInput.value.trim(),
      lotProject: lotProjectInput.value.trim(),
      xfabCloudUrl: xfabCloudUrlInput.value.trim()
    };

    // Validate required fields
    const errors = [];
    if (!data.sifoProject) errors.push('Sifo Project is required');
    if (!data.talosLotId) errors.push('Talos Lot ID is required');
    if (!data.lotProject) errors.push('Lot Project is required');
    if (!data.xfabCloudUrl) errors.push('XFab Cloud URL is required');

    // Validate URL format
    if (data.xfabCloudUrl && !isValidUrl(data.xfabCloudUrl)) {
      errors.push('Please enter a valid XFab Cloud URL');
    }

    if (errors.length > 0) {
      showError('Please fill in all required fields:\n' + errors.join('\n'));
      return;
    }

    // Show success message
    showSuccess('Form validated successfully! You can now send the RFQ email.');
  }

  function clearForm() {
    // Clear all form fields
    sifoProjectInput.value = '';
    talosLotIdInput.value = '';
    lotProjectInput.value = '';
    xfabCloudUrlInput.value = '';

    // Clear PDF file
    removePdfFile();

    // Hide send section and results
    sendSection.classList.add("hidden");
    resultsSection.classList.add("hidden");

    // Hide warning message
    hideFormWarning();

    // Clear stored data
    formData = {};

    // Trigger real-time validation to update UI
    checkFormCompletion();

    showSuccess('Form cleared successfully!');
  }

  // Restore form data from session
  function restoreFormData(data) {
    if (data.sifoProject) sifoProjectInput.value = data.sifoProject;
    if (data.talosLotId) talosLotIdInput.value = data.talosLotId;
    if (data.lotProject) lotProjectInput.value = data.lotProject;
    if (data.xfabCloudUrl) xfabCloudUrlInput.value = data.xfabCloudUrl;

    formData = data;
    sendSection.classList.remove("hidden");
  }

  // Utility functions
  function isValidUrl(string) {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  }

  function showError(message) {
    alert('Error: ' + message);
  }

  function showSuccess(message) {
    alert('Success: ' + message);
  }

  function showLoading(message) {
    if (loadingModal && loadingText) {
      loadingText.textContent = message;
      loadingModal.classList.remove("hidden");
    }
  }

  function hideLoading() {
    if (loadingModal) {
      loadingModal.classList.add("hidden");
    }
  }

  function showResults(data) {
    if (resultsSection) {
      resultsSection.classList.remove("hidden");
      // You can add more result display logic here if needed
      console.log("Email results:", data);
    }
  }





    function sendAllEmails() {
      // Collect current form data directly from inputs
      const currentFormData = {
        sifoProject: sifoProjectInput.value.trim(),
        talosLotId: talosLotIdInput.value.trim(),
        lotProject: lotProjectInput.value.trim(),
        xfabCloudUrl: xfabCloudUrlInput.value.trim()
      };

      // Validate required fields before sending
      const errors = [];
      if (!currentFormData.sifoProject) errors.push('Sifo Project is required');
      if (!currentFormData.talosLotId) errors.push('Talos Lot ID is required');
      if (!currentFormData.lotProject) errors.push('Lot Project is required');
      if (!currentFormData.xfabCloudUrl) errors.push('XFab Cloud URL is required');

      if (errors.length > 0) {
        showError('Please fill in all required fields:\n' + errors.join('\n'));
        return;
      }

      Swal.fire({
        title: "Send RFQ Email?",
        text: "Are you sure you want to send the RFQ email to XFab team?",
        icon: "question",
        showCancelButton: true,
        confirmButtonColor: "#28a745",
        cancelButtonColor: "#6c757d",
        confirmButtonText: "Yes, Send Email!",
        cancelButtonText: "Cancel",
      }).then((result) => {
        if (result.isConfirmed) {
          showLoading("Sending RFQ email...");

          // Prepare form data for sending
          const emailData = new FormData();
          emailData.append('sifoProject', currentFormData.sifoProject);
          emailData.append('talosLotId', currentFormData.talosLotId);
          emailData.append('lotProject', currentFormData.lotProject);
          emailData.append('xfabCloudUrl', currentFormData.xfabCloudUrl);
          emailData.append('mode', currentMode);

          // Add PDF file if uploaded
          if (uploadedPdfFile) {
            emailData.append('pdfFile', uploadedPdfFile);
          }

          fetch("/rfq/send", {
            method: "POST",
            body: emailData
          })
            .then((response) => response.json())
            .then((data) => {
              hideLoading();
              showResults(data);
            })
            .catch((error) => {
              hideLoading();
              showError("Error sending emails: " + error.message);
            });
        }
      });
    }

    function showResults(data) {
      const resultsContent = document.getElementById("resultsContent");

      if (data.success) {
        // Clean results display without test mode references
        const results = data.results || {};
        const errorsCount = results.errors_count || 0;
        const hasErrors = results.errors && results.errors.length > 0;

        resultsContent.innerHTML = `
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h3 class="text-lg font-medium text-green-800 mb-2">
                        <i class="fas fa-check-circle mr-2"></i>RFQ Email Sent Successfully
                    </h3>
                    <div class="text-sm text-green-700">
                        <p><strong>Status:</strong> Email delivered successfully</p>
                        <p><strong>Recipients:</strong> XFab team notified</p>
                        ${errorsCount > 0 ? `<p><strong>Issues:</strong> ${errorsCount} warnings</p>` : ''}
                    </div>
                    ${
                      hasErrors
                        ? `
                        <div class="mt-3">
                            <p class="font-medium text-red-700">Warnings:</p>
                            <ul class="text-sm text-red-600 list-disc list-inside">
                                ${results.errors
                                  .map((error) => `<li>${error}</li>`)
                                  .join("")}
                            </ul>
                        </div>
                    `
                        : ""
                    }
                </div>
            `;
      } else {
        resultsContent.innerHTML = `
                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <h3 class="text-lg font-medium text-red-800 mb-2">
                        <i class="fas fa-exclamation-circle mr-2"></i>Email Sending Failed
                    </h3>
                    <p class="text-sm text-red-700">${data.message}</p>
                </div>
            `;
      }

      resultsSection.classList.remove("hidden");
    }



    function showLoading(text) {
      loadingText.textContent = text;
      loadingModal.classList.remove("hidden");
    }

    function hideLoading() {
      loadingModal.classList.add("hidden");
    }

    function showSuccess(message) {
      Swal.fire({
        icon: "success",
        title: "Success!",
        text: message,
        timer: 3000,
        showConfirmButton: false,
        toast: true,
        position: "top-end",
      });
    }

    function showError(message) {
      Swal.fire({
        icon: "error",
        title: "Error!",
        text: message,
        confirmButtonText: "OK",
        confirmButtonColor: "#dc3545",
      });
    }



    function clearSession() {
      Swal.fire({
        title: "Clear Form Data?",
        text: "This will clear all form fields and reset the RFQ form.",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#dc3545",
        cancelButtonColor: "#6c757d",
        confirmButtonText: "Yes, Clear Form!",
        cancelButtonText: "Cancel",
      }).then((result) => {
        if (result.isConfirmed) {
          // Clear form and reset state
          clearForm();
          resultsSection.classList.add("hidden");
          sendSection.classList.add("hidden");
          showSuccess("Form cleared! You can now start fresh.");
        }
      });
    }
  });


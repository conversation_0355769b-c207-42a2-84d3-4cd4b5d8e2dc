// Advanced Analytics Dashboard for Talaria
// Provides comprehensive business intelligence, KPI tracking, and predictive insights

class AnalyticsDashboard {
  constructor() {
    this.charts = new Map();
    this.kpiWidgets = new Map();
    this.dataCache = new Map();
    this.refreshInterval = null;
    this.isRealTimeEnabled = true;
    
    this.init();
  }

  init() {
    this.setupAnalyticsContainer();
    this.initializeCharts();
    this.setupKPIWidgets();
    this.setupFilters();
    this.startRealTimeUpdates();
    this.setupExportFunctionality();
  }

  setupAnalyticsContainer() {
    // Create analytics dashboard container if it doesn't exist
    let container = document.getElementById('analytics-dashboard');
    if (!container) {
      container = document.createElement('div');
      container.id = 'analytics-dashboard';
      container.className = 'analytics-dashboard glass-card';
      
      // Insert after main dashboard or in designated area
      const mainDashboard = document.querySelector('.main-content') || document.body;
      mainDashboard.appendChild(container);
    }

    container.innerHTML = `
      <div class="analytics-header">
        <h2><i class="fas fa-chart-line"></i> Business Intelligence</h2>
        <div class="analytics-controls">
          <select id="analytics-timeframe" class="timeframe-selector">
            <option value="7d">Last 7 Days</option>
            <option value="30d" selected>Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
            <option value="1y">Last Year</option>
          </select>
          <button id="export-analytics" class="export-btn">
            <i class="fas fa-download"></i> Export
          </button>
          <button id="toggle-realtime" class="realtime-toggle active">
            <i class="fas fa-broadcast-tower"></i> Live
          </button>
        </div>
      </div>
      
      <div class="kpi-grid">
        <div class="kpi-widget" id="throughput-kpi">
          <div class="kpi-header">
            <h3>Throughput Rate</h3>
            <i class="fas fa-tachometer-alt"></i>
          </div>
          <div class="kpi-value">
            <span class="value">0</span>
            <span class="unit">wafers/day</span>
          </div>
          <div class="kpi-trend">
            <span class="trend-indicator">+0%</span>
            <span class="trend-period">vs last period</span>
          </div>
        </div>
        
        <div class="kpi-widget" id="efficiency-kpi">
          <div class="kpi-header">
            <h3>Process Efficiency</h3>
            <i class="fas fa-cogs"></i>
          </div>
          <div class="kpi-value">
            <span class="value">0</span>
            <span class="unit">%</span>
          </div>
          <div class="kpi-trend">
            <span class="trend-indicator">+0%</span>
            <span class="trend-period">vs target</span>
          </div>
        </div>
        
        <div class="kpi-widget" id="quality-kpi">
          <div class="kpi-header">
            <h3>Quality Score</h3>
            <i class="fas fa-award"></i>
          </div>
          <div class="kpi-value">
            <span class="value">0</span>
            <span class="unit">/100</span>
          </div>
          <div class="kpi-trend">
            <span class="trend-indicator">+0%</span>
            <span class="trend-period">quality index</span>
          </div>
        </div>
        
        <div class="kpi-widget" id="cost-kpi">
          <div class="kpi-header">
            <h3>Cost per Wafer</h3>
            <i class="fas fa-dollar-sign"></i>
          </div>
          <div class="kpi-value">
            <span class="value">$0</span>
            <span class="unit">avg</span>
          </div>
          <div class="kpi-trend">
            <span class="trend-indicator">+0%</span>
            <span class="trend-period">cost optimization</span>
          </div>
        </div>
      </div>
      
      <div class="charts-grid">
        <div class="chart-container">
          <div class="chart-header">
            <h3>Inventory Flow Analysis</h3>
            <div class="chart-controls">
              <button class="chart-type-btn active" data-type="line">Line</button>
              <button class="chart-type-btn" data-type="bar">Bar</button>
              <button class="chart-type-btn" data-type="area">Area</button>
            </div>
          </div>
          <canvas id="inventory-flow-chart"></canvas>
        </div>
        
        <div class="chart-container">
          <div class="chart-header">
            <h3>Shipment Performance</h3>
            <div class="chart-controls">
              <button class="chart-filter-btn active" data-filter="all">All</button>
              <button class="chart-filter-btn" data-filter="on-time">On-Time</button>
              <button class="chart-filter-btn" data-filter="delayed">Delayed</button>
            </div>
          </div>
          <canvas id="shipment-performance-chart"></canvas>
        </div>
        
        <div class="chart-container">
          <div class="chart-header">
            <h3>Location Utilization</h3>
            <div class="chart-controls">
              <button class="chart-view-btn active" data-view="capacity">Capacity</button>
              <button class="chart-view-btn" data-view="efficiency">Efficiency</button>
            </div>
          </div>
          <canvas id="location-utilization-chart"></canvas>
        </div>
        
        <div class="chart-container">
          <div class="chart-header">
            <h3>Predictive Trends</h3>
            <div class="chart-controls">
              <button class="prediction-btn active" data-period="7d">7 Days</button>
              <button class="prediction-btn" data-period="30d">30 Days</button>
              <button class="prediction-btn" data-period="90d">90 Days</button>
            </div>
          </div>
          <canvas id="predictive-trends-chart"></canvas>
        </div>
      </div>
      
      <div class="insights-panel">
        <h3><i class="fas fa-lightbulb"></i> AI-Powered Insights</h3>
        <div id="insights-content">
          <div class="insight-loading">Analyzing data patterns...</div>
        </div>
      </div>
    `;
  }

  initializeCharts() {
    // Initialize Chart.js charts with modern styling
    this.initInventoryFlowChart();
    this.initShipmentPerformanceChart();
    this.initLocationUtilizationChart();
    this.initPredictiveTrendsChart();
  }

  initInventoryFlowChart() {
    const ctx = document.getElementById('inventory-flow-chart');
    if (!ctx) return;

    this.charts.set('inventory-flow', new Chart(ctx, {
      type: 'line',
      data: {
        labels: [],
        datasets: [{
          label: 'Incoming',
          data: [],
          borderColor: '#4f46e5',
          backgroundColor: 'rgba(79, 70, 229, 0.1)',
          tension: 0.4,
          fill: true
        }, {
          label: 'Outgoing',
          data: [],
          borderColor: '#ef4444',
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          tension: 0.4,
          fill: true
        }, {
          label: 'Net Change',
          data: [],
          borderColor: '#10b981',
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          tension: 0.4,
          fill: true
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'top',
            labels: {
              usePointStyle: true,
              padding: 20
            }
          },
          tooltip: {
            mode: 'index',
            intersect: false,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            titleColor: '#fff',
            bodyColor: '#fff',
            borderColor: '#4f46e5',
            borderWidth: 1
          }
        },
        scales: {
          x: {
            display: true,
            grid: {
              color: 'rgba(0, 0, 0, 0.1)'
            }
          },
          y: {
            display: true,
            grid: {
              color: 'rgba(0, 0, 0, 0.1)'
            }
          }
        },
        interaction: {
          mode: 'nearest',
          axis: 'x',
          intersect: false
        }
      }
    }));
  }

  initShipmentPerformanceChart() {
    const ctx = document.getElementById('shipment-performance-chart');
    if (!ctx) return;

    this.charts.set('shipment-performance', new Chart(ctx, {
      type: 'doughnut',
      data: {
        labels: ['On Time', 'Delayed', 'Early'],
        datasets: [{
          data: [0, 0, 0],
          backgroundColor: [
            '#10b981',
            '#ef4444',
            '#3b82f6'
          ],
          borderWidth: 0,
          hoverOffset: 4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              padding: 20,
              usePointStyle: true
            }
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                const percentage = ((context.parsed / total) * 100).toFixed(1);
                return `${context.label}: ${context.parsed} (${percentage}%)`;
              }
            }
          }
        }
      }
    }));
  }

  initLocationUtilizationChart() {
    const ctx = document.getElementById('location-utilization-chart');
    if (!ctx) return;

    this.charts.set('location-utilization', new Chart(ctx, {
      type: 'bar',
      data: {
        labels: [],
        datasets: [{
          label: 'Utilization %',
          data: [],
          backgroundColor: 'rgba(79, 70, 229, 0.8)',
          borderColor: '#4f46e5',
          borderWidth: 1,
          borderRadius: 4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                return `${context.label}: ${context.parsed.y}% utilized`;
              }
            }
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            max: 100,
            ticks: {
              callback: function(value) {
                return value + '%';
              }
            }
          }
        }
      }
    }));
  }

  initPredictiveTrendsChart() {
    const ctx = document.getElementById('predictive-trends-chart');
    if (!ctx) return;

    this.charts.set('predictive-trends', new Chart(ctx, {
      type: 'line',
      data: {
        labels: [],
        datasets: [{
          label: 'Historical',
          data: [],
          borderColor: '#6b7280',
          backgroundColor: 'rgba(107, 114, 128, 0.1)',
          tension: 0.4
        }, {
          label: 'Predicted',
          data: [],
          borderColor: '#f59e0b',
          backgroundColor: 'rgba(245, 158, 11, 0.1)',
          borderDash: [5, 5],
          tension: 0.4
        }, {
          label: 'Confidence Interval',
          data: [],
          borderColor: 'rgba(245, 158, 11, 0.3)',
          backgroundColor: 'rgba(245, 158, 11, 0.1)',
          fill: '+1'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'top'
          },
          tooltip: {
            mode: 'index',
            intersect: false
          }
        },
        scales: {
          x: {
            display: true
          },
          y: {
            display: true
          }
        }
      }
    }));
  }

  setupKPIWidgets() {
    // Setup interactive KPI widgets with real-time updates
    const kpiWidgets = document.querySelectorAll('.kpi-widget');
    
    kpiWidgets.forEach(widget => {
      widget.addEventListener('click', () => {
        this.showKPIDetails(widget.id);
      });
    });
  }

  setupFilters() {
    // Timeframe selector
    const timeframeSelector = document.getElementById('analytics-timeframe');
    if (timeframeSelector) {
      timeframeSelector.addEventListener('change', (e) => {
        this.updateTimeframe(e.target.value);
      });
    }

    // Chart type buttons
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('chart-type-btn')) {
        this.changeChartType(e.target.dataset.type, e.target.closest('.chart-container'));
      }
      
      if (e.target.classList.contains('chart-filter-btn')) {
        this.applyChartFilter(e.target.dataset.filter, e.target.closest('.chart-container'));
      }
    });

    // Real-time toggle
    const realtimeToggle = document.getElementById('toggle-realtime');
    if (realtimeToggle) {
      realtimeToggle.addEventListener('click', () => {
        this.toggleRealTime();
      });
    }
  }

  async loadAnalyticsData(timeframe = '30d') {
    try {
      const response = await fetch(`/api/analytics/dashboard?timeframe=${timeframe}`, {
        headers: {
          'X-Requested-With': 'XMLHttpRequest'
        }
      });

      const data = await response.json();
      
      if (data.success) {
        this.updateKPIs(data.kpis);
        this.updateCharts(data.charts);
        this.updateInsights(data.insights);
        this.dataCache.set(timeframe, data);
      }
    } catch (error) {
      console.error('Failed to load analytics data:', error);
      this.showError('Failed to load analytics data');
    }
  }

  updateKPIs(kpis) {
    Object.entries(kpis).forEach(([key, kpi]) => {
      const widget = document.getElementById(`${key}-kpi`);
      if (widget) {
        const valueElement = widget.querySelector('.value');
        const trendElement = widget.querySelector('.trend-indicator');
        
        if (valueElement) {
          this.animateKPIValue(valueElement, kpi.value, kpi.format);
        }
        
        if (trendElement) {
          trendElement.textContent = `${kpi.trend > 0 ? '+' : ''}${kpi.trend}%`;
          trendElement.className = `trend-indicator ${kpi.trend >= 0 ? 'positive' : 'negative'}`;
        }
      }
    });
  }

  animateKPIValue(element, newValue, format = 'number') {
    const currentValue = parseFloat(element.textContent.replace(/[^0-9.-]/g, '')) || 0;
    const duration = 1000;
    const startTime = performance.now();

    const animate = (currentTime) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      const easedProgress = this.easeOutCubic(progress);
      const interpolatedValue = currentValue + (newValue - currentValue) * easedProgress;
      
      element.textContent = this.formatKPIValue(interpolatedValue, format);
      
      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };
    
    requestAnimationFrame(animate);
  }

  formatKPIValue(value, format) {
    switch (format) {
      case 'currency':
        return `$${value.toFixed(2)}`;
      case 'percentage':
        return `${value.toFixed(1)}%`;
      case 'decimal':
        return value.toFixed(1);
      default:
        return Math.round(value).toString();
    }
  }

  updateCharts(chartData) {
    Object.entries(chartData).forEach(([chartName, data]) => {
      const chart = this.charts.get(chartName);
      if (chart) {
        chart.data = data;
        chart.update('active');
      }
    });
  }

  updateInsights(insights) {
    const insightsContent = document.getElementById('insights-content');
    if (!insightsContent || !insights) return;

    insightsContent.innerHTML = insights.map(insight => `
      <div class="insight-item ${insight.priority}">
        <div class="insight-icon">
          <i class="fas ${insight.icon}"></i>
        </div>
        <div class="insight-content">
          <h4>${insight.title}</h4>
          <p>${insight.description}</p>
          ${insight.action ? `<button class="insight-action" data-action="${insight.action}">${insight.actionText}</button>` : ''}
        </div>
      </div>
    `).join('');
  }

  startRealTimeUpdates() {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
    }

    this.refreshInterval = setInterval(() => {
      if (this.isRealTimeEnabled) {
        this.loadAnalyticsData();
      }
    }, 30000); // Update every 30 seconds
  }

  toggleRealTime() {
    this.isRealTimeEnabled = !this.isRealTimeEnabled;
    const toggle = document.getElementById('toggle-realtime');
    
    if (toggle) {
      toggle.classList.toggle('active', this.isRealTimeEnabled);
      toggle.innerHTML = this.isRealTimeEnabled ? 
        '<i class="fas fa-broadcast-tower"></i> Live' : 
        '<i class="fas fa-pause"></i> Paused';
    }

    if (this.isRealTimeEnabled) {
      this.startRealTimeUpdates();
    } else {
      clearInterval(this.refreshInterval);
    }
  }

  easeOutCubic(t) {
    return 1 - Math.pow(1 - t, 3);
  }

  setupExportFunctionality() {
    const exportBtn = document.getElementById('export-analytics');
    if (exportBtn) {
      exportBtn.addEventListener('click', () => {
        this.exportAnalytics();
      });
    }
  }

  async exportAnalytics() {
    try {
      const timeframe = document.getElementById('analytics-timeframe').value;
      const response = await fetch(`/api/analytics/export?timeframe=${timeframe}`, {
        method: 'GET',
        headers: {
          'X-Requested-With': 'XMLHttpRequest'
        }
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `talaria-analytics-${timeframe}-${new Date().toISOString().split('T')[0]}.xlsx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Export failed:', error);
      this.showError('Failed to export analytics data');
    }
  }

  showError(message) {
    if (typeof Swal !== 'undefined') {
      Swal.fire({
        title: 'Error',
        text: message,
        icon: 'error',
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000
      });
    }
  }
}

// Initialize analytics dashboard
document.addEventListener('DOMContentLoaded', () => {
  if (window.location.pathname === '/' || window.location.pathname === '/dashboard') {
    window.analyticsDashboard = new AnalyticsDashboard();
    
    // Load initial data
    window.analyticsDashboard.loadAnalyticsData();
  }
});

// Export for global access
window.AnalyticsDashboard = AnalyticsDashboard;

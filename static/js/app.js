document.addEventListener("DOMContentLoaded", function () {
  // Initial form handling
  const initialForm = document.getElementById("initialForm");
  const finalForm = document.getElementById("finalForm");

  if (initialForm) {
    initialForm.addEventListener("submit", handleInitialFormSubmit);
  }

  if (finalForm) {
    finalForm.addEventListener("submit", handleFinalFormSubmit);
  } else {
    // Only log error if we're on a page that should have the final form
    const currentPath = window.location.pathname;
    if (currentPath.includes("print_labels")) {
      console.warn(
        "Element with id 'finalForm' not found on print_labels page"
      );
    }
  }
});

function handleInitialFormSubmit(e) {
  e.preventDefault();
  const formData = new FormData(this);
  const waferPairsInput = document.getElementById("wafer-pairs-input");

  if (waferPairsInput) {
    formData.append("wafer_pairs", waferPairsInput.value);
  }

  submitFormData("/print_labels", formData);
}

function handleFinalFormSubmit(e) {
  e.preventDefault();
  const formData = new FormData(this);
  const slotInputs = document.querySelectorAll('input[name="new_slot_ids[]"]');

  if (slotInputs.length > 0) {
    const newSlotIds = Array.from(slotInputs).map((input) => input.value);
    formData.append("new_slot_ids", JSON.stringify(newSlotIds));
  }

  submitFormData(this.action, formData, true);
}

function submitFormData(url, formData, isFinalForm = false) {
  // Show loading state
  Swal.fire({
    title: "Processing...",
    text: "Please wait...",
    allowOutsideClick: false,
    allowEscapeKey: false,
    showConfirmButton: false,
    didOpen: () => {
      Swal.showLoading();
    },
  });

  fetch(url, {
    method: "POST",
    body: formData,
  })
    .then((response) => {
      if (!response.ok) {
        return response.text().then((text) => {
          throw new Error(text);
        });
      }
      return response.json();
    })
    .then((data) => {
      if (data.status === "success") {
        if (isFinalForm) {
          window.location.href = "/download_files";
        } else {
          window.location.href = "/print_labels";
        }
      } else {
        throw new Error(data.message || "An unknown error occurred");
      }
    })
    .catch((error) => {
      console.error("Error:", error);
      Swal.fire({
        title: "Error!",
        text:
          error.message ||
          "An unexpected error occurred. Please try again later.",
        icon: "error",
        customClass: { container: "my-swal" },
      });
    });
}

function printLabel() {
  const printerIpInput = document.getElementById("printer-ip");
  const copiesInput = document.getElementById("copies");

  if (!printerIpInput || !printerIpInput.value) {
    Swal.fire({
      title: "Error",
      text: "Please enter a valid printer IP address",
      icon: "error",
    });
    return;
  }

  const data = {
    printer_ip: printerIpInput.value,
    copy_number: copiesInput ? parseInt(copiesInput.value) || 1 : 1,
  };

  fetch("/print_to_honeywell", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-CSRFToken": document.querySelector('meta[name="csrf-token"]')?.content,
    },
    body: JSON.stringify(data),
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        Swal.fire({
          title: "Success",
          text: "Label sent to printer successfully",
          icon: "success",
        });
      } else {
        throw new Error(data.message || "Failed to print label");
      }
    })
    .catch((error) => {
      Swal.fire({
        title: "Error",
        text: error.message || "An error occurred while printing",
        icon: "error",
      });
    });
}

function testPrinterConnection() {
  const printerIpInput = document.getElementById("printer-ip");
  if (!printerIpInput || !printerIpInput.value) {
    Swal.fire({
      title: "Error",
      text: "Please enter a printer IP address",
      icon: "error",
    });
    return;
  }

  fetch("/test_printer_connection", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-CSRFToken": document.querySelector('meta[name="csrf-token"]')?.content,
    },
    body: JSON.stringify({ printer_ip: printerIpInput.value }),
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        Swal.fire({
          title: "Success",
          text: "Printer is connected and accessible",
          icon: "success",
        });
      } else {
        throw new Error(data.message);
      }
    })
    .catch((error) => {
      Swal.fire({
        title: "Error",
        text: error.message || "Failed to connect to printer",
        icon: "error",
      });
    });
}

// Export functions to global scope
//window.previewLabels = previewLabels;
//window.closePreviewModal = closePreviewModal;
window.printLabel = printLabel;
window.testPrinterConnection = testPrinterConnection;

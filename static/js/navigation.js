/**
 * Creates navigation buttons for page navigation
 * @param {string} prevUrl - URL for the previous page button
 * @param {string} nextUrl - URL for the next page button
 * @param {string} prevText - Text for the previous button
 * @param {string} nextText - Text for the next button
 * @returns {HTMLElement} - Container with navigation buttons
 */
function createNavigationButtons(
  prevUrl,
  nextUrl,
  prevText = "Previous",
  nextText = "Next"
) {
  const container = document.createElement("div");
  container.className = "flex justify-between mt-6";

  if (prevUrl && prevUrl !== "None") {
    const prevButton = document.createElement("a");
    prevButton.href = prevUrl;
    prevButton.className =
      "px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300";
    prevButton.textContent = prevText || "Previous";
    container.appendChild(prevButton);
  }

  if (nextUrl && nextUrl !== "None") {
    const nextButton = document.createElement("a");
    nextButton.href = nextUrl;
    nextButton.className =
      "px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 ml-auto";
    nextButton.textContent = nextText || "Next";
    container.appendChild(nextButton);
  }

  return container;
}

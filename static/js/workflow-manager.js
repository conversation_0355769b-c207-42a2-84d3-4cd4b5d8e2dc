/**
 * Workflow Manager - UI Management for Workflow Automation
 */

class WorkflowManager {
  constructor(engine) {
    this.engine = engine;
    this.currentTab = 'workflows';
    this.workflowTemplates = [];
    
    this.init();
  }

  init() {
    this.setupEventListeners();
    this.loadWorkflowTemplates();
    this.updateDashboard();
    this.loadWorkflows();
    console.log('🎛️ Workflow Manager initialized');
  }

  /**
   * Load workflow templates
   */
  loadWorkflowTemplates() {
    try {
      // Load templates from the global WORKFLOW_TEMPLATES if available
      if (typeof WORKFLOW_TEMPLATES !== 'undefined') {
        this.workflowTemplates = WORKFLOW_TEMPLATES;
        console.log(`📋 Loaded ${this.workflowTemplates.length} workflow templates`);
      } else {
        console.warn('⚠️ WORKFLOW_TEMPLATES not found, using empty array');
        this.workflowTemplates = [];
      }
    } catch (error) {
      console.error('❌ Failed to load workflow templates:', error);
      this.workflowTemplates = [];
    }
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Tab switching
    document.querySelectorAll('.workflow-tab').forEach(tab => {
      tab.addEventListener('click', (e) => {
        this.switchTab(e.target.dataset.tab);
      });
    });

    // Tutorial button
    const tutorialBtn = document.getElementById('tutorial-btn');
    if (tutorialBtn) {
      tutorialBtn.addEventListener('click', () => this.startTutorial());
    }

    // Create workflow button
    const createBtn = document.getElementById('create-workflow-btn');
    if (createBtn) {
      createBtn.addEventListener('click', () => this.createWorkflow());
    }

    // Automation toggle
    const toggleBtn = document.getElementById('automation-toggle');
    if (toggleBtn) {
      toggleBtn.addEventListener('click', () => this.toggleAutomation());
    }

    // Search and filter
    const searchInput = document.getElementById('workflow-search');
    if (searchInput) {
      searchInput.addEventListener('input', () => this.filterWorkflows());
    }

    const filterSelect = document.getElementById('workflow-filter');
    if (filterSelect) {
      filterSelect.addEventListener('change', () => this.filterWorkflows());
    }

    // Log filter
    const logFilter = document.getElementById('log-filter');
    if (logFilter) {
      logFilter.addEventListener('change', () => this.filterExecutionLog());
    }

    // Clear log button
    const clearLogBtn = document.getElementById('clear-log-btn');
    if (clearLogBtn) {
      clearLogBtn.addEventListener('click', () => this.clearExecutionLog());
    }
  }

  /**
   * Switch between tabs
   */
  switchTab(tabName) {
    // Update tab buttons
    document.querySelectorAll('.workflow-tab').forEach(tab => {
      tab.classList.remove('active', 'border-blue-500', 'text-blue-600', 'dark:text-blue-400');
      tab.classList.add('border-transparent', 'text-gray-500', 'dark:text-gray-400');
    });

    const activeTab = document.querySelector(`[data-tab="${tabName}"]`);
    if (activeTab) {
      activeTab.classList.add('active', 'border-blue-500', 'text-blue-600', 'dark:text-blue-400');
      activeTab.classList.remove('border-transparent', 'text-gray-500', 'dark:text-gray-400');
    }

    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => {
      content.classList.add('hidden');
    });

    const activeContent = document.getElementById(`${tabName}-tab`);
    if (activeContent) {
      activeContent.classList.remove('hidden');
    }

    this.currentTab = tabName;

    // Load tab-specific content
    switch (tabName) {
      case 'workflows':
        this.loadWorkflows();
        break;
      case 'templates':
        this.loadTemplates();
        break;
      case 'execution-log':
        this.loadExecutionLog();
        break;
      case 'settings':
        this.loadSettings();
        break;
    }
  }

  /**
   * Update dashboard statistics
   */
  updateDashboard() {
    const workflows = this.engine.getWorkflows();
    const stats = this.engine.getExecutionStats();

    // Update statistics
    const totalWorkflows = document.getElementById('total-workflows');
    if (totalWorkflows) totalWorkflows.textContent = workflows.length;

    const activeWorkflows = document.getElementById('active-workflows');
    if (activeWorkflows) {
      activeWorkflows.textContent = workflows.filter(w => w.enabled).length;
    }

    const totalExecutions = document.getElementById('total-executions');
    if (totalExecutions) totalExecutions.textContent = stats.total;

    const successRate = document.getElementById('success-rate');
    if (successRate) successRate.textContent = stats.successRate;

    // Update automation status
    const statusElement = document.getElementById('automation-status');
    const toggleBtn = document.getElementById('automation-toggle');
    if (statusElement && toggleBtn) {
      if (this.engine.isEnabled) {
        statusElement.textContent = 'Enabled';
        toggleBtn.classList.remove('bg-red-600', 'hover:bg-red-700');
        toggleBtn.classList.add('bg-green-600', 'hover:bg-green-700');
      } else {
        statusElement.textContent = 'Disabled';
        toggleBtn.classList.remove('bg-green-600', 'hover:bg-green-700');
        toggleBtn.classList.add('bg-red-600', 'hover:bg-red-700');
      }
    }
  }

  /**
   * Load and display workflows
   */
  loadWorkflows() {
    const container = document.getElementById('workflows-container');
    const emptyState = document.getElementById('workflows-empty');
    const quickStartGuide = document.getElementById('quick-start-guide');
    
    if (!container) return;

    const workflows = this.engine.getWorkflows();

    if (workflows.length === 0) {
      container.classList.add('hidden');
      if (emptyState) emptyState.classList.remove('hidden');
      if (quickStartGuide) quickStartGuide.classList.remove('hidden');
      return;
    }

    container.classList.remove('hidden');
    if (emptyState) emptyState.classList.add('hidden');
    if (quickStartGuide) quickStartGuide.classList.add('hidden');

    container.innerHTML = workflows.map(workflow => this.generateWorkflowCard(workflow)).join('');
  }

  /**
   * Generate workflow card HTML
   */
  generateWorkflowCard(workflow) {
    const stats = this.engine.getExecutionStats(workflow.id);
    
    return `
      <div class="workflow-card" data-workflow-id="${workflow.id}">
        <div class="flex justify-between items-start mb-4">
          <div class="flex-1">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-1">
              ${workflow.name}
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              ${workflow.description || 'No description provided'}
            </p>
          </div>
          
          <div class="flex items-center space-x-2">
            <span class="workflow-status ${workflow.enabled ? 'enabled' : 'disabled'}">
              <i class="fas fa-${workflow.enabled ? 'play' : 'pause'} mr-1"></i>
              ${workflow.enabled ? 'Enabled' : 'Disabled'}
            </span>
            
            <div class="relative">
              <button class="workflow-menu-btn p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                      onclick="this.nextElementSibling.classList.toggle('hidden')">
                <i class="fas fa-ellipsis-v"></i>
              </button>
              
              <div class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 hidden z-10">
                <button onclick="window.safeWorkflowCall('editWorkflow', '${workflow.id}')"
                        class="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                  <i class="fas fa-edit mr-2"></i>Edit
                </button>
                <button onclick="window.safeWorkflowCall('duplicateWorkflow', '${workflow.id}')"
                        class="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                  <i class="fas fa-copy mr-2"></i>Duplicate
                </button>
                <button onclick="window.safeWorkflowCall('toggleWorkflow', '${workflow.id}')"
                        class="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                  <i class="fas fa-${workflow.enabled ? 'pause' : 'play'} mr-2"></i>
                  ${workflow.enabled ? 'Disable' : 'Enable'}
                </button>
                <button onclick="window.safeWorkflowCall('testWorkflow', '${workflow.id}')"
                        class="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                  <i class="fas fa-play-circle mr-2"></i>Test Run
                </button>
                <hr class="my-1 border-gray-200 dark:border-gray-600">
                <button onclick="window.safeWorkflowCall('deleteWorkflow', '${workflow.id}')"
                        class="w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900">
                  <i class="fas fa-trash mr-2"></i>Delete
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Triggers -->
        <div class="mb-4">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Triggers</h4>
          <div class="flex flex-wrap">
            ${workflow.triggers.map(trigger => `
              <span class="trigger-badge">
                <i class="fas fa-bolt mr-1"></i>
                ${trigger.type}
              </span>
            `).join('')}
          </div>
        </div>
        
        <!-- Actions -->
        <div class="mb-4">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Actions</h4>
          <div class="flex flex-wrap">
            ${workflow.actions.map(action => `
              <span class="action-badge">
                <i class="fas fa-cog mr-1"></i>
                ${action.type}
              </span>
            `).join('')}
          </div>
        </div>
        
        <!-- Statistics -->
        <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
          <div class="grid grid-cols-3 gap-4 text-center">
            <div>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">${stats.total}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">Executions</p>
            </div>
            <div>
              <p class="text-2xl font-bold text-green-600 dark:text-green-400">${stats.successful}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">Success</p>
            </div>
            <div>
              <p class="text-2xl font-bold ${stats.failed > 0 ? 'text-red-600 dark:text-red-400' : 'text-gray-400'}">${stats.failed}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">Failed</p>
            </div>
          </div>
          
          ${workflow.lastExecuted ? `
            <p class="text-xs text-gray-500 dark:text-gray-400 text-center mt-2">
              Last run: ${new Date(workflow.lastExecuted).toLocaleString()}
            </p>
          ` : ''}
        </div>
      </div>
    `;
  }

  /**
   * Create new workflow
   */
  createWorkflow() {
    if (typeof Swal !== 'undefined') {
      Swal.fire({
        title: 'Create New Workflow',
        html: this.generateWorkflowBuilderHTML(),
        width: '90%',
        showCancelButton: true,
        confirmButtonText: 'Create Workflow',
        cancelButtonText: 'Cancel',
        didOpen: () => {
          this.initializeWorkflowBuilder();
        },
        preConfirm: () => {
          return this.saveNewWorkflow();
        }
      }).then((result) => {
        if (result.isConfirmed) {
          this.loadWorkflows();
          this.updateDashboard();
          
          Swal.fire({
            title: 'Success!',
            text: 'Workflow created successfully',
            icon: 'success',
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000
          });
        }
      });
    }
  }

  /**
   * Generate workflow builder HTML
   */
  generateWorkflowBuilderHTML() {
    return `
      <div class="workflow-builder max-h-96 overflow-y-auto">
        <div class="space-y-6">
          <!-- Step 1: Basic Information -->
          <div class="border border-blue-200 dark:border-blue-700 rounded-lg p-4">
            <h3 class="text-lg font-semibold text-blue-700 dark:text-blue-300 mb-3 flex items-center">
              <span class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm mr-2">1</span>
              Basic Information
            </h3>
            <div class="space-y-3">
              <div>
                <label class="block text-sm font-medium mb-2">Workflow Name</label>
                <input type="text" id="workflow-name" class="w-full px-3 py-2 border rounded-lg"
                       placeholder="e.g., Low Stock Alert for Wafers">
              </div>
              <div>
                <label class="block text-sm font-medium mb-2">Description</label>
                <textarea id="workflow-description" class="w-full px-3 py-2 border rounded-lg" rows="2"
                          placeholder="What will this workflow do for your wafer inventory?"></textarea>
              </div>
            </div>
          </div>

          <!-- Step 2: When should it trigger? -->
          <div class="border border-purple-200 dark:border-purple-700 rounded-lg p-4">
            <h3 class="text-lg font-semibold text-purple-700 dark:text-purple-300 mb-3 flex items-center">
              <span class="w-6 h-6 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm mr-2">2</span>
              When should this run?
            </h3>
            <div>
              <label class="block text-sm font-medium mb-2">Trigger Event</label>
              <select id="workflow-trigger" class="w-full px-3 py-2 border rounded-lg">
                <option value="">Choose when to trigger this workflow...</option>
                <optgroup label="Inventory Events">
                  <option value="inventory.updated">When a wafer is updated</option>
                  <option value="inventory.added">When a new wafer is added</option>
                  <option value="location.changed">When wafer location changes</option>
                  <option value="wafer.before_add">Before adding a wafer (validation)</option>
                </optgroup>
                <optgroup label="Scheduled Events">
                  <option value="time.daily">Daily at a specific time</option>
                  <option value="time.hourly">Every hour</option>
                  <option value="time.weekly">Weekly</option>
                </optgroup>
                <optgroup label="System Events">
                  <option value="shipment.created">When a shipment is created</option>
                  <option value="quality.check_required">When quality check is needed</option>
                </optgroup>
              </select>
              <p class="text-xs text-gray-500 mt-1">This determines when your workflow will run automatically</p>
            </div>
          </div>

          <!-- Step 3: What conditions? -->
          <div class="border border-green-200 dark:border-green-700 rounded-lg p-4">
            <h3 class="text-lg font-semibold text-green-700 dark:text-green-300 mb-3 flex items-center">
              <span class="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-sm mr-2">3</span>
              What conditions? (Optional)
            </h3>
            <div id="conditions-section">
              <div class="mb-3">
                <label class="block text-sm font-medium mb-2">Add a condition</label>
                <select id="condition-type" class="w-full px-3 py-2 border rounded-lg">
                  <option value="">No conditions - always run</option>
                  <option value="quantity_low">When quantity is low (less than X)</option>
                  <option value="status_equals">When status equals specific value</option>
                  <option value="location_equals">When in specific location</option>
                  <option value="time_range">During specific time range</option>
                </select>
              </div>
              
              <!-- Condition parameters (shown based on selection) -->
              <div id="condition-params" class="hidden space-y-2">
                <div id="quantity-param" class="hidden">
                  <label class="block text-sm font-medium mb-1">Minimum quantity threshold</label>
                  <input type="number" id="quantity-threshold" class="w-full px-3 py-2 border rounded-lg" placeholder="10">
                </div>
                <div id="status-param" class="hidden">
                  <label class="block text-sm font-medium mb-1">Status value</label>
                  <select id="status-value" class="w-full px-3 py-2 border rounded-lg">
                    <option value="quality_hold">Quality Hold</option>
                    <option value="available">Available</option>
                    <option value="shipped">Shipped</option>
                    <option value="reserved">Reserved</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          <!-- Step 4: What action? -->
          <div class="border border-orange-200 dark:border-orange-700 rounded-lg p-4">
            <h3 class="text-lg font-semibold text-orange-700 dark:text-orange-300 mb-3 flex items-center">
              <span class="w-6 h-6 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm mr-2">4</span>
              What should happen?
            </h3>
            <div>
              <label class="block text-sm font-medium mb-2">Action to perform</label>
              <select id="workflow-action" class="w-full px-3 py-2 border rounded-lg">
                <option value="">Choose what to do...</option>
                <optgroup label="Notifications">
                  <option value="email.send">Send email notification</option>
                  <option value="notification.show">Show dashboard notification</option>
                </optgroup>
                <optgroup label="Integrations">
                  <option value="odoo.create_record">Create record in ODOO</option>
                  <option value="asana.create_task">Create task in Asana</option>
                  <option value="icarium.sync">Sync with Icarium</option>
                </optgroup>
                <optgroup label="System Actions">
                  <option value="system.log">Log to system</option>
                  <option value="api.call">Call external API</option>
                </optgroup>
              </select>
            </div>

            <!-- Email Parameters (shown when email action selected) -->
            <div id="email-params" class="hidden mt-4 space-y-3 bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
              <h4 class="font-medium text-gray-900 dark:text-white">Email Settings</h4>
              <div>
                <label class="block text-sm font-medium mb-1">Send to</label>
                <input type="email" id="email-to" class="w-full px-3 py-2 border rounded-lg"
                       placeholder="<EMAIL>">
              </div>
              <div>
                <label class="block text-sm font-medium mb-1">Subject</label>
                <input type="text" id="email-subject" class="w-full px-3 py-2 border rounded-lg"
                       placeholder="Wafer Inventory Alert">
              </div>
              <div>
                <label class="block text-sm font-medium mb-1">Message</label>
                <textarea id="email-body" class="w-full px-3 py-2 border rounded-lg" rows="3"
                          placeholder="Alert: Wafer {{wafer_id}} quantity is {{quantity}}"></textarea>
              </div>
            </div>

            <!-- Asana Parameters -->
            <div id="asana-params" class="hidden mt-4 space-y-3 bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
              <h4 class="font-medium text-gray-900 dark:text-white">Asana Task Settings</h4>
              <div>
                <label class="block text-sm font-medium mb-1">Task title</label>
                <input type="text" id="asana-title" class="w-full px-3 py-2 border rounded-lg"
                       placeholder="Quality check required for {{wafer_id}}">
              </div>
              <div>
                <label class="block text-sm font-medium mb-1">Project</label>
                <input type="text" id="asana-project" class="w-full px-3 py-2 border rounded-lg"
                       placeholder="Inventory Management">
              </div>
            </div>
          </div>

          <!-- Step 5: Final settings -->
          <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <h3 class="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center">
              <span class="w-6 h-6 bg-gray-500 text-white rounded-full flex items-center justify-center text-sm mr-2">5</span>
              Final Settings
            </h3>
            <div class="flex items-center">
              <input type="checkbox" id="workflow-enabled" checked class="mr-2">
              <label for="workflow-enabled" class="text-sm">Enable this workflow immediately</label>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Initialize workflow builder
   */
  initializeWorkflowBuilder() {
    // Show/hide action parameters based on selection
    const actionSelect = document.getElementById('workflow-action');
    const emailParams = document.getElementById('email-params');
    const asanaParams = document.getElementById('asana-params');

    if (actionSelect) {
      actionSelect.addEventListener('change', (e) => {
        // Hide all parameter sections
        const paramSections = [emailParams, asanaParams];
        paramSections.forEach(section => {
          if (section) section.classList.add('hidden');
        });

        // Show relevant parameter section
        switch (e.target.value) {
          case 'email.send':
            if (emailParams) emailParams.classList.remove('hidden');
            break;
          case 'asana.create_task':
            if (asanaParams) asanaParams.classList.remove('hidden');
            break;
        }
      });
    }

    // Show/hide condition parameters based on selection
    const conditionSelect = document.getElementById('condition-type');
    const conditionParams = document.getElementById('condition-params');
    const quantityParam = document.getElementById('quantity-param');
    const statusParam = document.getElementById('status-param');

    if (conditionSelect) {
      conditionSelect.addEventListener('change', (e) => {
        // Hide all condition parameters
        if (conditionParams) conditionParams.classList.add('hidden');
        if (quantityParam) quantityParam.classList.add('hidden');
        if (statusParam) statusParam.classList.add('hidden');

        // Show relevant condition parameters
        if (e.target.value) {
          if (conditionParams) conditionParams.classList.remove('hidden');
          
          switch (e.target.value) {
            case 'quantity_low':
              if (quantityParam) quantityParam.classList.remove('hidden');
              break;
            case 'status_equals':
              if (statusParam) statusParam.classList.remove('hidden');
              break;
          }
        }
      });
    }
  }

  /**
   * Save new workflow
   */
  saveNewWorkflow() {
    const name = document.getElementById('workflow-name')?.value;
    const description = document.getElementById('workflow-description')?.value;
    const triggerType = document.getElementById('workflow-trigger')?.value;
    const actionType = document.getElementById('workflow-action')?.value;
    const enabled = document.getElementById('workflow-enabled')?.checked;

    if (!name || !triggerType || !actionType) {
      Swal.showValidationMessage('Please fill in all required fields');
      return false;
    }

    const workflow = {
      id: this.engine.generateId(),
      name: name,
      description: description || '',
      enabled: enabled,
      triggers: [{
        type: triggerType,
        conditions: null
      }],
      actions: [],
      variables: {}
    };

    // Build action based on type
    if (actionType === 'email.send') {
      const emailTo = document.getElementById('email-to')?.value;
      const emailSubject = document.getElementById('email-subject')?.value;
      const emailBody = document.getElementById('email-body')?.value;

      if (!emailTo || !emailSubject) {
        Swal.showValidationMessage('Please fill in email parameters');
        return false;
      }

      workflow.actions.push({
        type: 'email.send',
        parameters: {
          to: emailTo,
          subject: emailSubject,
          body: emailBody || 'Automated notification from Talaria Dashboard'
        }
      });
    } else if (actionType === 'notification.show') {
      workflow.actions.push({
        type: 'notification.show',
        parameters: {
          title: 'Workflow Notification',
          message: `Workflow "${name}" has been triggered`,
          type: 'info',
          timer: 5000
        }
      });
    } else {
      // Generic action for other types
      workflow.actions.push({
        type: actionType,
        parameters: {}
      });
    }

    try {
      this.engine.registerWorkflow(workflow);
      return true;
    } catch (error) {
      Swal.showValidationMessage(`Error creating workflow: ${error.message}`);
      return false;
    }
  }

  /**
   * Edit existing workflow
   */
  editWorkflow(workflowId) {
    console.log('🔧 Edit workflow called for ID:', workflowId);

    const workflow = this.engine.getWorkflow(workflowId);
    if (!workflow) {
      console.error('❌ Workflow not found:', workflowId);
      Swal.fire('Error', 'Workflow not found', 'error');
      return;
    }

    console.log('✅ Found workflow:', workflow.name);

    // For now, show a simple edit dialog
    Swal.fire({
      title: 'Edit Workflow',
      text: `Editing "${workflow.name}". Advanced workflow editing will be available in the next update.`,
      icon: 'info'
    });
  }

  /**
   * Duplicate workflow
   */
  duplicateWorkflow(workflowId) {
    const workflow = this.engine.getWorkflow(workflowId);
    if (!workflow) {
      Swal.fire('Error', 'Workflow not found', 'error');
      return;
    }

    const duplicated = {
      ...workflow,
      id: this.engine.generateId(),
      name: `${workflow.name} (Copy)`,
      enabled: false
    };

    try {
      this.engine.registerWorkflow(duplicated);
      this.loadWorkflows();
      this.updateDashboard();

      Swal.fire({
        title: 'Success!',
        text: 'Workflow duplicated successfully',
        icon: 'success',
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000
      });
    } catch (error) {
      Swal.fire('Error', `Failed to duplicate workflow: ${error.message}`, 'error');
    }
  }

  /**
   * Toggle workflow enabled/disabled
   */
  toggleWorkflow(workflowId) {
    const workflow = this.engine.getWorkflow(workflowId);
    if (!workflow) {
      Swal.fire('Error', 'Workflow not found', 'error');
      return;
    }

    try {
      this.engine.updateWorkflow(workflowId, { enabled: !workflow.enabled });
      this.loadWorkflows();
      this.updateDashboard();

      const status = workflow.enabled ? 'disabled' : 'enabled';
      Swal.fire({
        title: 'Success!',
        text: `Workflow ${status} successfully`,
        icon: 'success',
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000
      });
    } catch (error) {
      Swal.fire('Error', `Failed to toggle workflow: ${error.message}`, 'error');
    }
  }

  /**
   * Test workflow execution
   */
  async testWorkflow(workflowId) {
    console.log('🧪 Test workflow called for ID:', workflowId);

    try {
      const workflow = this.engine.getWorkflow(workflowId);
      if (!workflow) {
        throw new Error('Workflow not found');
      }

      console.log('✅ Testing workflow:', workflow.name);

      await this.engine.triggerWorkflow(workflowId, {
        test: true,
        timestamp: new Date().toISOString()
      });

      Swal.fire({
        title: 'Test Successful!',
        text: `Workflow "${workflow.name}" executed successfully`,
        icon: 'success',
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000
      });
    } catch (error) {
      console.error('❌ Test workflow failed:', error);
      Swal.fire('Error', `Test failed: ${error.message}`, 'error');
    }
  }

  /**
   * Delete workflow
   */
  deleteWorkflow(workflowId) {
    const workflow = this.engine.getWorkflow(workflowId);
    if (!workflow) {
      Swal.fire('Error', 'Workflow not found', 'error');
      return;
    }

    Swal.fire({
      title: 'Delete Workflow?',
      text: `Are you sure you want to delete "${workflow.name}"? This action cannot be undone.`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (result.isConfirmed) {
        try {
          this.engine.deleteWorkflow(workflowId);
          this.loadWorkflows();
          this.updateDashboard();

          Swal.fire({
            title: 'Deleted!',
            text: 'Workflow has been deleted.',
            icon: 'success',
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000
          });
        } catch (error) {
          Swal.fire('Error', `Failed to delete workflow: ${error.message}`, 'error');
        }
      }
    });
  }

  /**
   * Toggle automation on/off
   */
  toggleAutomation() {
    const isEnabled = this.engine.isEnabled;
    this.engine.setEnabled(!isEnabled);
    this.updateDashboard();

    const status = isEnabled ? 'disabled' : 'enabled';
    Swal.fire({
      title: `Automation ${status.charAt(0).toUpperCase() + status.slice(1)}!`,
      text: `Workflow automation has been ${status}`,
      icon: isEnabled ? 'warning' : 'success',
      toast: true,
      position: 'top-end',
      showConfirmButton: false,
      timer: 3000
    });
  }

  /**
   * Filter workflows
   */
  filterWorkflows() {
    // Implementation for search and filter functionality
    console.log('Filtering workflows...');
    this.loadWorkflows();
  }

  /**
   * Load templates
   */
  loadTemplates() {
    console.log('Loading workflow templates...');
    const container = document.getElementById('templates-container');
    const popularContainer = document.getElementById('popular-templates');
    
    if (!container) return;

    // Get popular templates specifically for wafer management
    const popularTemplates = this.workflowTemplates.filter(template => 
      ['low-stock-alert', 'smart-wafer-validation', 'daily-new-wafer-notifications', 'odoo-inventory-sync'].includes(template.id)
    );

    // Populate popular templates
    if (popularContainer) {
      popularContainer.innerHTML = popularTemplates.map(template => this.generateTemplateCard(template, true)).join('');
    }

    // Populate all templates
    container.innerHTML = this.workflowTemplates.map(template => this.generateTemplateCard(template)).join('');
  }

  /**
   * Generate template card HTML
   */
  generateTemplateCard(template, isPopular = false) {
    const badgeClass = isPopular ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    const badge = isPopular ? '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 mb-2"><i class="fas fa-star mr-1"></i>Popular</span>' : '';
    
    return `
      <div class="workflow-card template-card" data-template-id="${template.id}" data-category="${template.category.toLowerCase().replace(/\s+/g, '-')}">
        <div class="flex justify-between items-start mb-4">
          <div class="flex-1">
            ${badge}
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2 flex items-center">
              <i class="${template.icon} text-${template.color}-500 mr-2"></i>
              ${template.name}
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">
              ${template.description}
            </p>
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${badgeClass}">
              ${template.category}
            </span>
          </div>
        </div>
        
        <!-- Template Preview -->
        <div class="mb-4">
          <div class="space-y-2">
            <div class="flex items-center text-xs">
              <span class="text-gray-500 dark:text-gray-400 w-16">Triggers:</span>
              <div class="flex flex-wrap">
                ${template.triggers.slice(0, 2).map(trigger => `
                  <span class="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded text-xs mr-1">
                    ${trigger.type}
                  </span>
                `).join('')}
                ${template.triggers.length > 2 ? `<span class="text-gray-400 text-xs">+${template.triggers.length - 2} more</span>` : ''}
              </div>
            </div>
            <div class="flex items-center text-xs">
              <span class="text-gray-500 dark:text-gray-400 w-16">Actions:</span>
              <div class="flex flex-wrap">
                ${template.actions.slice(0, 2).map(action => `
                  <span class="inline-flex items-center px-2 py-1 bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 rounded text-xs mr-1">
                    ${action.type}
                  </span>
                `).join('')}
                ${template.actions.length > 2 ? `<span class="text-gray-400 text-xs">+${template.actions.length - 2} more</span>` : ''}
              </div>
            </div>
          </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex space-x-2 pt-4 border-t border-gray-200 dark:border-gray-700">
          <button onclick="window.safeWorkflowCall('previewTemplate', '${template.id}')"
                  class="flex-1 px-3 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg text-sm">
            <i class="fas fa-eye mr-1"></i>Preview
          </button>
          <button onclick="window.safeWorkflowCall('useTemplate', '${template.id}')"
                  class="flex-1 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm">
            <i class="fas fa-plus mr-1"></i>Use Template
          </button>
        </div>
      </div>
    `;
  }

  /**
   * Start interactive tutorial
   */
  startTutorial() {
    if (typeof Swal !== 'undefined') {
      this.tutorialStep = 1;
      this.showTutorialStep();
    } else {
      console.warn('SweetAlert2 not available, showing basic tutorial');
      this.showBasicTutorial();
    }
  }

  /**
   * Show tutorial step
   */
  showTutorialStep() {
    const steps = [
      {
        title: 'Welcome to Workflow Automation! 🤖',
        html: `
          <div class="text-left">
            <p class="mb-4">Workflow automation helps you automate repetitive tasks in your wafer inventory management.</p>
            <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg mb-4">
              <h4 class="font-semibold mb-2">What can you automate?</h4>
              <ul class="list-disc list-inside space-y-1 text-sm">
                <li>Get alerts when wafer stock is low</li>
                <li>Automatically sync data with Icarium</li>
                <li>Send daily inventory reports via email</li>
                <li>Create tasks in Asana for quality checks</li>
                <li>Update records in ODOO system</li>
              </ul>
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-400">Let's walk through how to set up your first workflow!</p>
          </div>
        `,
        confirmButtonText: 'Start Tutorial'
      },
      {
        title: 'Step 1: Understanding Workflows 📋',
        html: `
          <div class="text-left">
            <p class="mb-4">A workflow consists of three main parts:</p>
            <div class="space-y-3">
              <div class="flex items-start space-x-3 p-3 bg-blue-50 dark:bg-blue-900 rounded-lg">
                <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                <div>
                  <h4 class="font-semibold">Trigger</h4>
                  <p class="text-sm text-gray-600 dark:text-gray-400">When should the workflow run? (e.g., when inventory is updated)</p>
                </div>
              </div>
              <div class="flex items-start space-x-3 p-3 bg-purple-50 dark:bg-purple-900 rounded-lg">
                <div class="w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                <div>
                  <h4 class="font-semibold">Conditions</h4>
                  <p class="text-sm text-gray-600 dark:text-gray-400">What criteria must be met? (e.g., quantity less than 10)</p>
                </div>
              </div>
              <div class="flex items-start space-x-3 p-3 bg-green-50 dark:bg-green-900 rounded-lg">
                <div class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                <div>
                  <h4 class="font-semibold">Actions</h4>
                  <p class="text-sm text-gray-600 dark:text-gray-400">What should happen? (e.g., send email alert)</p>
                </div>
              </div>
            </div>
          </div>
        `,
        confirmButtonText: 'Next'
      },
      {
        title: 'Step 2: Using Templates 📝',
        html: `
          <div class="text-left">
            <p class="mb-4">The easiest way to get started is using pre-built templates:</p>
            <div class="bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900 dark:to-orange-900 p-4 rounded-lg mb-4">
              <h4 class="font-semibold mb-2 flex items-center">
                <i class="fas fa-star text-yellow-500 mr-2"></i>
                Popular Templates for Wafer Management
              </h4>
              <div class="grid grid-cols-1 gap-2 text-sm">
                <div class="flex items-center space-x-2">
                  <i class="fas fa-exclamation-triangle text-amber-500"></i>
                  <span><strong>Low Stock Alert:</strong> Get notified when wafer quantities are low</span>
                </div>
                <div class="flex items-center space-x-2">
                  <i class="fas fa-sync-alt text-blue-500"></i>
                  <span><strong>Smart Wafer Validation:</strong> Check Icarium before adding wafers</span>
                </div>
                <div class="flex items-center space-x-2">
                  <i class="fas fa-bell text-orange-500"></i>
                  <span><strong>Daily Wafer Notifications:</strong> Get daily reports of new wafers</span>
                </div>
              </div>
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-400">Click on the "Templates" tab to browse and use these templates.</p>
          </div>
        `,
        confirmButtonText: 'Show Me Templates'
      },
      {
        title: 'Step 3: Your First Workflow 🚀',
        html: `
          <div class="text-left">
            <p class="mb-4">Let's create a simple "Low Stock Alert" workflow:</p>
            <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg mb-4">
              <h4 class="font-semibold mb-3">Example: Low Stock Alert</h4>
              <div class="space-y-2 text-sm">
                <div class="flex items-center space-x-2">
                  <span class="w-16 text-blue-600 font-medium">Trigger:</span>
                  <span>When inventory is updated</span>
                </div>
                <div class="flex items-center space-x-2">
                  <span class="w-16 text-purple-600 font-medium">Condition:</span>
                  <span>If quantity is less than 10</span>
                </div>
                <div class="flex items-center space-x-2">
                  <span class="w-16 text-green-600 font-medium">Action:</span>
                  <span>Send <NAME_EMAIL></span>
                </div>
              </div>
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-400">Ready to try it? We'll take you to the templates now!</p>
          </div>
        `,
        confirmButtonText: 'Go to Templates'
      }
    ];

    const currentStep = steps[this.tutorialStep - 1];
    
    Swal.fire({
      title: currentStep.title,
      html: currentStep.html,
      icon: 'info',
      confirmButtonText: currentStep.confirmButtonText,
      showCancelButton: this.tutorialStep > 1,
      cancelButtonText: 'Previous',
      allowOutsideClick: false,
      customClass: {
        popup: 'tutorial-popup'
      }
    }).then((result) => {
      if (result.isConfirmed) {
        this.tutorialStep++;
        if (this.tutorialStep <= steps.length) {
          this.showTutorialStep();
        } else {
          // Tutorial complete, switch to templates tab
          this.switchTab('templates');
          this.showTemplates();
        }
      } else if (result.dismiss === Swal.DismissReason.cancel && this.tutorialStep > 1) {
        this.tutorialStep--;
        this.showTutorialStep();
      }
    });
  }

  /**
   * Show templates (called from tutorial or quick start)
   */
  showTemplates() {
    this.switchTab('templates');
    // Highlight popular templates
    setTimeout(() => {
      const popularSection = document.getElementById('popular-templates');
      if (popularSection) {
        popularSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
        popularSection.classList.add('ring-2', 'ring-blue-400', 'ring-opacity-50');
        setTimeout(() => {
          popularSection.classList.remove('ring-2', 'ring-blue-400', 'ring-opacity-50');
        }, 3000);
      }
    }, 300);
  }

  /**
   * Preview a template
   */
  previewTemplate(templateId) {
    const template = this.workflowTemplates.find(t => t.id === templateId);
    if (!template) {
      Swal.fire('Error', 'Template not found', 'error');
      return;
    }

    Swal.fire({
      title: template.name,
      html: `
        <div class="text-left max-h-96 overflow-y-auto">
          <div class="mb-4">
            <div class="flex items-center space-x-2 mb-2">
              <i class="${template.icon} text-${template.color}-500"></i>
              <span class="text-sm bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">${template.category}</span>
            </div>
            <p class="text-gray-600 dark:text-gray-400">${template.description}</p>
          </div>
          
          <div class="space-y-4">
            <div>
              <h4 class="font-semibold mb-2 flex items-center">
                <i class="fas fa-bolt text-blue-500 mr-2"></i>
                Triggers
              </h4>
              <div class="space-y-2">
                ${template.triggers.map(trigger => `
                  <div class="bg-blue-50 dark:bg-blue-900 p-3 rounded-lg">
                    <div class="font-medium">${trigger.type}</div>
                    ${trigger.conditions ? `<div class="text-sm text-gray-600 dark:text-gray-400 mt-1">Conditions: ${JSON.stringify(trigger.conditions, null, 2)}</div>` : ''}
                  </div>
                `).join('')}
              </div>
            </div>
            
            <div>
              <h4 class="font-semibold mb-2 flex items-center">
                <i class="fas fa-cog text-purple-500 mr-2"></i>
                Actions
              </h4>
              <div class="space-y-2">
                ${template.actions.map(action => `
                  <div class="bg-purple-50 dark:bg-purple-900 p-3 rounded-lg">
                    <div class="font-medium">${action.type}</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      ${Object.entries(action.parameters || {}).map(([key, value]) => 
                        `<div><strong>${key}:</strong> ${typeof value === 'string' ? value : JSON.stringify(value)}</div>`
                      ).join('')}
                    </div>
                  </div>
                `).join('')}
              </div>
            </div>
          </div>
        </div>
      `,
      width: '80%',
      showCancelButton: true,
      confirmButtonText: 'Use This Template',
      cancelButtonText: 'Close'
    }).then((result) => {
      if (result.isConfirmed) {
        this.useTemplate(templateId);
      }
    });
  }

  /**
   * Use a template to create a workflow
   */
  useTemplate(templateId) {
    const template = this.workflowTemplates.find(t => t.id === templateId);
    if (!template) {
      Swal.fire('Error', 'Template not found', 'error');
      return;
    }

    // Create workflow from template
    try {
      const workflow = this.createWorkflowFromTemplate(template);
      this.engine.registerWorkflow(workflow);
      
      this.loadWorkflows();
      this.updateDashboard();
      this.switchTab('workflows');

      Swal.fire({
        title: 'Success!',
        text: `Workflow "${workflow.name}" created from template`,
        icon: 'success',
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000
      });
    } catch (error) {
      Swal.fire('Error', `Failed to create workflow: ${error.message}`, 'error');
    }
  }

  /**
   * Create workflow from template
   */
  createWorkflowFromTemplate(template) {
    return {
      id: this.engine.generateId(),
      name: template.name,
      description: template.description,
      enabled: true,
      triggers: JSON.parse(JSON.stringify(template.triggers)),
      actions: JSON.parse(JSON.stringify(template.actions)),
      variables: JSON.parse(JSON.stringify(template.variables || {})),
      createdFrom: template.id,
      createdAt: new Date().toISOString()
    };
  }

  /**
   * Show basic tutorial (fallback)
   */
  showBasicTutorial() {
    alert(`Workflow Automation Tutorial:

1. TRIGGERS: Define when workflows should run (e.g., inventory updated)
2. CONDITIONS: Set criteria that must be met (e.g., quantity < 10)  
3. ACTIONS: Choose what happens (e.g., send email alert)

Popular workflows for wafer management:
• Low Stock Alerts
• Icarium Sync Automation  
• Daily Inventory Reports
• Quality Check Notifications

Click "Templates" tab to get started with pre-built workflows!`);
    
    this.switchTab('templates');
  }

  /**
   * Load execution log
   */
  loadExecutionLog() {
    console.log('Loading execution log...');
    // Implementation for execution log
  }

  /**
   * Filter execution log
   */
  filterExecutionLog() {
    console.log('Filtering execution log...');
    // Implementation for log filtering
  }

  /**
   * Clear execution log
   */
  clearExecutionLog() {
    Swal.fire({
      title: 'Clear Execution Log?',
      text: 'This will remove all execution history. Are you sure?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'Yes, clear it!'
    }).then((result) => {
      if (result.isConfirmed) {
        this.engine.executionHistory = [];
        this.engine.saveExecutionHistory();
        this.loadExecutionLog();

        Swal.fire({
          title: 'Cleared!',
          text: 'Execution log has been cleared.',
          icon: 'success',
          toast: true,
          position: 'top-end',
          showConfirmButton: false,
          timer: 3000
        });
      }
    });
  }

  /**
   * Load settings
   */
  loadSettings() {
    console.log('Loading settings...');
    // Implementation for settings
  }
}

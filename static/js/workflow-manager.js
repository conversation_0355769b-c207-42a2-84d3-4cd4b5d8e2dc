/**
 * Workflow Manager - UI Management for Workflow Automation
 */

class WorkflowManager {
  constructor(engine) {
    this.engine = engine;
    this.currentTab = 'workflows';
    this.workflowTemplates = [];
    
    this.init();
  }

  init() {
    this.setupEventListeners();
    this.loadWorkflowTemplates();
    this.updateDashboard();
    this.loadWorkflows();
    console.log('🎛️ Workflow Manager initialized');
  }

  /**
   * Load workflow templates
   */
  loadWorkflowTemplates() {
    try {
      // Load templates from the global WORKFLOW_TEMPLATES if available
      if (typeof WORKFLOW_TEMPLATES !== 'undefined') {
        this.workflowTemplates = WORKFLOW_TEMPLATES;
        console.log(`📋 Loaded ${this.workflowTemplates.length} workflow templates`);
      } else {
        console.warn('⚠️ WORKFLOW_TEMPLATES not found, using empty array');
        this.workflowTemplates = [];
      }
    } catch (error) {
      console.error('❌ Failed to load workflow templates:', error);
      this.workflowTemplates = [];
    }
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Tab switching
    document.querySelectorAll('.workflow-tab').forEach(tab => {
      tab.addEventListener('click', (e) => {
        this.switchTab(e.target.dataset.tab);
      });
    });

    // Create workflow button
    const createBtn = document.getElementById('create-workflow-btn');
    if (createBtn) {
      createBtn.addEventListener('click', () => this.createWorkflow());
    }

    // Automation toggle
    const toggleBtn = document.getElementById('automation-toggle');
    if (toggleBtn) {
      toggleBtn.addEventListener('click', () => this.toggleAutomation());
    }

    // Search and filter
    const searchInput = document.getElementById('workflow-search');
    if (searchInput) {
      searchInput.addEventListener('input', () => this.filterWorkflows());
    }

    const filterSelect = document.getElementById('workflow-filter');
    if (filterSelect) {
      filterSelect.addEventListener('change', () => this.filterWorkflows());
    }

    // Log filter
    const logFilter = document.getElementById('log-filter');
    if (logFilter) {
      logFilter.addEventListener('change', () => this.filterExecutionLog());
    }

    // Clear log button
    const clearLogBtn = document.getElementById('clear-log-btn');
    if (clearLogBtn) {
      clearLogBtn.addEventListener('click', () => this.clearExecutionLog());
    }
  }

  /**
   * Switch between tabs
   */
  switchTab(tabName) {
    // Update tab buttons
    document.querySelectorAll('.workflow-tab').forEach(tab => {
      tab.classList.remove('active', 'border-blue-500', 'text-blue-600', 'dark:text-blue-400');
      tab.classList.add('border-transparent', 'text-gray-500', 'dark:text-gray-400');
    });

    const activeTab = document.querySelector(`[data-tab="${tabName}"]`);
    if (activeTab) {
      activeTab.classList.add('active', 'border-blue-500', 'text-blue-600', 'dark:text-blue-400');
      activeTab.classList.remove('border-transparent', 'text-gray-500', 'dark:text-gray-400');
    }

    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => {
      content.classList.add('hidden');
    });

    const activeContent = document.getElementById(`${tabName}-tab`);
    if (activeContent) {
      activeContent.classList.remove('hidden');
    }

    this.currentTab = tabName;

    // Load tab-specific content
    switch (tabName) {
      case 'workflows':
        this.loadWorkflows();
        break;
      case 'templates':
        this.loadTemplates();
        break;
      case 'execution-log':
        this.loadExecutionLog();
        break;
      case 'settings':
        this.loadSettings();
        break;
    }
  }

  /**
   * Update dashboard statistics
   */
  updateDashboard() {
    const workflows = this.engine.getWorkflows();
    const stats = this.engine.getExecutionStats();

    // Update statistics
    const totalWorkflows = document.getElementById('total-workflows');
    if (totalWorkflows) totalWorkflows.textContent = workflows.length;

    const activeWorkflows = document.getElementById('active-workflows');
    if (activeWorkflows) {
      activeWorkflows.textContent = workflows.filter(w => w.enabled).length;
    }

    const totalExecutions = document.getElementById('total-executions');
    if (totalExecutions) totalExecutions.textContent = stats.total;

    const successRate = document.getElementById('success-rate');
    if (successRate) successRate.textContent = stats.successRate;

    // Update automation status
    const statusElement = document.getElementById('automation-status');
    const toggleBtn = document.getElementById('automation-toggle');
    if (statusElement && toggleBtn) {
      if (this.engine.isEnabled) {
        statusElement.textContent = 'Enabled';
        toggleBtn.classList.remove('bg-red-600', 'hover:bg-red-700');
        toggleBtn.classList.add('bg-green-600', 'hover:bg-green-700');
      } else {
        statusElement.textContent = 'Disabled';
        toggleBtn.classList.remove('bg-green-600', 'hover:bg-green-700');
        toggleBtn.classList.add('bg-red-600', 'hover:bg-red-700');
      }
    }
  }

  /**
   * Load and display workflows
   */
  loadWorkflows() {
    const container = document.getElementById('workflows-container');
    const emptyState = document.getElementById('workflows-empty');
    
    if (!container) return;

    const workflows = this.engine.getWorkflows();

    if (workflows.length === 0) {
      container.classList.add('hidden');
      if (emptyState) emptyState.classList.remove('hidden');
      return;
    }

    container.classList.remove('hidden');
    if (emptyState) emptyState.classList.add('hidden');

    container.innerHTML = workflows.map(workflow => this.generateWorkflowCard(workflow)).join('');
  }

  /**
   * Generate workflow card HTML
   */
  generateWorkflowCard(workflow) {
    const stats = this.engine.getExecutionStats(workflow.id);
    
    return `
      <div class="workflow-card" data-workflow-id="${workflow.id}">
        <div class="flex justify-between items-start mb-4">
          <div class="flex-1">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-1">
              ${workflow.name}
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              ${workflow.description || 'No description provided'}
            </p>
          </div>
          
          <div class="flex items-center space-x-2">
            <span class="workflow-status ${workflow.enabled ? 'enabled' : 'disabled'}">
              <i class="fas fa-${workflow.enabled ? 'play' : 'pause'} mr-1"></i>
              ${workflow.enabled ? 'Enabled' : 'Disabled'}
            </span>
            
            <div class="relative">
              <button class="workflow-menu-btn p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                      onclick="this.nextElementSibling.classList.toggle('hidden')">
                <i class="fas fa-ellipsis-v"></i>
              </button>
              
              <div class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 hidden z-10">
                <button onclick="window.safeWorkflowCall('editWorkflow', '${workflow.id}')"
                        class="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                  <i class="fas fa-edit mr-2"></i>Edit
                </button>
                <button onclick="window.safeWorkflowCall('duplicateWorkflow', '${workflow.id}')"
                        class="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                  <i class="fas fa-copy mr-2"></i>Duplicate
                </button>
                <button onclick="window.safeWorkflowCall('toggleWorkflow', '${workflow.id}')"
                        class="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                  <i class="fas fa-${workflow.enabled ? 'pause' : 'play'} mr-2"></i>
                  ${workflow.enabled ? 'Disable' : 'Enable'}
                </button>
                <button onclick="window.safeWorkflowCall('testWorkflow', '${workflow.id}')"
                        class="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                  <i class="fas fa-play-circle mr-2"></i>Test Run
                </button>
                <hr class="my-1 border-gray-200 dark:border-gray-600">
                <button onclick="window.safeWorkflowCall('deleteWorkflow', '${workflow.id}')"
                        class="w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900">
                  <i class="fas fa-trash mr-2"></i>Delete
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Triggers -->
        <div class="mb-4">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Triggers</h4>
          <div class="flex flex-wrap">
            ${workflow.triggers.map(trigger => `
              <span class="trigger-badge">
                <i class="fas fa-bolt mr-1"></i>
                ${trigger.type}
              </span>
            `).join('')}
          </div>
        </div>
        
        <!-- Actions -->
        <div class="mb-4">
          <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Actions</h4>
          <div class="flex flex-wrap">
            ${workflow.actions.map(action => `
              <span class="action-badge">
                <i class="fas fa-cog mr-1"></i>
                ${action.type}
              </span>
            `).join('')}
          </div>
        </div>
        
        <!-- Statistics -->
        <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
          <div class="grid grid-cols-3 gap-4 text-center">
            <div>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">${stats.total}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">Executions</p>
            </div>
            <div>
              <p class="text-2xl font-bold text-green-600 dark:text-green-400">${stats.successful}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">Success</p>
            </div>
            <div>
              <p class="text-2xl font-bold ${stats.failed > 0 ? 'text-red-600 dark:text-red-400' : 'text-gray-400'}">${stats.failed}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">Failed</p>
            </div>
          </div>
          
          ${workflow.lastExecuted ? `
            <p class="text-xs text-gray-500 dark:text-gray-400 text-center mt-2">
              Last run: ${new Date(workflow.lastExecuted).toLocaleString()}
            </p>
          ` : ''}
        </div>
      </div>
    `;
  }

  /**
   * Create new workflow
   */
  createWorkflow() {
    if (typeof Swal !== 'undefined') {
      Swal.fire({
        title: 'Create New Workflow',
        html: this.generateWorkflowBuilderHTML(),
        width: '90%',
        showCancelButton: true,
        confirmButtonText: 'Create Workflow',
        cancelButtonText: 'Cancel',
        didOpen: () => {
          this.initializeWorkflowBuilder();
        },
        preConfirm: () => {
          return this.saveNewWorkflow();
        }
      }).then((result) => {
        if (result.isConfirmed) {
          this.loadWorkflows();
          this.updateDashboard();
          
          Swal.fire({
            title: 'Success!',
            text: 'Workflow created successfully',
            icon: 'success',
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000
          });
        }
      });
    }
  }

  /**
   * Generate workflow builder HTML
   */
  generateWorkflowBuilderHTML() {
    return `
      <div class="workflow-builder max-h-96 overflow-y-auto">
        <div class="space-y-4">
          <!-- Basic Information -->
          <div>
            <label class="block text-sm font-medium mb-2">Workflow Name</label>
            <input type="text" id="workflow-name" class="w-full px-3 py-2 border rounded-lg"
                   placeholder="Enter workflow name">
          </div>

          <div>
            <label class="block text-sm font-medium mb-2">Description</label>
            <textarea id="workflow-description" class="w-full px-3 py-2 border rounded-lg" rows="3"
                      placeholder="Describe what this workflow does"></textarea>
          </div>

          <!-- Trigger Selection -->
          <div>
            <label class="block text-sm font-medium mb-2">Trigger Event</label>
            <select id="workflow-trigger" class="w-full px-3 py-2 border rounded-lg">
              <option value="">Select trigger event</option>
              <option value="inventory.updated">Inventory Updated</option>
              <option value="inventory.added">Inventory Added</option>
              <option value="location.changed">Location Changed</option>
              <option value="shipment.created">Shipment Created</option>
              <option value="time.daily">Daily Schedule</option>
            </select>
          </div>

          <!-- Action Selection -->
          <div>
            <label class="block text-sm font-medium mb-2">Action Type</label>
            <select id="workflow-action" class="w-full px-3 py-2 border rounded-lg">
              <option value="">Select action type</option>
              <option value="email.send">Send Email</option>
              <option value="notification.show">Show Notification</option>
              <option value="odoo.create_record">Create ODOO Record</option>
              <option value="asana.create_task">Create Asana Task</option>
              <option value="system.log">Log Message</option>
            </select>
          </div>

          <!-- Email Parameters (shown when email action selected) -->
          <div id="email-params" class="hidden space-y-3">
            <div>
              <label class="block text-sm font-medium mb-1">Email To</label>
              <input type="email" id="email-to" class="w-full px-3 py-2 border rounded-lg"
                     placeholder="<EMAIL>">
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">Subject</label>
              <input type="text" id="email-subject" class="w-full px-3 py-2 border rounded-lg"
                     placeholder="Email subject">
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">Message</label>
              <textarea id="email-body" class="w-full px-3 py-2 border rounded-lg" rows="3"
                        placeholder="Email message"></textarea>
            </div>
          </div>

          <div class="flex items-center">
            <input type="checkbox" id="workflow-enabled" checked>
            <label for="workflow-enabled" class="ml-2 text-sm">Enable this workflow</label>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Initialize workflow builder
   */
  initializeWorkflowBuilder() {
    // Show/hide action parameters based on selection
    const actionSelect = document.getElementById('workflow-action');
    const emailParams = document.getElementById('email-params');

    if (actionSelect) {
      actionSelect.addEventListener('change', (e) => {
        if (emailParams) {
          if (e.target.value === 'email.send') {
            emailParams.classList.remove('hidden');
          } else {
            emailParams.classList.add('hidden');
          }
        }
      });
    }
  }

  /**
   * Save new workflow
   */
  saveNewWorkflow() {
    const name = document.getElementById('workflow-name')?.value;
    const description = document.getElementById('workflow-description')?.value;
    const triggerType = document.getElementById('workflow-trigger')?.value;
    const actionType = document.getElementById('workflow-action')?.value;
    const enabled = document.getElementById('workflow-enabled')?.checked;

    if (!name || !triggerType || !actionType) {
      Swal.showValidationMessage('Please fill in all required fields');
      return false;
    }

    const workflow = {
      id: this.engine.generateId(),
      name: name,
      description: description || '',
      enabled: enabled,
      triggers: [{
        type: triggerType,
        conditions: null
      }],
      actions: [],
      variables: {}
    };

    // Build action based on type
    if (actionType === 'email.send') {
      const emailTo = document.getElementById('email-to')?.value;
      const emailSubject = document.getElementById('email-subject')?.value;
      const emailBody = document.getElementById('email-body')?.value;

      if (!emailTo || !emailSubject) {
        Swal.showValidationMessage('Please fill in email parameters');
        return false;
      }

      workflow.actions.push({
        type: 'email.send',
        parameters: {
          to: emailTo,
          subject: emailSubject,
          body: emailBody || 'Automated notification from Talaria Dashboard'
        }
      });
    } else if (actionType === 'notification.show') {
      workflow.actions.push({
        type: 'notification.show',
        parameters: {
          title: 'Workflow Notification',
          message: `Workflow "${name}" has been triggered`,
          type: 'info',
          timer: 5000
        }
      });
    } else {
      // Generic action for other types
      workflow.actions.push({
        type: actionType,
        parameters: {}
      });
    }

    try {
      this.engine.registerWorkflow(workflow);
      return true;
    } catch (error) {
      Swal.showValidationMessage(`Error creating workflow: ${error.message}`);
      return false;
    }
  }

  /**
   * Edit existing workflow
   */
  editWorkflow(workflowId) {
    console.log('🔧 Edit workflow called for ID:', workflowId);

    const workflow = this.engine.getWorkflow(workflowId);
    if (!workflow) {
      console.error('❌ Workflow not found:', workflowId);
      Swal.fire('Error', 'Workflow not found', 'error');
      return;
    }

    console.log('✅ Found workflow:', workflow.name);

    // For now, show a simple edit dialog
    Swal.fire({
      title: 'Edit Workflow',
      text: `Editing "${workflow.name}". Advanced workflow editing will be available in the next update.`,
      icon: 'info'
    });
  }

  /**
   * Duplicate workflow
   */
  duplicateWorkflow(workflowId) {
    const workflow = this.engine.getWorkflow(workflowId);
    if (!workflow) {
      Swal.fire('Error', 'Workflow not found', 'error');
      return;
    }

    const duplicated = {
      ...workflow,
      id: this.engine.generateId(),
      name: `${workflow.name} (Copy)`,
      enabled: false
    };

    try {
      this.engine.registerWorkflow(duplicated);
      this.loadWorkflows();
      this.updateDashboard();

      Swal.fire({
        title: 'Success!',
        text: 'Workflow duplicated successfully',
        icon: 'success',
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000
      });
    } catch (error) {
      Swal.fire('Error', `Failed to duplicate workflow: ${error.message}`, 'error');
    }
  }

  /**
   * Toggle workflow enabled/disabled
   */
  toggleWorkflow(workflowId) {
    const workflow = this.engine.getWorkflow(workflowId);
    if (!workflow) {
      Swal.fire('Error', 'Workflow not found', 'error');
      return;
    }

    try {
      this.engine.updateWorkflow(workflowId, { enabled: !workflow.enabled });
      this.loadWorkflows();
      this.updateDashboard();

      const status = workflow.enabled ? 'disabled' : 'enabled';
      Swal.fire({
        title: 'Success!',
        text: `Workflow ${status} successfully`,
        icon: 'success',
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000
      });
    } catch (error) {
      Swal.fire('Error', `Failed to toggle workflow: ${error.message}`, 'error');
    }
  }

  /**
   * Test workflow execution
   */
  async testWorkflow(workflowId) {
    console.log('🧪 Test workflow called for ID:', workflowId);

    try {
      const workflow = this.engine.getWorkflow(workflowId);
      if (!workflow) {
        throw new Error('Workflow not found');
      }

      console.log('✅ Testing workflow:', workflow.name);

      await this.engine.triggerWorkflow(workflowId, {
        test: true,
        timestamp: new Date().toISOString()
      });

      Swal.fire({
        title: 'Test Successful!',
        text: `Workflow "${workflow.name}" executed successfully`,
        icon: 'success',
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000
      });
    } catch (error) {
      console.error('❌ Test workflow failed:', error);
      Swal.fire('Error', `Test failed: ${error.message}`, 'error');
    }
  }

  /**
   * Delete workflow
   */
  deleteWorkflow(workflowId) {
    const workflow = this.engine.getWorkflow(workflowId);
    if (!workflow) {
      Swal.fire('Error', 'Workflow not found', 'error');
      return;
    }

    Swal.fire({
      title: 'Delete Workflow?',
      text: `Are you sure you want to delete "${workflow.name}"? This action cannot be undone.`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'Yes, delete it!'
    }).then((result) => {
      if (result.isConfirmed) {
        try {
          this.engine.deleteWorkflow(workflowId);
          this.loadWorkflows();
          this.updateDashboard();

          Swal.fire({
            title: 'Deleted!',
            text: 'Workflow has been deleted.',
            icon: 'success',
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000
          });
        } catch (error) {
          Swal.fire('Error', `Failed to delete workflow: ${error.message}`, 'error');
        }
      }
    });
  }

  /**
   * Toggle automation on/off
   */
  toggleAutomation() {
    const isEnabled = this.engine.isEnabled;
    this.engine.setEnabled(!isEnabled);
    this.updateDashboard();

    const status = isEnabled ? 'disabled' : 'enabled';
    Swal.fire({
      title: `Automation ${status.charAt(0).toUpperCase() + status.slice(1)}!`,
      text: `Workflow automation has been ${status}`,
      icon: isEnabled ? 'warning' : 'success',
      toast: true,
      position: 'top-end',
      showConfirmButton: false,
      timer: 3000
    });
  }

  /**
   * Filter workflows
   */
  filterWorkflows() {
    // Implementation for search and filter functionality
    console.log('Filtering workflows...');
    this.loadWorkflows();
  }

  /**
   * Load templates
   */
  loadTemplates() {
    console.log('Loading workflow templates...');
    // Implementation for template loading
  }

  /**
   * Load execution log
   */
  loadExecutionLog() {
    console.log('Loading execution log...');
    // Implementation for execution log
  }

  /**
   * Filter execution log
   */
  filterExecutionLog() {
    console.log('Filtering execution log...');
    // Implementation for log filtering
  }

  /**
   * Clear execution log
   */
  clearExecutionLog() {
    Swal.fire({
      title: 'Clear Execution Log?',
      text: 'This will remove all execution history. Are you sure?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'Yes, clear it!'
    }).then((result) => {
      if (result.isConfirmed) {
        this.engine.executionHistory = [];
        this.engine.saveExecutionHistory();
        this.loadExecutionLog();

        Swal.fire({
          title: 'Cleared!',
          text: 'Execution log has been cleared.',
          icon: 'success',
          toast: true,
          position: 'top-end',
          showConfirmButton: false,
          timer: 3000
        });
      }
    });
  }

  /**
   * Load settings
   */
  loadSettings() {
    console.log('Loading settings...');
    // Implementation for settings
  }
}

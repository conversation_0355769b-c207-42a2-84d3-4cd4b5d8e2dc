/**
 * Progress Indicator System
 * Manages progress bars and step indicators for multi-step processes
 */

class ProgressIndicator {
    constructor() {
        this.container = document.getElementById('progress-container');
        this.progressBar = document.getElementById('progress-bar');
        this.progressTitle = document.getElementById('progress-title');
        this.progressPercentage = document.getElementById('progress-percentage');
        this.progressSteps = document.getElementById('progress-steps');
        this.currentStep = 0;
        this.totalSteps = 0;
        this.steps = [];
    }

    /**
     * Initialize a new progress workflow
     */
    init(title, steps) {
        this.steps = steps;
        this.totalSteps = steps.length;
        this.currentStep = 0;
        
        this.progressTitle.textContent = title;
        this.updateProgress(0);
        this.renderSteps();
        this.show();
    }

    /**
     * Show the progress indicator
     */
    show() {
        this.container.classList.remove('hidden');
        this.container.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }

    /**
     * Hide the progress indicator
     */
    hide() {
        this.container.classList.add('hidden');
    }

    /**
     * Update progress to specific step
     */
    updateProgress(stepIndex, customMessage = null) {
        this.currentStep = stepIndex;
        const percentage = Math.round((stepIndex / this.totalSteps) * 100);
        
        // Update progress bar
        this.progressBar.style.width = `${percentage}%`;
        this.progressPercentage.textContent = `${percentage}%`;
        
        // Update title with custom message or current step
        if (customMessage) {
            this.progressTitle.textContent = customMessage;
        } else if (stepIndex < this.totalSteps) {
            this.progressTitle.textContent = this.steps[stepIndex];
        } else {
            this.progressTitle.textContent = 'Complete!';
        }
        
        // Update step indicators
        this.updateStepIndicators();
    }

    /**
     * Move to next step
     */
    nextStep(customMessage = null) {
        if (this.currentStep < this.totalSteps) {
            this.updateProgress(this.currentStep + 1, customMessage);
        }
    }

    /**
     * Complete the progress
     */
    complete(message = 'Process completed successfully!') {
        this.updateProgress(this.totalSteps, message);
        this.progressBar.classList.add('bg-green-600');
        this.progressBar.classList.remove('bg-blue-600');
        
        // Auto-hide after 3 seconds
        setTimeout(() => {
            this.hide();
            this.reset();
        }, 3000);
    }

    /**
     * Show error state
     */
    error(message = 'An error occurred') {
        this.progressTitle.textContent = message;
        this.progressBar.classList.add('bg-red-600');
        this.progressBar.classList.remove('bg-blue-600');
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            this.hide();
            this.reset();
        }, 5000);
    }

    /**
     * Reset progress indicator
     */
    reset() {
        this.currentStep = 0;
        this.totalSteps = 0;
        this.steps = [];
        this.progressBar.style.width = '0%';
        this.progressBar.classList.remove('bg-green-600', 'bg-red-600');
        this.progressBar.classList.add('bg-blue-600');
        this.progressSteps.innerHTML = '';
    }

    /**
     * Render step indicators
     */
    renderSteps() {
        this.progressSteps.innerHTML = '';
        
        this.steps.forEach((step, index) => {
            const stepElement = document.createElement('div');
            stepElement.className = 'flex items-center';
            stepElement.innerHTML = `
                <div class="step-indicator w-6 h-6 rounded-full border-2 flex items-center justify-center mr-2" 
                     data-step="${index}">
                    <span class="text-xs font-medium">${index + 1}</span>
                </div>
                <span class="step-text">${step}</span>
            `;
            this.progressSteps.appendChild(stepElement);
        });
        
        this.updateStepIndicators();
    }

    /**
     * Update step indicator states
     */
    updateStepIndicators() {
        const indicators = this.progressSteps.querySelectorAll('.step-indicator');
        
        indicators.forEach((indicator, index) => {
            const stepIndex = parseInt(indicator.dataset.step);
            
            // Reset classes
            indicator.classList.remove('border-blue-600', 'bg-blue-600', 'text-white', 
                                     'border-green-600', 'bg-green-600', 'border-gray-300');
            
            if (stepIndex < this.currentStep) {
                // Completed step
                indicator.classList.add('border-green-600', 'bg-green-600', 'text-white');
                indicator.innerHTML = '<i class="fas fa-check text-xs"></i>';
            } else if (stepIndex === this.currentStep) {
                // Current step
                indicator.classList.add('border-blue-600', 'bg-blue-600', 'text-white');
                indicator.innerHTML = `<span class="text-xs font-medium">${stepIndex + 1}</span>`;
            } else {
                // Future step
                indicator.classList.add('border-gray-300');
                indicator.innerHTML = `<span class="text-xs font-medium">${stepIndex + 1}</span>`;
            }
        });
    }

    /**
     * Static method to create and manage progress for common workflows
     */
    static createLabelGenerationProgress() {
        const progress = new ProgressIndicator();
        const steps = [
            'Validating input data',
            'Fetching wafer information',
            'Generating label PDF',
            'Creating packing slip',
            'Sending to printer'
        ];
        progress.init('Generating Labels', steps);
        return progress;
    }

    static createInventoryUpdateProgress() {
        const progress = new ProgressIndicator();
        const steps = [
            'Validating changes',
            'Updating database',
            'Refreshing inventory view',
            'Logging changes'
        ];
        progress.init('Updating Inventory', steps);
        return progress;
    }

    static createRFQProgress() {
        const progress = new ProgressIndicator();
        const steps = [
            'Processing form data',
            'Generating Excel file',
            'Preparing email',
            'Sending RFQ request'
        ];
        progress.init('Sending RFQ Request', steps);
        return progress;
    }
}

// Initialize global progress indicator
document.addEventListener('DOMContentLoaded', () => {
    window.progressIndicator = new ProgressIndicator();
});

// Export for use in other scripts
window.ProgressIndicator = ProgressIndicator;

// Real-Time Dashboard Enhancement for Talaria
// Provides live updates for inventory metrics, notifications, and system status

class RealTimeDashboard {
  constructor() {
    this.socket = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    this.updateQueue = [];
    this.lastUpdateTime = null;
    this.notificationQueue = [];
    this.animationQueue = [];

    this.init();
  }

  init() {
    this.createConnectionStatusIndicator();
    this.connectSocket();
    this.setupEventListeners();
    this.startPeriodicUpdates();
    this.setupVisibilityChangeHandler();
    this.setupNotificationSystem();
  }

  createConnectionStatusIndicator() {
    // Create connection status indicator if it doesn't exist
    let statusIndicator = document.getElementById('connection-status');
    if (!statusIndicator) {
      statusIndicator = document.createElement('div');
      statusIndicator.id = 'connection-status';
      statusIndicator.className = 'connection-status disconnected';
      statusIndicator.innerHTML = '🔴 Connecting...';

      // Add to top-right corner
      statusIndicator.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 8px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        z-index: 1000;
        background: rgba(239, 68, 68, 0.9);
        color: white;
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        cursor: pointer;
      `;

      document.body.appendChild(statusIndicator);

      // Add click handler for manual refresh
      statusIndicator.addEventListener('click', () => {
        if (this.isConnected) {
          this.requestInitialData();
          this.showUpdateNotification('system', 'Dashboard refreshed manually');
        } else {
          this.connectSocket();
        }
      });
    }
  }

  connectSocket() {
    try {
      // Check if SocketIO is already available globally (from existing setup)
      if (typeof io !== 'undefined') {
        this.socket = io({
          transports: ['websocket', 'polling'],
          upgrade: true,
          rememberUpgrade: true
        });
      } else {
        console.error('SocketIO not available. Make sure socket.io is loaded.');
        this.showConnectionStatus('error');
        return;
      }

      this.socket.on('connect', () => {
        console.log('✅ Real-time dashboard connected');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.showConnectionStatus('connected');

        // Join dashboard room for targeted updates
        this.socket.emit('join_room', { room: 'dashboard' });

        // Request initial data
        this.requestInitialData();
      });

      this.socket.on('disconnect', () => {
        console.log('❌ Real-time dashboard disconnected');
        this.isConnected = false;
        this.showConnectionStatus('disconnected');
        this.attemptReconnect();
      });

      // Listen for initial data on connection
      this.socket.on('dashboard_initial_data', (data) => {
        console.log('📊 Received initial dashboard data');
        this.handleInitialData(data);
      });

      // Listen for real-time inventory updates
      this.socket.on('inventory_update', (data) => {
        console.log('📦 Inventory update received:', data);
        this.handleInventoryUpdate(data);
      });

      // Listen for shipment updates
      this.socket.on('shipment_update', (data) => {
        console.log('🚚 Shipment update received:', data);
        this.handleShipmentUpdate(data);
      });

      // Listen for system notifications
      this.socket.on('system_notification', (data) => {
        console.log('🔔 System notification received:', data);
        this.handleSystemNotification(data);
      });

      // Listen for dashboard metrics updates
      this.socket.on('dashboard_metrics', (data) => {
        console.log('📊 Dashboard metrics update:', data);
        this.updateDashboardMetrics(data);
      });

      // Listen for system events (existing)
      this.socket.on('system_event', (data) => {
        console.log('⚡ System event received:', data);
        this.handleSystemEvent(data);
      });

      // Listen for errors
      this.socket.on('dashboard_error', (data) => {
        console.error('❌ Dashboard error:', data);
        this.handleError(data);
      });

    } catch (error) {
      console.error('Failed to initialize socket connection:', error);
      this.showConnectionStatus('error');
    }
  }

  requestInitialData() {
    if (this.isConnected) {
      this.socket.emit('request_dashboard_data', {
        timestamp: Date.now(),
        page: window.location.pathname
      });
    }
  }

  handleInitialData(data) {
    console.log('Processing initial dashboard data...');

    // Update connection status
    this.showConnectionStatus('connected');

    // Process inventory stats if available
    if (data.inventory_stats) {
      this.updateInventoryMetrics(data.inventory_stats);
    }

    // Process Asana stats if available
    if (data.asana_stats) {
      this.updateAsanaMetrics(data.asana_stats);
    }

    // Update last refresh time
    this.updateLastRefreshTime();
  }

  handleInventoryUpdate(data) {
    console.log('📦 Processing inventory update...');

    // Update inventory metrics if stats are provided
    if (data.stats) {
      this.updateInventoryMetrics(data.stats);
    }

    // Show notification for significant changes
    if (data.change_type && data.change_type === 'significant') {
      const message = this.getInventoryUpdateMessage(data.type, data.data);
      this.showUpdateNotification('inventory', message);
    }

    // Update last update timestamp
    this.updateLastRefreshTime();
  }

  updateInventoryMetrics(stats) {
    // Update available lots
    if (stats.available_lots !== undefined) {
      this.animateCounterUpdate('available-lots', stats.available_lots);
    }

    // Update available wafers
    if (stats.available_wafers !== undefined) {
      this.animateCounterUpdate('available-wafers', stats.available_wafers);
    }

    // Update shipped wafers
    if (stats.shipped_wafers !== undefined) {
      this.animateCounterUpdate('shipped-wafers', stats.shipped_wafers);
    }

    // Update progress bars
    if (stats.capacity_percentage !== undefined) {
      this.updateProgressBar('capacity', stats.capacity_percentage);
    }

    if (stats.wafer_percentage !== undefined) {
      this.updateProgressBar('wafer', stats.wafer_percentage);
    }

    if (stats.quarterly_progress !== undefined) {
      this.updateProgressBar('quarterly', stats.quarterly_progress);
    }
  }

  updateAsanaMetrics(stats) {
    // Update total shipments
    if (stats.total_shipments !== undefined) {
      this.animateCounterUpdate('total-shipments', stats.total_shipments);
    }

    // Update on-time rate
    if (stats.on_time_rate !== undefined) {
      this.animateCounterUpdate('on-time-rate', stats.on_time_rate);
    }

    // Update active shipments
    if (stats.active_shipments !== undefined) {
      this.animateCounterUpdate('active-shipments', stats.active_shipments);
    }
  }

  getInventoryUpdateMessage(type, data) {
    switch (type) {
      case 'wafer_added':
        return `New wafer added: ${data.wafer_id || 'Unknown'}`;
      case 'wafer_removed':
        return `Wafer removed: ${data.wafer_id || 'Unknown'}`;
      case 'bulk_update':
        return `Bulk update completed: ${data.count || 0} wafers affected`;
      case 'location_change':
        return `Wafer location updated: ${data.wafer_id || 'Unknown'}`;
      default:
        return 'Inventory updated';
    }
  }

  handleShipmentUpdate(data) {
    console.log('🚚 Shipment update received:', data);
    
    // Update shipment-related metrics
    if (data.active_shipments !== undefined) {
      this.animateCounterUpdate('active-shipments', data.active_shipments);
    }

    // Update shipment status indicators
    if (data.status_updates) {
      this.updateShipmentStatuses(data.status_updates);
    }

    // Show notification for new shipments
    if (data.new_shipment) {
      this.showUpdateNotification('shipment', `New shipment: ${data.new_shipment.name}`);
    }
  }

  handleSystemNotification(data) {
    console.log('🔔 System notification received:', data);
    
    // Show system-wide notifications
    this.showSystemAlert(data.type, data.message, data.priority);
    
    // Update notification bell
    this.updateNotificationBell(data.unread_count);
  }

  updateDashboardMetrics(data) {
    console.log('📊 Dashboard metrics update:', data);
    
    // Batch update all metrics to avoid UI flickering
    this.batchUpdateMetrics(data.metrics);
    
    // Update charts if data provided
    if (data.chart_data) {
      this.updateCharts(data.chart_data);
    }
  }

  animateCounterUpdate(elementId, newValue) {
    const element = document.querySelector(`[data-stat="${elementId}"]`);
    if (!element) {
      console.warn(`Element with data-stat="${elementId}" not found`);
      return;
    }

    const currentValue = parseInt(element.textContent.replace(/[^\d]/g, '')) || 0;
    const difference = newValue - currentValue;

    if (difference === 0) return;

    // Add visual feedback for changes
    element.classList.add('updating');

    // Animate the counter
    this.animateValue(element, currentValue, newValue, 1000);

    // Show change indicator
    this.showChangeIndicator(element, difference);

    setTimeout(() => {
      element.classList.remove('updating');
    }, 1000);
  }

  animateValue(element, start, end, duration) {
    const startTime = performance.now();
    const change = end - start;

    const updateValue = (currentTime) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // Use easing function for smooth animation
      const easedProgress = this.easeOutCubic(progress);
      const currentValue = Math.round(start + (change * easedProgress));

      element.textContent = currentValue;

      if (progress < 1) {
        requestAnimationFrame(updateValue);
      }
    };

    requestAnimationFrame(updateValue);
  }

  easeOutCubic(t) {
    return 1 - Math.pow(1 - t, 3);
  }

  showChangeIndicator(element, difference) {
    // Remove any existing indicators
    const existingIndicator = element.parentNode.querySelector('.change-indicator');
    if (existingIndicator) {
      existingIndicator.remove();
    }

    const indicator = document.createElement('span');
    indicator.className = `change-indicator ${difference > 0 ? 'positive' : 'negative'}`;
    indicator.textContent = `${difference > 0 ? '+' : ''}${difference}`;
    indicator.style.cssText = `
      position: absolute;
      top: -10px;
      right: 10px;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 0.75rem;
      font-weight: 600;
      z-index: 10;
      animation: slideInDown 0.3s ease-out;
      background: ${difference > 0 ? 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)' : 'linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%)'};
      color: white;
    `;

    // Make sure parent has relative positioning
    if (getComputedStyle(element.parentNode).position === 'static') {
      element.parentNode.style.position = 'relative';
    }

    element.parentNode.appendChild(indicator);

    // Animate and remove
    setTimeout(() => {
      indicator.style.animation = 'fadeOutUp 0.3s ease-in forwards';
      setTimeout(() => {
        if (indicator.parentNode) {
          indicator.remove();
        }
      }, 300);
    }, 2000);
  }

  animateValue(element, start, end, duration) {
    const startTime = performance.now();
    const change = end - start;

    const updateValue = (currentTime) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // Use easing function for smooth animation
      const easedProgress = this.easeOutCubic(progress);
      const currentValue = Math.round(start + (change * easedProgress));
      
      element.textContent = currentValue;
      
      if (progress < 1) {
        requestAnimationFrame(updateValue);
      }
    };
    
    requestAnimationFrame(updateValue);
  }

  easeOutCubic(t) {
    return 1 - Math.pow(1 - t, 3);
  }

  showChangeIndicator(element, difference) {
    const indicator = document.createElement('span');
    indicator.className = `change-indicator ${difference > 0 ? 'positive' : 'negative'}`;
    indicator.textContent = `${difference > 0 ? '+' : ''}${difference}`;
    
    element.parentNode.appendChild(indicator);
    
    // Animate and remove
    setTimeout(() => {
      indicator.classList.add('fade-out');
      setTimeout(() => indicator.remove(), 300);
    }, 2000);
  }

  updateProgressBar(type, percentage) {
    const progressBar = document.querySelector(`.progress-bar-fill.gradient-${type === 'capacity' ? 'blue' : 'green'}`);
    if (progressBar) {
      progressBar.style.width = `${Math.min(percentage, 100)}%`;
      progressBar.classList.add('updating');
      setTimeout(() => progressBar.classList.remove('updating'), 500);
    }
  }

  setupNotificationSystem() {
    // Add CSS for animations if not already present
    if (!document.getElementById('realtime-animations')) {
      const style = document.createElement('style');
      style.id = 'realtime-animations';
      style.textContent = `
        @keyframes slideInDown {
          from { transform: translateY(-20px); opacity: 0; }
          to { transform: translateY(0); opacity: 1; }
        }
        @keyframes fadeOutUp {
          to { transform: translateY(-20px); opacity: 0; }
        }
        @keyframes counterPulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.05); }
        }
        .updating {
          animation: counterPulse 0.6s ease-in-out;
        }
        .connection-status.connected {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
        }
        .connection-status.disconnected {
          background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%) !important;
          animation: pulse 2s infinite;
        }
        .connection-status.error {
          background: linear-gradient(135deg, #fa709a 0%, #fee140 100%) !important;
        }
        @keyframes pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.7; }
        }
      `;
      document.head.appendChild(style);
    }
  }

  showUpdateNotification(type, message) {
    // Use SweetAlert2 for elegant notifications if available
    if (typeof Swal !== 'undefined') {
      Swal.fire({
        title: 'Live Update',
        text: message,
        icon: 'info',
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        customClass: {
          popup: 'real-time-notification'
        }
      });
    } else {
      // Fallback to simple notification
      this.showSimpleNotification(message, type);
    }
  }

  showSimpleNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `simple-notification ${type}`;
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 80px;
      right: 20px;
      padding: 12px 16px;
      background: rgba(79, 70, 229, 0.9);
      color: white;
      border-radius: 8px;
      font-size: 14px;
      z-index: 1001;
      animation: slideInDown 0.3s ease-out;
      max-width: 300px;
      backdrop-filter: blur(10px);
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
      notification.style.animation = 'fadeOutUp 0.3s ease-in forwards';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.remove();
        }
      }, 300);
    }, 3000);
  }

  showConnectionStatus(status) {
    const statusElement = document.getElementById('connection-status');
    if (statusElement) {
      statusElement.className = `connection-status ${status}`;

      switch (status) {
        case 'connected':
          statusElement.innerHTML = '🟢 Live';
          statusElement.title = 'Connected - Real-time updates active';
          break;
        case 'disconnected':
          statusElement.innerHTML = '🔴 Offline';
          statusElement.title = 'Disconnected - Click to reconnect';
          break;
        case 'error':
          statusElement.innerHTML = '🟡 Error';
          statusElement.title = 'Connection error - Click to retry';
          break;
        default:
          statusElement.innerHTML = '🔄 Connecting...';
          statusElement.title = 'Connecting to server...';
      }
    }
  }

  handleShipmentUpdate(data) {
    console.log('🚚 Processing shipment update...');

    // Update Asana metrics if stats are provided
    if (data.stats) {
      this.updateAsanaMetrics(data.stats);
    }

    // Show notification for new shipments
    if (data.type === 'new_shipment' && data.data) {
      this.showUpdateNotification('shipment', `New shipment: ${data.data.name || 'Unknown'}`);
    }

    // Update last refresh time
    this.updateLastRefreshTime();
  }

  handleSystemNotification(data) {
    console.log('🔔 Processing system notification...');

    // Show system-wide notifications
    this.showSystemAlert(data.type, data.message, data.priority);

    // Update notification bell if it exists
    this.updateNotificationBell(data.unread_count);
  }

  handleSystemEvent(data) {
    console.log('⚡ Processing system event...');

    // Handle different types of system events
    switch (data.type) {
      case 'maintenance':
        this.showUpdateNotification('system', `System maintenance: ${data.message}`);
        break;
      case 'update':
        this.showUpdateNotification('system', `System update: ${data.message}`);
        break;
      default:
        console.log('System event:', data.message);
    }
  }

  handleError(data) {
    console.error('❌ Dashboard error received:', data);
    this.showSimpleNotification(`Error: ${data.error}`, 'error');
  }

  updateDashboardMetrics(data) {
    console.log('📊 Processing dashboard metrics update...');

    if (data.success && data.stats) {
      // Update inventory metrics
      this.updateInventoryMetrics(data.stats);

      // Update Asana metrics if available
      if (data.stats.total_shipments !== undefined) {
        this.updateAsanaMetrics(data.stats);
      }

      // Update charts if data provided (integrate with existing home.js)
      if (data.stats.section_data && window.updateChartsWithData) {
        window.updateChartsWithData(data.stats);
      }
    }

    // Update last refresh time
    this.updateLastRefreshTime();
  }

  attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(() => {
        this.connectSocket();
      }, this.reconnectDelay * this.reconnectAttempts);
    }
  }

  startPeriodicUpdates() {
    // Fallback periodic updates every 30 seconds
    setInterval(() => {
      if (!this.isConnected) {
        this.fetchDashboardData();
      }
    }, 30000);
  }

  async fetchDashboardData() {
    try {
      const response = await fetch('/api/dashboard/stats');
      const data = await response.json();
      
      if (data.success) {
        this.updateDashboardMetrics(data);
      }
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
    }
  }

  setupVisibilityChangeHandler() {
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible' && this.isConnected) {
        // Request fresh data when tab becomes visible
        this.requestInitialData();
      }
    });
  }

  updateProgressBar(type, percentage) {
    const progressBar = document.querySelector(`.progress-bar-fill.gradient-${type === 'capacity' ? 'blue' : type === 'wafer' ? 'green' : 'amber'}`);
    if (progressBar) {
      const displayPercentage = Math.min(percentage, 100);
      progressBar.style.width = `${displayPercentage}%`;
      progressBar.classList.add('updating');

      // Add shimmer effect
      setTimeout(() => {
        progressBar.classList.remove('updating');
      }, 500);
    }
  }

  showSystemAlert(type, message, priority) {
    const alertClass = priority === 'high' ? 'error' : priority === 'medium' ? 'warning' : 'info';
    this.showSimpleNotification(message, alertClass);
  }

  updateNotificationBell(unreadCount) {
    const bellElement = document.querySelector('.notification-bell, .fa-bell');
    if (bellElement && unreadCount > 0) {
      // Add notification badge
      let badge = bellElement.parentNode.querySelector('.notification-badge');
      if (!badge) {
        badge = document.createElement('span');
        badge.className = 'notification-badge';
        badge.style.cssText = `
          position: absolute;
          top: -5px;
          right: -5px;
          background: #ef4444;
          color: white;
          border-radius: 50%;
          width: 18px;
          height: 18px;
          font-size: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
        `;
        bellElement.parentNode.style.position = 'relative';
        bellElement.parentNode.appendChild(badge);
      }
      badge.textContent = unreadCount > 99 ? '99+' : unreadCount;
    }
  }

  updateLastRefreshTime() {
    this.lastUpdateTime = new Date();

    // Update last update time if element exists
    const timeElement = document.getElementById('last-update-time');
    if (timeElement) {
      timeElement.textContent = `Last updated: ${this.lastUpdateTime.toLocaleTimeString()}`;
    }

    // Update data freshness indicator
    const statusDot = document.querySelector('.pulse-dot');
    if (statusDot) {
      statusDot.classList.remove('bg-red-400');
      statusDot.classList.add('bg-green-400');
    }
  }

  attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

      this.showConnectionStatus('connecting');

      setTimeout(() => {
        this.connectSocket();
      }, this.reconnectDelay * this.reconnectAttempts);
    } else {
      console.error('Max reconnection attempts reached');
      this.showConnectionStatus('error');
    }
  }

  startPeriodicUpdates() {
    // Fallback periodic updates every 30 seconds when not connected
    setInterval(() => {
      if (!this.isConnected) {
        console.log('Not connected, attempting to reconnect...');
        this.connectSocket();
      }
    }, 30000);
  }

  setupEventListeners() {
    // Manual refresh button
    const refreshBtn = document.getElementById('manual-refresh');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => {
        this.requestInitialData();
        this.showUpdateNotification('system', 'Dashboard refreshed manually');
      });
    }

    // Refresh chart button (existing)
    const refreshChartBtn = document.getElementById('refresh-chart');
    if (refreshChartBtn) {
      refreshChartBtn.addEventListener('click', () => {
        this.requestInitialData();
      });
    }
  }

  setupVisibilityChangeHandler() {
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible' && this.isConnected) {
        // Request fresh data when tab becomes visible
        console.log('Tab became visible, requesting fresh data...');
        this.requestInitialData();
      }
    });
  }
}

  setupEventListeners() {
    // Manual refresh button
    const refreshBtn = document.getElementById('manual-refresh');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => {
        this.requestInitialData();
        this.showUpdateNotification('system', 'Dashboard refreshed manually');
      });
    }
  }
}

// Initialize real-time dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Only initialize on dashboard pages
  if (window.location.pathname === '/' || window.location.pathname === '/dashboard' || window.location.pathname.includes('home')) {
    console.log('🚀 Initializing Real-Time Dashboard...');

    // Wait a bit for other scripts to load
    setTimeout(() => {
      window.realTimeDashboard = new RealTimeDashboard();
      console.log('✅ Real-Time Dashboard initialized');
    }, 1000);
  }
});

// Export for global access
window.RealTimeDashboard = RealTimeDashboard;

// Integration with existing home.js
if (typeof window !== 'undefined') {
  window.addEventListener('load', () => {
    // Hook into existing chart update function if available
    if (typeof window.updateChartsWithData === 'function') {
      const originalUpdateCharts = window.updateChartsWithData;
      window.updateChartsWithData = function(data) {
        // Call original function
        originalUpdateCharts.call(this, data);

        // Notify real-time dashboard
        if (window.realTimeDashboard) {
          window.realTimeDashboard.updateLastRefreshTime();
        }
      };
    }
  });
}

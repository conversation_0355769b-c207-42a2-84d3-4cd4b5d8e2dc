/**
 * Enhanced Preferences Management
 * Modern JavaScript for Talaria Dashboard preferences
 */

class PreferencesManager {
    constructor() {
        this.currentTab = 'profile';
        this.preferences = {};
        this.hasUnsavedChanges = false;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadPreferences();
        this.setupAutoSave();
        this.setupTabSwitching();
        this.setupQuickSettings();
    }

    setupEventListeners() {
        // Profile image upload
        const changeProfileBtn = document.getElementById('change-profile-picture');
        const profileUpload = document.getElementById('profile-image-upload');
        const profileForm = document.getElementById('profile-image-form');

        if (changeProfileBtn && profileUpload) {
            changeProfileBtn.addEventListener('click', () => profileUpload.click());
            profileUpload.addEventListener('change', (e) => this.handleImagePreview(e));
        }

        if (profileForm) {
            profileForm.addEventListener('submit', (e) => this.uploadProfileImage(e));
        }

        // Import/Export buttons
        const exportBtn = document.getElementById('export-preferences');
        const importBtn = document.getElementById('import-preferences');
        const importFile = document.getElementById('import-file');

        if (exportBtn) exportBtn.addEventListener('click', () => this.exportPreferences());
        if (importBtn) importBtn.addEventListener('click', () => importFile.click());
        if (importFile) importFile.addEventListener('change', (e) => this.importPreferences(e));

        // Save button
        const saveBtn = document.getElementById('save-all-preferences');
        if (saveBtn) saveBtn.addEventListener('click', () => this.saveAllPreferences());

        // Form change detection
        document.addEventListener('change', (e) => {
            if (e.target.matches('input, select, textarea')) {
                this.markAsChanged();
            }
        });

        // Prevent accidental navigation
        window.addEventListener('beforeunload', (e) => {
            if (this.hasUnsavedChanges) {
                e.preventDefault();
                e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
            }
        });
    }

    setupTabSwitching() {
        const tabs = document.querySelectorAll('.preference-tab');
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const targetTab = tab.dataset.tab;
                this.switchTab(targetTab);
            });
        });
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.preference-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`).classList.add('active');

        this.currentTab = tabName;

        // Load tab-specific content if needed
        this.loadTabContent(tabName);
    }

    setupQuickSettings() {
        // Quick theme buttons
        const themeButtons = document.querySelectorAll('.theme-quick-btn');
        themeButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const theme = btn.dataset.theme;
                this.applyQuickTheme(theme);
                this.updateQuickButtons('theme', theme);
            });
        });

        // Quick font size buttons
        const fontButtons = document.querySelectorAll('.font-size-btn');
        fontButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const size = btn.dataset.size;
                this.applyQuickFontSize(size);
                this.updateQuickButtons('font', size);
            });
        });
    }

    updateQuickButtons(type, value) {
        if (type === 'theme') {
            document.querySelectorAll('.theme-quick-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.theme === value);
            });
        } else if (type === 'font') {
            document.querySelectorAll('.font-size-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.size === value);
            });
        }
    }

    applyQuickTheme(theme) {
        // Apply theme immediately
        if (theme === 'dark') {
            document.documentElement.classList.add('dark');
        } else if (theme === 'light') {
            document.documentElement.classList.remove('dark');
        } else {
            // System theme
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            document.documentElement.classList.toggle('dark', prefersDark);
        }

        // Update form
        const themeInput = document.querySelector(`input[name="theme"][value="${theme}"]`);
        if (themeInput) themeInput.checked = true;

        this.markAsChanged();
    }

    applyQuickFontSize(size) {
        const sizeMap = { small: '14px', medium: '16px', large: '18px' };
        document.documentElement.style.fontSize = sizeMap[size];

        // Update form
        const fontSelect = document.querySelector('select[name="fontSize"]');
        if (fontSelect) fontSelect.value = size;

        this.markAsChanged();
    }

    async loadPreferences() {
        try {
            const response = await fetch('/api/preferences');
            if (!response.ok) throw new Error('Failed to load preferences');

            this.preferences = await response.json();
            this.populateForm();
            this.updateLastSaved();
            
            // Apply current preferences
            this.applyPreferences();
            
        } catch (error) {
            console.error('Error loading preferences:', error);
            this.showNotification('Failed to load preferences', 'error');
        }
    }

    populateForm() {
        // Theme
        const themeInput = document.querySelector(`input[name="theme"][value="${this.preferences.theme}"]`);
        if (themeInput) themeInput.checked = true;
        this.updateQuickButtons('theme', this.preferences.theme);

        // Font settings
        const fontSizeSelect = document.querySelector('select[name="fontSize"]');
        if (fontSizeSelect) fontSizeSelect.value = this.preferences.fontSize;
        this.updateQuickButtons('font', this.preferences.fontSize);

        const fontFamilySelect = document.querySelector('select[name="fontFamily"]');
        if (fontFamilySelect) fontFamilySelect.value = this.preferences.fontFamily;

        // Accent color
        const accentInput = document.querySelector(`input[name="accentColor"][value="${this.preferences.accentColor || 'blue'}"]`);
        if (accentInput) accentInput.checked = true;

        // Notifications
        const emailNotif = document.querySelector('input[name="emailNotifications"]');
        if (emailNotif) emailNotif.checked = this.preferences.emailNotifications;

        const browserNotif = document.querySelector('input[name="browserNotifications"]');
        if (browserNotif) browserNotif.checked = this.preferences.browserNotifications;
    }

    applyPreferences() {
        // Apply theme
        this.applyQuickTheme(this.preferences.theme);
        
        // Apply font settings
        this.applyQuickFontSize(this.preferences.fontSize);
        
        if (this.preferences.fontFamily) {
            document.documentElement.style.fontFamily = this.preferences.fontFamily;
        }
    }

    async saveAllPreferences() {
        try {
            // Show progress
            const saveBtn = document.getElementById('save-all-preferences');
            const originalText = saveBtn.innerHTML;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Saving...';
            saveBtn.disabled = true;

            // Collect all form data
            const formData = this.collectFormData();
            
            const response = await fetch('/api/preferences', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify(formData)
            });

            if (!response.ok) throw new Error('Failed to save preferences');

            this.preferences = formData;
            this.hasUnsavedChanges = false;
            this.updateLastSaved();
            this.showNotification('Preferences saved successfully!', 'success');

        } catch (error) {
            console.error('Error saving preferences:', error);
            this.showNotification('Failed to save preferences', 'error');
        } finally {
            // Restore button
            const saveBtn = document.getElementById('save-all-preferences');
            saveBtn.innerHTML = originalText;
            saveBtn.disabled = false;
        }
    }

    collectFormData() {
        const data = {};
        
        // Collect from all forms
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            const formData = new FormData(form);
            for (const [key, value] of formData.entries()) {
                if (key.includes('Notifications')) {
                    data[key] = true; // Checkboxes
                } else {
                    data[key] = value;
                }
            }
        });

        // Handle unchecked checkboxes
        const checkboxes = document.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            if (!checkbox.checked) {
                data[checkbox.name] = false;
            }
        });

        return data;
    }

    async handleImagePreview(event) {
        const file = event.target.files[0];
        if (!file) return;

        // Validate file
        if (!file.type.startsWith('image/')) {
            this.showNotification('Please select an image file', 'error');
            return;
        }

        if (file.size > 5 * 1024 * 1024) { // 5MB
            this.showNotification('Image must be smaller than 5MB', 'error');
            return;
        }

        // Show preview
        const reader = new FileReader();
        reader.onload = (e) => {
            const container = document.getElementById('profile-image-container');
            let img = document.getElementById('current-profile-image');
            
            if (!img) {
                img = document.createElement('img');
                img.id = 'current-profile-image';
                img.className = 'w-full h-full object-cover';
                img.alt = 'Profile Picture';
                
                // Remove initial
                const initial = container.querySelector('.profile-initial');
                if (initial) initial.remove();
                
                container.appendChild(img);
            }
            
            img.src = e.target.result;
        };
        reader.readAsDataURL(file);

        // Show upload form
        document.getElementById('profile-image-form').classList.remove('hidden');
    }

    async uploadProfileImage(event) {
        event.preventDefault();
        
        try {
            const formData = new FormData(event.target);
            
            const response = await fetch('/api/profile/upload-image', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: formData
            });

            if (!response.ok) throw new Error('Upload failed');

            const result = await response.json();
            this.showNotification('Profile picture updated successfully!', 'success');
            
            // Hide upload form
            document.getElementById('profile-image-form').classList.add('hidden');
            
        } catch (error) {
            console.error('Error uploading image:', error);
            this.showNotification('Failed to upload image', 'error');
        }
    }

    exportPreferences() {
        const data = {
            preferences: this.preferences,
            exportDate: new Date().toISOString(),
            version: '1.0'
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `talaria-preferences-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
        this.showNotification('Preferences exported successfully!', 'success');
    }

    async importPreferences(event) {
        const file = event.target.files[0];
        if (!file) return;

        try {
            const text = await file.text();
            const data = JSON.parse(text);
            
            if (data.preferences) {
                this.preferences = data.preferences;
                this.populateForm();
                this.applyPreferences();
                this.markAsChanged();
                this.showNotification('Preferences imported successfully!', 'success');
            } else {
                throw new Error('Invalid preferences file');
            }
        } catch (error) {
            console.error('Error importing preferences:', error);
            this.showNotification('Failed to import preferences', 'error');
        }
        
        // Clear file input
        event.target.value = '';
    }

    markAsChanged() {
        this.hasUnsavedChanges = true;
        const saveBtn = document.getElementById('save-all-preferences');
        if (saveBtn && !saveBtn.classList.contains('btn-warning')) {
            saveBtn.classList.add('btn-warning');
            saveBtn.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>Save Changes';
        }
    }

    updateLastSaved() {
        const lastSavedEl = document.getElementById('last-saved');
        if (lastSavedEl) {
            lastSavedEl.textContent = new Date().toLocaleTimeString();
        }
    }

    setupAutoSave() {
        // Auto-save every 30 seconds if there are changes
        setInterval(() => {
            if (this.hasUnsavedChanges) {
                this.saveAllPreferences();
            }
        }, 30000);
    }

    loadTabContent(tabName) {
        // Load additional content for specific tabs if needed
        switch (tabName) {
            case 'notifications':
                this.loadNotificationSettings();
                break;
            case 'workflow':
                this.loadWorkflowSettings();
                break;
            case 'security':
                this.loadSecuritySettings();
                break;
            case 'advanced':
                this.loadAdvancedSettings();
                break;
        }
    }

    loadNotificationSettings() {
        // Implementation for notification settings
    }

    loadWorkflowSettings() {
        // Implementation for workflow settings
    }

    loadSecuritySettings() {
        // Implementation for security settings
    }

    loadAdvancedSettings() {
        // Implementation for advanced settings
    }

    getCSRFToken() {
        const token = document.querySelector('meta[name="csrf-token"]');
        return token ? token.getAttribute('content') : '';
    }

    showNotification(message, type = 'info') {
        if (window.Swal) {
            const icon = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            Swal.fire({
                toast: true,
                position: 'top-end',
                icon: icon,
                title: message,
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true
            });
        } else {
            // Fallback notification
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
}

// Global functions for quick actions
function downloadUserData() {
    // Implementation for downloading user data
    window.preferencesManager.showNotification('User data download started', 'info');
}

function clearUserCache() {
    // Clear browser cache and local storage
    if ('caches' in window) {
        caches.keys().then(names => {
            names.forEach(name => caches.delete(name));
        });
    }
    localStorage.clear();
    sessionStorage.clear();
    window.preferencesManager.showNotification('Cache cleared successfully', 'success');
}

function resetAllPreferences() {
    if (confirm('Are you sure you want to reset all preferences to default? This cannot be undone.')) {
        fetch('/api/preferences/reset', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': window.preferencesManager.getCSRFToken()
            }
        }).then(response => {
            if (response.ok) {
                location.reload();
            } else {
                throw new Error('Reset failed');
            }
        }).catch(error => {
            window.preferencesManager.showNotification('Failed to reset preferences', 'error');
        });
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.preferencesManager = new PreferencesManager();
});

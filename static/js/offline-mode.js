/**
 * Offline Mode Implementation for Talaria Dashboard
 * Provides basic functionality when network connectivity is poor or unavailable
 */

class OfflineManager {
    constructor() {
        this.isOnline = navigator.onLine;
        this.wasOffline = false;
        this.cache = new Map();
        this.pendingActions = [];
        this.dbName = 'TalariaOfflineDB';
        this.dbVersion = 1;
        this.db = null;

        this.init();
    }

    async init() {
        // Initialize IndexedDB for offline storage
        await this.initDatabase();
        
        // Setup event listeners
        this.setupEventListeners();
        
        // Check initial connection status
        this.updateConnectionStatus();
        
        // Setup periodic sync
        this.setupPeriodicSync();
    }

    /**
     * Initialize IndexedDB for offline data storage
     */
    async initDatabase() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);
            
            request.onerror = () => reject(request.error);
            request.onsuccess = () => {
                this.db = request.result;
                resolve(this.db);
            };
            
            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                
                // Create object stores for different data types
                if (!db.objectStoreNames.contains('inventory')) {
                    const inventoryStore = db.createObjectStore('inventory', { keyPath: 'wafer_id' });
                    inventoryStore.createIndex('lot_id', 'lot_id', { unique: false });
                    inventoryStore.createIndex('location_id', 'location_id', { unique: false });
                }
                
                if (!db.objectStoreNames.contains('lots')) {
                    db.createObjectStore('lots', { keyPath: 'lot_id' });
                }
                
                if (!db.objectStoreNames.contains('locations')) {
                    const locationStore = db.createObjectStore('locations', { keyPath: 'location_id' });
                    locationStore.createIndex('label', 'label', { unique: false });
                }
                
                if (!db.objectStoreNames.contains('pending_actions')) {
                    const actionsStore = db.createObjectStore('pending_actions', { keyPath: 'id', autoIncrement: true });
                    actionsStore.createIndex('timestamp', 'timestamp', { unique: false });
                    actionsStore.createIndex('type', 'type', { unique: false });
                }
                
                if (!db.objectStoreNames.contains('cache')) {
                    const cacheStore = db.createObjectStore('cache', { keyPath: 'key' });
                    cacheStore.createIndex('timestamp', 'timestamp', { unique: false });
                }
            };
        });
    }

    /**
     * Setup event listeners for online/offline events
     */
    setupEventListeners() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.updateConnectionStatus();
            this.syncPendingActions();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.updateConnectionStatus();
        });

        // Listen for beforeunload to save pending data
        window.addEventListener('beforeunload', () => {
            this.savePendingData();
        });
    }

    /**
     * Update UI to reflect connection status
     */
    updateConnectionStatus() {
        const statusIndicator = this.createStatusIndicator();

        if (this.isOnline) {
            statusIndicator.className = 'offline-status online';
            statusIndicator.innerHTML = '<i class="fas fa-wifi"></i>';
            statusIndicator.title = 'Online - All features available';

            // Only show toast if we were previously offline
            if (this.wasOffline) {
                this.showToast('Connection restored', 'success');
                this.wasOffline = false;
            }
        } else {
            statusIndicator.className = 'offline-status offline';
            statusIndicator.innerHTML = '<i class="fas fa-wifi-slash"></i> Offline';
            statusIndicator.title = 'Offline Mode - Limited functionality available';
            this.showToast('Working offline - limited functionality available', 'warning');
            this.wasOffline = true;
        }
    }

    /**
     * Create or get status indicator element
     */
    createStatusIndicator() {
        let indicator = document.getElementById('offline-status');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'offline-status';
            indicator.className = 'offline-status';

            // Try to add to the dedicated connection status container
            const statusContainer = document.getElementById('connection-status-container');

            if (statusContainer) {
                // Perfect! Use the dedicated container
                statusContainer.appendChild(indicator);
            } else {
                // Fallback: try to add to the navigation bar
                const rightSideNav = document.querySelector('nav .flex.items-center.space-x-4');
                const notificationButton = document.querySelector('button[title="Notifications"]');

                if (rightSideNav && notificationButton) {
                    // Insert before the notifications button
                    rightSideNav.insertBefore(indicator, notificationButton);
                } else {
                    // Last fallback: add to the main content area with fixed positioning
                    indicator.classList.add('fixed-position');
                    const mainContent = document.getElementById('main-content');
                    if (mainContent) {
                        mainContent.insertBefore(indicator, mainContent.firstChild);
                    } else {
                        // Last resort: add to body
                        document.body.appendChild(indicator);
                    }
                }
            }
        }
        return indicator;
    }

    /**
     * Cache data for offline access
     */
    async cacheData(key, data, expiry = 24 * 60 * 60 * 1000) { // 24 hours default
        const cacheEntry = {
            key,
            data,
            timestamp: Date.now(),
            expiry: Date.now() + expiry
        };

        const transaction = this.db.transaction(['cache'], 'readwrite');
        const store = transaction.objectStore('cache');
        await store.put(cacheEntry);
    }

    /**
     * Retrieve cached data
     */
    async getCachedData(key) {
        if (!this.db) return null;

        const transaction = this.db.transaction(['cache'], 'readonly');
        const store = transaction.objectStore('cache');
        
        return new Promise((resolve) => {
            const request = store.get(key);
            request.onsuccess = () => {
                const result = request.result;
                if (result && result.expiry > Date.now()) {
                    resolve(result.data);
                } else {
                    resolve(null);
                }
            };
            request.onerror = () => resolve(null);
        });
    }

    /**
     * Store inventory data for offline access
     */
    async storeInventoryData(inventoryData) {
        if (!this.db) return;

        const transaction = this.db.transaction(['inventory'], 'readwrite');
        const store = transaction.objectStore('inventory');
        
        for (const item of inventoryData) {
            await store.put(item);
        }
    }

    /**
     * Get offline inventory data
     */
    async getOfflineInventory(filters = {}) {
        if (!this.db) return [];

        const transaction = this.db.transaction(['inventory'], 'readonly');
        const store = transaction.objectStore('inventory');
        
        return new Promise((resolve) => {
            const request = store.getAll();
            request.onsuccess = () => {
                let results = request.result || [];
                
                // Apply basic filters
                if (filters.lot_id) {
                    results = results.filter(item => 
                        item.lot_id && item.lot_id.includes(filters.lot_id)
                    );
                }
                
                if (filters.wafer_id) {
                    results = results.filter(item => 
                        item.wafer_id && item.wafer_id.includes(filters.wafer_id)
                    );
                }
                
                resolve(results);
            };
            request.onerror = () => resolve([]);
        });
    }

    /**
     * Queue action for later sync when online
     */
    async queueAction(action) {
        const actionEntry = {
            ...action,
            timestamp: Date.now(),
            id: Date.now() + Math.random()
        };

        this.pendingActions.push(actionEntry);

        if (this.db) {
            const transaction = this.db.transaction(['pending_actions'], 'readwrite');
            const store = transaction.objectStore('pending_actions');
            await store.add(actionEntry);
        }

        this.showToast(`Action queued for sync: ${action.type}`, 'info');
    }

    /**
     * Sync pending actions when online
     */
    async syncPendingActions() {
        if (!this.isOnline || !this.db) return;

        const transaction = this.db.transaction(['pending_actions'], 'readwrite');
        const store = transaction.objectStore('pending_actions');
        
        const request = store.getAll();
        request.onsuccess = async () => {
            const actions = request.result || [];
            
            for (const action of actions) {
                try {
                    await this.executeAction(action);
                    await store.delete(action.id);
                    this.showToast(`Synced: ${action.type}`, 'success');
                } catch (error) {
                    console.error('Failed to sync action:', action, error);
                }
            }
        };
    }

    /**
     * Execute a queued action
     */
    async executeAction(action) {
        switch (action.type) {
            case 'inventory_update':
                return this.syncInventoryUpdate(action.data);
            case 'inventory_add':
                return this.syncInventoryAdd(action.data);
            case 'inventory_delete':
                return this.syncInventoryDelete(action.data);
            default:
                console.warn('Unknown action type:', action.type);
        }
    }

    /**
     * Sync inventory update
     */
    async syncInventoryUpdate(data) {
        const response = await fetch('/api/inventory/modify', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.getCSRFToken()
            },
            body: JSON.stringify(data)
        });

        if (!response.ok) {
            throw new Error(`Sync failed: ${response.statusText}`);
        }

        return response.json();
    }

    /**
     * Setup periodic sync attempts
     */
    setupPeriodicSync() {
        setInterval(() => {
            if (this.isOnline) {
                this.syncPendingActions();
            }
        }, 30000); // Try sync every 30 seconds when online
    }

    /**
     * Get CSRF token for API requests
     */
    getCSRFToken() {
        const token = document.querySelector('meta[name="csrf-token"]');
        return token ? token.getAttribute('content') : '';
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'info') {
        if (window.Swal) {
            Swal.fire({
                toast: true,
                position: 'top-end',
                icon: type === 'success' ? 'success' : type === 'error' ? 'error' : 'info',
                title: message,
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true
            });
        }
    }

    /**
     * Save pending data before page unload
     */
    savePendingData() {
        if (this.pendingActions.length > 0) {
            localStorage.setItem('talaria_pending_actions', JSON.stringify(this.pendingActions));
        }
    }

    /**
     * Check if feature is available offline
     */
    isFeatureAvailableOffline(feature) {
        const offlineFeatures = [
            'inventory_view',
            'basic_search',
            'cached_data_view'
        ];
        
        return offlineFeatures.includes(feature);
    }
}

// Initialize offline manager
document.addEventListener('DOMContentLoaded', () => {
    window.offlineManager = new OfflineManager();
});

// Export for use in other scripts
window.OfflineManager = OfflineManager;

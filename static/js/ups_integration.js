/**
 * UPS Integration JavaScript
 * Handles UPS shipping functionality in the Talaria Dashboard
 */

// UPS API functions
const upsAPI = {
  // Get Asana task information
  async getAsanaInfo(asanaUrl) {
    try {
      const response = await fetch("/ups/api/get-asana-info", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRFToken": getCsrfToken(),
        },
        body: JSON.stringify({ asana_url: asanaUrl }),
      });

      if (!response.ok) {
        throw new Error(`Server error: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error getting Asana task information:", error);
      throw error;
    }
  },
  // Create shipping label
  async createLabel(formData) {
    try {
      const response = await fetch("/ups/api/create-label", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRFToken": getCsrfToken(),
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error(`Server error: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error creating UPS label:", error);
      throw error;
    }
  },

  // Track package
  async trackPackage(trackingNumber) {
    try {
      const response = await fetch(`/ups/api/track/${trackingNumber}`, {
        method: "GET",
        headers: {
          "X-CSRFToken": getCsrfToken(),
        },
      });

      if (!response.ok) {
        throw new Error(`Server error: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error tracking UPS package:", error);
      throw error;
    }
  },
  // Check authentication
  async checkAuthentication() {
    try {
      const response = await fetch("/ups/api/check-auth", {
        method: "GET",
        headers: {
          "X-CSRFToken": getCsrfToken(),
        },
      });

      if (!response.ok) {
        // If not authenticated, redirect to auth login
        window.location.href = "/ups/auth/login";
        return false;
      }

      return true;
    } catch (error) {
      console.error("Error checking authentication:", error);
      return false;
    }
  },

  // Get shipping rates
  async getShippingRates(formData) {
    try {
      const response = await fetch("/ups/api/rates", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRFToken": getCsrfToken(),
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error(`Server error: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error getting UPS shipping rates:", error);
      throw error;
    }
  },

  // Validate address
  async validateAddress(addressData) {
    try {
      const response = await fetch("/ups/api/validate-address", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRFToken": getCsrfToken(),
        },
        body: JSON.stringify(addressData),
      });

      if (!response.ok) {
        throw new Error(`Server error: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error validating address with UPS:", error);
      throw error;
    }
  },

  // Upload to Asana
  async uploadToAsana(trackingNumber, asanaTaskGid) {
    try {
      const response = await fetch("/ups/api/upload-to-asana", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRFToken": getCsrfToken(),
        },
        body: JSON.stringify({
          tracking_number: trackingNumber,
          asana_task_gid: asanaTaskGid,
        }),
      });

      if (!response.ok) {
        throw new Error(`Server error: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error("Error uploading to Asana:", error);
      throw error;
    }
  },
};

// UI handlers
const upsUI = {
  // Initialize UPS UI components
  init() {
    this.setupEventListeners();
    this.setupFormValidation();
  },

  // Set up event listeners
  setupEventListeners() {
    // Create label form submission
    const createLabelForm = document.getElementById("ups-shipping-form");
    if (createLabelForm) {
      createLabelForm.addEventListener(
        "submit",
        this.handleCreateLabel.bind(this)
      );
    }

    // Track package form submission
    const trackForm = document.getElementById("ups-tracking-form");
    if (trackForm) {
      trackForm.addEventListener("submit", this.handleTrackPackage.bind(this));
    }

    // Get rates button
    const getRatesBtn = document.getElementById("get-rates-btn");
    if (getRatesBtn) {
      getRatesBtn.addEventListener("click", this.handleGetRates.bind(this));
    }

    // Validate address button
    const validateAddressBtn = document.getElementById("validate-address-btn");
    if (validateAddressBtn) {
      validateAddressBtn.addEventListener(
        "click",
        this.handleValidateAddress.bind(this)
      );
    }

    // Asana URL field
    const asanaUrlField = document.getElementById("asana_url");
    if (asanaUrlField) {
      asanaUrlField.addEventListener(
        "blur",
        this.handleAsanaUrlChange.bind(this)
      );
    }
  },

  // Set up form validation
  setupFormValidation() {
    // Add validation rules for UPS forms
    const shippingForm = document.getElementById("ups-shipping-form");
    if (shippingForm) {
      shippingForm.addEventListener("input", (e) => {
        const target = e.target;
        if (target.required && !target.value.trim()) {
          target.classList.add("border-red-500");
        } else {
          target.classList.remove("border-red-500");
        }
      });
    }
  },

  // Handle create label form submission
  async handleCreateLabel(e) {
    e.preventDefault();

    // Show loading state
    this.showLoading("Creating shipping label...");

    try {
      // Get form data
      const form = e.target;
      const formData = new FormData(form);
      const data = Object.fromEntries(formData.entries());

      // Call API to create label
      const result = await upsAPI.createLabel(data);

      if (!result.success) {
        throw new Error(result.message || "Failed to create shipping label");
      }

      // Show success message
      this.showSuccess(
        "Shipping label created successfully",
        `Tracking Number: ${result.tracking_number}`
      );

      // Display tracking number and download links
      this.displayLabelResult(result);

      // Clear form
      form.reset();
    } catch (error) {
      console.error("Error creating shipping label:", error);
      this.showError("Failed to create shipping label", error.message);
    } finally {
      this.hideLoading();
    }
  },

  // Handle track package form submission
  async handleTrackPackage(e) {
    e.preventDefault();

    // Get tracking number
    const trackingNumber = document
      .getElementById("tracking-number")
      .value.trim();

    if (!trackingNumber) {
      this.showError("Tracking number is required");
      return;
    }

    // Show loading state
    this.showLoading("Tracking package...");

    try {
      // Call API to track package
      const result = await upsAPI.trackPackage(trackingNumber);

      if (!result.success) {
        throw new Error(result.message || "Failed to track package");
      }

      // Display tracking information
      this.displayTrackingInfo(result.tracking_info);
    } catch (error) {
      console.error("Error tracking package:", error);
      this.showError("Failed to track package", error.message);
    } finally {
      this.hideLoading();
    }
  },

  // Handle get rates button click
  async handleGetRates(e) {
    e.preventDefault();

    // Get form data from shipping form
    const form = document.getElementById("ups-shipping-form");
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());

    // Validate required fields for rates
    const requiredFields = [
      "recipient_name",
      "address_line1",
      "city",
      "postal_code",
      "country_code",
      "length",
      "width",
      "height",
      "weight",
    ];

    const missingFields = requiredFields.filter((field) => !data[field]);

    if (missingFields.length > 0) {
      this.showError(
        "Missing required fields",
        `Please fill in: ${missingFields.join(", ")}`
      );
      return;
    }

    // Show loading state
    this.showLoading("Getting shipping rates...");

    try {
      // Call API to get rates
      const result = await upsAPI.getShippingRates(data);

      if (!result.success) {
        throw new Error(result.message || "Failed to get shipping rates");
      }

      // Display rates
      this.displayShippingRates(result.rates);
    } catch (error) {
      console.error("Error getting shipping rates:", error);
      this.showError("Failed to get shipping rates", error.message);
    } finally {
      this.hideLoading();
    }
  },

  // Handle Asana URL change
  async handleAsanaUrlChange(e) {
    const asanaUrl = e.target.value.trim();

    if (!asanaUrl) {
      return; // No URL provided
    }

    // Show loading state
    this.showLoading("Getting Asana task information...");

    try {
      // Call API to get Asana task information
      const result = await upsAPI.getAsanaInfo(asanaUrl);

      if (!result.success) {
        throw new Error(
          result.message || "Failed to get Asana task information"
        );
      }

      // Populate form with recipient information
      const recipientInfo = result.recipient_info;

      if (Object.keys(recipientInfo).length === 0) {
        this.showWarning(
          "No Information Found",
          "No recipient information found in the Asana task."
        );
        return;
      }

      // Update form fields
      for (const [field, value] of Object.entries(recipientInfo)) {
        const inputField = document.getElementById(field);
        if (inputField && value) {
          inputField.value = value;
        }
      }

      this.showSuccess(
        "Form Updated",
        "Recipient information has been populated from Asana task."
      );
    } catch (error) {
      console.error("Error getting Asana task information:", error);
      this.showError("Failed to get Asana task information", error.message);
    } finally {
      this.hideLoading();
    }
  },

  // Handle validate address button click
  async handleValidateAddress(e) {
    e.preventDefault();

    // Get address data from shipping form
    const form = document.getElementById("ups-shipping-form");
    const formData = new FormData(form);

    const addressData = {
      address_line1: formData.get("address_line1") || "",
      address_line2: formData.get("address_line2") || "",
      city: formData.get("city") || "",
      state_province: formData.get("state_province") || "",
      postal_code: formData.get("postal_code") || "",
      country_code: formData.get("country_code") || "",
    };

    // Validate required fields
    const requiredFields = [
      "address_line1",
      "city",
      "postal_code",
      "country_code",
    ];

    const missingFields = requiredFields.filter((field) => !addressData[field]);

    if (missingFields.length > 0) {
      this.showError(
        "Missing required fields",
        `Please fill in: ${missingFields.join(", ")}`
      );
      return;
    }

    // Show loading state
    this.showLoading("Validating address...");

    try {
      // Call API to validate address
      const result = await upsAPI.validateAddress(addressData);

      if (!result.success) {
        throw new Error(result.message || "Failed to validate address");
      }

      // Display validation result
      if (result.is_valid) {
        this.showSuccess("Address Validation", "The address is valid");
      } else {
        // Show suggested addresses if available
        if (
          result.suggested_addresses &&
          result.suggested_addresses.length > 0
        ) {
          this.displaySuggestedAddresses(result.suggested_addresses);
        } else {
          this.showWarning(
            "Address Validation",
            result.validation_result || "The address may not be valid"
          );
        }
      }
    } catch (error) {
      console.error("Error validating address:", error);
      this.showError("Failed to validate address", error.message);
    } finally {
      this.hideLoading();
    }
  },

  // Display label creation result
  displayLabelResult(result) {
    const resultDiv = document.getElementById("ups-label-result");
    if (!resultDiv) return;

    resultDiv.innerHTML = `
      <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow mt-4">
        <h3 class="text-lg font-semibold mb-2">Shipping Label Created</h3>
        <p class="mb-2"><strong>Tracking Number:</strong> ${
          result.tracking_number
        }</p>
        <div class="flex space-x-2 mt-3">
          <a href="/ups/api/download/label/${
            result.tracking_number
          }" target="_blank"
             class="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
            <i class="fas fa-download mr-1"></i> Download Label
          </a>
          ${
            result.is_international
              ? `
            <a href="/ups/api/download/invoice/${result.tracking_number}" target="_blank"
               class="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700">
              <i class="fas fa-file-invoice mr-1"></i> Download Invoice
            </a>
          `
              : ""
          }
        </div>
      </div>
    `;

    resultDiv.classList.remove("hidden");
  },

  // Display tracking information
  displayTrackingInfo(trackingInfo) {
    const resultDiv = document.getElementById("ups-tracking-result");
    if (!resultDiv) return;

    // Format activities
    let activitiesHtml = "";
    if (trackingInfo.activities && trackingInfo.activities.length > 0) {
      activitiesHtml = `
        <h4 class="font-semibold mt-3 mb-2">Shipment Progress</h4>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead>
              <tr>
                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
              ${trackingInfo.activities
                .map(
                  (activity) => `
                <tr>
                  <td class="px-3 py-2 whitespace-nowrap text-sm">${activity.date}</td>
                  <td class="px-3 py-2 whitespace-nowrap text-sm">${activity.status}</td>
                  <td class="px-3 py-2 whitespace-nowrap text-sm">${activity.location}</td>
                </tr>
              `
                )
                .join("")}
            </tbody>
          </table>
        </div>
      `;
    }

    resultDiv.innerHTML = `
      <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow mt-4">
        <h3 class="text-lg font-semibold mb-2">Tracking Information</h3>
        <p class="mb-1"><strong>Tracking Number:</strong> ${
          trackingInfo.tracking_number
        }</p>
        <p class="mb-1"><strong>Status:</strong> ${
          trackingInfo.status_description
        }</p>
        <p class="mb-1"><strong>Service:</strong> ${
          trackingInfo.service_type
        }</p>
        <p class="mb-1"><strong>Weight:</strong> ${trackingInfo.weight}</p>
        ${
          trackingInfo.estimated_delivery
            ? `<p class="mb-1"><strong>Estimated Delivery:</strong> ${trackingInfo.estimated_delivery}</p>`
            : ""
        }
        ${activitiesHtml}
      </div>
    `;

    resultDiv.classList.remove("hidden");
  },

  // Display shipping rates
  displayShippingRates(rates) {
    const resultDiv = document.getElementById("ups-rates-result");
    if (!resultDiv) return;

    if (!rates || rates.length === 0) {
      resultDiv.innerHTML = `
        <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow mt-4">
          <p>No shipping rates available for this destination.</p>
        </div>
      `;
      resultDiv.classList.remove("hidden");
      return;
    }

    resultDiv.innerHTML = `
      <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow mt-4">
        <h3 class="text-lg font-semibold mb-2">Available Shipping Options</h3>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead>
              <tr>
                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cost</th>
                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Delivery Time</th>
                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
              ${rates
                .map(
                  (rate) => `
                <tr>
                  <td class="px-3 py-2 whitespace-nowrap text-sm">${
                    rate.service_name
                  }</td>
                  <td class="px-3 py-2 whitespace-nowrap text-sm">${
                    rate.total_charge
                  }</td>
                  <td class="px-3 py-2 whitespace-nowrap text-sm">${
                    rate.business_days
                      ? `${rate.business_days} business days`
                      : "Varies"
                  }</td>
                  <td class="px-3 py-2 whitespace-nowrap text-sm">
                    <button class="select-service-btn px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 text-xs"
                            data-service-code="${rate.service_code}">
                      Select
                    </button>
                  </td>
                </tr>
              `
                )
                .join("")}
            </tbody>
          </table>
        </div>
      </div>
    `;

    // Add event listeners to select buttons
    const selectButtons = resultDiv.querySelectorAll(".select-service-btn");
    selectButtons.forEach((button) => {
      button.addEventListener("click", () => {
        const serviceCode = button.getAttribute("data-service-code");
        document.getElementById("service_code").value = serviceCode;
        this.showSuccess(
          "Service Selected",
          `Selected service: ${button.closest("tr").cells[0].textContent}`
        );
      });
    });

    resultDiv.classList.remove("hidden");
  },

  // Display suggested addresses
  displaySuggestedAddresses(addresses) {
    const resultDiv = document.getElementById("ups-address-result");
    if (!resultDiv) return;

    resultDiv.innerHTML = `
      <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow mt-4">
        <h3 class="text-lg font-semibold mb-2">Suggested Addresses</h3>
        <p class="mb-3">The address you entered may not be valid. Please consider using one of these suggested addresses:</p>
        <div class="space-y-3">
          ${addresses
            .map(
              (address, index) => `
            <div class="p-3 border rounded">
              <p>${address.address_line1}</p>
              ${address.address_line2 ? `<p>${address.address_line2}</p>` : ""}
              <p>${address.city}, ${address.state} ${address.postal_code}</p>
              <p>${address.country_code}</p>
              <button class="use-address-btn mt-2 px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 text-xs"
                      data-address-index="${index}">
                Use This Address
              </button>
            </div>
          `
            )
            .join("")}
        </div>
      </div>
    `;

    // Add event listeners to use address buttons
    const useButtons = resultDiv.querySelectorAll(".use-address-btn");
    useButtons.forEach((button) => {
      button.addEventListener("click", () => {
        const index = parseInt(button.getAttribute("data-address-index"));
        const address = addresses[index];

        // Update form fields with suggested address
        document.getElementById("address_line1").value =
          address.address_line1 || "";
        document.getElementById("address_line2").value =
          address.address_line2 || "";
        document.getElementById("city").value = address.city || "";
        document.getElementById("state_province").value = address.state || "";
        document.getElementById("postal_code").value =
          address.postal_code || "";
        document.getElementById("country_code").value =
          address.country_code || "";

        this.showSuccess(
          "Address Updated",
          "The form has been updated with the selected address"
        );
      });
    });

    resultDiv.classList.remove("hidden");
  },

  // Show loading message
  showLoading(message = "Loading...") {
    Swal.fire({
      title: message,
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      },
    });
  },

  // Hide loading message
  hideLoading() {
    Swal.close();
  },

  // Show success message
  showSuccess(title, message = "") {
    Swal.fire({
      icon: "success",
      title: title,
      text: message,
      timer: 3000,
    });
  },

  // Show error message
  showError(title, message = "") {
    Swal.fire({
      icon: "error",
      title: title,
      text: message,
    });
  },

  // Show warning message
  showWarning(title, message = "") {
    Swal.fire({
      icon: "warning",
      title: title,
      text: message,
    });
  },
};

// Helper function to get CSRF token
function getCsrfToken() {
  return document
    .querySelector('meta[name="csrf-token"]')
    .getAttribute("content");
}

// Initialize UPS integration when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  upsUI.init();
});

// Make functions available globally
window.upsAPI = upsAPI;
window.upsUI = upsUI;

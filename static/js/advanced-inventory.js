/**
 * Advanced Inventory Management System
 * Features: AI-powered search, batch operations, predictive analytics, keyboard shortcuts
 */

class AdvancedInventoryManager {
  constructor() {
    this.searchCache = new Map();
    this.suggestions = new Map();
    this.batchOperations = new BatchOperationsManager();
    this.analytics = new InventoryAnalytics();
    this.smartSearch = new SmartSearchEngine();

    this.init();
  }

  init() {
    this.setupEventListeners();
    this.loadSearchPresets();
    this.initializeSmartSearch();
    console.log('🚀 Advanced Inventory Manager initialized');
  }

  setupEventListeners() {
    // Smart search input
    const smartSearchInput = document.getElementById('smart-search');
    if (smartSearchInput) {
      smartSearchInput.addEventListener('input', this.handleSmartSearch.bind(this));
      smartSearchInput.addEventListener('keydown', this.handleSearchKeydown.bind(this));
      smartSearchInput.addEventListener('focus', this.showSearchSuggestions.bind(this));
    }

    // Advanced search toggle
    const toggleAdvanced = document.getElementById('toggle-advanced-search');
    if (toggleAdvanced) {
      toggleAdvanced.addEventListener('click', this.toggleAdvancedSearch.bind(this));
    }

    // Search presets
    document.querySelectorAll('.preset-btn').forEach(btn => {
      btn.addEventListener('click', this.applySearchPreset.bind(this));
    });

    // Batch operations panel
    const toggleBatchPanel = document.getElementById('toggle-batch-panel');
    if (toggleBatchPanel) {
      toggleBatchPanel.addEventListener('click', this.openBatchPanel.bind(this));
    }

    const closeBatchPanel = document.getElementById('close-batch-panel');
    if (closeBatchPanel) {
      closeBatchPanel.addEventListener('click', this.closeBatchPanel.bind(this));
    }

    // Batch tabs
    document.querySelectorAll('.batch-tab').forEach(tab => {
      tab.addEventListener('click', this.switchBatchTab.bind(this));
    });

    // Selection actions
    const selectAllVisible = document.getElementById('select-all-visible');
    if (selectAllVisible) {
      selectAllVisible.addEventListener('click', this.selectAllVisible.bind(this));
    }

    const clearSelection = document.getElementById('clear-selection');
    if (clearSelection) {
      clearSelection.addEventListener('click', this.clearSelection.bind(this));
    }
  }

  async handleSmartSearch(event) {
    const query = event.target.value.trim();
    
    if (query.length < 2) {
      this.hideSearchSuggestions();
      return;
    }

    // Debounce search
    clearTimeout(this.searchTimeout);
    this.searchTimeout = setTimeout(async () => {
      await this.performSmartSearch(query);
    }, 300);
  }

  async performSmartSearch(query) {
    try {
      // Check cache first
      if (this.searchCache.has(query)) {
        this.displaySearchSuggestions(this.searchCache.get(query));
        return;
      }

      // Show loading indicator
      this.showSearchLoading();

      // Perform AI-powered search via API
      const response = await fetch('/api/inventory/smart-suggestions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ query })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to get suggestions');
      }

      const suggestions = data.suggestions || [];

      // Add local pattern-based suggestions
      const localSuggestions = await this.smartSearch.getSuggestions(query);
      suggestions.push(...localSuggestions);

      // Cache results
      this.searchCache.set(query, suggestions);

      // Display suggestions
      this.displaySearchSuggestions(suggestions);

    } catch (error) {
      console.error('Smart search error:', error);
      this.hideSearchSuggestions();
    }
  }

  displaySearchSuggestions(suggestions) {
    const suggestionsContainer = document.getElementById('search-suggestions');
    if (!suggestionsContainer) return;

    if (!suggestions || suggestions.length === 0) {
      this.hideSearchSuggestions();
      return;
    }

    suggestionsContainer.innerHTML = suggestions.map(suggestion => `
      <div class="suggestion-item" data-type="${suggestion.type}" data-value="${suggestion.value}">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <i class="fas ${this.getSuggestionIcon(suggestion.type)} mr-2 text-gray-400"></i>
            <span class="text-sm font-medium">${suggestion.label}</span>
          </div>
          <span class="text-xs text-gray-500">${suggestion.type}</span>
        </div>
        ${suggestion.description ? `<div class="text-xs text-gray-500 mt-1">${suggestion.description}</div>` : ''}
      </div>
    `).join('');

    // Add click handlers
    suggestionsContainer.querySelectorAll('.suggestion-item').forEach(item => {
      item.addEventListener('click', this.applySuggestion.bind(this));
    });

    suggestionsContainer.classList.remove('hidden');
  }

  getSuggestionIcon(type) {
    const icons = {
      'wafer': 'fa-microchip',
      'lot': 'fa-layer-group',
      'location': 'fa-map-marker-alt',
      'module': 'fa-cube',
      'cassette': 'fa-box',
      'recent': 'fa-clock',
      'filter': 'fa-filter'
    };
    return icons[type] || 'fa-search';
  }

  applySuggestion(event) {
    const item = event.currentTarget;
    const type = item.dataset.type;
    const value = item.dataset.value;

    // Apply the suggestion to appropriate field
    this.applySearchValue(type, value);
    
    // Hide suggestions
    this.hideSearchSuggestions();
    
    // Trigger search
    searchInventory();
  }

  applySearchValue(type, value) {
    const fieldMap = {
      'wafer': 'wafer-id',
      'lot': 'lot-id',
      'location': 'location-id',
      'module': 'module-name',
      'cassette': 'cassette-id',
      'xfab': 'xfab-id',
      'mask': 'mask-set-id'
    };

    const fieldId = fieldMap[type];
    if (fieldId) {
      const field = document.getElementById(fieldId);
      if (field) {
        field.value = value;
        field.focus();
      }
    }
  }

  showSearchLoading() {
    const suggestionsContainer = document.getElementById('search-suggestions');
    if (suggestionsContainer) {
      suggestionsContainer.innerHTML = `
        <div class="suggestion-item">
          <div class="flex items-center">
            <i class="fas fa-spinner fa-spin mr-2 text-blue-500"></i>
            <span class="text-sm text-gray-600">Searching...</span>
          </div>
        </div>
      `;
      suggestionsContainer.classList.remove('hidden');
    }
  }

  hideSearchSuggestions() {
    const suggestionsContainer = document.getElementById('search-suggestions');
    if (suggestionsContainer) {
      suggestionsContainer.classList.add('hidden');
    }
  }

  showSearchSuggestions() {
    const input = document.getElementById('smart-search');
    if (input && input.value.length >= 2) {
      this.performSmartSearch(input.value);
    }
  }

  handleSearchKeydown(event) {
    const suggestionsContainer = document.getElementById('search-suggestions');
    if (!suggestionsContainer || suggestionsContainer.classList.contains('hidden')) {
      return;
    }

    const suggestions = suggestionsContainer.querySelectorAll('.suggestion-item');
    let currentIndex = Array.from(suggestions).findIndex(item => 
      item.classList.contains('highlighted')
    );

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        currentIndex = Math.min(currentIndex + 1, suggestions.length - 1);
        this.highlightSuggestion(suggestions, currentIndex);
        break;
      case 'ArrowUp':
        event.preventDefault();
        currentIndex = Math.max(currentIndex - 1, 0);
        this.highlightSuggestion(suggestions, currentIndex);
        break;
      case 'Enter':
        event.preventDefault();
        if (currentIndex >= 0 && suggestions[currentIndex]) {
          suggestions[currentIndex].click();
        } else {
          searchInventory();
        }
        break;
      case 'Escape':
        this.hideSearchSuggestions();
        break;
    }
  }

  highlightSuggestion(suggestions, index) {
    suggestions.forEach((item, i) => {
      if (i === index) {
        item.classList.add('highlighted', 'bg-blue-50');
      } else {
        item.classList.remove('highlighted', 'bg-blue-50');
      }
    });
  }

  toggleAdvancedSearch() {
    const advancedFields = document.getElementById('advanced-search-fields');
    const toggleBtn = document.getElementById('toggle-advanced-search');
    
    if (advancedFields.classList.contains('hidden')) {
      advancedFields.classList.remove('hidden');
      toggleBtn.innerHTML = '<i class="fas fa-chevron-up mr-1"></i>Simple';
    } else {
      advancedFields.classList.add('hidden');
      toggleBtn.innerHTML = '<i class="fas fa-cog mr-1"></i>Advanced';
    }
  }

  applySearchPreset(event) {
    console.log('🔍 Quick filter clicked:', event.currentTarget.dataset.preset);
    const preset = event.currentTarget.dataset.preset;

    // Show advanced search fields if they're hidden (needed for date filters)
    const advancedFields = document.getElementById('advanced-search-fields');
    if (advancedFields && advancedFields.classList.contains('hidden')) {
      advancedFields.classList.remove('hidden');
      const toggleBtn = document.getElementById('toggle-advanced-search');
      if (toggleBtn) {
        toggleBtn.innerHTML = '<i class="fas fa-chevron-up mr-1"></i>Simple';
      }
      console.log('👁️ Advanced search fields shown');
    }

    // Clear existing search
    this.clearSearchFields();
    console.log('✅ Search fields cleared');

    // Apply preset filters
    switch (preset) {
      case 'recent':
        console.log('📅 Applying Recent Arrivals filter');
        this.applyRecentArrivalsFilter();
        break;
      case 'shipped':
        console.log('🚚 Applying Recently Shipped filter');
        this.applyRecentlyShippedFilter();
        break;
      case 'unshipped':
        console.log('📦 Applying Available Stock filter');
        this.applyAvailableStockFilter();
        break;
      case 'location-issues':
        console.log('⚠️ Applying Location Issues filter');
        this.applyLocationIssuesFilter();
        break;
      default:
        console.warn('❌ Unknown preset:', preset);
    }

    // Update active preset
    document.querySelectorAll('.preset-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    event.currentTarget.classList.add('active');
    console.log('🎯 Active preset updated');

    // Show a notification that the filter was applied
    if (typeof Swal !== 'undefined') {
      Swal.fire({
        title: 'Quick Filter Applied',
        text: `${this.getPresetDisplayName(preset)} filter has been applied`,
        icon: 'success',
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 2000
      });
    }

    // Trigger search
    console.log('🔍 Triggering search...');
    if (typeof searchInventory === 'function') {
      searchInventory();
      console.log('✅ Search triggered successfully');
    } else {
      console.error('❌ searchInventory function not found');
    }
  }

  getPresetDisplayName(preset) {
    const names = {
      'recent': 'Recent Arrivals',
      'shipped': 'Recently Shipped',
      'unshipped': 'Available Stock',
      'location-issues': 'Location Issues'
    };
    return names[preset] || preset;
  }

  clearSearchFields() {
    const fields = [
      'wafer-id', 'lot-id', 'xfab-id', 'mask-set-id', 'module-name',
      'cassette-id', 'slot-id', 'location-id', 'arrived-at-from',
      'arrived-at-to', 'sent-at-from', 'sent-at-to'
    ];
    
    fields.forEach(fieldId => {
      const field = document.getElementById(fieldId);
      if (field) field.value = '';
    });
  }

  applyRecentArrivalsFilter() {
    const fromDate = new Date();
    fromDate.setDate(fromDate.getDate() - 7); // Last 7 days
    const dateString = fromDate.toISOString().split('T')[0];

    const fromField = document.getElementById('arrived-at-from');
    if (fromField) {
      fromField.value = dateString;
      console.log('📅 Set arrived-at-from to:', dateString);
    } else {
      console.error('❌ arrived-at-from field not found');
    }
  }

  applyRecentlyShippedFilter() {
    const fromDate = new Date();
    fromDate.setDate(fromDate.getDate() - 7); // Last 7 days
    const dateString = fromDate.toISOString().split('T')[0];

    const fromField = document.getElementById('sent-at-from');
    if (fromField) {
      fromField.value = dateString;
      console.log('🚚 Set sent-at-from to:', dateString);
    } else {
      console.error('❌ sent-at-from field not found');
    }
  }

  applyAvailableStockFilter() {
    // Clear sent_at fields to show only unshipped items
    const sentFromField = document.getElementById('sent-at-from');
    const sentToField = document.getElementById('sent-at-to');

    if (sentFromField) {
      sentFromField.value = '';
      console.log('📦 Cleared sent-at-from field');
    } else {
      console.error('❌ sent-at-from field not found');
    }

    if (sentToField) {
      sentToField.value = '';
      console.log('📦 Cleared sent-at-to field');
    } else {
      console.error('❌ sent-at-to field not found');
    }
  }

  applyLocationIssuesFilter() {
    // This would identify items with potential location issues
    // For now, just log that it was called
    console.log('⚠️ Location Issues filter applied (placeholder implementation)');

    // You could implement specific logic here, such as:
    // - Search for items with empty location_id
    // - Search for items in specific "problem" locations
    // - etc.
  }

  openBatchPanel() {
    const panel = document.getElementById('batch-operations-panel');
    if (panel) {
      panel.classList.remove('hidden');
      this.updateBatchSummary();
    }
  }

  closeBatchPanel() {
    const panel = document.getElementById('batch-operations-panel');
    if (panel) {
      panel.classList.add('hidden');
    }
  }

  switchBatchTab(event) {
    const tabName = event.currentTarget.dataset.tab;
    
    // Update tab buttons
    document.querySelectorAll('.batch-tab').forEach(tab => {
      tab.classList.remove('active', 'border-indigo-500', 'text-indigo-600');
      tab.classList.add('border-transparent', 'text-gray-500');
    });
    
    event.currentTarget.classList.add('active', 'border-indigo-500', 'text-indigo-600');
    event.currentTarget.classList.remove('border-transparent', 'text-gray-500');
    
    // Show/hide tab content
    document.querySelectorAll('.batch-tab-content').forEach(content => {
      content.classList.add('hidden');
    });
    
    const targetContent = document.getElementById(`${tabName}-tab`);
    if (targetContent) {
      targetContent.classList.remove('hidden');
    }
    
    // Load tab-specific content
    if (tabName === 'analytics') {
      this.analytics.loadAnalytics();
    }
  }

  updateBatchSummary() {
    const selectedItems = document.querySelectorAll('input[name="inventory-item"]:checked');
    const selectedCount = selectedItems.length;
    
    // Update counts
    const countElement = document.getElementById('batch-selected-count');
    if (countElement) {
      countElement.textContent = selectedCount;
    }
    
    // Calculate unique lots and locations
    const lots = new Set();
    const locations = new Set();
    
    selectedItems.forEach(item => {
      const row = item.closest('tr');
      if (row) {
        const cells = row.querySelectorAll('td');
        if (cells.length > 2) {
          lots.add(cells[2].textContent.trim()); // Lot ID column
          locations.add(cells[8].textContent.trim()); // Location column
        }
      }
    });
    
    const lotsElement = document.getElementById('batch-unique-lots');
    if (lotsElement) {
      lotsElement.textContent = lots.size;
    }
    
    const locationsElement = document.getElementById('batch-unique-locations');
    if (locationsElement) {
      locationsElement.textContent = locations.size;
    }
  }

  selectAllVisible() {
    const checkboxes = document.querySelectorAll('#inventory-table-body input[name="inventory-item"]');
    checkboxes.forEach(checkbox => {
      checkbox.checked = true;
    });
    updateSelectedCount();
    this.updateBatchSummary();
  }

  clearSelection() {
    const checkboxes = document.querySelectorAll('input[name="inventory-item"]');
    checkboxes.forEach(checkbox => {
      checkbox.checked = false;
    });
    
    const selectAll = document.getElementById('select-all');
    if (selectAll) {
      selectAll.checked = false;
    }
    
    updateSelectedCount();
    this.updateBatchSummary();
  }



  loadSearchPresets() {
    // Load saved search presets from localStorage
    const savedPresets = localStorage.getItem('inventory-search-presets');
    if (savedPresets) {
      try {
        const presets = JSON.parse(savedPresets);
        this.addCustomPresets(presets);
      } catch (error) {
        console.error('Error loading search presets:', error);
      }
    }
  }

  addCustomPresets(presets) {
    const presetsContainer = document.getElementById('search-presets');
    if (!presetsContainer) return;
    
    presets.forEach(preset => {
      const button = document.createElement('button');
      button.className = 'preset-btn';
      button.dataset.preset = preset.id;
      button.innerHTML = `<i class="fas ${preset.icon} mr-1"></i>${preset.name}`;
      button.addEventListener('click', () => this.applyCustomPreset(preset));
      presetsContainer.appendChild(button);
    });
  }

  applyCustomPreset(preset) {
    // Apply custom preset configuration
    Object.entries(preset.filters).forEach(([field, value]) => {
      const element = document.getElementById(field);
      if (element) {
        element.value = value;
      }
    });

    searchInventory();
  }
}

/**
 * Smart Search Engine with AI-powered suggestions
 */
class SmartSearchEngine {
  constructor() {
    this.searchHistory = this.loadSearchHistory();
    this.commonPatterns = new Map();
    this.fieldSuggestions = new Map();
  }

  async getSuggestions(query) {
    const suggestions = [];

    // Add recent search suggestions
    suggestions.push(...this.getRecentSearchSuggestions(query));

    // Add field-specific suggestions
    suggestions.push(...this.getFieldSuggestions(query));

    // Add pattern-based suggestions
    suggestions.push(...this.getPatternSuggestions(query));

    // Add smart filter suggestions
    suggestions.push(...this.getSmartFilterSuggestions(query));

    return suggestions.slice(0, 8); // Limit to 8 suggestions
  }

  getRecentSearchSuggestions(query) {
    const suggestions = [];
    const lowerQuery = query.toLowerCase();

    this.searchHistory.forEach(search => {
      if (search.query.toLowerCase().includes(lowerQuery)) {
        suggestions.push({
          type: 'recent',
          label: search.query,
          value: search.query,
          description: `Used ${search.count} times`
        });
      }
    });

    return suggestions.slice(0, 3);
  }

  getFieldSuggestions(query) {
    const suggestions = [];
    const patterns = [
      { regex: /^[A-Z]{2,3}\d{4,6}$/i, type: 'wafer', description: 'Wafer ID pattern' },
      { regex: /^LOT[_-]?\d+$/i, type: 'lot', description: 'Lot ID pattern' },
      { regex: /^[A-Z]\d{2,3}$/i, type: 'cassette', description: 'Cassette ID pattern' },
      { regex: /^[A-Z]{2,4}[_-]?\d+$/i, type: 'xfab', description: 'XFAB ID pattern' },
      { regex: /^M\d{3,4}$/i, type: 'mask', description: 'Mask Set ID pattern' }
    ];

    patterns.forEach(pattern => {
      if (pattern.regex.test(query)) {
        suggestions.push({
          type: pattern.type,
          label: query,
          value: query,
          description: pattern.description
        });
      }
    });

    return suggestions;
  }

  getPatternSuggestions(query) {
    const suggestions = [];

    // Location-based suggestions
    if (query.toLowerCase().includes('clean') || query.toLowerCase().includes('room')) {
      suggestions.push({
        type: 'location',
        label: 'Clean Room',
        value: 'clean_room',
        description: 'Filter by clean room location'
      });
    }

    if (query.toLowerCase().includes('storage') || query.toLowerCase().includes('warehouse')) {
      suggestions.push({
        type: 'location',
        label: 'Storage',
        value: 'storage',
        description: 'Filter by storage location'
      });
    }

    return suggestions;
  }

  getSmartFilterSuggestions(query) {
    const suggestions = [];
    const lowerQuery = query.toLowerCase();

    // Time-based suggestions
    if (lowerQuery.includes('today') || lowerQuery.includes('recent')) {
      suggestions.push({
        type: 'filter',
        label: 'Recent Arrivals (Last 7 days)',
        value: 'recent_arrivals',
        description: 'Items arrived in the last week'
      });
    }

    if (lowerQuery.includes('shipped') || lowerQuery.includes('sent')) {
      suggestions.push({
        type: 'filter',
        label: 'Recently Shipped',
        value: 'recent_shipped',
        description: 'Items shipped in the last week'
      });
    }

    if (lowerQuery.includes('available') || lowerQuery.includes('stock')) {
      suggestions.push({
        type: 'filter',
        label: 'Available Stock',
        value: 'available_stock',
        description: 'Items not yet shipped'
      });
    }

    return suggestions;
  }

  recordSearch(query) {
    const existing = this.searchHistory.find(item => item.query === query);
    if (existing) {
      existing.count++;
      existing.lastUsed = new Date().toISOString();
    } else {
      this.searchHistory.push({
        query,
        count: 1,
        lastUsed: new Date().toISOString()
      });
    }

    // Keep only last 50 searches
    this.searchHistory = this.searchHistory
      .sort((a, b) => new Date(b.lastUsed) - new Date(a.lastUsed))
      .slice(0, 50);

    this.saveSearchHistory();
  }

  loadSearchHistory() {
    try {
      const history = localStorage.getItem('inventory-search-history');
      return history ? JSON.parse(history) : [];
    } catch (error) {
      console.error('Error loading search history:', error);
      return [];
    }
  }

  saveSearchHistory() {
    try {
      localStorage.setItem('inventory-search-history', JSON.stringify(this.searchHistory));
    } catch (error) {
      console.error('Error saving search history:', error);
    }
  }
}

/**
 * Batch Operations Manager
 */
class BatchOperationsManager {
  constructor() {
    this.currentOperation = null;
    this.progressCallback = null;
  }

  async executeBatchOperation(operation, items, options = {}) {
    this.currentOperation = {
      type: operation,
      items,
      options,
      startTime: new Date(),
      completed: 0,
      failed: 0,
      errors: []
    };

    try {
      this.showProgress();

      switch (operation) {
        case 'bulk-modify':
          await this.executeBulkModify(items, options);
          break;
        case 'bulk-location':
          await this.executeBulkLocationUpdate(items, options);
          break;
        default:
          throw new Error(`Unknown operation: ${operation}`);
      }

      this.showSuccess();

    } catch (error) {
      this.showError(error);
    } finally {
      this.currentOperation = null;
    }
  }

  async executeBulkModify(items, options) {
    const batchSize = 10; // Process in batches of 10
    const batches = this.createBatches(items, batchSize);

    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];

      try {
        await this.processBatch(batch, options);
        this.updateProgress(i + 1, batches.length);
      } catch (error) {
        this.currentOperation.errors.push({
          batch: i,
          error: error.message,
          items: batch
        });
      }
    }
  }

  async processBatch(items, options) {
    const response = await fetch('/api/inventory/bulk-modify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': getCSRFToken()
      },
      body: JSON.stringify({
        items: items.map(item => item.wafer_id),
        updates: options
      })
    });

    if (!response.ok) {
      throw new Error(`Batch operation failed: ${response.statusText}`);
    }

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.message || 'Batch operation failed');
    }

    this.currentOperation.completed += items.length;
  }

  createBatches(items, batchSize) {
    const batches = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }

  updateProgress(currentBatch, totalBatches) {
    const percentage = Math.round((currentBatch / totalBatches) * 100);

    const progressBar = document.getElementById('batch-progress-bar');
    if (progressBar) {
      progressBar.style.width = `${percentage}%`;
    }

    const progressText = document.getElementById('batch-progress-text');
    if (progressText) {
      progressText.textContent = `${this.currentOperation.completed} / ${this.currentOperation.items.length} completed`;
    }

    const progressDetails = document.getElementById('batch-progress-details');
    if (progressDetails) {
      progressDetails.textContent = `Processing batch ${currentBatch} of ${totalBatches}`;
    }
  }

  showProgress() {
    const progressContainer = document.getElementById('batch-progress');
    if (progressContainer) {
      progressContainer.classList.remove('hidden');
    }
  }

  showSuccess() {
    const { completed, failed, errors } = this.currentOperation;

    if (typeof Swal !== 'undefined') {
      Swal.fire({
        title: 'Batch Operation Complete',
        html: `
          <div class="text-left">
            <p><strong>Completed:</strong> ${completed} items</p>
            ${failed > 0 ? `<p><strong>Failed:</strong> ${failed} items</p>` : ''}
            ${errors.length > 0 ? `<p class="text-red-600 text-sm mt-2">Some errors occurred. Check console for details.</p>` : ''}
          </div>
        `,
        icon: errors.length > 0 ? 'warning' : 'success',
        confirmButtonText: 'OK'
      });
    }

    // Hide progress
    const progressContainer = document.getElementById('batch-progress');
    if (progressContainer) {
      progressContainer.classList.add('hidden');
    }

    // Refresh the inventory table
    searchInventory();
  }

  showError(error) {
    console.error('Batch operation error:', error);

    if (typeof Swal !== 'undefined') {
      Swal.fire({
        title: 'Batch Operation Failed',
        text: error.message || 'An unexpected error occurred',
        icon: 'error',
        confirmButtonText: 'OK'
      });
    }

    // Hide progress
    const progressContainer = document.getElementById('batch-progress');
    if (progressContainer) {
      progressContainer.classList.add('hidden');
    }
  }
}

/**
 * Inventory Analytics with Predictive Insights
 */
class InventoryAnalytics {
  constructor() {
    this.insights = [];
    this.predictions = [];
  }

  async loadAnalytics() {
    try {
      // Load analytics data from API
      const response = await fetch('/api/inventory/analytics', {
        method: 'GET',
        headers: {
          'X-Requested-With': 'XMLHttpRequest'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Failed to load analytics');
      }

      const analyticsData = data.analytics;

      // Generate insights from real data
      this.generateInsights(analyticsData);

      // Generate predictions from real data
      this.generatePredictions(analyticsData);

      // Display results
      this.displayInsights();
      this.displayPredictions();

    } catch (error) {
      console.error('Error loading analytics:', error);
      this.showAnalyticsError(error.message);
    }
  }

  async fetchInventoryData() {
    // This would fetch current inventory data
    // For now, we'll simulate with the current table data
    const tableRows = document.querySelectorAll('#inventory-table-body tr');
    const data = [];

    tableRows.forEach(row => {
      const cells = row.querySelectorAll('td');
      if (cells.length > 1) {
        data.push({
          wafer_id: cells[1]?.textContent?.trim(),
          lot_id: cells[2]?.textContent?.trim(),
          xfab_id: cells[3]?.textContent?.trim(),
          location: cells[8]?.textContent?.trim(),
          arrived_at: cells[9]?.textContent?.trim(),
          sent_at: cells[10]?.textContent?.trim()
        });
      }
    });

    return data;
  }

  generateInsights(analyticsData) {
    this.insights = [];

    // Location utilization analysis
    if (analyticsData.location_utilization) {
      const overutilized = analyticsData.location_utilization.filter(loc => loc.utilization > 0.8);
      if (overutilized.length > 0) {
        this.insights.push({
          type: 'warning',
          title: 'Location Capacity Issues',
          description: `${overutilized.length} locations are over 80% capacity`,
          action: 'Consider redistributing inventory',
          icon: 'fa-exclamation-triangle'
        });
      }

      // High utilization locations
      const highUtilization = analyticsData.location_utilization.filter(loc => loc.count > 50);
      if (highUtilization.length > 0) {
        this.insights.push({
          type: 'info',
          title: 'High Activity Locations',
          description: `${highUtilization.length} locations have high inventory activity`,
          action: 'Monitor for efficiency opportunities',
          icon: 'fa-warehouse'
        });
      }
    }

    // Processing efficiency insights
    if (analyticsData.processing_efficiency) {
      const avgDays = analyticsData.processing_efficiency.avg_processing_days;
      const completedItems = analyticsData.processing_efficiency.completed_items;

      this.insights.push({
        type: avgDays > 21 ? 'warning' : 'success',
        title: 'Processing Efficiency',
        description: `Average processing time: ${avgDays} days (${completedItems} items completed)`,
        action: avgDays > 21 ? 'Review processing bottlenecks' : 'Processing times are optimal',
        icon: 'fa-chart-line'
      });
    }

    // Inventory status insights
    if (analyticsData.inventory_status) {
      const { total_items, available_items, shipped_items } = analyticsData.inventory_status;
      const shipmentRate = total_items > 0 ? (shipped_items / total_items * 100).toFixed(1) : 0;

      this.insights.push({
        type: 'info',
        title: 'Inventory Overview',
        description: `${available_items} items available, ${shipmentRate}% shipment rate`,
        action: 'Inventory levels are within normal range',
        icon: 'fa-boxes'
      });
    }
  }

  generatePredictions(analyticsData) {
    this.predictions = [];

    // Capacity prediction based on current utilization
    if (analyticsData.location_utilization && analyticsData.inventory_status) {
      const totalCapacity = analyticsData.location_utilization.length * 100; // Assume 100 per location
      const currentItems = analyticsData.inventory_status.available_items;
      const utilizationRate = currentItems / totalCapacity;

      const daysToCapacity = utilizationRate > 0.9 ? 7 : Math.round((0.9 - utilizationRate) * totalCapacity / 15); // Assume 15 items/day growth

      this.predictions.push({
        type: 'capacity',
        title: 'Storage Capacity Forecast',
        description: `Estimated to reach 90% capacity in ${Math.max(daysToCapacity, 1)} days`,
        confidence: 0.75,
        recommendation: utilizationRate > 0.8 ? 'Plan for additional storage space' : 'Current capacity sufficient',
        icon: 'fa-warehouse'
      });
    }

    // Processing time prediction
    if (analyticsData.processing_efficiency) {
      const avgDays = analyticsData.processing_efficiency.avg_processing_days;
      const trend = avgDays > 21 ? 'increasing' : avgDays < 14 ? 'decreasing' : 'stable';

      this.predictions.push({
        type: 'processing',
        title: 'Processing Time Optimization',
        description: `Processing times are ${trend} (current avg: ${avgDays} days)`,
        confidence: 0.80,
        recommendation: trend === 'increasing' ? 'Review process bottlenecks' : 'Processing efficiency is good',
        icon: 'fa-cogs'
      });
    }

    // Demand prediction based on arrival trends
    if (analyticsData.arrival_trends) {
      const recentTrends = analyticsData.arrival_trends.slice(-7); // Last 7 days
      const avgDaily = recentTrends.reduce((sum, day) => sum + day.count, 0) / recentTrends.length;
      const trend = avgDaily > 20 ? 'increase' : avgDaily < 10 ? 'decrease' : 'stable demand';

      this.predictions.push({
        type: 'demand',
        title: 'Demand Forecast',
        description: `Expected ${trend} over next 30 days (avg: ${Math.round(avgDaily)} items/day)`,
        confidence: 0.65,
        recommendation: trend.includes('increase') ? 'Prepare for higher volume' : 'Current capacity sufficient',
        icon: 'fa-trending-up'
      });
    }
  }

  showAnalyticsError(message) {
    const insightsContainer = document.getElementById('inventory-insights');
    const predictionsContainer = document.getElementById('predictive-analytics');

    const errorHtml = `
      <div class="p-4 bg-red-50 dark:bg-red-900 rounded-lg border border-red-200 dark:border-red-700">
        <div class="flex items-center">
          <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
          <div>
            <h4 class="font-medium text-red-900 dark:text-red-100">Analytics Error</h4>
            <p class="text-sm text-red-700 dark:text-red-300 mt-1">${message}</p>
          </div>
        </div>
      </div>
    `;

    if (insightsContainer) {
      insightsContainer.innerHTML = errorHtml;
    }

    if (predictionsContainer) {
      predictionsContainer.innerHTML = errorHtml;
    }
  }

  analyzeLocationUtilization(data) {
    const locationCounts = {};
    data.forEach(item => {
      if (item.location && !item.sent_at) {
        locationCounts[item.location] = (locationCounts[item.location] || 0) + 1;
      }
    });

    // Assume max capacity of 100 per location for demo
    const overutilized = Object.entries(locationCounts)
      .filter(([location, count]) => count > 80)
      .map(([location]) => location);

    return { overutilized, locationCounts };
  }

  analyzeLotCompletion(data) {
    const now = new Date();
    const stagnant = [];

    data.forEach(item => {
      if (item.arrived_at && !item.sent_at) {
        const arrivedDate = new Date(item.arrived_at);
        const daysSinceArrival = (now - arrivedDate) / (1000 * 60 * 60 * 24);

        if (daysSinceArrival > 30) {
          stagnant.push(item.lot_id);
        }
      }
    });

    return { stagnant: [...new Set(stagnant)] };
  }

  analyzeEfficiency(data) {
    const processingTimes = [];

    data.forEach(item => {
      if (item.arrived_at && item.sent_at) {
        const arrived = new Date(item.arrived_at);
        const sent = new Date(item.sent_at);
        const processingTime = (sent - arrived) / (1000 * 60 * 60 * 24);
        processingTimes.push(processingTime);
      }
    });

    const avgProcessingTime = processingTimes.length > 0
      ? Math.round(processingTimes.reduce((a, b) => a + b, 0) / processingTimes.length)
      : 0;

    return {
      avgProcessingTime,
      trend: 'stable' // This would be calculated based on historical data
    };
  }

  analyzeQuality(data) {
    // Simulate quality analysis
    // In reality, this would check for patterns indicating quality issues
    const issues = Math.floor(data.length * 0.02); // 2% quality issues

    return { issues };
  }

  predictCapacityNeeds(data) {
    const currentItems = data.filter(item => !item.sent_at).length;
    const dailyArrivalRate = 15; // Simulate daily arrival rate
    const dailyShipmentRate = 12; // Simulate daily shipment rate
    const maxCapacity = 1000; // Simulate max capacity

    const netGrowthRate = dailyArrivalRate - dailyShipmentRate;
    const remainingCapacity = maxCapacity - currentItems;
    const daysToCapacity = Math.floor(remainingCapacity / netGrowthRate);

    return {
      daysToCapacity: Math.max(daysToCapacity, 1),
      confidence: 0.75
    };
  }

  predictProcessingTimes(data) {
    const currentLots = [...new Set(data.filter(item => !item.sent_at).map(item => item.lot_id))];
    const avgDays = 14; // Simulate average processing time

    return {
      avgDays,
      confidence: 0.80,
      recommendation: 'Processing times are within normal range'
    };
  }

  predictDemand(data) {
    // Simulate demand prediction
    const trends = ['increase', 'decrease', 'stable'];
    const trend = trends[Math.floor(Math.random() * trends.length)];

    return {
      trend,
      confidence: 0.65,
      recommendation: trend === 'increase' ? 'Prepare for higher volume' : 'Current capacity sufficient'
    };
  }

  displayInsights() {
    const container = document.getElementById('inventory-insights');
    if (!container) return;

    container.innerHTML = this.insights.map(insight => `
      <div class="insight-item p-3 bg-white dark:bg-gray-800 rounded-lg border-l-4 ${this.getInsightBorderColor(insight.type)}">
        <div class="flex items-start">
          <i class="fas ${insight.icon} mt-1 mr-3 ${this.getInsightIconColor(insight.type)}"></i>
          <div class="flex-1">
            <h4 class="font-medium text-gray-900 dark:text-white">${insight.title}</h4>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">${insight.description}</p>
            <p class="text-xs text-blue-600 dark:text-blue-400 mt-2">${insight.action}</p>
          </div>
        </div>
      </div>
    `).join('');
  }

  displayPredictions() {
    const container = document.getElementById('predictive-analytics');
    if (!container) return;

    container.innerHTML = this.predictions.map(prediction => `
      <div class="prediction-item p-3 bg-white dark:bg-gray-800 rounded-lg">
        <div class="flex items-start">
          <i class="fas ${prediction.icon} mt-1 mr-3 text-green-500"></i>
          <div class="flex-1">
            <h4 class="font-medium text-gray-900 dark:text-white">${prediction.title}</h4>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">${prediction.description}</p>
            <div class="flex items-center justify-between mt-2">
              <p class="text-xs text-green-600 dark:text-green-400">${prediction.recommendation}</p>
              <span class="text-xs text-gray-500">
                ${Math.round(prediction.confidence * 100)}% confidence
              </span>
            </div>
          </div>
        </div>
      </div>
    `).join('');
  }

  getInsightBorderColor(type) {
    const colors = {
      'warning': 'border-yellow-400',
      'info': 'border-blue-400',
      'success': 'border-green-400',
      'error': 'border-red-400'
    };
    return colors[type] || 'border-gray-400';
  }

  getInsightIconColor(type) {
    const colors = {
      'warning': 'text-yellow-500',
      'info': 'text-blue-500',
      'success': 'text-green-500',
      'error': 'text-red-500'
    };
    return colors[type] || 'text-gray-500';
  }
}



// Initialize the advanced inventory manager when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  window.advancedInventoryManager = new AdvancedInventoryManager();
});

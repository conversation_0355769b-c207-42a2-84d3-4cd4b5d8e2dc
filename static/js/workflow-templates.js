/**
 * Predefined Workflow Templates
 * Ready-to-use automation workflows for common scenarios
 */

const WORKFLOW_TEMPLATES = [
  {
    id: 'low-stock-alert',
    name: 'Low Stock Alert',
    description: 'Send email notification when inventory levels are low',
    category: 'Inventory Management',
    icon: 'fas fa-exclamation-triangle',
    color: 'amber',
    triggers: [
      {
        type: 'inventory.updated',
        conditions: {
          operator: 'and',
          rules: [
            {
              field: 'quantity',
              operator: 'less_than',
              value: 10
            },
            {
              field: 'location_id',
              operator: 'exists'
            }
          ]
        }
      }
    ],
    actions: [
      {
        type: 'email.send',
        parameters: {
          to: '{{variables.alertEmail}}',
          subject: 'Low Stock Alert: {{eventData.wafer_id}}',
          body: 'Inventory item {{eventData.wafer_id}} is running low with only {{eventData.quantity}} units remaining.',
          template: 'low_stock_alert'
        }
      },
      {
        type: 'notification.show',
        parameters: {
          title: 'Low Stock Alert',
          message: 'Item {{eventData.wafer_id}} is running low',
          type: 'warning',
          timer: 5000
        }
      }
    ],
    variables: {
      alertEmail: '<EMAIL>',
      threshold: 10
    }
  },

  {
    id: 'shipment-notification',
    name: 'Shipment Notification',
    description: 'Notify stakeholders when items are shipped',
    category: 'Shipping',
    icon: 'fas fa-shipping-fast',
    color: 'blue',
    triggers: [
      {
        type: 'shipment.created',
        conditions: null
      }
    ],
    actions: [
      {
        type: 'email.send',
        parameters: {
          to: '{{variables.customerEmail}}',
          cc: '{{variables.internalEmail}}',
          subject: 'Shipment Confirmation - {{eventData.tracking_number}}',
          body: 'Your order has been shipped. Tracking number: {{eventData.tracking_number}}',
          template: 'shipment_confirmation'
        }
      },
      {
        type: 'api.call',
        parameters: {
          url: '{{variables.webhookUrl}}',
          method: 'POST',
          body: {
            event: 'shipment_created',
            data: '{{eventData}}'
          }
        }
      }
    ],
    variables: {
      customerEmail: '<EMAIL>',
      internalEmail: '<EMAIL>',
      webhookUrl: 'https://api.company.com/webhooks/shipment'
    }
  },

  {
    id: 'location-change-audit',
    name: 'Location Change Audit',
    description: 'Log and audit all location changes for compliance',
    category: 'Compliance',
    icon: 'fas fa-map-marker-alt',
    color: 'green',
    triggers: [
      {
        type: 'location.changed',
        conditions: null
      }
    ],
    actions: [
      {
        type: 'system.log',
        parameters: {
          message: 'Location changed for {{eventData.wafer_id}} from {{eventData.old_location}} to {{eventData.new_location}}',
          level: 'info',
          serverLog: true
        }
      },
      {
        type: 'database.update',
        parameters: {
          table: 'audit_log',
          operation: 'insert',
          data: {
            event_type: 'location_change',
            wafer_id: '{{eventData.wafer_id}}',
            old_location: '{{eventData.old_location}}',
            new_location: '{{eventData.new_location}}',
            user_id: '{{eventData.user_id}}',
            timestamp: '{{timestamp}}'
          }
        }
      }
    ],
    variables: {}
  },

  {
    id: 'daily-inventory-report',
    name: 'Daily Inventory Report',
    description: 'Generate and send daily inventory summary report',
    category: 'Reporting',
    icon: 'fas fa-chart-bar',
    color: 'purple',
    triggers: [
      {
        type: 'time.daily',
        conditions: {
          operator: 'and',
          rules: [
            {
              field: 'timestamp',
              operator: 'time_of_day',
              value: { start: 8, end: 9 }
            },
            {
              field: 'timestamp',
              operator: 'day_of_week',
              value: [1, 2, 3, 4, 5] // Monday to Friday
            }
          ]
        }
      }
    ],
    actions: [
      {
        type: 'api.call',
        parameters: {
          url: '/api/reports/inventory/daily',
          method: 'GET'
        }
      },
      {
        type: 'email.send',
        parameters: {
          to: '{{variables.reportRecipients}}',
          subject: 'Daily Inventory Report - {{timestamp}}',
          body: 'Please find attached the daily inventory report.',
          template: 'daily_report',
          attachments: ['daily_inventory_report.pdf']
        }
      }
    ],
    variables: {
      reportRecipients: '<EMAIL>,<EMAIL>'
    }
  },

  {
    id: 'quality-hold-alert',
    name: 'Quality Hold Alert',
    description: 'Alert quality team when items are placed on hold',
    category: 'Quality Control',
    icon: 'fas fa-pause-circle',
    color: 'red',
    triggers: [
      {
        type: 'inventory.updated',
        conditions: {
          operator: 'and',
          rules: [
            {
              field: 'status',
              operator: 'equals',
              value: 'quality_hold'
            }
          ]
        }
      }
    ],
    actions: [
      {
        type: 'notification.show',
        parameters: {
          title: 'Quality Hold Alert',
          message: 'Item {{eventData.wafer_id}} has been placed on quality hold',
          type: 'error',
          timer: 0
        }
      },
      {
        type: 'email.send',
        parameters: {
          to: '{{variables.qualityTeam}}',
          subject: 'URGENT: Quality Hold - {{eventData.wafer_id}}',
          body: 'Item {{eventData.wafer_id}} has been placed on quality hold. Immediate attention required.',
          template: 'quality_hold_alert'
        }
      },
      {
        type: 'system.delay',
        parameters: {
          seconds: 300
        }
      },
      {
        type: 'api.call',
        parameters: {
          url: '/api/quality/escalate',
          method: 'POST',
          body: {
            wafer_id: '{{eventData.wafer_id}}',
            reason: 'quality_hold',
            timestamp: '{{timestamp}}'
          }
        }
      }
    ],
    variables: {
      qualityTeam: '<EMAIL>'
    }
  },

  {
    id: 'expiry-warning',
    name: 'Expiry Date Warning',
    description: 'Warn about items approaching expiry date',
    category: 'Inventory Management',
    icon: 'fas fa-calendar-times',
    color: 'orange',
    triggers: [
      {
        type: 'time.daily',
        conditions: {
          operator: 'and',
          rules: [
            {
              field: 'timestamp',
              operator: 'time_of_day',
              value: { start: 9, end: 10 }
            }
          ]
        }
      }
    ],
    preConditions: {
      operator: 'and',
      rules: [
        {
          field: 'expiry_date',
          operator: 'date_between',
          value: {
            start: '{{timestamp}}',
            end: '{{variables.warningDays}}'
          }
        }
      ]
    },
    actions: [
      {
        type: 'email.send',
        parameters: {
          to: '{{variables.inventoryManager}}',
          subject: 'Expiry Warning: Items expiring soon',
          body: 'The following items are approaching their expiry date and require attention.',
          template: 'expiry_warning'
        }
      }
    ],
    variables: {
      inventoryManager: '<EMAIL>',
      warningDays: 30
    }
  },

  {
    id: 'auto-reorder',
    name: 'Automatic Reorder',
    description: 'Automatically create purchase orders for low stock items',
    category: 'Procurement',
    icon: 'fas fa-shopping-cart',
    color: 'indigo',
    triggers: [
      {
        type: 'inventory.updated',
        conditions: {
          operator: 'and',
          rules: [
            {
              field: 'quantity',
              operator: 'less_equal',
              value: '{{variables.reorderPoint}}'
            },
            {
              field: 'auto_reorder',
              operator: 'equals',
              value: true
            }
          ]
        }
      }
    ],
    actions: [
      {
        type: 'api.call',
        parameters: {
          url: '/api/procurement/create-order',
          method: 'POST',
          body: {
            item_id: '{{eventData.wafer_id}}',
            quantity: '{{variables.reorderQuantity}}',
            supplier: '{{eventData.supplier}}',
            priority: 'normal'
          }
        }
      },
      {
        type: 'email.send',
        parameters: {
          to: '{{variables.procurementTeam}}',
          subject: 'Auto Reorder Created: {{eventData.wafer_id}}',
          body: 'An automatic reorder has been created for {{eventData.wafer_id}} with quantity {{variables.reorderQuantity}}.',
          template: 'auto_reorder_notification'
        }
      },
      {
        type: 'system.log',
        parameters: {
          message: 'Auto reorder created for {{eventData.wafer_id}}',
          level: 'info',
          serverLog: true
        }
      }
    ],
    variables: {
      reorderPoint: 5,
      reorderQuantity: 50,
      procurementTeam: '<EMAIL>'
    }
  },

  {
    id: 'temperature-alert',
    name: 'Temperature Alert',
    description: 'Alert when storage temperature is out of range',
    category: 'Environmental Monitoring',
    icon: 'fas fa-thermometer-half',
    color: 'cyan',
    triggers: [
      {
        type: 'sensor.temperature',
        conditions: {
          operator: 'or',
          rules: [
            {
              field: 'temperature',
              operator: 'greater_than',
              value: '{{variables.maxTemp}}'
            },
            {
              field: 'temperature',
              operator: 'less_than',
              value: '{{variables.minTemp}}'
            }
          ]
        }
      }
    ],
    actions: [
      {
        type: 'notification.show',
        parameters: {
          title: 'Temperature Alert',
          message: 'Storage temperature is {{eventData.temperature}}°C - outside safe range',
          type: 'error',
          timer: 0
        }
      },
      {
        type: 'email.send',
        parameters: {
          to: '{{variables.facilityTeam}}',
          subject: 'URGENT: Temperature Alert - {{eventData.location}}',
          body: 'Temperature in {{eventData.location}} is {{eventData.temperature}}°C, outside the safe range of {{variables.minTemp}}-{{variables.maxTemp}}°C.',
          template: 'temperature_alert'
        }
      },
      {
        type: 'api.call',
        parameters: {
          url: '/api/alerts/temperature',
          method: 'POST',
          body: {
            location: '{{eventData.location}}',
            temperature: '{{eventData.temperature}}',
            threshold_min: '{{variables.minTemp}}',
            threshold_max: '{{variables.maxTemp}}',
            severity: 'high'
          }
        }
      }
    ],
    variables: {
      minTemp: 18,
      maxTemp: 25,
      facilityTeam: '<EMAIL>'
    }
  },

  {
    id: 'odoo-inventory-sync',
    name: 'ODOO Inventory Sync',
    description: 'Synchronize inventory changes with ODOO ERP system',
    category: 'ERP Integration',
    icon: 'fas fa-sync-alt',
    color: 'teal',
    triggers: [
      {
        type: 'inventory.updated',
        conditions: null
      }
    ],
    actions: [
      {
        type: 'odoo.search_records',
        parameters: {
          model: 'product.product',
          domain: [['default_code', '=', '{{eventData.wafer_id}}']],
          fields: ['id', 'qty_available']
        }
      },
      {
        type: 'odoo.update_record',
        parameters: {
          model: 'stock.quant',
          record_id: '{{variables.quantId}}',
          data: {
            quantity: '{{eventData.quantity}}',
            location_id: '{{variables.odooLocationId}}'
          }
        }
      },
      {
        type: 'system.log',
        parameters: {
          message: 'Inventory synced to ODOO for {{eventData.wafer_id}}',
          level: 'info',
          serverLog: true
        }
      }
    ],
    variables: {
      quantId: null,
      odooLocationId: 8
    }
  },

  {
    id: 'odoo-purchase-order',
    name: 'ODOO Purchase Order Creation',
    description: 'Create purchase orders in ODOO when stock is low',
    category: 'ERP Integration',
    icon: 'fas fa-shopping-cart',
    color: 'emerald',
    triggers: [
      {
        type: 'inventory.updated',
        conditions: {
          operator: 'and',
          rules: [
            {
              field: 'quantity',
              operator: 'less_equal',
              value: '{{variables.reorderPoint}}'
            }
          ]
        }
      }
    ],
    actions: [
      {
        type: 'odoo.search_records',
        parameters: {
          model: 'product.product',
          domain: [['default_code', '=', '{{eventData.wafer_id}}']],
          fields: ['id', 'seller_ids']
        }
      },
      {
        type: 'odoo.create_record',
        parameters: {
          model: 'purchase.order',
          data: {
            partner_id: '{{variables.supplierId}}',
            order_line: [{
              'product_id': '{{variables.productId}}',
              'product_qty': '{{variables.orderQuantity}}',
              'price_unit': '{{variables.unitPrice}}'
            }]
          }
        }
      },
      {
        type: 'email.send',
        parameters: {
          to: '{{variables.procurementEmail}}',
          subject: 'Purchase Order Created in ODOO: {{eventData.wafer_id}}',
          body: 'A purchase order has been automatically created in ODOO for {{eventData.wafer_id}} with quantity {{variables.orderQuantity}}.',
          template: 'purchase_order_notification'
        }
      }
    ],
    variables: {
      reorderPoint: 10,
      orderQuantity: 100,
      supplierId: 1,
      productId: null,
      unitPrice: 0,
      procurementEmail: '<EMAIL>'
    }
  },

  {
    id: 'odoo-quality-inspection',
    name: 'ODOO Quality Inspection',
    description: 'Create quality inspection records in ODOO',
    category: 'Quality Control',
    icon: 'fas fa-clipboard-check',
    color: 'violet',
    triggers: [
      {
        type: 'inventory.updated',
        conditions: {
          operator: 'and',
          rules: [
            {
              field: 'status',
              operator: 'equals',
              value: 'quality_check'
            }
          ]
        }
      }
    ],
    actions: [
      {
        type: 'odoo.create_record',
        parameters: {
          model: 'quality.check',
          data: {
            product_id: '{{variables.productId}}',
            lot_id: '{{eventData.lot_id}}',
            quality_state: 'none',
            user_id: '{{variables.qualityUserId}}',
            team_id: '{{variables.qualityTeamId}}'
          }
        }
      },
      {
        type: 'asana.create_task',
        parameters: {
          name: 'Quality Inspection: {{eventData.wafer_id}}',
          notes: 'Quality inspection required for wafer {{eventData.wafer_id}} in lot {{eventData.lot_id}}',
          project_gid: '{{variables.asanaProjectId}}',
          assignee: '{{variables.qualityInspector}}'
        }
      }
    ],
    variables: {
      productId: null,
      qualityUserId: 1,
      qualityTeamId: 1,
      asanaProjectId: 'project_123',
      qualityInspector: '<EMAIL>'
    }
  },

  {
    id: 'smart-wafer-validation',
    name: 'Smart Wafer Validation',
    description: 'Prevent duplicate wafer entries by checking Icarium before manual addition',
    category: 'Inventory Management',
    icon: 'fas fa-shield-alt',
    color: 'blue',
    triggers: [
      {
        type: 'wafer.before_add',
        conditions: {
          operator: 'and',
          rules: [
            {
              field: 'wafer_id',
              operator: 'exists'
            }
          ]
        }
      }
    ],
    actions: [
      {
        type: 'icarium.check_duplicate',
        parameters: {
          wafer_id: '{{eventData.wafer_id}}',
          show_suggestion: true,
          auto_sync_option: true
        }
      },
      {
        type: 'notification.show',
        parameters: {
          title: 'Wafer Validation',
          message: 'Checking wafer {{eventData.wafer_id}} in Icarium...',
          type: 'info',
          timer: 2000
        }
      }
    ],
    variables: {
      enable_auto_sync: true,
      show_details: true,
      cache_duration: 300
    }
  },

  {
    id: 'daily-new-wafer-notifications',
    name: 'Daily New Wafer Notifications',
    description: 'Automatically notify users about new wafers in Icarium that need syncing',
    category: 'Inventory Management',
    icon: 'fas fa-bell',
    color: 'orange',
    triggers: [
      {
        type: 'schedule.daily',
        conditions: {
          time: '09:00',
          timezone: 'Europe/Paris'
        }
      }
    ],
    actions: [
      {
        type: 'icarium.check_new_wafers',
        parameters: {
          hours_back: 24,
          send_notification: true,
          notification_threshold: 1
        }
      },
      {
        type: 'email.send',
        parameters: {
          to: ['<EMAIL>'],
          subject: 'Daily Wafer Sync Report - {{date}}',
          template: 'daily_wafer_report',
          data: {
            new_wafers_count: '{{newWafersCount}}',
            unsynced_count: '{{unsyncedCount}}',
            lots_affected: '{{lotsAffected}}'
          }
        }
      },
      {
        type: 'dashboard.notification',
        parameters: {
          title: 'Daily Wafer Report',
          message: '{{unsyncedCount}} wafers need syncing from Icarium',
          type: 'info',
          priority: 'medium',
          expires_hours: 24
        }
      }
    ],
    variables: {
      notification_threshold: 1,
      include_weekend: false,
      email_recipients: ['<EMAIL>'],
      dashboard_notification: true
    }
  },

  {
    id: 'hourly-urgent-wafer-alerts',
    name: 'Hourly Urgent Wafer Alerts',
    description: 'Send urgent alerts when large batches of wafers are added to Icarium',
    category: 'Inventory Management',
    icon: 'fas fa-exclamation-triangle',
    color: 'red',
    triggers: [
      {
        type: 'schedule.hourly',
        conditions: {
          minute: 0
        }
      }
    ],
    actions: [
      {
        type: 'icarium.check_new_wafers',
        parameters: {
          hours_back: 1,
          urgent_threshold: 10
        }
      },
      {
        type: 'notification.urgent',
        parameters: {
          title: '🚨 Urgent: Large Wafer Batch Detected',
          message: '{{unsyncedCount}} new wafers detected in the last hour',
          type: 'warning',
          priority: 'urgent',
          auto_dismiss: false
        },
        conditions: {
          field: 'unsyncedCount',
          operator: 'greater_than',
          value: 10
        }
      },
      {
        type: 'asana.create_task',
        parameters: {
          project_id: 'inventory_management',
          title: 'Urgent: Sync {{unsyncedCount}} new wafers',
          description: 'Large batch of wafers detected in Icarium. Immediate sync required.',
          priority: 'high',
          assignee: 'inventory_team'
        },
        conditions: {
          field: 'unsyncedCount',
          operator: 'greater_than',
          value: 20
        }
      }
    ],
    variables: {
      urgent_threshold: 10,
      critical_threshold: 20,
      create_asana_task: true,
      notify_management: false
    }
  },

  {
    id: 'smart-batch-sync-optimizer',
    name: 'Smart Batch Sync Optimizer',
    description: 'Automatically execute batch sync recommendations when efficiency thresholds are met',
    category: 'Inventory Management',
    icon: 'fas fa-layer-group',
    color: 'purple',
    triggers: [
      {
        type: 'schedule.daily',
        conditions: {
          time: '10:00',
          weekdays_only: true
        }
      }
    ],
    actions: [
      {
        type: 'smart_sync.analyze',
        parameters: {
          hours_back: 24,
          min_batch_size: 5,
          efficiency_threshold: 70
        }
      },
      {
        type: 'smart_sync.execute_batch_recommendations',
        parameters: {
          auto_execute: true,
          max_concurrent: 3,
          priority_threshold: 0.7
        },
        conditions: {
          field: 'efficiency_score',
          operator: 'greater_than',
          value: 70
        }
      },
      {
        type: 'notification.success',
        parameters: {
          title: '🚀 Smart Batch Sync Completed',
          message: 'Executed {{batch_count}} batch sync operations with {{success_rate}}% success rate',
          type: 'success',
          priority: 'medium'
        }
      }
    ],
    variables: {
      min_batch_size: 5,
      efficiency_threshold: 70,
      auto_execute_enabled: true,
      max_concurrent_batches: 3
    }
  },

  {
    id: 'predictive-sync-patterns',
    name: 'Predictive Sync Patterns',
    description: 'Learn from sync patterns to predict optimal sync times and suggest proactive actions',
    category: 'AI & Analytics',
    icon: 'fas fa-chart-line',
    color: 'teal',
    triggers: [
      {
        type: 'schedule.weekly',
        conditions: {
          day: 'monday',
          time: '07:00'
        }
      }
    ],
    actions: [
      {
        type: 'analytics.analyze_patterns',
        parameters: {
          lookback_weeks: 4,
          pattern_types: ['timing', 'volume', 'location', 'lot_size'],
          confidence_threshold: 0.8
        }
      },
      {
        type: 'prediction.generate_forecast',
        parameters: {
          forecast_days: 7,
          include_recommendations: true,
          alert_on_anomalies: true
        }
      },
      {
        type: 'dashboard.update_insights',
        parameters: {
          widget: 'sync_predictions',
          data: '{{forecast_data}}',
          expires_days: 7
        }
      }
    ],
    variables: {
      lookback_weeks: 4,
      confidence_threshold: 0.8,
      enable_predictions: true,
      anomaly_detection: true
    }
  },

  {
    id: 'location-priority-sync',
    name: 'Location Priority Sync',
    description: 'Prioritize sync operations based on location criticality and wafer volume',
    category: 'Inventory Management',
    icon: 'fas fa-map-marker-alt',
    color: 'green',
    triggers: [
      {
        type: 'inventory.location_threshold',
        conditions: {
          operator: 'and',
          rules: [
            {
              field: 'unsynced_count',
              operator: 'greater_than',
              value: 10
            },
            {
              field: 'location_priority',
              operator: 'greater_than',
              value: 0.7
            }
          ]
        }
      }
    ],
    actions: [
      {
        type: 'smart_sync.location_analysis',
        parameters: {
          location: '{{eventData.location}}',
          priority_boost: 0.2,
          urgent_threshold: 20
        }
      },
      {
        type: 'sync.execute_location',
        parameters: {
          location: '{{eventData.location}}',
          batch_mode: true,
          verify_before_sync: true
        },
        conditions: {
          field: 'location_priority',
          operator: 'greater_than',
          value: 0.8
        }
      },
      {
        type: 'notification.location_alert',
        parameters: {
          title: '📍 High Priority Location Sync',
          message: 'Location {{eventData.location}} has {{unsynced_count}} unsynced wafers',
          type: 'warning',
          action_url: '/inventory?location={{eventData.location}}'
        }
      }
    ],
    variables: {
      location_priorities: {
        'Ligentec FR': 1.0,
        'Production': 0.9,
        'QC': 0.8,
        'Storage': 0.6
      },
      urgent_threshold: 20,
      auto_sync_critical: true
    }
  },

  {
    id: 'efficiency-monitoring',
    name: 'Sync Efficiency Monitoring',
    description: 'Monitor sync efficiency and suggest optimizations for better performance',
    category: 'Performance',
    icon: 'fas fa-tachometer-alt',
    color: 'orange',
    triggers: [
      {
        type: 'schedule.hourly',
        conditions: {
          business_hours_only: true
        }
      }
    ],
    actions: [
      {
        type: 'efficiency.calculate_metrics',
        parameters: {
          time_window: 'last_hour',
          include_trends: true,
          benchmark_against: 'last_week'
        }
      },
      {
        type: 'optimization.suggest_improvements',
        parameters: {
          min_improvement: 10,
          focus_areas: ['batch_size', 'timing', 'location_grouping'],
          generate_action_plan: true
        },
        conditions: {
          field: 'efficiency_score',
          operator: 'less_than',
          value: 80
        }
      },
      {
        type: 'alert.efficiency_degradation',
        parameters: {
          title: '⚠️ Sync Efficiency Alert',
          message: 'Sync efficiency dropped to {{efficiency_score}}% (target: 80%+)',
          type: 'warning',
          priority: 'high'
        },
        conditions: {
          field: 'efficiency_score',
          operator: 'less_than',
          value: 70
        }
      }
    ],
    variables: {
      target_efficiency: 80,
      alert_threshold: 70,
      optimization_enabled: true,
      track_trends: true
    }
  },

  {
    id: 'odoo-shipment-tracking',
    name: 'ODOO Shipment Tracking',
    description: 'Update ODOO delivery orders with tracking information',
    category: 'Shipping',
    icon: 'fas fa-truck-loading',
    color: 'sky',
    triggers: [
      {
        type: 'shipment.created',
        conditions: null
      }
    ],
    actions: [
      {
        type: 'odoo.search_records',
        parameters: {
          model: 'stock.picking',
          domain: [['origin', '=', '{{eventData.order_reference}}']],
          fields: ['id', 'state']
        }
      },
      {
        type: 'odoo.update_record',
        parameters: {
          model: 'stock.picking',
          record_id: '{{variables.pickingId}}',
          data: {
            carrier_tracking_ref: '{{eventData.tracking_number}}',
            state: 'done'
          }
        }
      },
      {
        type: 'odoo.call_method',
        parameters: {
          model: 'stock.picking',
          method: 'action_done',
          args: ['{{variables.pickingId}}']
        }
      }
    ],
    variables: {
      pickingId: null
    }
  }
];

/**
 * Get all workflow templates
 */
function getWorkflowTemplates() {
  return WORKFLOW_TEMPLATES;
}

/**
 * Get workflow template by ID
 */
function getWorkflowTemplate(id) {
  return WORKFLOW_TEMPLATES.find(template => template.id === id);
}

/**
 * Get workflow templates by category
 */
function getWorkflowTemplatesByCategory(category) {
  return WORKFLOW_TEMPLATES.filter(template => template.category === category);
}

/**
 * Get all workflow categories
 */
function getWorkflowCategories() {
  const categories = [...new Set(WORKFLOW_TEMPLATES.map(template => template.category))];
  return categories.sort();
}

/**
 * Create workflow from template
 */
function createWorkflowFromTemplate(templateId, customizations = {}) {
  const template = getWorkflowTemplate(templateId);
  if (!template) {
    throw new Error(`Template not found: ${templateId}`);
  }

  const workflow = {
    id: 'wf_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
    name: customizations.name || template.name,
    description: customizations.description || template.description,
    enabled: customizations.enabled !== undefined ? customizations.enabled : true,
    triggers: JSON.parse(JSON.stringify(template.triggers)),
    actions: JSON.parse(JSON.stringify(template.actions)),
    preConditions: template.preConditions ? JSON.parse(JSON.stringify(template.preConditions)) : null,
    variables: { ...template.variables, ...customizations.variables }
  };

  return workflow;
}

/**
 * Smart Wafer Validation System
 * Prevents duplicate entries and suggests smart actions
 */

class SmartWaferValidator {
  constructor() {
    this.isValidating = false;
    this.validationCache = new Map();
    this.init();
  }

  init() {
    this.setupWaferInputValidation();
    this.setupBulkValidation();
    console.log('🧠 Smart Wafer Validator initialized');
  }

  /**
   * Setup real-time validation for wafer input fields
   */
  setupWaferInputValidation() {
    // Find wafer ID input fields and add validation
    const waferInputs = document.querySelectorAll('input[name*="wafer"], input[id*="wafer"]');
    
    waferInputs.forEach(input => {
      // Add debounced validation
      let validationTimeout;
      
      input.addEventListener('input', (e) => {
        clearTimeout(validationTimeout);
        validationTimeout = setTimeout(() => {
          this.validateWaferInput(e.target);
        }, 500); // 500ms debounce
      });

      // Add blur validation for immediate feedback
      input.addEventListener('blur', (e) => {
        this.validateWaferInput(e.target);
      });
    });
  }

  /**
   * Setup bulk validation for multiple wafer entries
   */
  setupBulkValidation() {
    // Add validation to forms that might contain multiple wafers
    const forms = document.querySelectorAll('form[data-wafer-form], .wafer-form');
    
    forms.forEach(form => {
      form.addEventListener('submit', (e) => {
        this.validateFormBeforeSubmit(e);
      });
    });
  }

  /**
   * Validate individual wafer input
   */
  async validateWaferInput(input) {
    const waferId = input.value.trim();
    
    if (!waferId || waferId.length < 3) {
      this.clearValidationStatus(input);
      return;
    }

    // Check cache first
    if (this.validationCache.has(waferId)) {
      const cachedResult = this.validationCache.get(waferId);
      this.showValidationResult(input, cachedResult);
      return;
    }

    // Show loading state
    this.showValidationLoading(input);

    try {
      const result = await this.checkWaferExists(waferId);
      
      // Cache result for 5 minutes
      this.validationCache.set(waferId, result);
      setTimeout(() => this.validationCache.delete(waferId), 5 * 60 * 1000);
      
      this.showValidationResult(input, result);
      
    } catch (error) {
      console.error('❌ Validation error:', error);
      this.showValidationError(input, 'Validation failed');
    }
  }

  /**
   * Check if wafer exists via API
   */
  async checkWaferExists(waferId) {
    const response = await fetch('/api/icarium/check-wafer-exists', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': this.getCSRFToken()
      },
      body: JSON.stringify({ wafer_id: waferId })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  }

  /**
   * Show validation result with smart suggestions
   */
  showValidationResult(input, result) {
    this.clearValidationStatus(input);
    
    const container = this.getOrCreateValidationContainer(input);
    const recommendation = result.recommendation;
    
    let icon, message, actionButton = '';
    
    switch (recommendation.action) {
      case 'sync_recommended':
        icon = '⚠️';
        message = `${recommendation.message}`;
        actionButton = `<button type="button" class="btn-sync-wafer" data-wafer-id="${result.wafer_id}">
          <i class="fas fa-sync"></i> Sync from Icarium
        </button>`;
        container.className = 'validation-container warning';
        break;
        
      case 'already_synced':
        icon = 'ℹ️';
        message = recommendation.message;
        container.className = 'validation-container info';
        break;
        
      case 'conflict':
        icon = '❌';
        message = recommendation.message;
        container.className = 'validation-container error';
        break;
        
      case 'safe_to_add':
        icon = '✅';
        message = 'Safe to add - wafer not found in either system';
        container.className = 'validation-container success';
        break;
        
      default:
        icon = '❓';
        message = recommendation.message;
        container.className = 'validation-container info';
    }
    
    container.innerHTML = `
      <div class="validation-message">
        <span class="validation-icon">${icon}</span>
        <span class="validation-text">${message}</span>
      </div>
      ${actionButton}
      ${this.createDetailsButton(result)}
    `;
    
    // Setup action button handlers
    this.setupActionButtons(container, result);
  }

  /**
   * Create details button for more information
   */
  createDetailsButton(result) {
    if (result.exists_in_icarium || result.exists_in_talaria) {
      return `<button type="button" class="btn-details" data-wafer-data='${JSON.stringify(result)}'>
        <i class="fas fa-info-circle"></i> Details
      </button>`;
    }
    return '';
  }

  /**
   * Setup action button event handlers
   */
  setupActionButtons(container, result) {
    // Sync button
    const syncBtn = container.querySelector('.btn-sync-wafer');
    if (syncBtn) {
      syncBtn.addEventListener('click', () => {
        this.handleSyncWafer(result.wafer_id);
      });
    }
    
    // Details button
    const detailsBtn = container.querySelector('.btn-details');
    if (detailsBtn) {
      detailsBtn.addEventListener('click', () => {
        this.showWaferDetails(result);
      });
    }
  }

  /**
   * Handle wafer sync from Icarium
   */
  async handleSyncWafer(waferId) {
    if (!confirm(`Sync wafer ${waferId} from Icarium to Talaria inventory?`)) {
      return;
    }

    try {
      const response = await fetch('/api/icarium/sync-wafer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': this.getCSRFToken()
        },
        body: JSON.stringify({ wafer_id: waferId })
      });

      const result = await response.json();
      
      if (result.success) {
        this.showSuccessMessage(`✅ Wafer ${waferId} synced successfully!`);
        // Clear cache and refresh validation
        this.validationCache.delete(waferId);
        // Optionally refresh the page or update the UI
        setTimeout(() => window.location.reload(), 2000);
      } else {
        this.showErrorMessage(`❌ Sync failed: ${result.message}`);
      }
      
    } catch (error) {
      console.error('❌ Sync error:', error);
      this.showErrorMessage('❌ Sync failed due to network error');
    }
  }

  /**
   * Show detailed wafer information
   */
  showWaferDetails(result) {
    const details = [];
    
    if (result.exists_in_icarium && result.icarium_data) {
      details.push(`
        <h4>📊 Icarium Data:</h4>
        <ul>
          <li><strong>Wafer ID:</strong> ${result.icarium_data.wafer_id}</li>
          <li><strong>Lot ID:</strong> ${result.icarium_data.lot_id}</li>
          <li><strong>Location:</strong> ${result.icarium_data.location_id}</li>
          <li><strong>Status:</strong> ${result.icarium_data.status}</li>
          <li><strong>Created:</strong> ${new Date(result.icarium_data.created_at).toLocaleString()}</li>
        </ul>
      `);
    }
    
    if (result.exists_in_talaria && result.talaria_data) {
      details.push(`
        <h4>📦 Talaria Data:</h4>
        <ul>
          <li><strong>Wafer ID:</strong> ${result.talaria_data.wafer_id}</li>
          <li><strong>Lot:</strong> ${result.talaria_data.lot}</li>
          <li><strong>Location:</strong> ${result.talaria_data.location}</li>
          <li><strong>Status:</strong> ${result.talaria_data.status}</li>
          <li><strong>Arrival:</strong> ${new Date(result.talaria_data.arrival_date).toLocaleString()}</li>
        </ul>
      `);
    }
    
    // Show modal with details
    if (typeof Swal !== 'undefined') {
      Swal.fire({
        title: `🔍 Wafer Details: ${result.wafer_id}`,
        html: details.join('<br>'),
        width: '600px',
        confirmButtonText: 'Close'
      });
    } else {
      alert(details.join('\n\n'));
    }
  }

  /**
   * Validate entire form before submission
   */
  async validateFormBeforeSubmit(event) {
    const form = event.target;
    const waferInputs = form.querySelectorAll('input[name*="wafer"], input[id*="wafer"]');
    
    if (waferInputs.length === 0) return; // No wafer inputs to validate
    
    const waferIds = Array.from(waferInputs)
      .map(input => input.value.trim())
      .filter(id => id.length > 0);
    
    if (waferIds.length === 0) return; // No wafer IDs to validate
    
    // Prevent form submission while validating
    event.preventDefault();
    
    try {
      const conflicts = await this.checkMultipleWafers(waferIds);
      
      if (conflicts.length > 0) {
        this.showConflictDialog(conflicts, () => {
          // User chose to proceed anyway
          form.submit();
        });
      } else {
        // No conflicts, proceed with submission
        form.submit();
      }
      
    } catch (error) {
      console.error('❌ Bulk validation error:', error);
      // Allow submission on validation error
      form.submit();
    }
  }

  /**
   * Check multiple wafers for conflicts
   */
  async checkMultipleWafers(waferIds) {
    const conflicts = [];
    
    for (const waferId of waferIds) {
      try {
        const result = await this.checkWaferExists(waferId);
        
        if (result.recommendation.action === 'sync_recommended' || 
            result.recommendation.action === 'already_synced') {
          conflicts.push({
            wafer_id: waferId,
            recommendation: result.recommendation
          });
        }
      } catch (error) {
        console.warn(`⚠️ Could not validate wafer ${waferId}:`, error);
      }
    }
    
    return conflicts;
  }

  /**
   * Show conflict resolution dialog
   */
  showConflictDialog(conflicts, proceedCallback) {
    const conflictList = conflicts.map(c => 
      `• ${c.wafer_id}: ${c.recommendation.message}`
    ).join('<br>');
    
    if (typeof Swal !== 'undefined') {
      Swal.fire({
        title: '⚠️ Wafer Conflicts Detected',
        html: `
          <p>The following wafers have potential issues:</p>
          <div style="text-align: left; margin: 15px 0;">
            ${conflictList}
          </div>
          <p>Do you want to proceed anyway?</p>
        `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Proceed Anyway',
        cancelButtonText: 'Cancel',
        confirmButtonColor: '#f59e0b'
      }).then((result) => {
        if (result.isConfirmed) {
          proceedCallback();
        }
      });
    } else {
      if (confirm(`Wafer conflicts detected:\n${conflicts.map(c => c.wafer_id).join(', ')}\n\nProceed anyway?`)) {
        proceedCallback();
      }
    }
  }

  // Utility methods
  showValidationLoading(input) {
    const container = this.getOrCreateValidationContainer(input);
    container.className = 'validation-container loading';
    container.innerHTML = '<div class="validation-message"><i class="fas fa-spinner fa-spin"></i> Checking...</div>';
  }

  clearValidationStatus(input) {
    const container = input.parentNode.querySelector('.validation-container');
    if (container) {
      container.remove();
    }
  }

  showValidationError(input, message) {
    const container = this.getOrCreateValidationContainer(input);
    container.className = 'validation-container error';
    container.innerHTML = `<div class="validation-message"><span class="validation-icon">❌</span> ${message}</div>`;
  }

  getOrCreateValidationContainer(input) {
    let container = input.parentNode.querySelector('.validation-container');
    if (!container) {
      container = document.createElement('div');
      container.className = 'validation-container';
      input.parentNode.appendChild(container);
    }
    return container;
  }

  showSuccessMessage(message) {
    if (typeof Swal !== 'undefined') {
      Swal.fire({
        title: 'Success!',
        text: message,
        icon: 'success',
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000
      });
    } else {
      alert(message);
    }
  }

  showErrorMessage(message) {
    if (typeof Swal !== 'undefined') {
      Swal.fire({
        title: 'Error',
        text: message,
        icon: 'error'
      });
    } else {
      alert(message);
    }
  }

  getCSRFToken() {
    const token = document.querySelector('meta[name="csrf-token"]');
    return token ? token.getAttribute('content') : '';
  }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
  window.smartWaferValidator = new SmartWaferValidator();
});

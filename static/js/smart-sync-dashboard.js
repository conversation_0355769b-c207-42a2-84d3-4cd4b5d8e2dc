/**
 * Smart Sync Dashboard
 * Displays intelligent sync recommendations and analytics
 */

class SmartSyncDashboard {
  constructor(containerId) {
    this.containerId = containerId;
    this.container = document.getElementById(containerId);
    this.recommendations = [];
    this.analysis = {};
    this.init();
  }

  init() {
    if (!this.container) {
      console.error(`❌ Container ${this.containerId} not found`);
      return;
    }

    this.createDashboard();
    this.loadSettings(); // Load saved settings
    this.loadRecommendations();
    console.log('🧠 Smart Sync Dashboard initialized');
  }

  createDashboard() {
    this.container.innerHTML = `
      <div class="smart-sync-dashboard p-6">
        <div class="dashboard-header flex items-center justify-between mb-8">
          <div class="flex items-center space-x-4">
            <div class="p-3 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl shadow-lg">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
              </svg>
            </div>
            <div>
              <h2 class="text-xl font-bold text-slate-800 dark:text-slate-100">
                Smart Sync Recommendations
              </h2>
              <p class="text-sm text-slate-600 dark:text-slate-400">AI-powered synchronization insights</p>
            </div>
          </div>
          <div class="dashboard-actions flex items-center space-x-3">
            <button type="button" class="btn-refresh inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2" onclick="window.smartSyncDashboard?.loadRecommendations()">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              Refresh
            </button>
            <button type="button" class="btn-settings inline-flex items-center px-4 py-2 bg-gradient-to-r from-slate-500 to-slate-600 hover:from-slate-600 hover:to-slate-700 text-white font-medium rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2" onclick="window.smartSyncDashboard?.showSettings()">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              Settings
            </button>
          </div>
        </div>
        
        <div class="dashboard-content">
          <div class="loading-state flex flex-col items-center justify-center py-16 space-y-4">
            <div class="relative">
              <div class="w-12 h-12 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
            </div>
            <div class="text-center">
              <p class="text-lg font-medium text-slate-700 dark:text-slate-300">Analyzing sync patterns</p>
              <p class="text-sm text-slate-500 dark:text-slate-400 mt-1">Please wait while we gather intelligent recommendations...</p>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  async loadRecommendations() {
    try {
      this.showLoading();

      const response = await fetch('/api/icarium/smart-sync-recommendations?hours=168', {
        method: 'GET',
        credentials: 'same-origin', // Include cookies for session authentication
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest' // Mark as AJAX request
        }
      });

      if (response.status === 401 || response.status === 403) {
        // Authentication required
        console.warn('Authentication required for smart sync recommendations');
        this.showError('Authentication required. Please log in.');
        return;
      }

      const data = await response.json();

      if (data.success) {
        this.recommendations = data.recommendations;
        this.analysis = data.analysis;
        this.renderDashboard(data);
      } else {
        this.showError('Failed to load smart recommendations');
      }

    } catch (error) {
      console.error('❌ Error loading smart sync recommendations:', error);
      this.showError('Network error loading recommendations');
    }
  }

  renderDashboard(data) {
    const content = this.container.querySelector('.dashboard-content');
    
    if (!data.recommendations || data.recommendations.length === 0) {
      content.innerHTML = `
        <div class="empty-state flex flex-col items-center justify-center py-16 space-y-6">
          <div class="p-6 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-full">
            <svg class="w-16 h-16 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div class="text-center space-y-2">
            <h3 class="text-2xl font-bold text-slate-800 dark:text-slate-100">All Caught Up!</h3>
            <p class="text-slate-600 dark:text-slate-400 max-w-md">No sync recommendations at this time. All wafers are properly synchronized between Icarium and Talaria.</p>
          </div>
          <div class="flex items-center space-x-2 text-sm text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20 px-4 py-2 rounded-full">
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>System synchronized</span>
          </div>
        </div>
      `;
      return;
    }

    content.innerHTML = `
      <div class="sync-analytics">
        ${this.renderAnalytics(data.analysis)}
      </div>
      
      <div class="recommendations-section">
        <h3 class="section-title">
          <i class="fas fa-lightbulb"></i>
          Smart Recommendations (${data.recommendations.length})
        </h3>
        <div class="recommendations-grid">
          ${data.recommendations.map(rec => this.renderRecommendation(rec)).join('')}
        </div>
      </div>
      
      <div class="quick-actions">
        ${this.renderQuickActions(data)}
      </div>
    `;
  }

  renderAnalytics(analysis) {
    if (!analysis) return '';

    return `
      <div class="analytics-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="analytics-card bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 p-6 rounded-xl border border-orange-200 dark:border-orange-800 shadow-sm hover:shadow-md transition-shadow">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl font-bold text-orange-700 dark:text-orange-300">${analysis.total_unsynced_wafers || 0}</div>
              <div class="text-sm font-medium text-orange-600 dark:text-orange-400">Unsynced Wafers</div>
            </div>
            <div class="p-3 bg-orange-100 dark:bg-orange-900 rounded-lg">
              <svg class="w-6 h-6 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
              </svg>
            </div>
          </div>
        </div>
        
        <div class="analytics-card bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-6 rounded-xl border border-blue-200 dark:border-blue-800 shadow-sm hover:shadow-md transition-shadow">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl font-bold text-blue-700 dark:text-blue-300">${analysis.lots_affected || 0}</div>
              <div class="text-sm font-medium text-blue-600 dark:text-blue-400">Lots Affected</div>
            </div>
            <div class="p-3 bg-blue-100 dark:bg-blue-900 rounded-lg">
              <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
              </svg>
            </div>
          </div>
        </div>
        
        <div class="analytics-card bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 p-6 rounded-xl border border-purple-200 dark:border-purple-800 shadow-sm hover:shadow-md transition-shadow">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl font-bold text-purple-700 dark:text-purple-300">${analysis.average_age_hours ? Math.round(analysis.average_age_hours) : 0}h</div>
              <div class="text-sm font-medium text-purple-600 dark:text-purple-400">Avg Age</div>
            </div>
            <div class="p-3 bg-purple-100 dark:bg-purple-900 rounded-lg">
              <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
        </div>
        
        <div class="analytics-card bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-6 rounded-xl border border-green-200 dark:border-green-800 shadow-sm hover:shadow-md transition-shadow">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl font-bold text-green-700 dark:text-green-300">${analysis.sync_efficiency_score || 0}%</div>
              <div class="text-sm font-medium text-green-600 dark:text-green-400">Efficiency Score</div>
            </div>
            <div class="p-3 bg-green-100 dark:bg-green-900 rounded-lg">
              <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
              </svg>
            </div>
          </div>
        </div>
      </div>
      
      ${this.renderInsights(analysis)}
    `;
  }

  renderInsights(analysis) {
    const insights = [];
    
    if (analysis.largest_lot) {
      insights.push(`📦 Largest lot: <strong>${analysis.largest_lot.lot_id}</strong> (${analysis.largest_lot.wafer_count} wafers)`);
    }
    
    if (analysis.busiest_location) {
      insights.push(`📍 Busiest location: <strong>${analysis.busiest_location.location}</strong> (${analysis.busiest_location.wafer_count} wafers)`);
    }
    
    if (analysis.timing_insights?.peak_creation_hour !== undefined) {
      insights.push(`⏰ Peak creation time: <strong>${analysis.timing_insights.peak_creation_hour}:00</strong>`);
    }
    
    if (analysis.recommendations_summary?.urgent_items > 0) {
      insights.push(`🚨 <strong>${analysis.recommendations_summary.urgent_items}</strong> urgent items need immediate attention`);
    }

    if (insights.length === 0) return '';

    return `
      <div class="insights-section">
        <h4><i class="fas fa-chart-line"></i> Key Insights</h4>
        <ul class="insights-list">
          ${insights.map(insight => `<li>${insight}</li>`).join('')}
        </ul>
      </div>
    `;
  }

  renderRecommendation(rec) {
    const priorityClass = this.getPriorityClass(rec.priority_score);
    const impactIcon = this.getImpactIcon(rec.impact);
    
    return `
      <div class="recommendation-card ${priorityClass}" data-rec-id="${rec.id}">
        <div class="rec-header">
          <div class="rec-title">
            <i class="fas fa-${this.getTypeIcon(rec.type)}"></i>
            ${rec.title}
          </div>
          <div class="rec-priority">
            <span class="priority-badge ${priorityClass}">
              ${Math.round(rec.priority_score * 100)}%
            </span>
          </div>
        </div>
        
        <div class="rec-description">
          ${rec.description}
        </div>
        
        <div class="rec-metrics">
          <div class="metric">
            <i class="fas fa-${impactIcon}"></i>
            <span>Impact: ${rec.impact}</span>
          </div>
          <div class="metric">
            <i class="fas fa-clock"></i>
            <span>${rec.time_estimate}</span>
          </div>
          <div class="metric">
            <i class="fas fa-wrench"></i>
            <span>Effort: ${rec.effort}</span>
          </div>
        </div>
        
        <div class="rec-benefits">
          <h5>Benefits:</h5>
          <ul>
            ${rec.benefits.map(benefit => `<li>${benefit}</li>`).join('')}
          </ul>
        </div>
        
        <div class="rec-actions">
          <button class="btn-execute" onclick="smartSyncDashboard.executeRecommendation('${rec.id}')">
            <i class="fas fa-play"></i>
            Execute
          </button>
          <button class="btn-details" onclick="smartSyncDashboard.showRecommendationDetails('${rec.id}')">
            <i class="fas fa-info"></i>
            Details
          </button>
        </div>
      </div>
    `;
  }

  renderQuickActions(data) {
    const urgentCount = data.recommendations.filter(r => r.priority_score > 0.8).length;
    const batchCount = data.recommendations.filter(r => r.type === 'batch_sync').length;
    
    return `
      <div class="quick-actions-section mt-8">
        <div class="flex items-center space-x-3 mb-6">
          <div class="p-2 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-bold text-slate-800 dark:text-slate-100">Quick Actions</h3>
        </div>
        <div class="quick-actions-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button type="button" class="quick-action-btn group inline-flex flex-col items-center justify-center p-6 bg-gradient-to-br from-red-50 to-orange-50 hover:from-red-100 hover:to-orange-100 dark:from-red-900/20 dark:to-orange-900/20 dark:hover:from-red-900/30 dark:hover:to-orange-900/30 border border-red-200 dark:border-red-800 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 ${urgentCount === 0 ? 'opacity-50 cursor-not-allowed' : ''}" onclick="window.smartSyncDashboard?.executeUrgentActions()" ${urgentCount === 0 ? 'disabled' : ''}>
            <div class="p-3 bg-red-100 dark:bg-red-900 rounded-lg mb-3 group-hover:bg-red-200 dark:group-hover:bg-red-800 transition-colors">
              <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
              </svg>
            </div>
            <span class="text-sm font-medium text-red-700 dark:text-red-300">Execute Urgent</span>
            <span class="text-xs text-red-600 dark:text-red-400">(${urgentCount} items)</span>
          </button>
          
          <button type="button" class="quick-action-btn group inline-flex flex-col items-center justify-center p-6 bg-gradient-to-br from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/20 dark:hover:from-blue-900/30 dark:hover:to-indigo-900/30 border border-blue-200 dark:border-blue-800 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${batchCount === 0 ? 'opacity-50 cursor-not-allowed' : ''}" onclick="window.smartSyncDashboard?.executeBatchActions()" ${batchCount === 0 ? 'disabled' : ''}>
            <div class="p-3 bg-blue-100 dark:bg-blue-900 rounded-lg mb-3 group-hover:bg-blue-200 dark:group-hover:bg-blue-800 transition-colors">
              <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
              </svg>
            </div>
            <span class="text-sm font-medium text-blue-700 dark:text-blue-300">Batch Sync</span>
            <span class="text-xs text-blue-600 dark:text-blue-400">(${batchCount} items)</span>
          </button>
          
          <button type="button" class="quick-action-btn group inline-flex flex-col items-center justify-center p-6 bg-gradient-to-br from-purple-50 to-violet-50 hover:from-purple-100 hover:to-violet-100 dark:from-purple-900/20 dark:to-violet-900/20 dark:hover:from-purple-900/30 dark:hover:to-violet-900/30 border border-purple-200 dark:border-purple-800 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 ${data.recommendations.length === 0 ? 'opacity-50 cursor-not-allowed' : ''}" onclick="window.smartSyncDashboard?.executeAllRecommendations()" ${data.recommendations.length === 0 ? 'disabled' : ''}>
            <div class="p-3 bg-purple-100 dark:bg-purple-900 rounded-lg mb-3 group-hover:bg-purple-200 dark:group-hover:bg-purple-800 transition-colors">
              <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
              </svg>
            </div>
            <span class="text-sm font-medium text-purple-700 dark:text-purple-300">Execute All</span>
            <span class="text-xs text-purple-600 dark:text-purple-400">(${data.recommendations.length} items)</span>
          </button>
          
          <button type="button" class="quick-action-btn group inline-flex flex-col items-center justify-center p-6 bg-gradient-to-br from-green-50 to-emerald-50 hover:from-green-100 hover:to-emerald-100 dark:from-green-900/20 dark:to-emerald-900/20 dark:hover:from-green-900/30 dark:hover:to-emerald-900/30 border border-green-200 dark:border-green-800 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2" onclick="window.smartSyncDashboard?.showDetailedAnalysis()">
            <div class="p-3 bg-green-100 dark:bg-green-900 rounded-lg mb-3 group-hover:bg-green-200 dark:group-hover:bg-green-800 transition-colors">
              <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v6a2 2 0 01-2 2H9z"></path>
              </svg>
            </div>
            <span class="text-sm font-medium text-green-700 dark:text-green-300">Analysis</span>
            <span class="text-xs text-green-600 dark:text-green-400">Detailed View</span>
          </button>
        </div>
      </div>
    `;
  }

  async executeRecommendation(recId) {
    try {
      const recommendation = this.recommendations.find(r => r.id === recId);
      if (!recommendation) {
        this.showNotification('Recommendation not found', 'error');
        return;
      }

      // Show confirmation dialog
      const confirmed = await this.showConfirmationDialog(
        `Execute "${recommendation.title}"?`,
        `This will ${recommendation.description.toLowerCase()}. Estimated time: ${recommendation.time_estimate}.`
      );

      if (!confirmed) return;

      // Show progress
      this.showExecutionProgress(recId);

      // Execute the recommendation
      const response = await fetch('/api/icarium/execute-smart-recommendation', {
        method: 'POST',
        credentials: 'same-origin',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': this.getCSRFToken(),
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
          recommendation_id: recId,
          action_type: recommendation.action.type,
          parameters: recommendation.action.parameters
        })
      });

      const result = await response.json();

      if (result.success) {
        this.showNotification(`✅ ${recommendation.title} completed successfully!`, 'success');
        this.markRecommendationCompleted(recId);
        
        // Refresh recommendations after execution
        setTimeout(() => {
          this.loadRecommendations();
        }, 2000);
      } else {
        this.showNotification(`❌ Execution failed: ${result.message}`, 'error');
      }

    } catch (error) {
      console.error('❌ Error executing recommendation:', error);
      this.showNotification('Execution failed due to network error', 'error');
    }
  }

  showRecommendationDetails(recId) {
    const recommendation = this.recommendations.find(r => r.id === recId);
    if (!recommendation) return;

    const detailsHtml = `
      <div class="recommendation-details">
        <h4>${recommendation.title}</h4>
        <p><strong>Type:</strong> ${recommendation.type}</p>
        <p><strong>Priority Score:</strong> ${Math.round(recommendation.priority_score * 100)}%</p>
        <p><strong>Impact:</strong> ${recommendation.impact}</p>
        <p><strong>Effort:</strong> ${recommendation.effort}</p>
        <p><strong>Time Estimate:</strong> ${recommendation.time_estimate}</p>
        
        <h5>Data:</h5>
        <pre>${JSON.stringify(recommendation.data, null, 2)}</pre>
        
        <h5>Action:</h5>
        <pre>${JSON.stringify(recommendation.action, null, 2)}</pre>
      </div>
    `;

    if (typeof Swal !== 'undefined') {
      Swal.fire({
        title: 'Recommendation Details',
        html: detailsHtml,
        width: '700px',
        confirmButtonText: 'Close'
      });
    } else {
      alert(`Details for ${recommendation.title}:\n${JSON.stringify(recommendation, null, 2)}`);
    }
  }

  async executeUrgentActions() {
    const urgentRecs = this.recommendations.filter(r => r.priority_score > 0.8);
    
    if (urgentRecs.length === 0) {
      this.showNotification('No urgent actions available', 'info');
      return;
    }

    const confirmed = await this.showConfirmationDialog(
      `Execute ${urgentRecs.length} urgent actions?`,
      'This will execute all high-priority recommendations. This may take several minutes.'
    );

    if (!confirmed) return;

    for (const rec of urgentRecs) {
      await this.executeRecommendation(rec.id);
      // Small delay between executions
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  async executeBatchActions() {
    const batchRecs = this.recommendations.filter(r => r.type === 'batch_sync');
    
    if (batchRecs.length === 0) {
      this.showNotification('No batch actions available', 'info');
      return;
    }

    const confirmed = await this.showConfirmationDialog(
      `Execute ${batchRecs.length} batch sync actions?`,
      'This will execute all batch sync recommendations for maximum efficiency.'
    );

    if (!confirmed) return;

    for (const rec of batchRecs) {
      await this.executeRecommendation(rec.id);
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  async executeAllRecommendations() {
    if (!this.recommendations || this.recommendations.length === 0) {
      this.showNotification('No recommendations available to execute', 'info');
      return;
    }

    const confirmed = await this.showConfirmationDialog(
      `Execute all ${this.recommendations.length} recommendations?`,
      'This will execute all available recommendations. This may take several minutes.'
    );

    if (!confirmed) return;

    let successCount = 0;
    let errorCount = 0;

    for (const rec of this.recommendations) {
      try {
        await this.executeRecommendation(rec.id);
        successCount++;
        // Small delay between executions
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        console.error(`Failed to execute recommendation ${rec.id}:`, error);
        errorCount++;
      }
    }

    // Show summary
    const message = `Execution complete: ${successCount} successful, ${errorCount} failed`;
    const type = errorCount === 0 ? 'success' : (successCount > 0 ? 'warning' : 'error');
    this.showNotification(message, type);

    // Refresh recommendations after execution
    setTimeout(() => {
      this.loadRecommendations();
    }, 2000);
  }

  // Utility methods
  getPriorityClass(score) {
    if (score > 0.8) return 'priority-urgent';
    if (score > 0.6) return 'priority-high';
    if (score > 0.4) return 'priority-medium';
    return 'priority-low';
  }

  getTypeIcon(type) {
    const icons = {
      'batch_sync': 'layer-group',
      'location_sync': 'map-marker-alt',
      'urgent_sync': 'exclamation-triangle',
      'efficiency_sync': 'tachometer-alt'
    };
    return icons[type] || 'sync';
  }

  getImpactIcon(impact) {
    const icons = {
      'critical': 'exclamation-triangle',
      'high': 'arrow-up',
      'medium': 'minus',
      'low': 'arrow-down'
    };
    return icons[impact] || 'info';
  }

  showLoading() {
    const content = this.container.querySelector('.dashboard-content');
    content.innerHTML = `
      <div class="loading-state flex flex-col items-center justify-center py-16 space-y-4">
        <div class="relative">
          <div class="w-12 h-12 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
          <div class="absolute inset-0 flex items-center justify-center">
            <div class="w-3 h-3 bg-blue-600 rounded-full animate-pulse"></div>
          </div>
        </div>
        <div class="text-center space-y-2">
          <p class="text-lg font-medium text-slate-700 dark:text-slate-300">Analyzing sync patterns</p>
          <p class="text-sm text-slate-500 dark:text-slate-400">Please wait while we gather intelligent recommendations...</p>
        </div>
      </div>
    `;
  }

  showError(message) {
    const content = this.container.querySelector('.dashboard-content');
    content.innerHTML = `
      <div class="error-state flex flex-col items-center justify-center py-16 space-y-6">
        <div class="p-6 bg-gradient-to-br from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20 rounded-full">
          <svg class="w-16 h-16 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
          </svg>
        </div>
        <div class="text-center space-y-2">
          <h3 class="text-xl font-bold text-slate-800 dark:text-slate-100">Connection Error</h3>
          <p class="text-slate-600 dark:text-slate-400 max-w-md">${message}</p>
        </div>
        <button type="button" class="btn-retry inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium rounded-lg shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2" onclick="window.smartSyncDashboard?.loadRecommendations()">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          Try Again
        </button>
      </div>
    `;
  }

  showNotification(message, type = 'info') {
    if (typeof Swal !== 'undefined') {
      Swal.fire({
        text: message,
        icon: type,
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000
      });
    } else {
      console.log(`${type.toUpperCase()}: ${message}`);
    }
  }

  async showConfirmationDialog(title, text) {
    if (typeof Swal !== 'undefined') {
      const result = await Swal.fire({
        title: title,
        text: text,
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Execute',
        cancelButtonText: 'Cancel'
      });
      return result.isConfirmed;
    } else {
      return confirm(`${title}\n\n${text}`);
    }
  }

  showExecutionProgress(recId) {
    const card = this.container.querySelector(`[data-rec-id="${recId}"]`);
    if (card) {
      const executeBtn = card.querySelector('.btn-execute');
      executeBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Executing...';
      executeBtn.disabled = true;
    }
  }

  markRecommendationCompleted(recId) {
    const card = this.container.querySelector(`[data-rec-id="${recId}"]`);
    if (card) {
      card.classList.add('completed');
      const executeBtn = card.querySelector('.btn-execute');
      executeBtn.innerHTML = '<i class="fas fa-check"></i> Completed';
      executeBtn.disabled = true;
    }
  }

  getCSRFToken() {
    const token = document.querySelector('meta[name="csrf-token"]');
    return token ? token.getAttribute('content') : '';
  }

  showSettings() {
    if (typeof Swal !== 'undefined') {
      Swal.fire({
        title: '⚙️ Smart Sync Settings',
        html: `
          <div class="sync-settings">
            <div class="setting-item">
              <label>
                <input type="checkbox" id="autoRefresh" checked>
                Auto-refresh recommendations every 5 minutes
              </label>
            </div>
            <div class="setting-item">
              <label>
                <input type="checkbox" id="showNotifications" checked>
                Show desktop notifications for urgent items
              </label>
            </div>
            <div class="setting-item">
              <label for="refreshInterval">Refresh interval:</label>
              <select id="refreshInterval">
                <option value="1">1 minute</option>
                <option value="5" selected>5 minutes</option>
                <option value="10">10 minutes</option>
                <option value="30">30 minutes</option>
              </select>
            </div>
            <div class="setting-item">
              <label for="priorityThreshold">Priority threshold for urgent alerts:</label>
              <select id="priorityThreshold">
                <option value="0.6">60%</option>
                <option value="0.7">70%</option>
                <option value="0.8" selected>80%</option>
                <option value="0.9">90%</option>
              </select>
            </div>
            <div class="setting-item">
              <label>
                <input type="checkbox" id="enableScheduler" checked>
                Enable automated scheduler tasks
              </label>
            </div>
          </div>
        `,
        width: '500px',
        confirmButtonText: 'Save Settings',
        showCancelButton: true,
        cancelButtonText: 'Cancel'
      }).then((result) => {
        if (result.isConfirmed) {
          this.saveSettings();
        }
      });
    } else {
      alert('Settings feature requires SweetAlert2 library');
    }
  }

  saveSettings() {
    try {
      const settings = {
        autoRefresh: document.getElementById('autoRefresh')?.checked || true,
        showNotifications: document.getElementById('showNotifications')?.checked || true,
        refreshInterval: parseInt(document.getElementById('refreshInterval')?.value || '5'),
        priorityThreshold: parseFloat(document.getElementById('priorityThreshold')?.value || '0.8'),
        enableScheduler: document.getElementById('enableScheduler')?.checked || true
      };

      localStorage.setItem('smartSyncSettings', JSON.stringify(settings));

      // Apply settings
      this.applySettings(settings);

      this.showNotification('Settings saved successfully', 'success');
    } catch (error) {
      console.error('Error saving settings:', error);
      this.showNotification('Failed to save settings', 'error');
    }
  }

  applySettings(settings) {
    // Apply refresh interval
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
    }

    if (settings.autoRefresh) {
      this.refreshInterval = setInterval(() => {
        this.loadRecommendations();
      }, settings.refreshInterval * 60 * 1000);
    }

    // Store settings for use in other methods
    this.settings = settings;
  }

  loadSettings() {
    try {
      const saved = localStorage.getItem('smartSyncSettings');
      if (saved) {
        const settings = JSON.parse(saved);
        this.applySettings(settings);
        return settings;
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    }

    // Default settings
    const defaultSettings = {
      autoRefresh: true,
      showNotifications: true,
      refreshInterval: 5,
      priorityThreshold: 0.8,
      enableScheduler: true
    };

    this.applySettings(defaultSettings);
    return defaultSettings;
  }

  showDetailedAnalysis() {
    if (!this.analysis || Object.keys(this.analysis).length === 0) {
      this.showNotification('No analysis data available. Please refresh recommendations first.', 'info');
      return;
    }

    const analysisHtml = `
      <div class="detailed-analysis">
        <h4>📊 Comprehensive Sync Analysis</h4>

        <div class="analysis-section">
          <h5>📈 Key Metrics</h5>
          <ul>
            <li><strong>Total Unsynced Wafers:</strong> ${this.analysis.total_unsynced_wafers || 0}</li>
            <li><strong>Average Age:</strong> ${this.analysis.average_age_hours || 0} hours</li>
            <li><strong>Lots Affected:</strong> ${this.analysis.lots_affected || 0}</li>
            <li><strong>Locations Affected:</strong> ${this.analysis.locations_affected || 0}</li>
            <li><strong>Sync Efficiency Score:</strong> ${this.analysis.sync_efficiency_score || 0}%</li>
          </ul>
        </div>

        ${this.analysis.largest_lot ? `
        <div class="analysis-section">
          <h5>📦 Largest Lot</h5>
          <p><strong>${this.analysis.largest_lot.lot_id}</strong> with ${this.analysis.largest_lot.wafer_count} wafers</p>
        </div>
        ` : ''}

        ${this.analysis.busiest_location ? `
        <div class="analysis-section">
          <h5>📍 Busiest Location</h5>
          <p><strong>${this.analysis.busiest_location.location}</strong> with ${this.analysis.busiest_location.wafer_count} wafers</p>
        </div>
        ` : ''}

        ${this.analysis.timing_insights ? `
        <div class="analysis-section">
          <h5>⏰ Timing Insights</h5>
          <ul>
            ${this.analysis.timing_insights.peak_creation_hour !== undefined ?
              `<li><strong>Peak Creation Hour:</strong> ${this.analysis.timing_insights.peak_creation_hour}:00</li>` : ''}
            ${this.analysis.timing_insights.peak_creation_day ?
              `<li><strong>Peak Creation Day:</strong> ${this.analysis.timing_insights.peak_creation_day}</li>` : ''}
          </ul>
        </div>
        ` : ''}

        ${this.analysis.recommendations_summary ? `
        <div class="analysis-section">
          <h5>🎯 Recommendations Summary</h5>
          <ul>
            <li><strong>High Priority Actions:</strong> ${this.analysis.recommendations_summary.high_priority_actions || 0}</li>
            <li><strong>Batch Opportunities:</strong> ${this.analysis.recommendations_summary.batch_opportunities || 0}</li>
            <li><strong>Urgent Items:</strong> ${this.analysis.recommendations_summary.urgent_items || 0}</li>
          </ul>
        </div>
        ` : ''}
      </div>
    `;

    if (typeof Swal !== 'undefined') {
      Swal.fire({
        title: '📊 Detailed Analysis',
        html: analysisHtml,
        width: '700px',
        confirmButtonText: 'Close'
      });
    } else {
      console.log('Detailed Analysis:', this.analysis);
      alert('Detailed analysis logged to console');
    }
  }

  getCSRFToken() {
    const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                 document.querySelector('input[name="csrf_token"]')?.value;
    return token || '';
  }
}

// SmartSyncDashboard class is available for manual initialization

/**
 * Smart Sync Dashboard
 * Displays intelligent sync recommendations and analytics
 */

class SmartSyncDashboard {
  constructor(containerId) {
    this.containerId = containerId;
    this.container = document.getElementById(containerId);
    this.recommendations = [];
    this.analysis = {};
    this.init();
  }

  init() {
    if (!this.container) {
      console.error(`❌ Container ${this.containerId} not found`);
      return;
    }

    this.createDashboard();
    this.loadSettings(); // Load saved settings
    this.loadRecommendations();
    console.log('🧠 Smart Sync Dashboard initialized');
  }

  createDashboard() {
    this.container.innerHTML = `
      <div class="smart-sync-dashboard">
        <div class="dashboard-header">
          <h2 class="dashboard-title">
            <i class="fas fa-brain"></i>
            Smart Sync Recommendations
          </h2>
          <div class="dashboard-actions">
            <button class="btn-refresh" onclick="smartSyncDashboard.loadRecommendations()">
              <i class="fas fa-sync"></i>
              Refresh
            </button>
            <button class="btn-settings" onclick="smartSyncDashboard.showSettings()">
              <i class="fas fa-cog"></i>
              Settings
            </button>
          </div>
        </div>
        
        <div class="dashboard-content">
          <div class="loading-state">
            <i class="fas fa-spinner fa-spin"></i>
            <span>Analyzing sync patterns...</span>
          </div>
        </div>
      </div>
    `;
  }

  async loadRecommendations() {
    try {
      this.showLoading();

      const response = await fetch('/api/icarium/smart-sync-recommendations?hours=168', {
        method: 'GET',
        credentials: 'same-origin', // Include cookies for session authentication
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest' // Mark as AJAX request
        }
      });

      if (response.status === 401 || response.status === 403) {
        // Authentication required
        console.warn('Authentication required for smart sync recommendations');
        this.showError('Authentication required. Please log in.');
        return;
      }

      const data = await response.json();

      if (data.success) {
        this.recommendations = data.recommendations;
        this.analysis = data.analysis;
        this.renderDashboard(data);
      } else {
        this.showError('Failed to load smart recommendations');
      }

    } catch (error) {
      console.error('❌ Error loading smart sync recommendations:', error);
      this.showError('Network error loading recommendations');
    }
  }

  renderDashboard(data) {
    const content = this.container.querySelector('.dashboard-content');
    
    if (!data.recommendations || data.recommendations.length === 0) {
      content.innerHTML = `
        <div class="empty-state">
          <i class="fas fa-check-circle"></i>
          <h3>All Caught Up!</h3>
          <p>No sync recommendations at this time. All wafers are properly synchronized.</p>
        </div>
      `;
      return;
    }

    content.innerHTML = `
      <div class="sync-analytics">
        ${this.renderAnalytics(data.analysis)}
      </div>
      
      <div class="recommendations-section">
        <h3 class="section-title">
          <i class="fas fa-lightbulb"></i>
          Smart Recommendations (${data.recommendations.length})
        </h3>
        <div class="recommendations-grid">
          ${data.recommendations.map(rec => this.renderRecommendation(rec)).join('')}
        </div>
      </div>
      
      <div class="quick-actions">
        ${this.renderQuickActions(data)}
      </div>
    `;
  }

  renderAnalytics(analysis) {
    if (!analysis) return '';

    return `
      <div class="analytics-grid">
        <div class="analytics-card">
          <div class="card-icon">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <div class="card-content">
            <div class="card-value">${analysis.total_unsynced_wafers || 0}</div>
            <div class="card-label">Unsynced Wafers</div>
          </div>
        </div>
        
        <div class="analytics-card">
          <div class="card-icon">
            <i class="fas fa-boxes"></i>
          </div>
          <div class="card-content">
            <div class="card-value">${analysis.lots_affected || 0}</div>
            <div class="card-label">Lots Affected</div>
          </div>
        </div>
        
        <div class="analytics-card">
          <div class="card-icon">
            <i class="fas fa-clock"></i>
          </div>
          <div class="card-content">
            <div class="card-value">${analysis.average_age_hours ? Math.round(analysis.average_age_hours) : 0}h</div>
            <div class="card-label">Avg Age</div>
          </div>
        </div>
        
        <div class="analytics-card">
          <div class="card-icon">
            <i class="fas fa-tachometer-alt"></i>
          </div>
          <div class="card-content">
            <div class="card-value">${analysis.sync_efficiency_score || 0}%</div>
            <div class="card-label">Efficiency Score</div>
          </div>
        </div>
      </div>
      
      ${this.renderInsights(analysis)}
    `;
  }

  renderInsights(analysis) {
    const insights = [];
    
    if (analysis.largest_lot) {
      insights.push(`📦 Largest lot: <strong>${analysis.largest_lot.lot_id}</strong> (${analysis.largest_lot.wafer_count} wafers)`);
    }
    
    if (analysis.busiest_location) {
      insights.push(`📍 Busiest location: <strong>${analysis.busiest_location.location}</strong> (${analysis.busiest_location.wafer_count} wafers)`);
    }
    
    if (analysis.timing_insights?.peak_creation_hour !== undefined) {
      insights.push(`⏰ Peak creation time: <strong>${analysis.timing_insights.peak_creation_hour}:00</strong>`);
    }
    
    if (analysis.recommendations_summary?.urgent_items > 0) {
      insights.push(`🚨 <strong>${analysis.recommendations_summary.urgent_items}</strong> urgent items need immediate attention`);
    }

    if (insights.length === 0) return '';

    return `
      <div class="insights-section">
        <h4><i class="fas fa-chart-line"></i> Key Insights</h4>
        <ul class="insights-list">
          ${insights.map(insight => `<li>${insight}</li>`).join('')}
        </ul>
      </div>
    `;
  }

  renderRecommendation(rec) {
    const priorityClass = this.getPriorityClass(rec.priority_score);
    const impactIcon = this.getImpactIcon(rec.impact);
    
    return `
      <div class="recommendation-card ${priorityClass}" data-rec-id="${rec.id}">
        <div class="rec-header">
          <div class="rec-title">
            <i class="fas fa-${this.getTypeIcon(rec.type)}"></i>
            ${rec.title}
          </div>
          <div class="rec-priority">
            <span class="priority-badge ${priorityClass}">
              ${Math.round(rec.priority_score * 100)}%
            </span>
          </div>
        </div>
        
        <div class="rec-description">
          ${rec.description}
        </div>
        
        <div class="rec-metrics">
          <div class="metric">
            <i class="fas fa-${impactIcon}"></i>
            <span>Impact: ${rec.impact}</span>
          </div>
          <div class="metric">
            <i class="fas fa-clock"></i>
            <span>${rec.time_estimate}</span>
          </div>
          <div class="metric">
            <i class="fas fa-wrench"></i>
            <span>Effort: ${rec.effort}</span>
          </div>
        </div>
        
        <div class="rec-benefits">
          <h5>Benefits:</h5>
          <ul>
            ${rec.benefits.map(benefit => `<li>${benefit}</li>`).join('')}
          </ul>
        </div>
        
        <div class="rec-actions">
          <button class="btn-execute" onclick="smartSyncDashboard.executeRecommendation('${rec.id}')">
            <i class="fas fa-play"></i>
            Execute
          </button>
          <button class="btn-details" onclick="smartSyncDashboard.showRecommendationDetails('${rec.id}')">
            <i class="fas fa-info"></i>
            Details
          </button>
        </div>
      </div>
    `;
  }

  renderQuickActions(data) {
    const urgentCount = data.recommendations.filter(r => r.priority_score > 0.8).length;
    const batchCount = data.recommendations.filter(r => r.type === 'batch_sync').length;
    
    return `
      <div class="quick-actions-section">
        <h3 class="section-title">
          <i class="fas fa-bolt"></i>
          Quick Actions
        </h3>
        <div class="quick-actions-grid">
          <button class="quick-action-btn urgent" onclick="smartSyncDashboard.executeUrgentActions()" ${urgentCount === 0 ? 'disabled' : ''}>
            <i class="fas fa-exclamation-triangle"></i>
            <span>Execute Urgent (${urgentCount})</span>
          </button>
          
          <button class="quick-action-btn batch" onclick="smartSyncDashboard.executeBatchActions()" ${batchCount === 0 ? 'disabled' : ''}>
            <i class="fas fa-layer-group"></i>
            <span>Batch Sync (${batchCount})</span>
          </button>
          
          <button class="quick-action-btn all" onclick="smartSyncDashboard.executeAllRecommendations()" ${data.recommendations.length === 0 ? 'disabled' : ''}>
            <i class="fas fa-magic"></i>
            <span>Execute All (${data.recommendations.length})</span>
          </button>
          
          <button class="quick-action-btn analyze" onclick="smartSyncDashboard.showDetailedAnalysis()">
            <i class="fas fa-chart-bar"></i>
            <span>Detailed Analysis</span>
          </button>
        </div>
      </div>
    `;
  }

  async executeRecommendation(recId) {
    try {
      const recommendation = this.recommendations.find(r => r.id === recId);
      if (!recommendation) {
        this.showNotification('Recommendation not found', 'error');
        return;
      }

      // Show confirmation dialog
      const confirmed = await this.showConfirmationDialog(
        `Execute "${recommendation.title}"?`,
        `This will ${recommendation.description.toLowerCase()}. Estimated time: ${recommendation.time_estimate}.`
      );

      if (!confirmed) return;

      // Show progress
      this.showExecutionProgress(recId);

      // Execute the recommendation
      const response = await fetch('/api/icarium/execute-smart-recommendation', {
        method: 'POST',
        credentials: 'same-origin',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': this.getCSRFToken(),
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
          recommendation_id: recId,
          action_type: recommendation.action.type,
          parameters: recommendation.action.parameters
        })
      });

      const result = await response.json();

      if (result.success) {
        this.showNotification(`✅ ${recommendation.title} completed successfully!`, 'success');
        this.markRecommendationCompleted(recId);
        
        // Refresh recommendations after execution
        setTimeout(() => {
          this.loadRecommendations();
        }, 2000);
      } else {
        this.showNotification(`❌ Execution failed: ${result.message}`, 'error');
      }

    } catch (error) {
      console.error('❌ Error executing recommendation:', error);
      this.showNotification('Execution failed due to network error', 'error');
    }
  }

  showRecommendationDetails(recId) {
    const recommendation = this.recommendations.find(r => r.id === recId);
    if (!recommendation) return;

    const detailsHtml = `
      <div class="recommendation-details">
        <h4>${recommendation.title}</h4>
        <p><strong>Type:</strong> ${recommendation.type}</p>
        <p><strong>Priority Score:</strong> ${Math.round(recommendation.priority_score * 100)}%</p>
        <p><strong>Impact:</strong> ${recommendation.impact}</p>
        <p><strong>Effort:</strong> ${recommendation.effort}</p>
        <p><strong>Time Estimate:</strong> ${recommendation.time_estimate}</p>
        
        <h5>Data:</h5>
        <pre>${JSON.stringify(recommendation.data, null, 2)}</pre>
        
        <h5>Action:</h5>
        <pre>${JSON.stringify(recommendation.action, null, 2)}</pre>
      </div>
    `;

    if (typeof Swal !== 'undefined') {
      Swal.fire({
        title: 'Recommendation Details',
        html: detailsHtml,
        width: '700px',
        confirmButtonText: 'Close'
      });
    } else {
      alert(`Details for ${recommendation.title}:\n${JSON.stringify(recommendation, null, 2)}`);
    }
  }

  async executeUrgentActions() {
    const urgentRecs = this.recommendations.filter(r => r.priority_score > 0.8);
    
    if (urgentRecs.length === 0) {
      this.showNotification('No urgent actions available', 'info');
      return;
    }

    const confirmed = await this.showConfirmationDialog(
      `Execute ${urgentRecs.length} urgent actions?`,
      'This will execute all high-priority recommendations. This may take several minutes.'
    );

    if (!confirmed) return;

    for (const rec of urgentRecs) {
      await this.executeRecommendation(rec.id);
      // Small delay between executions
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  async executeBatchActions() {
    const batchRecs = this.recommendations.filter(r => r.type === 'batch_sync');
    
    if (batchRecs.length === 0) {
      this.showNotification('No batch actions available', 'info');
      return;
    }

    const confirmed = await this.showConfirmationDialog(
      `Execute ${batchRecs.length} batch sync actions?`,
      'This will execute all batch sync recommendations for maximum efficiency.'
    );

    if (!confirmed) return;

    for (const rec of batchRecs) {
      await this.executeRecommendation(rec.id);
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  async executeAllRecommendations() {
    if (!this.recommendations || this.recommendations.length === 0) {
      this.showNotification('No recommendations available to execute', 'info');
      return;
    }

    const confirmed = await this.showConfirmationDialog(
      `Execute all ${this.recommendations.length} recommendations?`,
      'This will execute all available recommendations. This may take several minutes.'
    );

    if (!confirmed) return;

    let successCount = 0;
    let errorCount = 0;

    for (const rec of this.recommendations) {
      try {
        await this.executeRecommendation(rec.id);
        successCount++;
        // Small delay between executions
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        console.error(`Failed to execute recommendation ${rec.id}:`, error);
        errorCount++;
      }
    }

    // Show summary
    const message = `Execution complete: ${successCount} successful, ${errorCount} failed`;
    const type = errorCount === 0 ? 'success' : (successCount > 0 ? 'warning' : 'error');
    this.showNotification(message, type);

    // Refresh recommendations after execution
    setTimeout(() => {
      this.loadRecommendations();
    }, 2000);
  }

  // Utility methods
  getPriorityClass(score) {
    if (score > 0.8) return 'priority-urgent';
    if (score > 0.6) return 'priority-high';
    if (score > 0.4) return 'priority-medium';
    return 'priority-low';
  }

  getTypeIcon(type) {
    const icons = {
      'batch_sync': 'layer-group',
      'location_sync': 'map-marker-alt',
      'urgent_sync': 'exclamation-triangle',
      'efficiency_sync': 'tachometer-alt'
    };
    return icons[type] || 'sync';
  }

  getImpactIcon(impact) {
    const icons = {
      'critical': 'exclamation-triangle',
      'high': 'arrow-up',
      'medium': 'minus',
      'low': 'arrow-down'
    };
    return icons[impact] || 'info';
  }

  showLoading() {
    const content = this.container.querySelector('.dashboard-content');
    content.innerHTML = `
      <div class="loading-state">
        <i class="fas fa-spinner fa-spin"></i>
        <span>Analyzing sync patterns...</span>
      </div>
    `;
  }

  showError(message) {
    const content = this.container.querySelector('.dashboard-content');
    content.innerHTML = `
      <div class="error-state">
        <i class="fas fa-exclamation-triangle"></i>
        <span>${message}</span>
        <button class="btn-retry" onclick="smartSyncDashboard.loadRecommendations()">
          <i class="fas fa-redo"></i>
          Retry
        </button>
      </div>
    `;
  }

  showNotification(message, type = 'info') {
    if (typeof Swal !== 'undefined') {
      Swal.fire({
        text: message,
        icon: type,
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000
      });
    } else {
      console.log(`${type.toUpperCase()}: ${message}`);
    }
  }

  async showConfirmationDialog(title, text) {
    if (typeof Swal !== 'undefined') {
      const result = await Swal.fire({
        title: title,
        text: text,
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Execute',
        cancelButtonText: 'Cancel'
      });
      return result.isConfirmed;
    } else {
      return confirm(`${title}\n\n${text}`);
    }
  }

  showExecutionProgress(recId) {
    const card = this.container.querySelector(`[data-rec-id="${recId}"]`);
    if (card) {
      const executeBtn = card.querySelector('.btn-execute');
      executeBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Executing...';
      executeBtn.disabled = true;
    }
  }

  markRecommendationCompleted(recId) {
    const card = this.container.querySelector(`[data-rec-id="${recId}"]`);
    if (card) {
      card.classList.add('completed');
      const executeBtn = card.querySelector('.btn-execute');
      executeBtn.innerHTML = '<i class="fas fa-check"></i> Completed';
      executeBtn.disabled = true;
    }
  }

  getCSRFToken() {
    const token = document.querySelector('meta[name="csrf-token"]');
    return token ? token.getAttribute('content') : '';
  }

  showSettings() {
    if (typeof Swal !== 'undefined') {
      Swal.fire({
        title: '⚙️ Smart Sync Settings',
        html: `
          <div class="sync-settings">
            <div class="setting-item">
              <label>
                <input type="checkbox" id="autoRefresh" checked>
                Auto-refresh recommendations every 5 minutes
              </label>
            </div>
            <div class="setting-item">
              <label>
                <input type="checkbox" id="showNotifications" checked>
                Show desktop notifications for urgent items
              </label>
            </div>
            <div class="setting-item">
              <label for="refreshInterval">Refresh interval:</label>
              <select id="refreshInterval">
                <option value="1">1 minute</option>
                <option value="5" selected>5 minutes</option>
                <option value="10">10 minutes</option>
                <option value="30">30 minutes</option>
              </select>
            </div>
            <div class="setting-item">
              <label for="priorityThreshold">Priority threshold for urgent alerts:</label>
              <select id="priorityThreshold">
                <option value="0.6">60%</option>
                <option value="0.7">70%</option>
                <option value="0.8" selected>80%</option>
                <option value="0.9">90%</option>
              </select>
            </div>
            <div class="setting-item">
              <label>
                <input type="checkbox" id="enableScheduler" checked>
                Enable automated scheduler tasks
              </label>
            </div>
          </div>
        `,
        width: '500px',
        confirmButtonText: 'Save Settings',
        showCancelButton: true,
        cancelButtonText: 'Cancel'
      }).then((result) => {
        if (result.isConfirmed) {
          this.saveSettings();
        }
      });
    } else {
      alert('Settings feature requires SweetAlert2 library');
    }
  }

  saveSettings() {
    try {
      const settings = {
        autoRefresh: document.getElementById('autoRefresh')?.checked || true,
        showNotifications: document.getElementById('showNotifications')?.checked || true,
        refreshInterval: parseInt(document.getElementById('refreshInterval')?.value || '5'),
        priorityThreshold: parseFloat(document.getElementById('priorityThreshold')?.value || '0.8'),
        enableScheduler: document.getElementById('enableScheduler')?.checked || true
      };

      localStorage.setItem('smartSyncSettings', JSON.stringify(settings));

      // Apply settings
      this.applySettings(settings);

      this.showNotification('Settings saved successfully', 'success');
    } catch (error) {
      console.error('Error saving settings:', error);
      this.showNotification('Failed to save settings', 'error');
    }
  }

  applySettings(settings) {
    // Apply refresh interval
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
    }

    if (settings.autoRefresh) {
      this.refreshInterval = setInterval(() => {
        this.loadRecommendations();
      }, settings.refreshInterval * 60 * 1000);
    }

    // Store settings for use in other methods
    this.settings = settings;
  }

  loadSettings() {
    try {
      const saved = localStorage.getItem('smartSyncSettings');
      if (saved) {
        const settings = JSON.parse(saved);
        this.applySettings(settings);
        return settings;
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    }

    // Default settings
    const defaultSettings = {
      autoRefresh: true,
      showNotifications: true,
      refreshInterval: 5,
      priorityThreshold: 0.8,
      enableScheduler: true
    };

    this.applySettings(defaultSettings);
    return defaultSettings;
  }

  showDetailedAnalysis() {
    if (!this.analysis || Object.keys(this.analysis).length === 0) {
      this.showNotification('No analysis data available. Please refresh recommendations first.', 'info');
      return;
    }

    const analysisHtml = `
      <div class="detailed-analysis">
        <h4>📊 Comprehensive Sync Analysis</h4>

        <div class="analysis-section">
          <h5>📈 Key Metrics</h5>
          <ul>
            <li><strong>Total Unsynced Wafers:</strong> ${this.analysis.total_unsynced_wafers || 0}</li>
            <li><strong>Average Age:</strong> ${this.analysis.average_age_hours || 0} hours</li>
            <li><strong>Lots Affected:</strong> ${this.analysis.lots_affected || 0}</li>
            <li><strong>Locations Affected:</strong> ${this.analysis.locations_affected || 0}</li>
            <li><strong>Sync Efficiency Score:</strong> ${this.analysis.sync_efficiency_score || 0}%</li>
          </ul>
        </div>

        ${this.analysis.largest_lot ? `
        <div class="analysis-section">
          <h5>📦 Largest Lot</h5>
          <p><strong>${this.analysis.largest_lot.lot_id}</strong> with ${this.analysis.largest_lot.wafer_count} wafers</p>
        </div>
        ` : ''}

        ${this.analysis.busiest_location ? `
        <div class="analysis-section">
          <h5>📍 Busiest Location</h5>
          <p><strong>${this.analysis.busiest_location.location}</strong> with ${this.analysis.busiest_location.wafer_count} wafers</p>
        </div>
        ` : ''}

        ${this.analysis.timing_insights ? `
        <div class="analysis-section">
          <h5>⏰ Timing Insights</h5>
          <ul>
            ${this.analysis.timing_insights.peak_creation_hour !== undefined ?
              `<li><strong>Peak Creation Hour:</strong> ${this.analysis.timing_insights.peak_creation_hour}:00</li>` : ''}
            ${this.analysis.timing_insights.peak_creation_day ?
              `<li><strong>Peak Creation Day:</strong> ${this.analysis.timing_insights.peak_creation_day}</li>` : ''}
          </ul>
        </div>
        ` : ''}

        ${this.analysis.recommendations_summary ? `
        <div class="analysis-section">
          <h5>🎯 Recommendations Summary</h5>
          <ul>
            <li><strong>High Priority Actions:</strong> ${this.analysis.recommendations_summary.high_priority_actions || 0}</li>
            <li><strong>Batch Opportunities:</strong> ${this.analysis.recommendations_summary.batch_opportunities || 0}</li>
            <li><strong>Urgent Items:</strong> ${this.analysis.recommendations_summary.urgent_items || 0}</li>
          </ul>
        </div>
        ` : ''}
      </div>
    `;

    if (typeof Swal !== 'undefined') {
      Swal.fire({
        title: '📊 Detailed Analysis',
        html: analysisHtml,
        width: '700px',
        confirmButtonText: 'Close'
      });
    } else {
      console.log('Detailed Analysis:', this.analysis);
      alert('Detailed analysis logged to console');
    }
  }

  getCSRFToken() {
    const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                 document.querySelector('input[name="csrf_token"]')?.value;
    return token || '';
  }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
  if (document.getElementById('smart-sync-dashboard')) {
    window.smartSyncDashboard = new SmartSyncDashboard('smart-sync-dashboard');
  }
});

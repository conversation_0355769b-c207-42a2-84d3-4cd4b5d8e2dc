// API Debug Utility
// Add this to your project to help debug API issues

const ApiDebugger = {
  // Test HTTP methods against an endpoint
  async testEndpoint(endpoint, methods = ["GET", "POST"]) {
    console.group(`Testing endpoint: ${endpoint}`);
    const results = {};

    for (const method of methods) {
      try {
        console.log(`Testing ${method} request to ${endpoint}`);

        const options = {
          method: method,
          headers: {
            "X-Requested-With": "XMLHttpRequest",
          },
          credentials: "same-origin",
        };

        // Add CSRF token for non-GET requests
        if (method !== "GET") {
          const csrfToken = this.getCSRFToken();
          if (csrfToken) {
            options.headers["X-CSRFToken"] = csrfToken;
          }

          // Add empty body for POST/PUT requests
          if (method === "POST" || method === "PUT") {
            // Use FormData for better Flask compatibility
            const formData = new FormData();
            formData.append("csrf_token", csrfToken || "");
            options.body = formData;
          }
        }

        const response = await fetch(endpoint, options);

        // Log response details
        console.log(`Status: ${response.status} ${response.statusText}`);
        console.log(
          "Headers:",
          Object.fromEntries([...response.headers.entries()])
        );

        // Try to parse response as JSON
        let data;
        const contentType = response.headers.get("Content-Type");

        if (contentType && contentType.includes("application/json")) {
          data = await response.json();
          console.log("Response data:", data);
        } else {
          const text = await response.text();
          console.log(
            "Response text (first 500 chars):",
            text.substring(0, 500)
          );

          // Check if response might be HTML error page
          if (text.includes("<!DOCTYPE html>") || text.includes("<html>")) {
            console.warn("Response appears to be HTML, not JSON");
          }

          data = { text: text.substring(0, 100) + "..." };
        }

        results[method] = {
          status: response.status,
          contentType: contentType,
          success: response.ok,
          data: data,
        };
      } catch (error) {
        console.error(`Error testing ${method}:`, error);
        results[method] = {
          error: error.message,
          success: false,
        };
      }
    }

    console.groupEnd();
    return results;
  },

  // Get a valid CSRF token from various sources
  getCSRFToken() {
    // Try input field
    const tokenInput = document.querySelector('input[name="csrf_token"]');
    if (tokenInput) return tokenInput.value;

    // Try meta tag
    const metaToken = document.querySelector('meta[name="csrf-token"]');
    if (metaToken) return metaToken.content;

    // Try cookie
    const cookies = document.cookie.split(";");
    for (const cookie of cookies) {
      const [name, value] = cookie.trim().split("=");
      if (name === "csrf_token") return value;
    }

    console.warn("CSRF token not found");
    return null;
  },

  // Check authentication status
  async checkAuth() {
    try {
      const response = await fetch("/api/auth/check", {
        headers: { "X-Requested-With": "XMLHttpRequest" },
        credentials: "same-origin",
      });

      if (!response.ok) {
        console.error(
          "Auth check failed:",
          response.status,
          response.statusText
        );
        return { authenticated: false, error: `Status ${response.status}` };
      }

      const contentType = response.headers.get("Content-Type");
      if (!contentType || !contentType.includes("application/json")) {
        console.error("Auth check returned non-JSON response");
        return { authenticated: false, error: "Non-JSON response" };
      }

      return await response.json();
    } catch (error) {
      console.error("Auth check error:", error);
      return { authenticated: false, error: error.message };
    }
  },

  // Test the sync endpoint specifically
  async testSyncEndpoint() {
    // First check auth
    const authStatus = await this.checkAuth();
    console.log("Authentication status:", authStatus);

    // Then test debug endpoint
    const debugResult = await this.testEndpoint(
      "/api/inventory/sync-wafers-debug",
      ["GET"]
    );
    console.log("Debug endpoint result:", debugResult);

    // Finally test the actual sync endpoint
    const syncResult = await this.testEndpoint("/api/inventory/sync-wafers", [
      "POST",
    ]);
    console.log("Sync endpoint result:", syncResult);

    return {
      auth: authStatus,
      debug: debugResult.GET,
      sync: syncResult.POST,
    };
  },
};

// Usage:
// 1. Open browser console
// 2. Run: ApiDebugger.testSyncEndpoint()
// 3. Check results in console

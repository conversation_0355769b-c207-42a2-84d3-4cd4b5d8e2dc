// locations.js

// Constants
const AUTOSAVE_DELAY = 3000; // 3 seconds
const TOAST_DURATION = 5000; // 5 seconds

// State management
let lastSavedState = {};
let autoSaveTimeout = null;

// Utility Functions
function getCSRFToken() {
  return document.querySelector('input[name="csrf_token"]').value;
}

async function makeRequest(url, method, data) {
  try {
    const headers = {
      "Content-Type": "application/json",
      "X-Requested-With": "XMLHttpRequest",
      "X-CSRFToken": getCSRFToken(),
    };

    const options = {
      method: method,
      headers: headers,
      credentials: "same-origin",
    };

    if (method !== "GET" && data) {
      options.body = JSON.stringify({
        ...data,
        csrf_token: getCSRFToken(),
      });
    }

    const response = await fetch(url, options);

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || "Request failed");
    }

    const contentType = response.headers.get("content-type");
    return contentType?.includes("application/json")
      ? response.json()
      : { success: true };
  } catch (error) {
    console.error("Request error:", error);
    throw error;
  }
}

// Toast Notifications
function showToast(message, type = "success") {
  const container = document.getElementById("toast-container");
  const toast = document.createElement("div");

  const bgColor = type === "success" ? "bg-green-500" : "bg-red-500";
  toast.className = `${bgColor} text-white px-6 py-3 rounded-lg shadow-lg mb-2 flex items-center`;

  const icon = type === "success" ? "✓" : "✕";
  toast.innerHTML = `
        <span class="mr-2">${icon}</span>
        <span>${message}</span>
    `;

  container.appendChild(toast);
  setTimeout(() => container.removeChild(toast), TOAST_DURATION);
}

// Loading State Management
function setLoadingState(elementId, isLoading) {
  const element = document.getElementById(elementId);
  if (!element) return;

  const originalContent = element.innerHTML;
  element.disabled = isLoading;

  if (isLoading) {
    element.setAttribute("data-original-content", originalContent);
    element.innerHTML = `
            <svg class="animate-spin h-5 w-5 mr-2 inline" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Loading...
        `;
  } else {
    const originalContent = element.getAttribute("data-original-content");
    if (originalContent) {
      element.innerHTML = originalContent;
    }
  }
}

// Form Validation
function validateForm() {
  const validationRules = {
    "location-name": {
      required: true,
      message: "Location name is required",
    },
    "shipping-address": {
      required: true,
      message: "Shipping address is required",
    },
    email: {
      required: true,
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      message: "Valid email is required",
    },
    phone: {
      required: true,
      pattern: /^\+?[\d\s-()]{8,}$/,
      message: "Valid phone number is required",
    },
    "contact-person": {
      required: true,
      message: "Contact person is required",
    },
  };

  const errors = [];

  for (const [fieldId, rules] of Object.entries(validationRules)) {
    const field = document.getElementById(fieldId);
    if (!field) continue;

    const value = field.value.trim();

    if (rules.required && !value) {
      errors.push(rules.message);
      field.classList.add("border-red-500");
    } else if (rules.pattern && !rules.pattern.test(value)) {
      errors.push(rules.message);
      field.classList.add("border-red-500");
    } else {
      field.classList.remove("border-red-500");
    }
  }

  return errors;
}

// Form Data Management
function getFormData() {
  const isNewLocation = document.getElementById(
    "add-new-location-toggle"
  ).checked;
  const locationName = isNewLocation
    ? document.getElementById("location-name-input").value
    : document.getElementById("location-name-dropdown").value;

  return {
    location_id: locationName,
    label: locationName,
    address: document.getElementById("shipping-address").value,
    email: document.getElementById("email").value,
    telephone: document.getElementById("phone").value,
    contact_person: document.getElementById("contact-person").value,
    updated_by: document.getElementById("updated-by").value,
  };
}

function clearForm() {
  document.getElementById("locationForm").reset();
  const toggle = document.getElementById("add-new-location-toggle");
  toggle.checked = false;
  toggleLocationInput();

  // Clear validation styles
  document.querySelectorAll(".border-red-500").forEach((el) => {
    el.classList.remove("border-red-500");
  });
}

// Location Management Functions
async function loadLocationDropdown() {
  try {
    setLoadingState("location-name-dropdown", true);
    const searchTerm = document.getElementById("location-search")?.value || "";

    const response = await makeRequest(
      `/api/locations?search=${encodeURIComponent(searchTerm)}`,
      "GET"
    );

    const dropdown = document.getElementById("location-name-dropdown");
    dropdown.innerHTML = '<option value="">Select Location</option>';

    if (response.success && response.locations) {
      response.locations.forEach((location) => {
        const option = document.createElement("option");
        option.value = location.location_id;
        option.textContent = location.label;
        dropdown.appendChild(option);
      });
    }
  } catch (error) {
    showToast(`Error loading locations: ${error.message}`, "error");
  } finally {
    setLoadingState("location-name-dropdown", false);
  }
}

async function viewLocation() {
  const locationId = document.getElementById("location-name-dropdown").value;
  if (!locationId) {
    showToast("Please select a location to view", "error");
    return;
  }

  try {
    setLoadingState("viewButton", true);
    const response = await makeRequest(
      `/api/location/view/${locationId}`,
      "GET"
    );

    if (response.success && response.location) {
      const location = response.location;
      document.getElementById("shipping-address").value =
        location.address || "";
      document.getElementById("email").value = location.email || "";
      document.getElementById("phone").value = location.telephone || "";
      document.getElementById("contact-person").value =
        location.contact_person || "";

      // Update last updated info
      document.getElementById("last-updated").value = location.updated_at || "";

      // Save current state for auto-save comparison
      lastSavedState = getFormData();
    }
  } catch (error) {
    showToast(`Error viewing location: ${error.message}`, "error");
  } finally {
    setLoadingState("viewButton", false);
  }
}

async function addLocation() {
  const errors = validateForm();
  if (errors.length > 0) {
    showToast(errors[0], "error");
    return;
  }

  try {
    setLoadingState("addButton", true);
    const formData = getFormData();
    const response = await makeRequest("/api/location/add", "POST", formData);

    if (response.success) {
      showToast("Location added successfully");
      await loadLocationDropdown();
      clearForm();
      lastSavedState = {};
    }
  } catch (error) {
    showToast(`Error adding location: ${error.message}`, "error");
  } finally {
    setLoadingState("addButton", false);
  }
}

async function modifyLocation() {
  const errors = validateForm();
  if (errors.length > 0) {
    showToast(errors[0], "error");
    return;
  }

  const locationId = document.getElementById("location-name-dropdown").value;
  if (!locationId) {
    showToast("Please select a location to modify", "error");
    return;
  }

  try {
    setLoadingState("modifyButton", true);
    const formData = getFormData();
    const response = await makeRequest("/api/location/modify", "PUT", formData);

    if (response.success) {
      showToast("Location updated successfully");
      await loadLocationDropdown();
      lastSavedState = getFormData();
    }
  } catch (error) {
    showToast(`Error modifying location: ${error.message}`, "error");
  } finally {
    setLoadingState("modifyButton", false);
  }
}

async function deleteLocation() {
  const locationId = document.getElementById("location-name-dropdown").value;
  if (!locationId) {
    showToast("Please select a location to delete", "error");
    return;
  }

  if (!confirm("Are you sure you want to delete this location?")) {
    return;
  }

  try {
    setLoadingState("deleteButton", true);
    const response = await makeRequest("/api/location/delete", "DELETE", {
      location_id: locationId,
    });

    if (response.success) {
      showToast("Location deleted successfully");
      await loadLocationDropdown();
      clearForm();
      lastSavedState = {};
    }
  } catch (error) {
    showToast(`Error deleting location: ${error.message}`, "error");
  } finally {
    setLoadingState("deleteButton", false);
  }
}

async function exportLocations() {
  try {
    setLoadingState("exportButton", true);
    window.location.href = "/api/locations/export";
    showToast("Export started");
  } catch (error) {
    showToast(`Error exporting locations: ${error.message}`, "error");
  } finally {
    setLoadingState("exportButton", false);
  }
}

// Auto-save functionality
function setupAutoSave() {
  const form = document.getElementById("locationForm");
  const formInputs = form.querySelectorAll("input, textarea, select");

  formInputs.forEach((input) => {
    input.addEventListener("change", () => {
      clearTimeout(autoSaveTimeout);
      autoSaveTimeout = setTimeout(autoSave, AUTOSAVE_DELAY);
    });
  });
}

async function autoSave() {
  const currentState = getFormData();
  if (JSON.stringify(currentState) !== JSON.stringify(lastSavedState)) {
    try {
      await modifyLocation();
    } catch (error) {
      console.error("Auto-save failed:", error);
    }
  }
}

// Keyboard Shortcuts
function setupKeyboardShortcuts() {
  document.addEventListener("keydown", (e) => {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key.toLowerCase()) {
        case "s":
          e.preventDefault();
          modifyLocation();
          break;
        case "n":
          e.preventDefault();
          document.getElementById("add-new-location-toggle").click();
          break;
        case "e":
          e.preventDefault();
          exportLocations();
          break;
      }
    } else if (e.key === "Escape") {
      e.preventDefault();
      clearForm();
    }
  });
}

// Search functionality
function setupSearch() {
  const searchInput = document.getElementById("location-search");
  let searchTimeout;

  searchInput.addEventListener("input", () => {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(loadLocationDropdown, 300);
  });
}

// Toggle location input type
function toggleLocationInput() {
  const toggle = document.getElementById("add-new-location-toggle");
  const dropdown = document.getElementById("location-name-dropdown");
  const input = document.getElementById("location-name-input");

  if (toggle.checked) {
    dropdown.classList.add("hidden");
    input.classList.remove("hidden");
  } else {
    dropdown.classList.remove("hidden");
    input.classList.add("hidden");
  }
}

// Initialize everything when the DOM is ready
document.addEventListener("DOMContentLoaded", function () {
  // Setup event listeners
  document
    .getElementById("add-new-location-toggle")
    .addEventListener("change", toggleLocationInput);

  document
    .getElementById("location-name-dropdown")
    .addEventListener("change", viewLocation);

  // Initialize features
  setupAutoSave();
  setupKeyboardShortcuts();
  setupSearch();
  loadLocationDropdown();

  // Set default value for "Updated By" field
  document.getElementById("updated-by").value = "system";
});

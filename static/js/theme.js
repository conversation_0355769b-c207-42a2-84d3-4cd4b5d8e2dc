// theme.js - Manages theme across the entire application

document.addEventListener("DOMContentLoaded", function () {
  // Prevent multiple initializations
  if (window.themeInitialized) {
    console.log("Theme already initialized, skipping...");
    return;
  }
  window.themeInitialized = true;

  const html = document.documentElement;
  const body = document.body;
  const themeToggle = document.getElementById("themeToggle");
  // Check for login page toggle
  const loginThemeToggle = document.getElementById("loginThemeToggle");

  // Create a custom event for theme changes
  const themeChangedEvent = new Event("themeChanged");

  // Check for saved theme preference or use system preference
  const savedTheme =
    localStorage.getItem("theme") ||
    (window.matchMedia("(prefers-color-scheme: dark)").matches
      ? "dark"
      : "light");

  // Function to set theme
  function setTheme(theme) {
    console.log("Setting theme to:", theme);

    // Apply theme to the HTML element for Tailwind's dark: variant
    html.classList.toggle("dark", theme === "dark");

    // Apply theme to body for custom CSS variables
    body.classList.toggle("dark", theme === "dark");

    // Add dark-transition class for smooth transitions
    body.classList.add("dark-transition");

    // Toggle sun/moon icons - find all instances on the page
    const sunIcons = document.querySelectorAll(".fa-sun");
    const moonIcons = document.querySelectorAll(".fa-moon");

    if (theme === "dark") {
      sunIcons.forEach(icon => icon.classList.add("hidden"));
      moonIcons.forEach(icon => icon.classList.remove("hidden"));
    } else {
      sunIcons.forEach(icon => icon.classList.remove("hidden"));
      moonIcons.forEach(icon => icon.classList.add("hidden"));
    }

    // Update any charts on the page
    updateChartsForTheme(theme);

    // Store theme preference
    localStorage.setItem("theme", theme);

    // Dispatch the theme changed event
    document.dispatchEvent(themeChangedEvent);
  }

  // Function to update chart colors for theme
  function updateChartsForTheme(theme) {
    if (window.Chart) {
      const charts = Object.values(window.Chart.instances || {});
      charts.forEach(chart => {
        // Update text colors
        if (chart.options.scales?.x) {
          chart.options.scales.x.ticks.color = theme === 'dark' ? '#9ca3af' : '#4b5563';
        }
        if (chart.options.scales?.y) {
          chart.options.scales.y.ticks.color = theme === 'dark' ? '#9ca3af' : '#4b5563';
        }
        
        // Update grid colors
        if (chart.options.scales?.x) {
          chart.options.scales.x.grid.color = theme === 'dark' ? 'rgba(75, 85, 99, 0.3)' : 'rgba(229, 231, 235, 0.8)';
        }
        if (chart.options.scales?.y) {
          chart.options.scales.y.grid.color = theme === 'dark' ? 'rgba(75, 85, 99, 0.3)' : 'rgba(229, 231, 235, 0.8)';
        }
        
        chart.update();
      });
    }
  }

  // Set initial theme
  setTheme(savedTheme);

  // Add event listeners for all theme toggles
  function setupThemeToggle(toggleElement) {
    if (toggleElement) {
      // Clone and replace to remove all existing listeners
      const newToggle = toggleElement.cloneNode(true);
      toggleElement.parentNode.replaceChild(newToggle, toggleElement);

      // Add a single event listener to the new toggle
      newToggle.addEventListener("click", function () {
        console.log("Theme toggle clicked");
        const currentTheme = html.classList.contains("dark") ? "light" : "dark";
        setTheme(currentTheme);
      });
    }
  }

  // Setup all theme toggles
  setupThemeToggle(themeToggle);
  setupThemeToggle(loginThemeToggle);

  // Listen for system theme changes
  window
    .matchMedia("(prefers-color-scheme: dark)")
    .addEventListener("change", (e) => {
      if (!localStorage.getItem("theme")) {
        setTheme(e.matches ? "dark" : "light");
      }
    });
});

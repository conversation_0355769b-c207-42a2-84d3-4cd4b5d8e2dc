// Flag to prevent duplicate submissions
let isSubmitting = false;

// Global state
let selectedWafers = new Set();
let currentSort = {
  column: null,
  direction: "asc",
};

// Pagination state management
const PaginationState = {
  currentPage: 1,
  itemsPerPage: 10,
  totalPages: 1,
  totalItems: 0,
  filterCriteria: null,

  // Load saved state from localStorage
  init() {
    try {
      const savedState = JSON.parse(localStorage.getItem("paginationState"));
      if (savedState) {
        this.currentPage = savedState.currentPage || 1;
        this.itemsPerPage = savedState.itemsPerPage || 10;
        this.filterCriteria = savedState.filterCriteria || null;
      }
    } catch (error) {
      console.error("Error loading pagination state:", error);
    }
  },

  // Save state to localStorage
  save() {
    try {
      localStorage.setItem(
        "paginationState",
        JSON.stringify({
          currentPage: this.currentPage,
          itemsPerPage: this.itemsPerPage,
          filterCriteria: this.filterCriteria,
        })
      );
    } catch (error) {
      console.error("Error saving pagination state:", error);
    }
  },

  // Update state and save
  update(newState) {
    Object.assign(this, newState);
    this.save();
  },
};

// State Management
const TableState = {
  selectedWafers: new Set(),
  currentSort: {
    column: null,
    direction: "asc",
  },
  pagination: {
    currentPage: 1,
    itemsPerPage: 10,
    totalPages: 1,
    totalItems: 0,
  },
  filters: {
    searchTerm: "",
    activeFilters: new Map(),
  },
};

// State management functions
const ShipmentState = {
  init() {
    selectedWafers = new Set();
    try {
      // Restore any saved state from sessionStorage
      const savedWafers = sessionStorage.getItem("selectedWafers");
      if (savedWafers) {
        selectedWafers = new Set(JSON.parse(savedWafers));
      }
    } catch (error) {
      console.error("Error initializing state:", error);
    }
  },

  addWafer(waferId) {
    selectedWafers.add(waferId);
    this.saveState();
    this.updateUI();
  },

  removeWafer(waferId) {
    selectedWafers.delete(waferId);
    this.saveState();
    this.updateUI();
  },

  clearSelection() {
    selectedWafers.clear();
    this.saveState();
    this.updateUI();
  },

  saveState() {
    try {
      sessionStorage.setItem(
        "selectedWafers",
        JSON.stringify(Array.from(selectedWafers))
      );
    } catch (error) {
      console.error("Error saving state:", error);
    }
  },

  updateUI() {
    const countDisplay = document.getElementById("wafer-count-display");
    const textarea = document.getElementById("wafer-ids-textarea");
    const waferCountInput = document.getElementById("wafer-count");

    if (countDisplay) {
      countDisplay.textContent = `${selectedWafers.size} wafers selected`;
    }

    if (textarea) {
      textarea.value = Array.from(selectedWafers).sort().join(", ");
    }

    if (waferCountInput) {
      waferCountInput.value = selectedWafers.size;
    }

    this.validateSelection();
  },

  validateSelection() {
    const validationMessage = document.getElementById(
      "wafer-validation-message"
    );
    const waferCountInput = document.getElementById("wafer-count");

    if (!validationMessage || !waferCountInput) return;

    const expectedCount = parseInt(waferCountInput.value) || 0;
    const actualCount = selectedWafers.size;

    if (expectedCount > 0 && actualCount !== expectedCount) {
      validationMessage.innerHTML = `
                <span class="text-yellow-600">
                    <i class="fas fa-exclamation-triangle mr-1"></i>
                    Selected wafers (${actualCount}) doesn't match expected count (${expectedCount})
                </span>`;
    } else {
      validationMessage.innerHTML = "";
    }
  },
};

// Setup form submission - centralized function to handle form submission
function setupFormSubmission() {
  // Get the form and button elements
  const shipmentForm = document.getElementById("shipment-form");
  const createBtn = document.querySelector("button.bg-green-600");

  if (!shipmentForm || !createBtn) {
    console.error("[DEBUG] Form or Create button not found");
    return;
  }

  // First remove any existing listeners by cloning and replacing elements
  const newForm = shipmentForm.cloneNode(true);
  shipmentForm.parentNode.replaceChild(newForm, shipmentForm);

  const newBtn = createBtn.cloneNode(true);
  createBtn.parentNode.replaceChild(newBtn, createBtn);

  // Add submit handler to form
  newForm.addEventListener("submit", function (e) {
    e.preventDefault();
    console.log("[DEBUG] Form submitted, but using button handler instead");
    // We let the button handler deal with the actual submission
  });

  // Add click handler to button with submission protection
  newBtn.addEventListener("click", function (e) {
    e.preventDefault();

    // Check if already submitting
    if (isSubmitting) {
      console.log(
        "[DEBUG] Submission already in progress, ignoring duplicate click"
      );
      return;
    }

    // Perform form validation
    if (!validateForm()) {
      console.log("[DEBUG] Form validation failed");
      return;
    }

    // Set submission flag
    isSubmitting = true;

    // Visual feedback
    const originalText = this.innerHTML;
    this.disabled = true;
    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating...';

    console.log("[DEBUG] Starting task creation");

    // Call the create function
    handleCreate(e)
      .then((result) => {
        console.log("[DEBUG] Task created successfully:", result);
        // Success message is handled in handleCreate
      })
      .catch((error) => {
        console.error("[DEBUG] Error creating task:", error);
        Swal.fire({
          icon: "error",
          title: "Error",
          text: error.message || "Failed to create task",
        });
      })
      .finally(() => {
        // Reset submission state
        isSubmitting = false;
        this.disabled = false;
        this.innerHTML = originalText;
      });
  });

  console.log("[DEBUG] Form submission handlers set up successfully");
}

/**
 * Handles the Upload button click event
 * Uses the backend to extract data from an Asana task URL and populate the form
 */
async function handleUpload(e) {
  e.preventDefault();
  const asanaLink = document.getElementById("asana-link").value.trim();

  if (!asanaLink) {
    Swal.fire({
      icon: "error",
      title: "Error",
      text: "Please enter an Asana task URL",
    });
    return;
  }

  // Show loading state
  const uploadBtn = e.target.closest("button");
  const originalText = uploadBtn.innerHTML;
  uploadBtn.disabled = true;
  uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';

  try {
    // FIXED: Added /shipment prefix to match blueprint
    const response = await fetch("/shipment/api/asana/upload", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CSRFToken": getCsrfToken(),
      },
      body: JSON.stringify({ asana_url: asanaLink }),
    });

    if (!response.ok) {
      throw new Error(`Server error: ${response.status}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(
        result.message || "Failed to extract data from Asana task"
      );
    }

    // Populate the form with the extracted data
    populateFormFromAsana(result.data);

    // Show success message
    Swal.fire({
      icon: "success",
      title: "Success",
      text: "Asana task data loaded successfully",
      timer: 2000,
    });
  } catch (error) {
    console.error("Error uploading from Asana:", error);
    Swal.fire({
      icon: "error",
      title: "Upload Failed",
      text: error.message || "Failed to extract data from Asana",
    });
  } finally {
    // Reset button state
    uploadBtn.disabled = false;
    uploadBtn.innerHTML = originalText;
  }
}

// Function to filter the wafer table by lot IDs
async function filterWaferTableByLotIds(lotIds) {
  const tbody = document.getElementById("inventory-table-body");

  if (!tbody) {
    console.error("[DEBUG] Table body not found");
    return;
  }

  try {
    // Add a visual indicator for loading
    tbody.classList.add("opacity-50");

    // Clear any previous filter message
    const existingMessage = document.getElementById("filter-message");
    if (existingMessage) existingMessage.remove();

    // If no lot IDs provided, show all wafers
    if (!lotIds || lotIds.length === 0) {
      await loadWaferInventory();
      return;
    }

    // Build URL with lot ID filters
    let url = "/shipment/api/available-wafers?";

    // Add lot IDs as filter parameters
    lotIds.forEach((id) => {
      url += `lot_ids[]=${encodeURIComponent(id)}&`;
    });

    // Add pagination parameters
    url += `page=1&per_page=${TableState.pagination.itemsPerPage}`;

    console.log("[DEBUG] Sending search request:", url);

    // Fetch filtered data
    const response = await fetch(url, {
      headers: {
        Accept: "application/json",
        "X-CSRFToken": getCsrfToken(),
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.success) {
      // Clear the table
      tbody.innerHTML = "";

      if (data.data.length === 0) {
        // No results found
        showNoResultsMessage(tbody, lotIds);
      } else {
        // Populate the table with the filtered data
        populateWaferTable(data.data);
        updatePaginationState(data.pagination);

        // Add filter status message
        const totalWafers = data.data.reduce(
          (acc, lot) => acc + lot.wafers.length,
          0
        );
        const messageDiv = document.createElement("div");
        messageDiv.id = "filter-message";
        messageDiv.className =
          "text-sm text-gray-600 dark:text-gray-400 mt-2 mb-2 px-4";
        messageDiv.innerHTML = `
                    <div class="flex justify-between items-center">
                        <span>Found ${totalWafers} wafers in ${
          data.data.length
        } lot(s) matching: ${lotIds.join(", ")}</span>
                        <button class="text-blue-600 hover:text-blue-800 text-sm" onclick="clearFilters()">
                            <i class="fas fa-times mr-1"></i>Clear Filter
                        </button>
                    </div>
                `;

        const tableContainer = tbody.closest(".overflow-x-auto");
        if (tableContainer) {
          tableContainer.parentNode.insertBefore(messageDiv, tableContainer);
        }

        // Highlight matching rows
        highlightMatchingRows(lotIds);
      }
    } else {
      throw new Error(data.message || "Failed to filter wafers");
    }
  } catch (error) {
    console.error("[DEBUG] Error filtering wafers:", error);
    Swal.fire({
      icon: "error",
      title: "Error",
      text: "Failed to filter wafers. Please try again.",
    });
  } finally {
    // Remove loading indicator
    tbody.classList.remove("opacity-50");
  }
}

// Function to show "no results" message
function showNoResultsMessage(tbody, lotIds) {
  const noResultsRow = document.createElement("tr");
  noResultsRow.innerHTML = `
        <td colspan="12" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
            <div class="flex flex-col items-center justify-center space-y-2 py-8">
                <i class="fas fa-search text-4xl mb-2 text-gray-300 dark:text-gray-600"></i>
                <p class="text-lg">No wafers found matching: ${lotIds.join(
                  ", "
                )}</p>
                <button class="mt-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700" onclick="clearFilters()">
                    <i class="fas fa-eraser mr-1"></i>Clear Search
                </button>
            </div>
        </td>
    `;
  tbody.appendChild(noResultsRow);
}

// Function to highlight matching rows
function highlightMatchingRows(lotIds) {
  // Convert lot IDs to lowercase for case-insensitive matching
  const lowerLotIds = lotIds.map((id) => id.toLowerCase());

  // Get all table rows
  const rows = document.querySelectorAll("#inventory-table-body tr");

  // Check each row and highlight if it matches
  rows.forEach((row) => {
    const lotIdCell = row.querySelector("td:nth-child(3)"); // Assuming lot ID is in the 3rd column
    if (lotIdCell) {
      const rowLotId = lotIdCell.textContent.trim().toLowerCase();
      const isMatch = lowerLotIds.some((id) => rowLotId.includes(id));

      if (isMatch) {
        // Add highlight class
        row.classList.add("highlight-match");
      }
    }
  });
}

// Event handler for the LGT Lot IDs input field
function handleLotIdsInput(e) {
  const lotIdsInput = e.target;
  const lotIdsValue = lotIdsInput.value.trim();

  if (lotIdsValue) {
    // Split by commas and clean up
    const lotIds = lotIdsValue
      .split(",")
      .map((id) => id.trim())
      .filter((id) => id);

    if (lotIds.length > 0) {
      // Filter the wafer table to show only wafers from these lots
      filterWaferTableByLotIds(lotIds);
    }
  } else {
    // If field is cleared, reset the table
    loadWaferInventory();
  }
}

// Add debounced event listener to LGT Lot IDs input
document.addEventListener("DOMContentLoaded", function () {
  const lotIdsInput = document.getElementById("lot-ids");
  if (lotIdsInput) {
    lotIdsInput.addEventListener("input", debounce(handleLotIdsInput, 500));
  }

  // Set up form submission handlers
  setupFormSubmission();
});

// Handle create
async function handleCreate(e) {
  e.preventDefault();

  // Safety check - don't proceed if not in submission state
  if (!isSubmitting) {
    console.error(
      "[DEBUG] Attempt to call handleCreate without proper submission flow"
    );
    return Promise.reject(new Error("Invalid submission state"));
  }

  try {
    console.log("[DEBUG] Starting task creation...");

    const form = document.getElementById("shipment-form");
    const formData = new FormData(form);

    // Convert FormData to an object
    const data = {};
    formData.forEach((value, key) => {
      data[key] = value;
    });

    // Get lot IDs from the input field
    const lotIdsInput = document.getElementById("lot-ids");
    if (lotIdsInput) {
      data.lot_ids = lotIdsInput.value.trim();
      console.log("[DEBUG] Found lot IDs:", data.lot_ids);
    } else {
      console.error("[DEBUG] lot-ids input field not found!");
    }

    // Get selected wafer IDs
    const waferIdsTextarea = document.getElementById("wafer_ids");
    const waferIds = waferIdsTextarea
      ? waferIdsTextarea.value.split(/[,\s]+/).filter((id) => id.trim())
      : [];

    // Add wafer IDs to the data
    data.wafer_ids = waferIds;

    // Convert checkboxes to boolean values
    ["need_reviewing", "label_free", "keep_cassette_closed"].forEach(
      (field) => {
        data[field] = formData.get(field) === "on";
      }
    );

    // Add special handling for shipment metadata
    data.shipment_metadata = {
      // Erfurt section
      xfab_po: formData.get("xfab_po") || "",
      xfab_device_id: formData.get("xfab_device_id") || "",
      project_id: formData.get("project_id") || "",

      // Eiger section
      eiger_number: formData.get("eiger_number") || "",
      tapeout: formData.get("tapeout") || "",
      vendor_lot: formData.get("vendor_lot") || "",
      customer_lot: formData.get("customer_lot") || "",
      rib: formData.get("rib") === "true",
      tox_target_sin: formData.get("tox_target_sin") || "",
      heater: formData.get("heater") === "true",
      undercut: formData.get("undercut") === "true",
      sin_tube_position: formData.get("sin_tube_position") || "",
      mask: formData.get("mask") || "",
      comments: formData.get("comments") || "",
    };

    // Get CSRF token
    const csrfToken = document.querySelector('input[name="csrf_token"]').value;

    // Log the data being sent (for debugging)
    console.log("[DEBUG] Sending data to create shipment:", data);
    console.log("[DEBUG] Data has lot_ids?", "lot_ids" in data);
    console.log("[DEBUG] Data stringified:", JSON.stringify(data));

    // Make the API call
    const response = await fetch("/shipment/api/shipments", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CSRFToken": csrfToken,
      },
      body: JSON.stringify(data),
    });

    console.log("[DEBUG] Request sent with method:", response.request?.method);
    console.log("[DEBUG] Response status:", response.status);
    console.log("[DEBUG] Response URL:", response.url); // Check if redirected

    const result = await response.json();
    console.log("[DEBUG] API response:", result);

    if (result.success) {
      Swal.fire({
        icon: "success",
        title: "Success!",
        text: "Shipment task created successfully",
        timer: 2000,
      });

      resetForm();

      // Clear form
      form.reset();

      // Reload wafer inventory if needed
      if (typeof loadWaferInventory === "function") {
        loadWaferInventory();
      }

      return result;
    } else {
      throw new Error(result.message || "Failed to create shipment");
    }
  } catch (error) {
    console.error("[DEBUG] Error in handleCreate:", error);
    throw error;
  }
}
// Add form validation for location fields
function validateForm() {
  const form = document.getElementById("shipment-form");

  // Check required fields
  const requiredFields = [
    "title",
    "priority",
    "location_id",
    "contact_person",
    "email",
    "address",
    "telephone",
    "destination_label",
  ];

  const missingFields = [];
  requiredFields.forEach((field) => {
    const element = form.elements[field];
    if (!element || !element.value.trim()) {
      missingFields.push(field);
    }
  });

  if (missingFields.length > 0) {
    Swal.fire({
      icon: "warning",
      title: "Missing Required Fields",
      text: `Please fill in the following fields: ${missingFields.join(", ")}`,
    });
    return false;
  }

  // Validate email format
  const email = form.elements["email"].value;
  if (email && !isValidEmail(email)) {
    Swal.fire({
      icon: "warning",
      title: "Invalid Email",
      text: "Please enter a valid email address",
    });
    return false;
  }

  // Validate telephone format
  const telephone = form.elements["telephone"].value;
  if (telephone && !isValidPhone(telephone)) {
    Swal.fire({
      icon: "warning",
      title: "Invalid Phone Number",
      text: "Please enter a valid phone number",
    });
    return false;
  }

  // Validate wafer count
  const waferIdsTextarea = document.getElementById("wafer_ids");
  const waferCount = document.getElementById("number_of_wafers");

  if (waferIdsTextarea && waferCount) {
    const waferIds = waferIdsTextarea.value
      .split(/[,\s]+/)
      .filter((id) => id.trim());
    const expectedCount = parseInt(waferCount.value) || 0;

    if (expectedCount > 0 && waferIds.length !== expectedCount) {
      Swal.fire({
        icon: "warning",
        title: "Wafer Count Mismatch",
        text: `Selected wafers (${waferIds.length}) doesn't match expected count (${expectedCount})`,
      });
      return false;
    }
  }

  return true;
}

// Helper functions for validation
function isValidEmail(email) {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
}

function isValidPhone(phone) {
  // Basic validation for phone numbers
  return /^[\d\s\-+()]{10,}$/.test(phone);
}

// Handle location change
function handleLocationChange(event) {
  try {
    const locationId = event.target.value;
    console.log("[DEBUG] Location changed to:", locationId);

    // Get all required form fields
    const fields = {
      contactInput: document.querySelector('[name="contact_person"]'),
      emailInput: document.querySelector('[name="email"]'),
      addressInput: document.querySelector('[name="address"]'),
      telephoneInput: document.querySelector('[name="telephone"]'),
      destinationLabel: document.querySelector('[name="destination_label"]'),
    };

    // Validate that all required fields exist
    const missingFields = Object.entries(fields)
      .filter(([key, element]) => !element)
      .map(([key]) => key);

    if (missingFields.length > 0) {
      console.error("[DEBUG] Missing form fields:", missingFields);
      throw new Error(
        "Required form fields not found: " + missingFields.join(", ")
      );
    }

    if (!locationId || locationId === "new") {
      clearLocationFields(fields);
      return;
    }

    // Show loading state
    event.target.disabled = true;
    Object.values(fields).forEach((field) => {
      if (field) {
        field.disabled = true;
        field.classList.add("opacity-50");
      }
    });

    // Fetch location details
    fetch(`/shipment/api/locations/${encodeURIComponent(locationId)}`, {
      headers: {
        Accept: "application/json",
        "X-CSRFToken": getCsrfToken(),
      },
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error("Failed to fetch location details");
        }
        return response.json();
      })
      .then((data) => {
        if (data.success) {
          // Update fields with location data
          updateLocationFields(fields, data.data);
        } else {
          throw new Error(data.message || "Failed to load location details");
        }
      })
      .catch((error) => {
        console.error("[DEBUG] Error fetching location details:", error);
        showError("Failed to load location details");
        clearLocationFields(fields);
      })
      .finally(() => {
        // Reset loading state
        event.target.disabled = false;
        Object.values(fields).forEach((field) => {
          if (field) {
            field.classList.remove("opacity-50");
          }
        });
      });
  } catch (error) {
    console.error("[DEBUG] Error in handleLocationChange:", error);
    showError(error.message || "Error handling location change");
  }
}

// Update location fields helper
function updateLocationFields(fields, locationData) {
  try {
    // Verify we have both fields and data
    if (!fields || !locationData) {
      throw new Error("Missing fields or location data");
    }

    // Map location data to form fields
    const fieldMapping = {
      contactInput: locationData.contact_person || "",
      emailInput: locationData.email || "",
      addressInput: locationData.address || "",
      telephoneInput: locationData.telephone || "",
      destinationLabel: locationData.label || "",
    };

    // Update each field with corresponding data
    Object.entries(fieldMapping).forEach(([fieldKey, value]) => {
      const field = fields[fieldKey];
      if (field) {
        field.value = value;
        field.disabled = false;
        field.dataset.prefilled = "true";
        field.classList.add("bg-gray-50");
      }
    });
  } catch (error) {
    console.error("[DEBUG] Error updating location fields:", error);
    throw error;
  }
}

// Clear location fields helper
function clearLocationFields(fields) {
  Object.values(fields).forEach((field) => {
    if (field) {
      field.value = "";
      field.disabled = false;
      field.classList.remove("bg-gray-50");
      field.removeAttribute("data-prefilled");
    }
  });
}

function handleDelete(e) {
  e.preventDefault();
  console.log("Delete clicked");
  if (selectedWafers.size === 0) {
    Swal.fire({
      title: "No Selection",
      text: "Please select wafers to delete",
      icon: "warning",
    });
    return;
  }

  Swal.fire({
    title: "Confirm Delete",
    text: `Are you sure you want to delete ${selectedWafers.size} selected items?`,
    icon: "warning",
    showCancelButton: true,
    confirmButtonText: "Delete",
    cancelButtonText: "Cancel",
  }).then((result) => {
    if (result.isConfirmed) {
      // Implement delete functionality
      console.log("Deleting wafers:", Array.from(selectedWafers));
    }
  });
}

/**
 * Handles the Modify button click event
 * Updates an existing Asana task with the current form data
 */
async function handleModify(e) {
  e.preventDefault();
  const asanaLink = document.getElementById("asana-link").value.trim();

  if (!asanaLink) {
    Swal.fire({
      icon: "error",
      title: "Error",
      text: "Please enter an Asana task URL to modify",
    });
    return;
  }

  // Validate form before proceeding
  if (!validateForm()) {
    return;
  }

  // Show loading state
  const modifyBtn = e.target.closest("button");
  const originalText = modifyBtn.innerHTML;
  modifyBtn.disabled = true;
  modifyBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';

  try {
    // Collect form data
    const form = document.getElementById("shipment-form");
    const formData = new FormData(form);

    // Convert FormData to plain object
    const data = {};
    formData.forEach((value, key) => {
      data[key] = value;
    });

    // Get wafer IDs
    const waferIdsText = document.getElementById("wafer_ids").value;
    data.wafer_ids = waferIdsText
      .split(/[,\s]+/)
      .map((id) => id.trim())
      .filter((id) => id);

    // Get lot IDs
    const lotIdsInput = document.getElementById("lot-ids");
    if (lotIdsInput && lotIdsInput.value) {
      data.lot_ids = lotIdsInput.value.trim();
    }

    // Get shipment date
    const shipmentDateInput = document.getElementById("shipment_date");
    if (shipmentDateInput && shipmentDateInput.value) {
      data.shipment_date = shipmentDateInput.value;
    }

    // Format checkbox fields
    data.need_reviewing = document.getElementById("need_reviewing").checked;
    data.label_free = document.getElementById("label_free").checked;
    data.keep_cassette_closed = document.getElementById(
      "keep_cassette_closed"
    ).checked;

    // Add metadata
    data.shipment_metadata = {
      xfab_po: formData.get("xfab_po") || "",
      xfab_device_id: formData.get("xfab_device_id") || "",
      project_id: formData.get("project_id") || "",
      eiger_number: formData.get("eiger_number") || "",
      tapeout: formData.get("tapeout") || "",
      vendor_lot: formData.get("vendor_lot") || "",
      customer_lot: formData.get("customer_lot") || "",
      rib: formData.get("rib") === "true",
      tox_target_sin: formData.get("tox_target_sin") || "",
      heater: formData.get("heater") === "true",
      undercut: formData.get("undercut") === "true",
      sin_tube_position: formData.get("sin_tube_position") || "",
      mask: formData.get("mask") || "",
      comments: formData.get("comments") || "",
    };

    // Log the data being sent (for debugging)
    console.log("Sending data to modify Asana task:", data);

    // Call the backend API to update the Asana task
    const response = await fetch("/shipment/api/asana/modify", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CSRFToken": getCsrfToken(),
      },
      body: JSON.stringify({
        asana_url: asanaLink,
        form_data: data,
      }),
    });

    // Handle non-OK responses with more detailed error info
    if (!response.ok) {
      const errorText = await response.text();
      console.error("Error response:", response.status, errorText);
      throw new Error(`Server error: ${response.status}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.message || "Failed to update Asana task");
    }

    // Show success message
    Swal.fire({
      icon: "success",
      title: "Success",
      text: "Asana task updated successfully",
      timer: 2000,
    });
  } catch (error) {
    console.error("Error modifying Asana task:", error);
    Swal.fire({
      icon: "error",
      title: "Update Failed",
      text: error.message || "Failed to update Asana task",
    });
  } finally {
    // Reset button state
    modifyBtn.disabled = false;
    modifyBtn.innerHTML = originalText;
  }
}

// Auto reset form function
function resetForm() {
  // Get the form element
  const form = document.getElementById("shipment-form");

  // Reset the form to clear all fields
  if (form) form.reset();

  // Clear the lot IDs field
  const lotIdsInput = document.getElementById("lot-ids");
  if (lotIdsInput) lotIdsInput.value = "";

  // Reset wafer selection
  if (typeof ShipmentState !== "undefined" && ShipmentState.clearSelection) {
    ShipmentState.clearSelection();
  } else if (typeof selectedWafers !== "undefined") {
    selectedWafers.clear();
    updateSelectedWafersCount();
  }

  // Reset wafer IDs textarea
  const waferIdsTextarea = document.getElementById("wafer_ids");
  if (waferIdsTextarea) waferIdsTextarea.value = "";

  // Reset table filters and restore all wafers
  if (typeof clearFilters === "function") {
    clearFilters();
  }

  // Reset any validation messages
  const validationMessage = document.getElementById("wafer-validation-message");
  if (validationMessage) validationMessage.innerHTML = "";

  // Hide any selection message
  const selectionMessage = document.getElementById("selection-message");
  if (selectionMessage) selectionMessage.classList.add("hidden");

  console.log("[DEBUG] Form has been reset automatically");
}

// Initialize Event Listeners
function initializeEventListeners() {
  console.log("[DEBUG] Setting up event listeners...");

  // Initialize pagination with retry mechanism
  const initPagination = () => {
    const paginationContainer = document.getElementById("pagination-container");
    if (paginationContainer) {
      try {
        initializePagination();
      } catch (error) {
        console.error("[DEBUG] Error initializing pagination:", error);
        setTimeout(initPagination, 100);
      }
    } else {
      console.warn("[DEBUG] Pagination container not found, retrying...");
      setTimeout(initPagination, 100);
    }
  };

  // Search functionality
  const asanaLink = document.getElementById("asana-link");
  const lotIds = document.getElementById("lot-ids");

  if (asanaLink) {
    asanaLink.addEventListener("input", debounce(handleAsanaLinkChange, 500));
    console.log("[DEBUG] Asana link listener attached");
  }

  if (lotIds) {
    lotIds.addEventListener("input", debounce(handleLotIdsChange, 500));
    console.log("[DEBUG] Lot IDs listener attached");
  }

  // Action buttons (SKIP the create button - it's handled in setupFormSubmission)
  const searchBtn = document.querySelector('button[title="Search"]');
  const eraserBtn = document.querySelector('button[title="eraser"]');
  const uploadBtn = document.querySelector("button.bg-blue-600");
  const deleteBtn = document.querySelector("button.bg-red-600");
  const modifyBtn = document.querySelector("button.bg-yellow-600");

  if (searchBtn) {
    searchBtn.addEventListener("click", handleSearch);
    console.log("[DEBUG] Search button listener attached");
  }

  if (eraserBtn) {
    eraserBtn.addEventListener("click", clearForm);
    console.log("[DEBUG] Eraser button listener attached");
  }

  if (uploadBtn) {
    uploadBtn.addEventListener("click", handleUpload);
    console.log("[DEBUG] Upload button listener attached");
  }

  if (deleteBtn) {
    deleteBtn.addEventListener("click", handleDelete);
    console.log("[DEBUG] Delete button listener attached");
  }

  if (modifyBtn) {
    modifyBtn.addEventListener("click", handleModify);
    console.log("[DEBUG] Modify button listener attached");
  }

  // Select all checkbox
  const selectAll = document.getElementById("select-all");
  if (selectAll) {
    selectAll.addEventListener("change", handleSelectAll);
    console.log("[DEBUG] Select all checkbox listener attached");
  }

  // Form handling - prevent default instead of using handleFormSubmit
  const shipmentForm = document.getElementById("shipment-form");
  if (shipmentForm) {
    shipmentForm.addEventListener("submit", (e) => e.preventDefault());
    console.log("[DEBUG] Form submit prevention attached");
  }

  // Location dropdown change
  const locationSelect = document.querySelector('select[name="location_id"]');
  if (locationSelect) {
    locationSelect.addEventListener("change", handleLocationChange);
    console.log("[DEBUG] Location select listener attached");
  }

  // Added wafer count input handling
  const waferCountInput = document.getElementById("wafer-count");
  if (waferCountInput) {
    waferCountInput.addEventListener("input", () => {
      validateWaferSelection();
      console.log("[DEBUG] Wafer count validation triggered");
    });
  }

  // Initialize table controls
  initializeTableControls();
  console.log("[DEBUG] Table controls initialized");

  // Initialize wafer selection handling
  initializeWaferSelection();
  console.log("[DEBUG] Wafer selection initialized");

  // Initialize pagination (moved to end)
  initPagination();
  console.log("[DEBUG] Pagination initialization requested");

  // Add keyboard navigation
  document.addEventListener("keydown", handlePaginationKeyboard);
  console.log("[DEBUG] Keyboard navigation listeners attached");

  // Add global error handling
  window.addEventListener("error", function (e) {
    console.error("[DEBUG] Global error caught:", e.error);
    showError(
      "An unexpected error occurred. Please check the console for details."
    );
  });

  console.log("[DEBUG] All event listeners initialized successfully");
}

// API Calls
async function fetchWithCsrf(url, options = {}) {
  const defaultOptions = {
    headers: {
      "Content-Type": "application/json",
      "X-CSRFToken": getCsrfToken(),
    },
    credentials: "same-origin", // This is important for CSRF
  };

  return fetch(url, { ...defaultOptions, ...options });
}

// Load locations from the DB
async function loadLocations() {
  try {
    console.log("[DEBUG] Loading locations...");
    const response = await fetch("/shipment/api/available-locations", {
      headers: {
        "X-CSRFToken": getCsrfToken(),
      },
    });

    if (!response.ok) throw new Error("Failed to fetch locations");

    const data = await response.json();
    if (data.success) {
      // Store locations in a global map for easy lookup
      window.locationMap = new Map(
        data.data.map((loc) => [loc.label, loc.location_id])
      );
      populateLocationDropdown(data.data);
      console.log("[DEBUG] Location map:", window.locationMap);
    } else {
      console.error("Failed to load locations:", data.message);
      showError("Failed to load locations");
    }
  } catch (error) {
    console.error("Error loading locations:", error);
    showError("Error loading locations");
  }
}

// Handle location change
function handleLocationChange(event) {
  try {
    const selectedLocationId = event.target.value;
    const form = document.querySelector("#shipment-form");
    const fields = {
      contactInput: form.querySelector('input[name="contact_person"]'),
      emailInput: form.querySelector('input[name="email"]'),
      addressInput: form.querySelector('input[name="address"]'),
      telephoneInput: form.querySelector('input[name="telephone"]'),
      destinationLabel: form.querySelector('input[name="destination_label"]'),
    };

    // Add clear button to each field
    Object.values(fields).forEach((input) => {
      if (!input) return;

      // Create clear button if it doesn't exist
      if (!input.nextElementSibling?.classList.contains("clear-field")) {
        const clearBtn = document.createElement("button");
        clearBtn.type = "button";
        clearBtn.className =
          "clear-field absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600";
        clearBtn.innerHTML = '<i class="fas fa-times"></i>';
        clearBtn.onclick = () => {
          input.value = "";
          input.disabled = false;
          input.focus();
        };

        // Wrap input in relative div if not already
        if (!input.parentElement.classList.contains("relative")) {
          const wrapper = document.createElement("div");
          wrapper.className = "relative";
          input.parentNode.insertBefore(wrapper, input);
          wrapper.appendChild(input);
          wrapper.appendChild(clearBtn);
        }
      }
    });

    // Add "Save as New Location" button if fields are modified
    const saveNewLocationBtn = document.createElement("button");
    saveNewLocationBtn.type = "button";
    saveNewLocationBtn.className =
      "hidden mt-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700";
    saveNewLocationBtn.textContent = "Save as New Location";
    saveNewLocationBtn.onclick = () => saveNewLocation(fields);

    if (!document.querySelector("#save-location-btn")) {
      const lastField = Object.values(fields).pop();
      if (lastField) {
        lastField.parentElement.after(saveNewLocationBtn);
        saveNewLocationBtn.id = "save-location-btn";
      }
    }

    // Show button when fields are modified
    Object.values(fields).forEach((input) => {
      if (!input) return;
      input.addEventListener("input", () => {
        saveNewLocationBtn.classList.remove("hidden");
      });
    });

    if (selectedLocationId === "new") {
      Object.values(fields).forEach((input) => {
        if (input) {
          input.value = "";
          input.disabled = false;
        }
      });
      saveNewLocationBtn.classList.remove("hidden");
      return;
    }

    // Fetch existing location data
    fetchLocationDetails(selectedLocationId, fields);
  } catch (error) {
    console.error("Error handling location change:", error);
    Swal.fire({
      icon: "error",
      title: "Error",
      text: "Failed to handle location change. Please try again.",
    });
  }
}

async function saveNewLocation(fields) {
  try {
    const locationData = {
      label: fields.destinationLabel.value,
      address: fields.addressInput.value,
      contact_person: fields.contactInput.value,
      email: fields.emailInput.value,
      telephone: fields.telephoneInput.value,
    };

    const response = await fetch("/shipment/api/locations", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CSRFToken": getCsrfToken(),
      },
      body: JSON.stringify(locationData),
    });

    const result = await response.json();

    if (result.success) {
      Swal.fire({
        icon: "success",
        title: "Success",
        text: "New location saved successfully",
      });
      // Refresh location dropdown
      loadLocations();
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error("Error saving new location:", error);
    Swal.fire({
      icon: "error",
      title: "Error",
      text: "Failed to save new location. Please try again.",
    });
  }
}

// Fetch Location Details
async function fetchLocationDetails(locationId) {
  try {
    console.log("[DEBUG] Fetching location details for:", locationId);

    // Get all form fields first
    const fields = {
      contact_person: document.querySelector('[name="contact_person"]'),
      email: document.querySelector('[name="email"]'),
      address: document.querySelector('[name="address"]'),
      telephone: document.querySelector('[name="telephone"]'),
      destination_label: document.querySelector('[name="destination_label"]'),
    };

    // Validate all fields exist before proceeding
    const missingFields = Object.entries(fields)
      .filter(([name, element]) => !element)
      .map(([name]) => name);

    if (missingFields.length > 0) {
      throw new Error(
        `Required form fields not found: ${missingFields.join(", ")}`
      );
    }

    // Add loading state to fields
    Object.values(fields).forEach((field) => {
      if (field) {
        field.disabled = true;
        field.classList.add("opacity-50");
      }
    });

    const response = await fetch(
      `/shipment/api/locations/${encodeURIComponent(locationId)}`,
      {
        headers: {
          Accept: "application/json",
          "X-CSRFToken": getCsrfToken(),
        },
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log("[DEBUG] Location data received:", data);

    if (!data.success) {
      throw new Error(data.message || "Failed to load location details");
    }

    // Map received data to form fields
    const mappings = {
      contact_person: data.data.contact_person || "",
      email: data.data.email || "",
      address: data.data.address || "",
      telephone: data.data.telephone || "",
      destination_label: data.data.destination_label || "",
    };

    // Update fields
    Object.entries(mappings).forEach(([fieldName, value]) => {
      const field = fields[fieldName];
      if (field) {
        field.value = value;
        field.disabled = false;
        field.dataset.prefilled = "true";
        field.classList.add("bg-gray-50");
      }
    });

    // Store the full location data on the form
    const form = document.getElementById("shipment-form");
    if (form) {
      form.setAttribute("data-location", JSON.stringify(data.data));
    }
  } catch (error) {
    console.error("[DEBUG] Error fetching location details:", error);
    throw error;
  } finally {
    // Reset loading state
    const fields = document.querySelectorAll(
      '[name^="contact_"], [name^="email"], [name^="address"], [name^="telephone"], [name^="destination_label"]'
    );
    fields.forEach((field) => {
      field.disabled = false;
      field.classList.remove("opacity-50");
    });
  }
}

// Populate location dropdown
function populateLocationDropdown(locations) {
  const select = document.querySelector('select[name="location_id"]');
  if (!select) return;

  select.innerHTML = '<option value="">Select Location</option>';
  locations.forEach((location) => {
    const option = document.createElement("option");
    option.value = location.label; // Keep label as value for now
    option.textContent = location.label;
    option.dataset.locationId = location.location_id; // Store ID in dataset
    select.appendChild(option);
  });
}

// Data Loading and Table Population
async function loadWaferInventory(page = 1) {
  console.log("[DEBUG] Loading wafer inventory", { page });
  const tbody = document.getElementById("inventory-table-body");
  showLoadingState(true);

  try {
    // Save the requested page to state
    TableState.pagination.currentPage = page;

    // Build URL with pagination parameters
    const url = `/shipment/api/available-wafers?page=${page}&per_page=${TableState.pagination.itemsPerPage}`;
    console.log("[DEBUG] Fetching data from:", url);

    const response = await fetch(url, {
      headers: {
        "X-CSRFToken": getCsrfToken(),
        Accept: "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.success) {
      populateWaferTable(data.data);
      updatePaginationState(data.pagination);
      updateTotalWaferCount(data.pagination.total_items);

      // Ensure the pagination UI is updated
      updatePaginationUI();
    } else {
      throw new Error(data.message || "Failed to load wafer inventory");
    }
  } catch (error) {
    console.error("[DEBUG] Error loading inventory:", error);
    showError("Failed to load wafer inventory");
    showEmptyTableMessage();
  } finally {
    showLoadingState(false);
  }
}

function addPaginationStyles() {
  // Check if styles already exist
  if (document.getElementById("pagination-styles")) {
    return;
  }

  const style = document.createElement("style");
  style.id = "pagination-styles";
  style.textContent = `
    .pagination-btn {
      padding: 0.5rem 1rem;
      border: 1px solid #d1d5db;
      border-radius: 0.375rem;
      background-color: white;
      color: #1f2937;
      font-size: 0.875rem;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    
    .pagination-btn:hover:not(:disabled) {
      background-color: #f3f4f6;
    }
    
    .pagination-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    
    .page-number {
      padding: 0.5rem 0.75rem;
      border-radius: 0.375rem;
      background-color: white;
      color: #1f2937;
      font-size: 0.875rem;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    
    .page-number:hover:not(.current) {
      background-color: #f3f4f6;
    }
    
    .page-number.current {
      background-color: #2563eb;
      color: white;
    }
  `;
  document.head.appendChild(style);
}

//Update Pagination State
function updatePaginationState(paginationData) {
  // Update pagination state
  TableState.pagination = {
    currentPage: paginationData.current_page,
    totalPages: paginationData.total_pages,
    totalItems: paginationData.total_items,
    itemsPerPage: paginationData.per_page,
  };

  // Update UI elements
  const showingStart = document.getElementById("showing-start");
  const showingEnd = document.getElementById("showing-end");
  const totalItems = document.getElementById("total-items");

  if (showingStart)
    showingStart.textContent =
      (TableState.pagination.currentPage - 1) *
        TableState.pagination.itemsPerPage +
      1;
  if (showingEnd)
    showingEnd.textContent = Math.min(
      TableState.pagination.currentPage * TableState.pagination.itemsPerPage,
      TableState.pagination.totalItems
    );
  if (totalItems) totalItems.textContent = TableState.pagination.totalItems;

  // Update pagination buttons
  updatePaginationUI();
}

// Show empty table message
function showEmptyTableMessage() {
  const tbody = document.getElementById("inventory-table-body");
  if (!tbody) return;

  tbody.innerHTML = `
        <tr>
            <td colspan="12" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                <div class="flex flex-col items-center justify-center space-y-2">
                    <i class="fas fa-inbox text-4xl mb-2"></i>
                    <p>No wafers found</p>
                    <button onclick="loadWaferInventory()" class="text-blue-600 hover:text-blue-800">
                        <i class="fas fa-sync-alt mr-1"></i>Refresh
                    </button>
                </div>
            </td>
        </tr>
    `;
}

// Helper function to update total wafer count
function updateTotalWaferCount(count) {
  const countElement = document.getElementById("total-wafer-count");
  if (countElement) {
    countElement.textContent = `${count} Available Wafers`;
  }
}

// Utility Functions
function showLoadingState(isLoading) {
  const overlay = document.getElementById("loading-overlay");
  if (overlay) {
    overlay.classList.toggle("hidden", !isLoading);
  }
}

// Update pagination UI
function updatePaginationUI() {
  const container = document.getElementById("pagination-container");
  if (!container) {
    console.error(
      "[DEBUG] Pagination container not found in updatePaginationUI"
    );
    return;
  }

  const { currentPage, totalPages, totalItems, itemsPerPage } =
    TableState.pagination;
  const start = (currentPage - 1) * itemsPerPage + 1;
  const end = Math.min(currentPage * itemsPerPage, totalItems);

  // Update showing text
  const showingStart = document.getElementById("showing-start");
  const showingEnd = document.getElementById("showing-end");
  const totalItemsElem = document.getElementById("total-items");

  if (showingStart) showingStart.textContent = totalItems > 0 ? start : 0;
  if (showingEnd) showingEnd.textContent = totalItems > 0 ? end : 0;
  if (totalItemsElem) totalItemsElem.textContent = totalItems;

  // Update per-page selector
  const perPageSelect = document.getElementById("per-page-select");
  if (perPageSelect && !perPageSelect.dataset.initialized) {
    // Set the current value
    perPageSelect.value = itemsPerPage.toString();

    // Remove any existing event listeners
    const newSelect = perPageSelect.cloneNode(true);
    perPageSelect.parentNode.replaceChild(newSelect, perPageSelect);

    // Add event listener
    newSelect.addEventListener("change", function (e) {
      const newPageSize = parseInt(e.target.value);
      TableState.pagination.itemsPerPage = newPageSize;
      TableState.pagination.currentPage = 1;
      loadWaferInventory(1);
    });

    newSelect.dataset.initialized = "true";
  }

  // Update prev/next buttons
  const prevButton = document.getElementById("prev-page-btn");
  const nextButton = document.getElementById("next-page-btn");

  if (prevButton) {
    prevButton.disabled = currentPage <= 1;
    // Make sure onclick is set correctly
    prevButton.onclick = function () {
      navigatePage("prev");
    };
  }

  if (nextButton) {
    nextButton.disabled = currentPage >= totalPages;
    // Make sure onclick is set correctly
    nextButton.onclick = function () {
      navigatePage("next");
    };
  }

  // Update page numbers
  const pageNumbers = document.getElementById("page-numbers");
  if (pageNumbers) {
    pageNumbers.innerHTML = generatePageNumbersHTML();
  }

  console.log("[DEBUG] Updated pagination UI", {
    currentPage,
    totalPages,
    totalItems,
    start,
    end,
  });
}

function generatePageNumbersHTML() {
  const { currentPage, totalPages } = TableState.pagination;
  const maxVisiblePages = 5;
  let pages = [];

  if (totalPages <= maxVisiblePages) {
    // Show all pages
    pages = Array.from({ length: totalPages }, (_, i) => i + 1);
  } else {
    // Always show first page
    pages.push(1);

    // Add ellipsis if needed
    if (currentPage > 3) {
      pages.push("...");
    }

    // Add pages around current page
    for (
      let i = Math.max(2, currentPage - 1);
      i <= Math.min(totalPages - 1, currentPage + 1);
      i++
    ) {
      pages.push(i);
    }

    // Add ellipsis if needed
    if (currentPage < totalPages - 2) {
      pages.push("...");
    }

    // Always show last page
    if (totalPages > 1) {
      pages.push(totalPages);
    }
  }

  // Create buttons HTML
  return pages
    .map((page) => {
      if (page === "...") {
        return `<span class="px-2 py-1">...</span>`;
      }
      return `
      <button class="page-number ${page === currentPage ? "current" : ""}"
              onclick="loadWaferInventory(${page})">
        ${page}
      </button>
    `;
    })
    .join("");
}

// Navigation Handlers
function handlePerPageChange(e) {
  const newPageSize = parseInt(e.target.value);
  TableState.pagination.itemsPerPage = newPageSize;
  TableState.pagination.currentPage = 1;
  loadWaferInventory(1);
}

// Generate page numbers
function generatePageNumbers() {
  const { currentPage, totalPages } = TableState.pagination;
  const maxVisiblePages = 5;
  let pages = [];

  if (totalPages <= maxVisiblePages) {
    pages = Array.from({ length: totalPages }, (_, i) => i + 1);
  } else {
    pages.push(1);
    if (currentPage > 3) pages.push("...");

    for (
      let i = Math.max(2, currentPage - 1);
      i <= Math.min(totalPages - 1, currentPage + 1);
      i++
    ) {
      pages.push(i);
    }

    if (currentPage < totalPages - 2) pages.push("...");
    pages.push(totalPages);
  }

  return pages
    .map((page) => {
      if (page === "...") {
        return `<span class="px-2">...</span>`;
      }
      return `
            <button class="page-number ${page === currentPage ? "current" : ""}"
                    onclick="loadWaferInventory(${page})">
                ${page}
            </button>
        `;
    })
    .join("");
}

function updatePageNumbers() {
  const pageNumbers = document.getElementById("page-numbers");
  const { currentPage, totalPages } = PaginationState;

  let pages = [];
  const maxVisiblePages = 5;

  if (totalPages <= maxVisiblePages) {
    // Show all pages
    pages = Array.from({ length: totalPages }, (_, i) => i + 1);
  } else {
    // Always show first and last page
    pages.push(1);

    // Calculate middle pages
    const leftBound = Math.max(2, currentPage - 1);
    const rightBound = Math.min(totalPages - 1, currentPage + 1);

    // Add ellipsis if needed
    if (leftBound > 2) pages.push("...");

    // Add middle pages
    for (let i = leftBound; i <= rightBound; i++) {
      pages.push(i);
    }

    // Add ellipsis if needed
    if (rightBound < totalPages - 1) pages.push("...");

    pages.push(totalPages);
  }

  // Create page buttons
  pageNumbers.innerHTML = pages
    .map((page) => {
      if (page === "...") {
        return `<span class="px-2 py-1">...</span>`;
      }
      return `
            <button class="page-number ${page === currentPage ? "current" : ""}"
                    onclick="loadWaferInventory(${page}, PaginationState.itemsPerPage)">
                ${page}
            </button>
        `;
    })
    .join("");
}

// Restore selected wafers after table reload
function restoreSelectedWafers() {
  const checkboxes = document.querySelectorAll(".wafer-checkbox");
  checkboxes.forEach((checkbox) => {
    const waferId = checkbox.dataset.waferId;
    const row = checkbox.closest("tr");
    if (TableState.selectedWafers.has(waferId)) {
      checkbox.checked = true;
      row?.classList.add("selected-row");
    }
  });
}

// Event Handlers
function handleAsanaLinkChange(event) {
  const asanaLink = event.target.value.trim();

  // Check if this is a new link that's different from any previously loaded link
  const previousLink = event.target.dataset.previousLink || "";

  if (asanaLink && asanaLink !== previousLink && asanaLink.length > 10) {
    // Store the new link for future comparison
    event.target.dataset.previousLink = asanaLink;

    // Reset the form before loading new data
    resetForm();

    // Restore the Asana link since we just cleared it
    event.target.value = asanaLink;

    // Now proceed with your existing code to fetch the Asana task data
    fetchAsanaTaskInfo(asanaLink);
  }
}

function handleLotIdsChange(event) {
  const lotIds = event.target.value;
  if (lotIds) {
    filterWaferTableByLotIds(lotIds.split(",").map((id) => id.trim()));
  } else {
    loadWaferInventory(); // Reset to show all
  }
}

// Pagination Management
function initializePagination() {
  console.log("[DEBUG] Initializing pagination");

  // Create the pagination container if it doesn't exist
  const container = createPaginationContainer();

  if (!container) {
    console.error("[DEBUG] Failed to create pagination container");
    return;
  }

  // Initialize per-page selector
  const perPageSelect = document.getElementById("per-page-select");
  if (perPageSelect) {
    perPageSelect.addEventListener("change", handlePerPageChange);
  }

  // Update pagination UI with current state
  updatePaginationUI();

  // Add keyboard navigation
  document.addEventListener("keydown", handlePaginationKeyboard);

  console.log("[DEBUG] Pagination initialization complete");
}

// Create the pagination container

function createPaginationContainer() {
  // First check if it already exists
  let container = document.getElementById("pagination-container");

  if (container) {
    return container;
  }

  // Get the table wrapper that will contain the pagination
  const tableWrapper = document.querySelector(".overflow-x-auto");
  if (!tableWrapper || !tableWrapper.parentNode) {
    console.error(
      "[DEBUG] Could not find table wrapper to append pagination container"
    );
    return null;
  }

  // Create the container
  container = document.createElement("div");
  container.id = "pagination-container";
  container.className =
    "mt-4 flex flex-wrap items-center justify-between px-4 py-3 bg-white border-t border-gray-200";

  // Add initial content
  container.innerHTML = `
    <div class="flex items-center">
      <select id="per-page-select" class="rounded-md border-gray-300 shadow-sm">
        <option value="10">10 per page</option>
        <option value="25">25 per page</option>
        <option value="50">50 per page</option>
        <option value="100">100 per page</option>
      </select>
      <span class="ml-3 text-sm text-gray-700">
        Showing <span id="showing-start">0</span> to <span id="showing-end">0</span> of <span id="total-items">0</span> results
      </span>
    </div>
    <div class="flex items-center space-x-2 mt-2 sm:mt-0">
      <button id="prev-page-btn" class="pagination-btn" onclick="navigatePage('prev')">
        Previous
      </button>
      <div class="flex items-center space-x-1" id="page-numbers">
        <button class="page-number current" onclick="loadWaferInventory(1)">1</button>
      </div>
      <button id="next-page-btn" class="pagination-btn" onclick="navigatePage('next')">
        Next
      </button>
    </div>
  `;

  // Append to table wrapper
  tableWrapper.parentNode.appendChild(container);

  console.log("[DEBUG] Created pagination container");
  return container;
}

// Ensure pagination container exists
function ensurePaginationContainer() {
  let container = document.getElementById("pagination-container");
  if (!container) {
    container = document.createElement("div");
    container.id = "pagination-container";
    container.className =
      "mt-4 flex items-center justify-between px-4 py-3 bg-white border-t border-gray-200";

    const tableWrapper = document.querySelector(".overflow-x-auto");
    if (tableWrapper?.parentNode) {
      tableWrapper.parentNode.appendChild(container);
    }
  }
  return container;
}

function initializeSearchState() {
  try {
    const lastSearch = JSON.parse(sessionStorage.getItem("lastSearch"));
    if (lastSearch) {
      const { asanaLink, lotIds } = lastSearch;
      if (asanaLink) document.getElementById("asana-link").value = asanaLink;
      if (lotIds) {
        document.getElementById("lot-ids").value = lotIds;
        filterWaferTableByLotIds(lotIds.split(",").map((id) => id.trim()));
      }
    }
  } catch (error) {
    console.error("Error restoring search state:", error);
  }
}

// Initialize search functionality
function initializeSearch() {
  // Add click handler to search button
  const searchButton = document.querySelector('button[title="Search"]');
  if (searchButton) {
    searchButton.removeEventListener("click", handleSearch);
    searchButton.addEventListener("click", handleSearch);
    console.log("[DEBUG] Added click handler to search button");
  } else {
    console.warn("[DEBUG] Search button not found");
  }

  // Add click handler to eraser/clear button
  const eraserButton = document.querySelector('button[title="eraser"]');
  if (eraserButton) {
    eraserButton.removeEventListener("click", handleClear);
    eraserButton.addEventListener("click", handleClear);
    console.log("[DEBUG] Added click handler to eraser button");
  } else {
    console.warn("[DEBUG] Eraser button not found");
  }

  // Setup input field listeners
  setupSearchInputListeners();
}

// Function to clear all filters and show all wafers
function clearFilters() {
  console.log("[DEBUG] Clearing filters");

  // Clear input fields
  const lotIdsInput = document.getElementById("lot-ids");
  const asanaLinkInput = document.getElementById("asana-link");

  if (lotIdsInput) lotIdsInput.value = "";
  if (asanaLinkInput) asanaLinkInput.value = "";

  // Remove any filter message
  const filterMessage = document.getElementById("filter-message");
  if (filterMessage) filterMessage.remove();

  // Reset to first page and load all wafers
  TableState.pagination.currentPage = 1;
  loadWaferInventory(1);
}

// Auto-search when typing in lot IDs field (with debounce)
function setupSearchInputListeners() {
  const lotIdsInput = document.getElementById("lot-ids");

  if (lotIdsInput) {
    // Remove any existing listeners to prevent duplicates
    lotIdsInput.removeEventListener("input", handleLotIdsInput);

    // Add the debounced input handler
    lotIdsInput.addEventListener("input", debounce(handleLotIdsInput, 500));
    console.log("[DEBUG] Added debounced input handler to lot IDs field");
  }
}

// Debounced function for handling lot IDs input
function handleLotIdsInput(e) {
  const value = e.target.value.trim();

  // Skip immediate search on empty field or very short inputs
  if (!value || value.length < 3) return;

  // Get lot IDs
  const lotIds = value
    .split(",")
    .map((id) => id.trim())
    .filter((id) => id);

  // Don't search if parsing resulted in no valid IDs
  if (lotIds.length === 0) return;

  console.log("[DEBUG] Auto-searching after input:", lotIds);
  filterWaferTableByLotIds(lotIds);
}

// Handler for the eraser/clear button
function handleClear(e) {
  e.preventDefault();
  clearFilters();
}

// Function to handle the search button click
function handleSearch(e) {
  e.preventDefault();
  console.log("[DEBUG] Search button clicked");

  // Get the lot IDs from the input field
  const lotIdsInput = document.getElementById("lot-ids");
  if (!lotIdsInput) {
    console.error("[DEBUG] Lot IDs input field not found");
    return;
  }

  const lotIdsValue = lotIdsInput.value.trim();

  // Check if there's anything to search
  if (!lotIdsValue) {
    console.log("[DEBUG] No lot IDs entered, showing all wafers");
    loadWaferInventory(1); // Reset to show all wafers
    return;
  }

  // Parse comma-separated lot IDs
  const lotIds = lotIdsValue
    .split(",")
    .map((id) => id.trim())
    .filter((id) => id);
  console.log("[DEBUG] Searching for lot IDs:", lotIds);

  // Show loading state
  const searchButton = document.querySelector('button[title="Search"]');
  if (searchButton) {
    searchButton.disabled = true;
    const originalContent = searchButton.innerHTML;
    searchButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

    // Reset after search completes
    setTimeout(() => {
      searchButton.disabled = false;
      searchButton.innerHTML = originalContent;
    }, 1000);
  }

  // Call the filtering function
  filterWaferTableByLotIds(lotIds);
}

// Handle keyboard navigation
function handlePaginationKeyboard(e) {
  if (e.target.tagName === "INPUT") return;

  switch (e.key) {
    case "ArrowLeft":
      if (!e.ctrlKey) navigatePage("prev");
      break;
    case "ArrowRight":
      if (!e.ctrlKey) navigatePage("next");
      break;
    case "Home":
      if (e.ctrlKey) loadWaferInventory(1);
      break;
    case "End":
      if (e.ctrlKey) loadWaferInventory(TableState.pagination.totalPages);
      break;
  }
}

// Navigate pages

function navigatePage(direction) {
  console.log("[DEBUG] Navigating page:", direction);

  const { currentPage, totalPages } = TableState.pagination;

  if (direction === "prev" && currentPage > 1) {
    loadWaferInventory(currentPage - 1);
  } else if (direction === "next" && currentPage < totalPages) {
    loadWaferInventory(currentPage + 1);
  } else {
    console.log("[DEBUG] Cannot navigate - at boundary or invalid direction", {
      direction,
      currentPage,
      totalPages,
    });
  }
}
// Handle jump to page
function handleJumpToPage() {
  const input = document.getElementById("jump-to-page");
  const page = parseInt(input.value);

  if (page >= 1 && page <= PaginationState.totalPages) {
    loadWaferInventory(page);
  } else {
    showError("Invalid page number");
    input.value = PaginationState.currentPage;
  }
}

// Add necessary styles
const style = document.createElement("style");
style.textContent = `
    .pagination-btn {
        @apply px-3 py-1 rounded-md border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed;
    }

    .page-number {
        @apply px-3 py-1 rounded-md hover:bg-gray-50;
    }

    .page-number.current {
        @apply bg-blue-600 text-white hover:bg-blue-700;
    }

    #inventory-table-body {
        transition: opacity 0.3s ease;
    }

    .table-fade-enter {
        opacity: 0;
    }

    .table-fade-enter-active {
        opacity: 1;
        transition: opacity 0.3s ease;
    }

    .table-fade-exit {
        opacity: 1;
    }

    .table-fade-exit-active {
        opacity: 0;
        transition: opacity 0.3s ease;
    }
`;
document.head.appendChild(style);

// Event Handlers
function handleSelectAll(e) {
  try {
    const checkboxes = document.querySelectorAll(
      '#inventory-table-body input[type="checkbox"]'
    );
    checkboxes.forEach((checkbox) => {
      checkbox.checked = e.target.checked;
      const waferId = checkbox.dataset.waferId;
      if (e.target.checked) {
        selectedWafers.add(waferId);
      } else {
        selectedWafers.delete(waferId);
      }
    });
    updateSelectedWafersCount(); // Changed from updateSelectedCount
  } catch (error) {
    console.error("Error in handleSelectAll:", error);
  }
}

// Populate wafer table
function populateWaferTable(data) {
  const tbody = document.getElementById("inventory-table-body");
  if (!tbody) return;

  tbody.innerHTML = "";
  data.forEach((lot) => {
    lot.wafers.forEach((wafer) => {
      const row = createWaferTableRow(wafer, lot);
      tbody.appendChild(row);
    });
  });

  // Restore selected states
  restoreSelectedWafers();
}

// Function to create wafer table rows
function createWaferTableRow(wafer, lot) {
  const row = document.createElement("tr");
  row.className = "hover:bg-gray-50 transition-colors duration-150";
  row.setAttribute("data-wafer-id", wafer.wafer_id);

  row.innerHTML = `
        <td class="px-6 py-4">
            <input type="checkbox" 
                   class="wafer-checkbox rounded border-gray-300"
                   data-wafer-id="${wafer.wafer_id}"
                   ${
                     TableState.selectedWafers.has(wafer.wafer_id)
                       ? "checked"
                       : ""
                   }>
        </td>
        <td class="px-6 py-4 font-medium">${wafer.wafer_id || "-"}</td>
        <td class="px-6 py-4">${lot.lot_id || "-"}</td>
        <td class="px-6 py-4">${lot.xfab_lot_id || "-"}</td>
        <td class="px-6 py-4">${lot.mask_set_id || "-"}</td>
        <td class="px-6 py-4">${wafer.module_name || "-"}</td>
        <td class="px-6 py-4">${wafer.cassette_id || "-"}</td>
        <td class="px-6 py-4">${wafer.slot_id || "-"}</td>
        <td class="px-6 py-4">
            <span class="status-badge ${getLocationColorClass(wafer.location)}">
                ${wafer.location || "-"}
            </span>
        </td>
        <td class="px-6 py-4">${formatDate(wafer.arrived_at)}</td>
        <td class="px-6 py-4">${formatDate(wafer.sent_at)}</td>
        <td class="px-6 py-4 text-right">
            <button onclick="showHistory('${wafer.wafer_id}')"
                    class="text-blue-600 hover:text-blue-900">
                <i class="fas fa-history"></i>
            </button>
        </td>
    `;

  // Add checkbox event listener
  const checkbox = row.querySelector(".wafer-checkbox");
  checkbox.addEventListener("change", (e) =>
    handleWaferSelection(e, wafer.wafer_id, row)
  );

  // Add row click handler (excluding checkbox and button clicks)
  row.addEventListener("click", (e) => {
    if (
      !e.target.closest('input[type="checkbox"]') &&
      !e.target.closest("button")
    ) {
      checkbox.click();
    }
  });

  return row;
}

// Selection Handlers
function handleWaferSelection(event, waferId, row) {
  try {
    console.log("[DEBUG] Wafer selection changed:", {
      waferId: waferId,
      checked: event.target.checked,
    });

    if (event.target.checked) {
      // Add to both selection sets
      selectedWafers.add(waferId);
      TableState.selectedWafers.add(waferId);
      row.classList.add("selected-row");
    } else {
      // Remove from both selection sets
      selectedWafers.delete(waferId);
      TableState.selectedWafers.delete(waferId);
      row.classList.remove("selected-row");
    }

    updateSelectedWafersCount();
    updateSelectionMessage();

    console.log("[DEBUG] Updated selection state:", {
      selectedSize: selectedWafers.size,
      tableStateSize: TableState.selectedWafers.size,
    });
  } catch (error) {
    console.error("[DEBUG] Error in handleWaferSelection:", error);
  }
}

// Helper function to update selection message
function updateSelectionMessage() {
  const messageContainer = document.getElementById("selection-message");
  if (!messageContainer) return;

  if (TableState.selectedWafers.size > 0) {
    messageContainer.innerHTML = `
            <div class="flex items-center justify-between">
                <span><strong>${TableState.selectedWafers.size}</strong> wafers selected</span>
                <button onclick="useSelectedWafers()" 
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    Use Selected Wafers
                </button>
            </div>
        `;
    messageContainer.classList.remove("hidden");
  } else {
    messageContainer.classList.add("hidden");
  }
}

// Helper function to get location color class
function getLocationColorClass(location) {
  const colorMap = {
    "Xfab FR": "bg-green-100 text-green-800",
    "Ligentec khalil FR": "bg-green-100 text-green-800",
    "Sent to Customer": "bg-blue-100 text-blue-800",
    "In Transit": "bg-yellow-100 text-yellow-800",
    Lost: "bg-red-100 text-red-800",
  };
  return colorMap[location] || "bg-gray-100 text-gray-800";
}

// Validate wafer selection
function validateWaferSelection() {
  const textarea = document.getElementById("wafer-ids-textarea");
  const waferCountInput = document.getElementById("wafer-count");
  const validationMessage = document.getElementById("wafer-validation-message");

  if (!textarea || !waferCountInput || !validationMessage) return;

  const expectedCount = parseInt(waferCountInput.value) || 0;
  const actualCount = selectedWafers.size;

  if (expectedCount > 0 && actualCount !== expectedCount) {
    validationMessage.innerHTML = `
            <span class="text-yellow-600">
                <i class="fas fa-exclamation-triangle mr-1"></i>
                Selected wafers (${actualCount}) doesn't match expected count (${expectedCount})
            </span>`;
  } else {
    validationMessage.innerHTML = "";
  }
}

// Utility Functions
function debounce(func, wait) {
  let timeout;
  return function (...args) {
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(this, args), wait);
  };
}

// Call the initialization when the document is ready
document.addEventListener("DOMContentLoaded", initializeSearch);

// Support for reinitialization if the page structure changes
function reinitializeSearch() {
  setTimeout(initializeSearch, 500);
}

// Add to window for external access
window.handleSearch = handleSearch;
window.clearFilters = clearFilters;
window.filterWaferTableByLotIds = filterWaferTableByLotIds;

// Add CSS for highlighting matched rows
const highlightStyle = document.createElement("style");
highlightStyle.textContent = `
    .highlight-match {
        background-color: rgba(59, 130, 246, 0.1);
        animation: highlightFade 2s ease-out;
    }
    
    @keyframes highlightFade {
        from { background-color: rgba(59, 130, 246, 0.2); }
        to { background-color: rgba(59, 130, 246, 0.05); }
    }
    
    #filter-message {
        background-color: rgba(59, 130, 246, 0.05);
        border-radius: 0.5rem;
        padding: 0.5rem 1rem;
        margin: 1rem 0;
    }
`;
document.head.appendChild(highlightStyle);

// Show date in a readable format
function formatDate(dateString) {
  if (!dateString) return "-";
  return new Date(dateString).toLocaleString(undefined, {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
}

// Sorting and Filtering Management
const SortingAndFilters = {
  // Sorting Functions
  handleSort(column) {
    if (TableState.currentSort.column === column) {
      TableState.currentSort.direction =
        TableState.currentSort.direction === "asc" ? "desc" : "asc";
    } else {
      TableState.currentSort.column = column;
      TableState.currentSort.direction = "asc";
    }

    this.updateSortIndicators();
    this.sortWaferTable();
  },

  updateSortIndicators() {
    // Clear all indicators first
    document.querySelectorAll(".sort-indicator").forEach((el) => {
      el.textContent = "";
      el.classList.remove("asc", "desc");
    });

    // Update current sort column indicator
    if (TableState.currentSort.column) {
      const indicator = document.getElementById(
        `sort-${TableState.currentSort.column}`
      );
      if (indicator) {
        indicator.classList.add(TableState.currentSort.direction);
        indicator.textContent =
          TableState.currentSort.direction === "asc" ? "↑" : "↓";
      }
    }
  },

  sortWaferTable() {
    const tbody = document.getElementById("inventory-table-body");
    const rows = Array.from(tbody.querySelectorAll("tr"));

    rows.sort((a, b) => {
      const aValue = this.getCellValue(a, TableState.currentSort.column);
      const bValue = this.getCellValue(b, TableState.currentSort.column);

      return TableState.currentSort.direction === "asc"
        ? this.compareValues(aValue, bValue)
        : this.compareValues(bValue, aValue);
    });

    // Re-append sorted rows
    tbody.innerHTML = "";
    rows.forEach((row) => tbody.appendChild(row));
  },

  getCellValue(row, column) {
    const cell = row.querySelector(
      `td:nth-child(${this.getColumnIndex(column)})`
    );
    return cell ? cell.textContent.trim() : "";
  },

  compareValues(a, b) {
    // Handle date comparisons
    if (this.isDate(a) && this.isDate(b)) {
      return new Date(a) - new Date(b);
    }
    // Handle numeric comparisons
    if (!isNaN(a) && !isNaN(b)) {
      return Number(a) - Number(b);
    }
    // Default string comparison
    return a.localeCompare(b);
  },

  isDate(value) {
    return !isNaN(Date.parse(value));
  },

  getColumnIndex(columnName) {
    const columnMap = {
      wafer_id: 2,
      lot_id: 3,
      xfab_id: 4,
      mask_set_id: 5,
      module_name: 6,
      cassette_id: 7,
      slot_id: 8,
      location_id: 9,
      arrived_at: 10,
      sent_at: 11,
    };
    return columnMap[columnName] || 2;
  },

  // Filtering Functions
  initializeFilters() {
    this.setupSearchListener();
    this.setupFilterButtons();
    this.setupQuickFilters();
  },

  setupSearchListener() {
    const searchInput = document.getElementById("table-search");
    if (searchInput) {
      searchInput.addEventListener(
        "input",
        debounce((e) => {
          TableState.filters.searchTerm = e.target.value.toLowerCase();
          this.applyFilters();
        }, 300)
      );
    }
  },

  setupFilterButtons() {
    document.querySelectorAll("[data-filter]").forEach((button) => {
      button.addEventListener("click", (e) => {
        const filterType = e.target.dataset.filter;
        const filterValue = e.target.dataset.value;
        this.toggleFilter(filterType, filterValue);
      });
    });
  },

  setupQuickFilters() {
    const quickFilterContainer = document.createElement("div");
    quickFilterContainer.className = "flex gap-2 mb-4";
    quickFilterContainer.innerHTML = `
            <button class="px-3 py-1 rounded-md bg-gray-100 hover:bg-gray-200" 
                    data-quick-filter="available">
                Available Wafers
            </button>
            <button class="px-3 py-1 rounded-md bg-gray-100 hover:bg-gray-200" 
                    data-quick-filter="in_transit">
                In Transit
            </button>
            <button class="px-3 py-1 rounded-md bg-gray-100 hover:bg-gray-200" 
                    data-quick-filter="received">
                Received
            </button>
        `;

    const tableContainer = document.querySelector(".overflow-x-auto");
    if (tableContainer) {
      tableContainer.parentNode.insertBefore(
        quickFilterContainer,
        tableContainer
      );
    }

    // Add quick filter handlers
    quickFilterContainer.addEventListener("click", (e) => {
      const filterButton = e.target.closest("[data-quick-filter]");
      if (filterButton) {
        this.applyQuickFilter(filterButton.dataset.quickFilter);
      }
    });
  },

  applyQuickFilter(filterType) {
    TableState.filters.activeFilters.clear();
    TableState.filters.activeFilters.set("quick", filterType);
    this.applyFilters();
  },

  toggleFilter(type, value) {
    if (!TableState.filters.activeFilters.has(type)) {
      TableState.filters.activeFilters.set(type, new Set());
    }

    const filterSet = TableState.filters.activeFilters.get(type);
    if (filterSet.has(value)) {
      filterSet.delete(value);
      if (filterSet.size === 0) {
        TableState.filters.activeFilters.delete(type);
      }
    } else {
      filterSet.add(value);
    }

    this.applyFilters();
    this.updateFilterUI();
  },

  async applyFilters() {
    showLoadingState(true);
    try {
      const params = new URLSearchParams({
        search: TableState.filters.searchTerm,
        page: TableState.pagination.currentPage,
        per_page: TableState.pagination.itemsPerPage,
      });

      // Add active filters to params
      TableState.filters.activeFilters.forEach((values, type) => {
        values.forEach((value) => {
          params.append(`filter[${type}][]`, value);
        });
      });

      await loadWaferInventory(1); // Reset to first page with new filters
    } finally {
      showLoadingState(false);
    }
  },

  clearFilters() {
    TableState.filters.searchTerm = "";
    TableState.filters.activeFilters.clear();

    // Reset UI
    const searchInput = document.getElementById("table-search");
    if (searchInput) {
      searchInput.value = "";
    }

    this.updateFilterUI();
    loadWaferInventory(1);
  },
};

// Initialize sorting and filtering when document is ready
document.addEventListener("DOMContentLoaded", () => {
  SortingAndFilters.initializeFilters();
});

function showError(message) {
  console.error(message);
  if (window.Swal) {
    Swal.fire({
      icon: "error",
      title: "Error",
      text: message,
    });
  } else {
    alert(message);
  }
}

function showSuccess(message) {
  Swal.fire({
    icon: "success",
    title: "Success",
    text: message,
    timer: 2000,
  });
}

// Selection Management
function clearSelection() {
  TableState.selectedWafers.clear();
  updateSelectedWafersCount();
  updateWaferCheckboxes();
  updateSelectionMessage();
}

// Clear form helper function
function clearForm() {
  try {
    console.log("Clearing form...");
    document.getElementById("shipment-form")?.reset();
    document.getElementById("asana-link").value = "";
    document.getElementById("lot-ids").value = "";
    ShipmentState.clearSelection();
    loadWaferInventory();
  } catch (error) {
    console.error("Error clearing form:", error);
  }
}

// Update selected wafers count
function updateSelectedWafersCount() {
  const count = TableState.selectedWafers.size;
  const countElement = document.getElementById("selected-wafer-count");
  if (countElement) {
    countElement.textContent = `${count} Selected`;
  }
}

// Update wafer checkboxes
function updateWaferCheckboxes() {
  const checkboxes = document.querySelectorAll(".wafer-checkbox");
  checkboxes.forEach((checkbox) => {
    const waferId = checkbox.dataset.waferId;
    checkbox.checked = TableState.selectedWafers.has(waferId);
    const row = checkbox.closest("tr");
    if (row) {
      row.classList.toggle("selected-row", checkbox.checked);
    }
  });
}

// Function to fetch Asana task information
async function fetchAsanaTaskInfo(asanaUrl) {
  try {
    console.log("[DEBUG] Fetching data for Asana URL:", asanaUrl);

    const response = await fetch("/shipment/api/asana/extract", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CSRFToken": getCsrfToken(),
      },
      body: JSON.stringify({ asana_url: asanaUrl }),
    });

    const data = await response.json();

    if (!response.ok) {
      const error = new Error(`HTTP error! status: ${response.status}`);
      error.status = response.status;
      error.data = data;
      throw error;
    }

    console.log("[DEBUG] Asana task data received:", data);
    return data;
  } catch (error) {
    console.error("[DEBUG] Error fetching Asana task:", error);
    throw error;
  }
}

// Helper function to check if Asana data is mostly empty
function isEmptyAsanaData(data) {
  // Check critical fields
  const criticalFields = [
    "title",
    "contact_person",
    "email",
    "address",
    "telephone",
    "destination_label",
  ];
  const emptyCount = criticalFields.filter((field) => !data[field]).length;

  // If more than half of critical fields are empty, consider the data incomplete
  return emptyCount > criticalFields.length / 2;
}

// Helper function to get CSRF token
function getCsrfToken() {
  const tokenInput = document.querySelector('input[name="csrf_token"]');
  if (tokenInput) return tokenInput.value;

  const tokenMeta = document.querySelector('meta[name="csrf-token"]');
  if (tokenMeta) return tokenMeta.getAttribute("content");

  return "";
}

/**
 * Populates form fields with Asana data
 */
function populateFormFromAsana(formData) {
  console.log("[DEBUG] Populating form with data:", formData);

  // Helper function to set field value with empty checking and logging
  function setFieldValue(fieldId, value) {
    const field =
      document.getElementById(fieldId) ||
      document.querySelector(`[name="${fieldId}"]`);

    if (!field) {
      console.warn(`[DEBUG] Field ${fieldId} not found`);
      return;
    }

    // Skip empty values
    if (value === undefined || value === null || value === "") {
      console.warn(`[DEBUG] Empty value for ${fieldId}, not updating`);
      return;
    }

    if (field.type === "checkbox") {
      // Handle boolean fields
      field.checked = value === true || value === "true";
      console.log(`[DEBUG] Setting checkbox ${fieldId} to ${field.checked}`);
    } else if (field.type === "select-one") {
      // Handle select fields with case-insensitive matching
      console.log(`[DEBUG] Setting select ${fieldId} to ${value}`);

      // For select fields, we need to find the matching option
      const options = Array.from(field.options);
      const matchingOption = options.find(
        (option) => option.value.toLowerCase() === String(value).toLowerCase()
      );

      if (matchingOption) {
        field.value = matchingOption.value;
      } else {
        console.warn(
          `[DEBUG] No matching option found for ${fieldId} with value ${value}`
        );
      }
    } else {
      // Handle text and other inputs
      field.value = value;
      console.log(`[DEBUG] Setting field ${fieldId} to ${value}`);
    }
  }

  // Determine shipment type (critical or standard)
  const type = hasEigerFields(formData) ? "critical" : "standard";
  setFieldValue("type", type);
  console.log(`[DEBUG] Determined shipment type: ${type}`);

  // Map all fields from the API response to form fields
  setFieldValue("title", formData.title);
  setFieldValue("shipment_date", formData.ship_date);
  setFieldValue("priority", formData.priority);
  setFieldValue("need_reviewing", formData.need_reviewing);
  setFieldValue("label_free", formData.label_free);
  setFieldValue("keep_cassette_closed", formData.keep_cassette_closed);
  setFieldValue("wafer_choice", formData.wafer_choice);
  setFieldValue("label_title", formData.label_title);
  setFieldValue("contact_person", formData.contact_person);
  setFieldValue("email", formData.email);
  setFieldValue("address", formData.address);
  setFieldValue("telephone", formData.telephone);
  setFieldValue("destination_label", formData.destination_label);
  setFieldValue("number_of_wafers", formData.number_of_wafers);
  setFieldValue("parcel_size", formData.parcel_size);

  // Erfurt fields
  setFieldValue("xfab_po", formData.xfab_po);
  setFieldValue("xfab_device_id", formData.xfab_device_id);
  setFieldValue("project_id", formData.project_id);

  // Eiger fields
  setFieldValue("eiger_number", formData.eiger_number);
  setFieldValue("tapeout", formData.tapeout);
  setFieldValue("vendor_lot", formData.vendor_lot);
  setFieldValue("customer_lot", formData.customer_lot);
  setFieldValue("rib", formData.rib);
  setFieldValue("tox_target_sin", formData.tox_target_sin);
  setFieldValue("heater", formData.heater);
  setFieldValue("undercut", formData.undercut);
  setFieldValue("sin_tube_position", formData.sin_tube_position);
  setFieldValue("mask", formData.mask);
  setFieldValue("comments", formData.comments);

  // New fields from PI task
  setFieldValue("corridor", formData.corridor);
  setFieldValue("lot_project", formData.lot_project);
  setFieldValue("lot_reservation", formData.lot_reservation);
  setFieldValue("account_manager", formData.account_manager);
  setFieldValue("sales_order", formData.sales_order);
  setFieldValue("customer_id", formData.customer_id);

  // Handle wafer IDs (special case)
  const waferIds = formData.wafer_ids || [];
  if (waferIds.length > 0) {
    setFieldValue("wafer_ids", waferIds.join(", "));
  }

  // Handle lot IDs - specifically update the LGT Lot IDs field
  const lotIdsInput = document.getElementById("lot-ids");
  if (lotIdsInput && formData.lot_ids) {
    console.log(`[DEBUG] Setting lot-ids to: ${formData.lot_ids}`);
    lotIdsInput.value = formData.lot_ids;

    // Filter the table to show matching wafers
    const lotIdArray = formData.lot_ids
      .split(",")
      .map((id) => id.trim())
      .filter((id) => id);
    if (
      lotIdArray.length > 0 &&
      typeof filterWaferTableByLotIds === "function"
    ) {
      console.log(`[DEBUG] Filtering wafer table by lot IDs: ${lotIdArray}`);
      filterWaferTableByLotIds(lotIdArray);
    }
  }
}

/**
 * Handles the Upload button click event
 * Uses the backend to extract data from an Asana task URL and populate the form
 */
async function handleUpload(e) {
  e.preventDefault();
  const asanaLink = document.getElementById("asana-link").value.trim();

  if (!asanaLink) {
    Swal.fire({
      icon: "error",
      title: "Error",
      text: "Please enter an Asana task URL",
    });
    return;
  }

  // Show loading state
  const uploadBtn = e.target.closest("button");
  const originalText = uploadBtn.innerHTML;
  uploadBtn.disabled = true;
  uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';

  try {
    // FIXED: Added /shipment prefix to match blueprint
    const response = await fetch("/shipment/api/asana/upload", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CSRFToken": getCsrfToken(),
      },
      body: JSON.stringify({
        asana_url: asanaLink,
        debug_mode: true, // Enable debug mode for more detailed logging
      }),
    });

    if (!response.ok) {
      throw new Error(`Server error: ${response.status}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(
        result.message || "Failed to extract data from Asana task"
      );
    }

    // Populate the form with the extracted data
    populateFormFromAsana(result.data);

    // Show success message
    Swal.fire({
      icon: "success",
      title: "Success",
      text: "Asana task data loaded successfully",
      timer: 2000,
    });
  } catch (error) {
    console.error("Error uploading from Asana:", error);
    Swal.fire({
      icon: "error",
      title: "Upload Failed",
      text: error.message || "Failed to extract data from Asana",
    });
  } finally {
    // Reset button state
    uploadBtn.disabled = false;
    uploadBtn.innerHTML = originalText;
  }
}

// Helper function to check if Eiger fields are filled
function hasEigerFields(data) {
  const eigerFields = [
    "eiger_number",
    "tapeout",
    "vendor_lot",
    "customer_lot",
    "rib",
    "tox_target_sin",
    "heater",
    "undercut",
    "sin_tube_position",
  ];

  // Check if any Eiger field has a value
  return eigerFields.some((field) => {
    const value = data[field];
    return value && value !== "" && value !== "false" && value !== "N/A";
  });
}

// Function to update selected wafers in the table
function updateSelectedWafers(waferIds) {
  // Clear current selection
  if (typeof selectedWafers !== "undefined") {
    selectedWafers.clear();

    // Add each wafer ID to the selection
    waferIds.forEach((id) => selectedWafers.add(id));

    // Update UI to reflect selected wafers
    updateSelectedWafersCount();
    updateSelectionMessage();

    // Update checkboxes in the table
    const checkboxes = document.querySelectorAll(".wafer-checkbox");
    checkboxes.forEach((checkbox) => {
      const waferId = checkbox.dataset.waferId;
      if (waferId && selectedWafers.has(waferId)) {
        checkbox.checked = true;
        const row = checkbox.closest("tr");
        if (row) row.classList.add("selected-row");
      }
    });

    console.log(
      `[DEBUG] Updated selected wafers: ${waferIds.length} wafers selected`
    );
  } else {
    console.warn("[DEBUG] selectedWafers set is not defined");
  }
}

// Sort functionality
function handleSort(column) {
  if (currentSort.column === column) {
    currentSort.direction = currentSort.direction === "asc" ? "desc" : "asc";
  } else {
    currentSort.column = column;
    currentSort.direction = "asc";
  }

  updateSortIndicators();
  sortWaferTable();
}

// Update sort indicators
function updateSortIndicators() {
  // Remove all existing indicators
  document.querySelectorAll(".sort-indicator").forEach((el) => {
    el.textContent = "";
  });

  // Add indicator to current sort column
  if (currentSort.column) {
    const indicator = document.getElementById(`sort-${currentSort.column}`);
    indicator.textContent = currentSort.direction === "asc" ? "↑" : "↓";
  }
}

// Function to valide table elements
function validateTableElements() {
  const tableWrapper = document.getElementById("table-wrapper");
  const toggleBtn = document.getElementById("toggle-table");
  const toggleIcon = document.getElementById("toggle-icon");

  console.log("[DEBUG] Table elements check:");
  console.log("tableWrapper found:", !!tableWrapper);
  console.log("toggleBtn found:", !!toggleBtn);
  console.log("toggleIcon found:", !!toggleIcon);

  if (tableWrapper) {
    console.log("tableWrapper initial classes:", tableWrapper.className);
    console.log("tableWrapper dimensions:", {
      offsetHeight: tableWrapper.offsetHeight,
      clientHeight: tableWrapper.clientHeight,
      scrollHeight: tableWrapper.scrollHeight,
    });
  }

  // Add to validateTableElements function
  if (tableWrapper) {
    const parent = tableWrapper.parentElement;
    console.log("Parent element:", parent);
    console.log("Parent element style:", {
      overflow: getComputedStyle(parent).overflow,
      maxHeight: getComputedStyle(parent).maxHeight,
      display: getComputedStyle(parent).display,
    });
  }
}

// Call this at initialization
validateTableElements();

// Helper function to toggle table visibility
function toggleTableHandler() {
  console.log("[DEBUG] Toggle button clicked (nuclear handler)");

  const tableWrapper = document.getElementById("table-wrapper");
  const toggleIcon = document.getElementById("toggle-icon");
  const toggleText = document.getElementById("toggle-text");

  const isHidden = tableWrapper.style.display === "none";

  if (isHidden) {
    // Show the table
    tableWrapper.style.display = "block";
    toggleIcon?.classList.add("rotated");
    toggleText.textContent = "Hide Table";
    localStorage.setItem("tableCollapsed", "false");
    console.log("[DEBUG] Table SHOWN using display:block");
  } else {
    // Hide the table
    tableWrapper.style.display = "none";
    toggleIcon?.classList.remove("rotated");
    toggleText.textContent = "Show Table";
    localStorage.setItem("tableCollapsed", "true");
    console.log("[DEBUG] Table HIDDEN using display:none");
  }
}

// Toggle button handler with console logging for debugging
function initializeTableControls() {
  const tableWrapper = document.getElementById("table-wrapper");
  const toggleBtn = document.getElementById("toggle-table");
  const toggleIcon = document.getElementById("toggle-icon");
  const toggleText = document.getElementById("toggle-text");
  const refreshBtn = document.getElementById("refresh-table");
  const clearBtn = document.getElementById("clear-selection");

  if (!tableWrapper || !toggleBtn) {
    console.error("[DEBUG] Required table elements not found");
    return;
  }

  // Initialize table state from localStorage or default to expanded
  const isCollapsed = localStorage.getItem("tableCollapsed") === "true";

  // Apply initial state immediately using display property
  if (isCollapsed) {
    tableWrapper.style.display = "none";
    toggleIcon?.classList.remove("rotated");
    toggleText.textContent = "Show Table";
  } else {
    tableWrapper.style.display = "block";
    toggleIcon?.classList.add("rotated");
    toggleText.textContent = "Hide Table";
  }

  // Replace previous event listeners with our nuclear handler
  toggleBtn.removeEventListener("click", toggleTableHandler);
  toggleBtn.addEventListener("click", toggleTableHandler);

  // Refresh button handler
  refreshBtn?.addEventListener("click", () => {
    loadWaferInventory(TableState.pagination.currentPage);
  });

  // Clear selection handler
  clearBtn?.addEventListener("click", () => {
    clearSelection();
  });
}

// Function to remove duplicate filter buttons
function removeDuplicateFilterButtons() {
  // Get all filter button containers
  const filterContainers = document.querySelectorAll(".flex.gap-2.mb-4");

  // If we have more than one, remove the extras
  if (filterContainers.length > 1) {
    // Keep only the first one
    for (let i = 1; i < filterContainers.length; i++) {
      filterContainers[i].style.display = "none";
      console.log("[DEBUG] Hiding duplicate filter buttons container", i);
    }
  }

  // Alternative approach if filtering by exact container doesn't work
  const allFilterButtons = document.querySelectorAll(
    "button[data-quick-filter]"
  );
  const uniqueFilters = new Set();

  allFilterButtons.forEach((button) => {
    const filterType = button.dataset.quickFilter;
    const buttonText = button.textContent.trim();
    const identifier = `${filterType}-${buttonText}`;

    if (uniqueFilters.has(identifier)) {
      // This is a duplicate, hide it
      button.style.display = "none";
      console.log("[DEBUG] Hiding duplicate button:", buttonText);
    } else {
      uniqueFilters.add(identifier);
    }
  });
}

function setTableState(tableWrapper, toggleIcon, toggleText, isCollapsed) {
  if (isCollapsed) {
    // Collapsed state
    tableWrapper.classList.add("collapsed");

    // Direct style manipulation with !important equivalent
    tableWrapper.style.cssText = `
            max-height: 0px !important; 
            opacity: 0 !important;
            overflow: hidden !important;
            margin: 0 !important;
            padding: 0 !important;
            border: none !important;
            visibility: collapse !important;
            display: block !important;
        `;

    toggleIcon?.classList.remove("rotated");
    toggleText.textContent = "Show Table";
    console.log("[DEBUG] Changed to: collapsed");
  } else {
    // Expanded state
    tableWrapper.classList.remove("collapsed");

    // Reset styles to defaults
    tableWrapper.style.cssText = `
            max-height: 1000px !important;
            opacity: 1 !important;
            overflow-y: auto !important;
            visibility: visible !important;
            display: block !important;
        `;

    toggleIcon?.classList.add("rotated");
    toggleText.textContent = "Hide Table";
    console.log("[DEBUG] Changed to: expanded");
  }

  console.log("[DEBUG] Updated classes:", tableWrapper.className);
  console.log("[DEBUG] Updated styles:", tableWrapper.style.cssText);

  // Force browser to recognize the style changes
  setTimeout(() => {
    console.log("[DEBUG] Table dimensions after change:", {
      offsetHeight: tableWrapper.offsetHeight,
      clientHeight: tableWrapper.clientHeight,
      scrollHeight: tableWrapper.scrollHeight,
      computedMaxHeight: window.getComputedStyle(tableWrapper).maxHeight,
    });
  }, 50);
}

//Use selected wafers
function useSelectedWafers() {
  try {
    console.log("[DEBUG] Running useSelectedWafers function");
    console.log("[DEBUG] Current selection state:", {
      selectedWafersSet: selectedWafers,
      tableStateWafers: TableState.selectedWafers,
      selectedCount: selectedWafers.size,
      tableStateCount: TableState.selectedWafers.size,
    });

    // Find required elements
    const waferIdsTextarea = document.getElementById("wafer_ids");
    const numberInput = document.getElementById("number_of_wafers");

    if (!waferIdsTextarea || !numberInput) {
      console.error("[DEBUG] Required form elements not found");
      showError("Form elements not found. Cannot update wafer selection.");
      return;
    }

    // Make sure all selections are synchronized
    // This ensures selectedWafers and TableState.selectedWafers contain the same wafers
    const combinedWafers = new Set([
      ...Array.from(selectedWafers || []),
      ...Array.from(TableState.selectedWafers || []),
    ]);

    // Update both selection sets
    selectedWafers = new Set(combinedWafers);
    TableState.selectedWafers = new Set(combinedWafers);

    // Convert to sorted array for display
    const selectedWafersList = Array.from(combinedWafers).sort();

    console.log("[DEBUG] Updated selection:", {
      selectedWafersCount: selectedWafers.size,
      tableStateCount: TableState.selectedWafers.size,
      combinedWafersCount: combinedWafers.size,
      selectedWafersList: selectedWafersList,
    });

    // Update form elements
    waferIdsTextarea.value = selectedWafersList.join(", ");

    if (selectedWafersList.length > 0) {
      numberInput.value = selectedWafersList.length;
    }

    // Update UI elements
    updateSelectionMessage();
    updateSelectedWafersCount();

    // Show success message
    Swal.fire({
      icon: "success",
      title: "Wafers Added",
      text: `${selectedWafersList.length} wafers have been added to the form`,
      timer: 2000,
    });
  } catch (error) {
    console.error("[DEBUG] Error in useSelectedWafers:", error);
    showError("Error processing selected wafers");
  }
}

// Update the "Use Selected Wafers" message
function updateUseSelectedWafersMessage() {
  const messageContainer = document.getElementById("selection-message");
  if (!messageContainer) return;

  if (selectedWafers.size > 0) {
    messageContainer.innerHTML = `
            <div class="flex items-center justify-between">
                <span><strong>${selectedWafers.size}</strong> wafers selected</span>
                <button id="use-selected-btn" 
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    Use Selected Wafers
                </button>
            </div>
        `;
    messageContainer.classList.remove("hidden");

    // Add click handler for the button
    const useSelectedBtn = document.getElementById("use-selected-btn");
    if (useSelectedBtn) {
      useSelectedBtn.onclick = () => {
        updateWaferFields();
        Swal.fire({
          icon: "success",
          title: "Wafers Added",
          text: `${selectedWafers.size} wafers have been added to the form`,
          timer: 2000,
        });
      };
    }
  } else {
    messageContainer.classList.add("hidden");
  }
}

// Update wafer-related fields
function updateWaferFields() {
  const waferIdsTextarea = document.getElementById("wafer_ids");
  const numberInput = document.getElementById("number_of_wafers");

  if (waferIdsTextarea) {
    waferIdsTextarea.value = Array.from(selectedWafers).join(", ");
  }

  if (numberInput) {
    numberInput.value = selectedWafers.size;
  }

  // Validate selection
  validateWaferSelection();
}

//Populate form fields
function populateFormFields(data) {
  Object.entries(data).forEach(([fieldName, value]) => {
    const element =
      document.getElementById(fieldName) ||
      document.querySelector(`[name="${fieldName}"]`);

    if (element) {
      if (element.type === "checkbox") {
        element.checked = Boolean(value);
      } else if (element.type === "select-one") {
        const option = Array.from(element.options).find(
          (opt) => opt.value.toLowerCase() === String(value).toLowerCase()
        );
        if (option) {
          element.value = option.value;
        }
      } else {
        element.value = value;
      }
    }
  });
}

// Validate form data
function validateFormData(data) {
  // Validate boolean fields
  const booleanFields = ["rib", "heater", "undercut", "label_free"];
  booleanFields.forEach((field) => {
    if (typeof data[field] !== "boolean") {
      data[field] = String(data[field]).toLowerCase() === "true";
    }
  });

  return data;
}

// Table State Update Function
function updateTableState(collapsed) {
  const tableWrapper = document.getElementById("table-wrapper");
  const toggleIcon = document.getElementById("toggle-icon");
  const toggleText = document.getElementById("toggle-text");

  if (tableWrapper && toggleIcon && toggleText) {
    if (collapsed) {
      tableWrapper.classList.add("collapsed");
      toggleIcon.classList.remove("rotate-180");
      toggleText.textContent = "Show Table";
    } else {
      tableWrapper.classList.remove("collapsed");
      toggleIcon.classList.add("rotate-180");
      toggleText.textContent = "Hide Table";
    }
  }
}

function sortWaferTable() {
  const tbody = document.getElementById("inventory-table-body");
  const rows = Array.from(tbody.querySelectorAll("tr"));

  rows.sort((a, b) => {
    const aValue = a.querySelector(
      `td:nth-child(${getColumnIndex(currentSort.column)})`
    ).textContent;
    const bValue = b.querySelector(
      `td:nth-child(${getColumnIndex(currentSort.column)})`
    ).textContent;

    if (currentSort.direction === "asc") {
      return aValue.localeCompare(bValue);
    } else {
      return bValue.localeCompare(aValue);
    }
  });

  tbody.innerHTML = "";
  rows.forEach((row) => tbody.appendChild(row));
}

function getColumnIndex(columnName) {
  // Map column names to their index in the table
  const columnMap = {
    wafer_id: 2,
    lot_id: 3,
    xfab_id: 4,
    mask_set_id: 5,
    module_name: 6,
    cassette_id: 7,
    slot_id: 8,
    location_id: 9,
    arrived_at: 10,
    sent_at: 11,
  };
  return columnMap[columnName] || 2;
}

// Initialize wafer selection handling
function initializeWaferSelection() {
  console.log("[DEBUG] Starting wafer selection initialization");

  // Get all required elements
  const elements = {
    textarea: document.getElementById("wafer-ids-textarea"),
    waferCountInput: document.getElementById("wafer-count"),
    waferCountDisplay: document.getElementById("wafer-count-display"),
    clearBtn: document.getElementById("clear-wafers-btn"),
    useSelectedBtn: document.getElementById("use-selected-btn"),
    validationMessage: document.getElementById("wafer-validation-message"),
  };

  // Check if all required elements exist
  const missingElements = Object.entries(elements)
    .filter(([key, element]) => !element)
    .map(([key]) => key);

  if (missingElements.length > 0) {
    console.error("[DEBUG] Missing required elements:", missingElements);
    return; // Exit gracefully if elements are missing
  }

  console.log("[DEBUG] All required elements found for wafer selection");

  // Function to parse wafer IDs from textarea
  function parseWaferIds(text) {
    return text
      .split(/[,\n]/)
      .map((id) => id.trim())
      .filter((id) => id.length > 0);
  }

  // Function to update wafer count display
  function updateWaferCount() {
    const waferIds = parseWaferIds(elements.textarea.value);
    elements.waferCountDisplay.textContent = `${waferIds.length} wafers entered`;

    // Compare with number_of_wafers if set
    const expectedCount = parseInt(elements.waferCountInput.value) || 0;
    if (expectedCount > 0 && waferIds.length !== expectedCount) {
      elements.validationMessage.innerHTML = `
                <span class="text-yellow-600">
                    <i class="fas fa-exclamation-triangle"></i>
                    Number of wafers (${waferIds.length}) doesn't match expected count (${expectedCount})
                </span>`;
    } else {
      elements.validationMessage.innerHTML = "";
    }
  }

  try {
    // Add event listeners with error handling
    elements.textarea.addEventListener("input", () => {
      try {
        updateWaferCount();
      } catch (error) {
        console.error("[DEBUG] Error in textarea input handler:", error);
      }
    });

    elements.waferCountInput.addEventListener("input", () => {
      try {
        updateWaferCount();
      } catch (error) {
        console.error("[DEBUG] Error in wafer count input handler:", error);
      }
    });

    elements.clearBtn.addEventListener("click", () => {
      try {
        elements.textarea.value = "";
        updateWaferCount();
      } catch (error) {
        console.error("[DEBUG] Error in clear button handler:", error);
      }
    });

    elements.useSelectedBtn.addEventListener("click", () => {
      try {
        const selectedWafersList = Array.from(selectedWafers).sort();
        elements.textarea.value = selectedWafersList.join(", ");
        updateWaferCount();

        if (selectedWafersList.length > 0) {
          elements.waferCountInput.value = selectedWafersList.length;
        }
      } catch (error) {
        console.error("[DEBUG] Error in use selected button handler:", error);
      }
    });

    // Add paste handling
    elements.textarea.addEventListener("paste", (e) => {
      try {
        e.preventDefault();
        const text = e.clipboardData.getData("text");
        const formattedText = parseWaferIds(text).join(", ");

        const start = elements.textarea.selectionStart;
        const end = elements.textarea.selectionEnd;
        elements.textarea.value =
          elements.textarea.value.substring(0, start) +
          formattedText +
          elements.textarea.value.substring(end);

        updateWaferCount();
      } catch (error) {
        console.error("[DEBUG] Error in paste handler:", error);
      }
    });

    console.log(
      "[DEBUG] Wafer selection initialization completed successfully"
    );
  } catch (error) {
    console.error(
      "[DEBUG] Error during wafer selection initialization:",
      error
    );
  }
}

// Create any missing required elements
function createMissingElement(elementId) {
  let element;
  switch (elementId) {
    case "wafer-ids-textarea":
      element = document.createElement("textarea");
      element.id = "wafer-ids-textarea";
      element.className =
        "w-full border rounded-md p-2 pr-8 font-mono text-sm resize-vertical";
      element.rows = 6;
      break;
    case "wafer-count":
      element = document.createElement("input");
      element.id = "wafer-count";
      element.type = "number";
      element.className = "w-full border rounded-md p-2";
      element.min = 1;
      break;
    case "wafer-count-display":
      element = document.createElement("div");
      element.id = "wafer-count-display";
      element.className = "text-sm text-gray-600";
      break;
    case "clear-wafers-btn":
      element = document.createElement("button");
      element.id = "clear-wafers-btn";
      element.className =
        "absolute top-2 right-2 text-gray-400 hover:text-gray-600";
      element.innerHTML = '<i class="fas fa-times"></i>';
      break;
    case "wafer-validation-message":
      element = document.createElement("div");
      element.id = "wafer-validation-message";
      element.className = "mt-2 text-sm";
      break;
  }

  if (element) {
    const container =
      document.querySelector(".wafer-selection-container") || document.body;
    container.appendChild(element);
  }
}

// Add a validation function
function validateWaferCount(actual, expected) {
  const validationMessage = document.getElementById("wafer-validation-message");
  if (!validationMessage) return;

  if (expected > 0 && actual !== expected) {
    validationMessage.innerHTML = `
            <span class="text-yellow-600">
                <i class="fas fa-exclamation-triangle"></i>
                Number of wafers (${actual}) doesn't match expected count (${expected})
            </span>`;
  } else {
    validationMessage.innerHTML = "";
  }
}

// Add this to check raw response
async function testAsanaExtraction(url) {
  try {
    const response = await fetch("/shipment/api/asana/extract", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CSRFToken": getCsrfToken(),
      },
      body: JSON.stringify({ asana_url: url }),
    });

    const data = await response.json();
    console.log("Raw API Response:", data);
    return data;
  } catch (error) {
    console.error("Error:", error);
  }
}

// Initialize form handlers - modified to avoid duplicate event listeners
function initializeFormHandlers() {
  console.log(
    "[DEBUG] Initialize form handlers called - no action taken as handlers are already set up"
  );
  // Do nothing - form submission is now handled in setupFormSubmission
}

// Set up event listeners for wafer selection
function setupWaferSelectionEventListeners(elements) {
  try {
    const { textarea, waferCountInput, clearBtn } = elements;

    if (textarea) {
      textarea.addEventListener("input", () => {
        try {
          updateWaferCount(elements);
        } catch (error) {
          console.error("[DEBUG] Error in textarea input handler:", error);
        }
      });
    }

    if (waferCountInput) {
      waferCountInput.addEventListener("input", () => {
        try {
          updateWaferCount(elements);
        } catch (error) {
          console.error("[DEBUG] Error in wafer count input handler:", error);
        }
      });
    }

    if (clearBtn) {
      clearBtn.addEventListener("click", () => {
        try {
          clearWaferSelection(elements);
        } catch (error) {
          console.error("[DEBUG] Error in clear button handler:", error);
        }
      });
    }

    // Add paste handling for textarea
    if (textarea) {
      textarea.addEventListener("paste", handleWaferPaste);
    }
  } catch (error) {
    console.error(
      "[DEBUG] Error setting up wafer selection event listeners:",
      error
    );
  }
}

// Initialize form - modified to avoid duplicate event listeners
function initializeForm() {
  console.log(
    "[DEBUG] Initialize form called - no action taken as form is already initialized"
  );
  // Do nothing - form submission is now handled in setupFormSubmission
}

// Validate shipment data
function validateShipmentData(data) {
  // Required fields for carrier info
  const requiredFields = {
    contact_person: "Contact Person",
    email: "Email",
    address: "Address",
    telephone: "Telephone",
    destination_label: "Destination Label",
    title: "Title",
    parcel_size: "Parcel Size",
  };

  // Check required fields
  const missingFields = [];
  Object.entries(requiredFields).forEach(([field, label]) => {
    if (!data[field]) {
      missingFields.push(label);
    }
  });

  if (missingFields.length > 0) {
    Swal.fire({
      icon: "error",
      title: "Missing Required Fields",
      text: `Please fill in: ${missingFields.join(", ")}`,
    });
    return false;
  }

  // Validate email format
  if (data.email && !isValidEmail(data.email)) {
    Swal.fire({
      icon: "error",
      title: "Invalid Email",
      text: "Please enter a valid email address",
    });
    return false;
  }

  // Validate phone format
  if (data.telephone && !isValidPhone(data.telephone)) {
    Swal.fire({
      icon: "error",
      title: "Invalid Phone Number",
      text: "Please enter a valid phone number",
    });
    return false;
  }

  // Validate wafer selection
  const selectedWaferCount = data.wafer_ids.length;
  const specifiedCount = parseInt(data.number_of_wafers);
  if (selectedWaferCount !== specifiedCount) {
    Swal.fire({
      icon: "error",
      title: "Wafer Count Mismatch",
      text: `Selected wafers (${selectedWaferCount}) does not match specified count (${specifiedCount})`,
    });
    return false;
  }

  // Add validation for Eiger fields if they're required
  if (data.shipment_metadata) {
    const eigerFields = [
      "eiger_number",
      "tapeout",
      "vendor_lot",
      "customer_lot",
    ];

    const missingEigerFields = eigerFields.filter(
      (field) =>
        data.shipment_metadata[field] &&
        data.shipment_metadata[field].trim() === ""
    );

    if (missingEigerFields.length > 0) {
      Swal.fire({
        icon: "warning",
        title: "Missing Eiger Details",
        text: `Some Eiger fields are empty: ${missingEigerFields
          .map((f) => f.replace("_", " "))
          .join(", ")}`,
      });
      // Note: This is a warning, not an error, so we don't return false
    }
  }

  return true;
}

// Success handler
function handleSubmissionSuccess(result) {
  if (result.success) {
    Swal.fire({
      icon: "success",
      title: "Success!",
      text: "Shipment created successfully",
      timer: 2000,
    });
    clearForm();
    loadWaferInventory();
  } else {
    throw new Error(result.message || "Failed to create shipment");
  }
}

// Error handler
function handleSubmissionError(error) {
  console.error("[DEBUG] Submission error:", error);
  showError(`Failed to create shipment: ${error.message}`);
}

// Table filter functionality
const TableFilters = {
  // State management
  activeFilters: new Map(),
  searchTerm: "",

  // Initialize filters
  init() {
    this.setupSearchListener();
    this.setupFilterButtons();
    this.setupClearFilters();
    this.setupQuickFilters();
  },

  // Setup search functionality
  setupSearchListener() {
    const searchInput = document.getElementById("table-search");
    if (!searchInput) return;

    searchInput.addEventListener(
      "input",
      debounce((e) => {
        this.searchTerm = e.target.value.toLowerCase();
        this.applyFilters();
      }, 300)
    );
  },

  // Setup filter buttons
  setupFilterButtons() {
    const filterToggles = document.querySelectorAll("[data-filter]");
    filterToggles.forEach((toggle) => {
      toggle.addEventListener("click", () => {
        const filterType = toggle.dataset.filter;
        const filterValue = toggle.dataset.value;

        this.toggleFilter(filterType, filterValue);
        this.applyFilters();
        this.updateFilterUI();
      });
    });
  },

  // Setup clear filters button
  setupClearFilters() {
    const clearFilterBtn = document.getElementById("clear-filters");
    if (clearFilterBtn) {
      clearFilterBtn.addEventListener("click", () => {
        this.clearFilters();
      });
    }
  },

  // Setup quick filters
  setupQuickFilters() {
    const quickFilterContainer = document.createElement("div");
    quickFilterContainer.className = "flex gap-2 mb-4";
    quickFilterContainer.innerHTML = `
            <button class="px-3 py-1 rounded-md bg-gray-100 hover:bg-gray-200" 
                    data-quick-filter="available">
                Available Wafers
            </button>
            <button class="px-3 py-1 rounded-md bg-gray-100 hover:bg-gray-200" 
                    data-quick-filter="in_transit">
                In Transit
            </button>
            <button class="px-3 py-1 rounded-md bg-gray-100 hover:bg-gray-200" 
                    data-quick-filter="received">
                Received
            </button>
        `;

    const tableContainer = document.querySelector(".overflow-x-auto");
    if (tableContainer) {
      tableContainer.parentNode.insertBefore(
        quickFilterContainer,
        tableContainer
      );
    }

    // Add quick filter handlers
    quickFilterContainer.addEventListener("click", (e) => {
      const filterButton = e.target.closest("[data-quick-filter]");
      if (filterButton) {
        this.applyQuickFilter(filterButton.dataset.quickFilter);
      }
    });
  },

  // Toggle filter
  toggleFilter(type, value) {
    if (!this.activeFilters.has(type)) {
      this.activeFilters.set(type, new Set());
    }

    const filterSet = this.activeFilters.get(type);
    if (filterSet instanceof Set) {
      if (filterSet.has(value)) {
        filterSet.delete(value);
        if (filterSet.size === 0) {
          this.activeFilters.delete(type);
        }
      } else {
        filterSet.add(value);
      }
    }
  },

  // Apply quick filter
  applyQuickFilter(filterType) {
    this.activeFilters.clear();
    if (filterType) {
      // Create a new Set with the filter value
      const filterSet = new Set([filterType]);
      this.activeFilters.set("quick", filterSet);
    }
    this.applyFilters();
  },

  // Apply filters
  async applyFilters() {
    showLoadingState(true);
    try {
      const params = new URLSearchParams({
        search: this.searchTerm,
        page: TableState.pagination.currentPage,
        per_page: TableState.pagination.itemsPerPage,
      });

      // Safely handle filter values
      this.activeFilters.forEach((filterValue, type) => {
        if (filterValue instanceof Set) {
          // Handle Set of values
          filterValue.forEach((value) => {
            params.append(`filter[${type}][]`, value);
          });
        } else if (Array.isArray(filterValue)) {
          // Handle array of values
          filterValue.forEach((value) => {
            params.append(`filter[${type}][]`, value);
          });
        } else if (filterValue && typeof filterValue === "object") {
          // Handle object of values
          Object.entries(filterValue).forEach(([key, value]) => {
            params.append(`filter[${type}][${key}]`, value);
          });
        } else if (filterValue !== null && filterValue !== undefined) {
          // Handle single value
          params.append(`filter[${type}]`, filterValue.toString());
        }
      });

      // Load filtered data
      await loadWaferInventory(1);
    } catch (error) {
      console.error("[DEBUG] Error applying filters:", error);
      showError("Error filtering data");
    } finally {
      showLoadingState(false);
    }
  },

  // Update filter UI
  updateFilterUI() {
    const filterToggles = document.querySelectorAll("[data-filter]");
    filterToggles.forEach((toggle) => {
      const type = toggle.dataset.filter;
      const value = toggle.dataset.value;
      const isActive =
        this.activeFilters.has(type) && this.activeFilters.get(type).has(value);

      toggle.classList.toggle("bg-blue-100", isActive);
      toggle.classList.toggle("text-blue-800", isActive);
    });

    this.updateFilterBadges();
  },

  // Update filter badges
  updateFilterBadges() {
    const container = document.getElementById("active-filters");
    if (!container) return;

    container.innerHTML = "";

    this.activeFilters.forEach((values, type) => {
      if (values instanceof Set) {
        values.forEach((value) => {
          const badge = document.createElement("div");
          badge.className =
            "inline-flex items-center px-2 py-1 rounded-full text-sm bg-blue-100 text-blue-800 mr-2 mb-2";
          badge.innerHTML = `
                        <span class="mr-1">${type}: ${value}</span>
                        <button class="ml-1 text-blue-600 hover:text-blue-800" 
                                onclick="TableFilters.removeFilter('${type}', '${value}')">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
          container.appendChild(badge);
        });
      }
    });
  },

  // Remove specific filter
  removeFilter(type, value) {
    if (this.activeFilters.has(type)) {
      const filterSet = this.activeFilters.get(type);
      if (filterSet instanceof Set) {
        filterSet.delete(value);
        if (filterSet.size === 0) {
          this.activeFilters.delete(type);
        }
      }
    }
    this.applyFilters();
    this.updateFilterUI();
  },

  // Clear all filters
  clearFilters() {
    const searchInput = document.getElementById("table-search");
    if (searchInput) {
      searchInput.value = "";
    }

    this.activeFilters.clear();
    this.searchTerm = "";

    const filterToggles = document.querySelectorAll("[data-filter]");
    filterToggles.forEach((toggle) => {
      toggle.classList.remove("bg-blue-100", "text-blue-800");
    });

    const activeBadges = document.getElementById("active-filters");
    if (activeBadges) {
      activeBadges.innerHTML = "";
    }

    TableState.pagination.currentPage = 1;
    loadWaferInventory(1);
  },

  // Loading state handlers
  showLoading() {
    const overlay = document.getElementById("loading-overlay");
    if (overlay) {
      overlay.classList.remove("hidden");
    }
  },

  hideLoading() {
    const overlay = document.getElementById("loading-overlay");
    if (overlay) {
      overlay.classList.add("hidden");
    }
  },
};

function addEnhancedTableStyles() {
  const style = document.createElement("style");
  style.textContent = `
        /* Enhanced table collapse styles */
        #table-wrapper {
            transition: max-height 0.5s ease, opacity 0.3s ease;
            max-height: 1000px; 
            opacity: 1;
            overflow-y: auto;
            display: block;
        }

        #table-wrapper.collapsed {
            max-height: 0 !important;
            opacity: 0 !important;
            overflow: hidden !important;
            pointer-events: none !important;
            margin: 0 !important;
            padding: 0 !important;
            border: none !important;
            visibility: collapse !important;
            position: absolute !important;
            z-index: -1 !important;
        }
    `;
  document.head.appendChild(style);
  console.log("[DEBUG] Enhanced table styles added");
}

//Remove unwanted UI elements
function removeUnwantedElements() {
  // Add CSS rules to hide elements without causing a loop
  const style = document.createElement("style");
  style.textContent = `
    /* Hide search bar, filters button, and status buttons */
    input[placeholder="Search wafers..."],
    #table-search,
    .relative.flex-1,
    button:has(.fa-filter),
    button[title="Filters"],
    button[data-quick-filter],
    .flex.gap-2.mb-4,
    div:has(> button[data-quick-filter]) {
      display: none !important;
    }
  `;
  document.head.appendChild(style);

  // One-time removal of elements (no observer)
  try {
    // Try to remove search bar
    document
      .querySelectorAll('input[placeholder="Search wafers..."], #table-search')
      .forEach((el) => el.closest(".relative.flex-1")?.remove());

    // Try to remove filters button
    document
      .querySelectorAll('button:has(.fa-filter), button[title="Filters"]')
      .forEach((el) => el.remove());

    // Try to remove quick filter buttons
    document
      .querySelectorAll("button[data-quick-filter]")
      .forEach((el) => el.closest(".flex.gap-2.mb-4")?.remove());

    console.log("[DEBUG] Unwanted UI elements removed");
  } catch (error) {
    console.error("[DEBUG] Error removing unwanted elements:", error);
  }
}

// Initialize the application
document.addEventListener("DOMContentLoaded", function () {
  console.log("[DEBUG] DOM content loaded, initializing application");

  try {
    // Initialize state
    ShipmentState.init();
    PaginationState.init();

    // Set up form submission handlers (centralized function)
    setupFormSubmission();

    // Add styles first before creating elements
    addPaginationStyles();
    addEnhancedTableStyles();

    // Create pagination container
    createPaginationContainer();

    // Initialize event listeners
    initializeEventListeners();

    // Initialize UI components
    initializeTableControls();
    initializeWaferSelection();

    // Remove unwanted UI elements
    removeUnwantedElements();

    // Remove duplicate filter buttons
    removeDuplicateFilterButtons();

    // Load data last (after UI is ready)
    loadLocations();
    loadWaferInventory();

    console.log("[DEBUG] Application initialized successfully");
  } catch (error) {
    console.error("[DEBUG] Error initializing application:", error);
    showError("Failed to initialize application. Please refresh the page.");
  }
});

// Make functions available globally
window.setupFormSubmission = setupFormSubmission;
window.handleCreate = handleCreate;
window.handleUpload = handleUpload;
window.handleModify = handleModify;
window.handleDelete = handleDelete;
window.handleLocationChange = handleLocationChange;
window.loadWaferInventory = loadWaferInventory;
window.clearFilters = clearFilters;
window.filterWaferTableByLotIds = filterWaferTableByLotIds;
window.navigatePage = navigatePage;
window.handleJumpToPage = handleJumpToPage;
window.clearSelection = clearSelection;
window.useSelectedWafers = useSelectedWafers;
window.toggleTableHandler = toggleTableHandler;
window.validateForm = validateForm; //Point de repère

/**
 * Bell Notification System JavaScript
 * Handles real-time notifications, bell icon updates, and notification panel
 */

class NotificationManager {
    constructor() {
        this.notificationBell = null;
        this.notificationBadge = null;
        this.notificationPanel = null;
        this.notificationList = null;
        this.isInitialized = false;
        this.refreshInterval = null;
        this.unreadCount = 0;
        
        this.init();
    }
    
    init() {
        if (this.isInitialized) return;
        
        // Find notification elements
        this.notificationBell = document.querySelector('[title="Notifications"]');
        this.notificationBadge = document.querySelector('.notification-badge');
        
        if (!this.notificationBell || !this.notificationBadge) {
            console.warn('Notification elements not found');
            return;
        }
        
        this.createNotificationPanel();
        this.setupEventListeners();
        this.loadNotifications();
        this.startPeriodicRefresh();
        
        this.isInitialized = true;
        console.log('Notification system initialized');
    }
    
    createNotificationPanel() {
        // Create notification panel HTML
        const panelHTML = `
            <div id="notification-panel" class="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50 hidden max-h-96 overflow-hidden">
                <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-800">Notifications</h3>
                    <div class="flex space-x-2">
                        <button id="mark-all-read-btn" class="text-sm text-blue-600 hover:text-blue-800" title="Mark all as read">
                            <i class="fas fa-check-double"></i>
                        </button>
                        <button id="refresh-notifications-btn" class="text-sm text-gray-600 hover:text-gray-800" title="Refresh">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button id="close-notifications-btn" class="text-sm text-gray-600 hover:text-gray-800" title="Close">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                <div id="notification-list" class="max-h-80 overflow-y-auto">
                    <div class="p-4 text-center text-gray-500">
                        <i class="fas fa-spinner fa-spin"></i> Loading notifications...
                    </div>
                </div>
                <div class="p-3 border-t border-gray-200 text-center">
                    <button id="view-all-notifications-btn" class="text-sm text-blue-600 hover:text-blue-800">
                        View All Notifications
                    </button>
                </div>
            </div>
        `;
        
        // Insert panel after the notification bell
        this.notificationBell.parentElement.style.position = 'relative';
        this.notificationBell.parentElement.insertAdjacentHTML('beforeend', panelHTML);
        
        this.notificationPanel = document.getElementById('notification-panel');
        this.notificationList = document.getElementById('notification-list');
    }
    
    setupEventListeners() {
        // Bell click to toggle panel
        this.notificationBell.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleNotificationPanel();
        });
        
        // Close panel when clicking outside
        document.addEventListener('click', (e) => {
            if (!this.notificationPanel.contains(e.target) && !this.notificationBell.contains(e.target)) {
                this.hideNotificationPanel();
            }
        });
        
        // Panel action buttons
        document.getElementById('mark-all-read-btn').addEventListener('click', () => {
            this.markAllAsRead();
        });
        
        document.getElementById('refresh-notifications-btn').addEventListener('click', () => {
            this.loadNotifications();
        });
        
        document.getElementById('close-notifications-btn').addEventListener('click', () => {
            this.hideNotificationPanel();
        });
        
        document.getElementById('view-all-notifications-btn').addEventListener('click', () => {
            // TODO: Navigate to full notifications page
            console.log('Navigate to full notifications page');
        });
    }
    
    toggleNotificationPanel() {
        if (this.notificationPanel.classList.contains('hidden')) {
            this.showNotificationPanel();
        } else {
            this.hideNotificationPanel();
        }
    }
    
    showNotificationPanel() {
        this.notificationPanel.classList.remove('hidden');
        this.loadNotifications(); // Refresh when opening
    }
    
    hideNotificationPanel() {
        this.notificationPanel.classList.add('hidden');
    }
    
    async loadNotifications() {
        try {
            const response = await fetch('/api/notifications?limit=10', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.success) {
                this.updateNotificationBadge(data.unread_count);
                this.renderNotifications(data.notifications);
            } else {
                console.error('Failed to load notifications:', data.message);
                this.showError('Failed to load notifications');
            }
        } catch (error) {
            console.error('Error loading notifications:', error);
            this.showError('Error loading notifications');
        }
    }
    
    async updateUnreadCount() {
        try {
            const response = await fetch('/api/notifications/unread-count', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.updateNotificationBadge(data.unread_count);
                }
            }
        } catch (error) {
            console.error('Error updating unread count:', error);
        }
    }
    
    updateNotificationBadge(count) {
        this.unreadCount = count;
        
        if (count > 0) {
            this.notificationBadge.textContent = count > 99 ? '99+' : count.toString();
            this.notificationBadge.style.display = 'block';
            this.notificationBadge.className = 'notification-badge absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center';
        } else {
            this.notificationBadge.style.display = 'none';
        }
    }
    
    renderNotifications(notifications) {
        if (!notifications || notifications.length === 0) {
            this.notificationList.innerHTML = `
                <div class="p-4 text-center text-gray-500">
                    <i class="fas fa-bell-slash text-2xl mb-2"></i>
                    <p>No notifications</p>
                </div>
            `;
            return;
        }
        
        const notificationHTML = notifications.map(notification => {
            const isUnread = !notification.is_read;
            const timeAgo = this.getTimeAgo(new Date(notification.created_at));
            const typeIcon = this.getNotificationIcon(notification.notification_type);
            
            return `
                <div class="notification-item p-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer ${isUnread ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''}" 
                     data-notification-id="${notification.id}">
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <i class="${typeIcon} text-${this.getNotificationColor(notification.notification_type)}"></i>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900 ${isUnread ? 'font-semibold' : ''}">${notification.title}</p>
                            <p class="text-sm text-gray-600 truncate">${notification.message}</p>
                            <p class="text-xs text-gray-400 mt-1">${timeAgo}</p>
                        </div>
                        <div class="flex-shrink-0 flex space-x-1">
                            ${isUnread ? '<div class="w-2 h-2 bg-blue-500 rounded-full"></div>' : ''}
                            <button class="delete-notification text-gray-400 hover:text-red-500" data-notification-id="${notification.id}" title="Delete">
                                <i class="fas fa-times text-xs"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }).join('');
        
        this.notificationList.innerHTML = notificationHTML;
        
        // Add event listeners for notification items
        this.notificationList.querySelectorAll('.notification-item').forEach(item => {
            item.addEventListener('click', (e) => {
                if (!e.target.closest('.delete-notification')) {
                    const notificationId = item.dataset.notificationId;
                    this.markAsRead(notificationId);
                }
            });
        });
        
        // Add event listeners for delete buttons
        this.notificationList.querySelectorAll('.delete-notification').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const notificationId = btn.dataset.notificationId;
                this.deleteNotification(notificationId);
            });
        });
    }
    
    getNotificationIcon(type) {
        const icons = {
            'info': 'fas fa-info-circle',
            'success': 'fas fa-check-circle',
            'warning': 'fas fa-exclamation-triangle',
            'error': 'fas fa-exclamation-circle',
            'email': 'fas fa-envelope'
        };
        return icons[type] || 'fas fa-bell';
    }
    
    getNotificationColor(type) {
        const colors = {
            'info': 'blue-500',
            'success': 'green-500',
            'warning': 'yellow-500',
            'error': 'red-500',
            'email': 'purple-500'
        };
        return colors[type] || 'gray-500';
    }
    
    getTimeAgo(date) {
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);
        
        if (diffInSeconds < 60) return 'Just now';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
        if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
        
        return date.toLocaleDateString();
    }
    
    async markAsRead(notificationId) {
        try {
            const response = await fetch(`/api/notifications/${notificationId}/read`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                }
            });
            
            if (response.ok) {
                this.loadNotifications(); // Refresh the list
            }
        } catch (error) {
            console.error('Error marking notification as read:', error);
        }
    }
    
    async markAllAsRead() {
        try {
            const response = await fetch('/api/notifications/mark-all-read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                }
            });
            
            if (response.ok) {
                this.loadNotifications(); // Refresh the list
                this.showSuccess('All notifications marked as read');
            }
        } catch (error) {
            console.error('Error marking all notifications as read:', error);
        }
    }
    
    async deleteNotification(notificationId) {
        try {
            const response = await fetch(`/api/notifications/${notificationId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                }
            });
            
            if (response.ok) {
                this.loadNotifications(); // Refresh the list
            }
        } catch (error) {
            console.error('Error deleting notification:', error);
        }
    }
    
    startPeriodicRefresh() {
        // Refresh unread count every 30 seconds
        this.refreshInterval = setInterval(() => {
            this.updateUnreadCount();
        }, 30000);
    }
    
    stopPeriodicRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }
    
    getCSRFToken() {
        const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                     document.querySelector('input[name="csrf_token"]')?.value;
        return token || '';
    }
    
    showSuccess(message) {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'success',
                title: 'Success',
                text: message,
                timer: 2000,
                showConfirmButton: false
            });
        } else {
            console.log('Success:', message);
        }
    }
    
    showError(message) {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: message
            });
        } else {
            console.error('Error:', message);
        }
    }
    
    // Public method to create notifications from other parts of the app
    async createEmailNotification(emailData) {
        try {
            const response = await fetch('/api/notifications/email', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                body: JSON.stringify(emailData)
            });
            
            if (response.ok) {
                this.updateUnreadCount(); // Update badge
            }
        } catch (error) {
            console.error('Error creating email notification:', error);
        }
    }
    
    destroy() {
        this.stopPeriodicRefresh();
        this.isInitialized = false;
    }
}

// Initialize notification manager when DOM is loaded
let notificationManager = null;

document.addEventListener('DOMContentLoaded', function() {
    notificationManager = new NotificationManager();
});

// Export for use in other scripts
window.NotificationManager = NotificationManager;
window.notificationManager = notificationManager;

// about.js - updated functions

document.addEventListener("DOMContentLoaded", function () {
  // Initialize everything at startup
  initializePerformanceCharts();
  setupChangelogFilters();
  setupHealthMonitor();
  initializeInteractiveFeatures();
  startRealtimeUpdates();
});
function initializePerformanceCharts() {
  // Performance Chart
  const perfCtx = document.getElementById("performanceChart")?.getContext("2d");
  if (perfCtx) {
    new Chart(perfCtx, {
      type: "line",
      data: {
        labels: [
          "6h ago",
          "5h ago",
          "4h ago",
          "3h ago",
          "2h ago",
          "1h ago",
          "Now",
        ],
        datasets: [
          {
            label: "System Load",
            data: [65, 59, 80, 81, 56, 55, 40],
            borderColor: "#3B82F6",
            tension: 0.4,
            fill: false,
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: true,
            position: "top",
          },
        },
        scales: {
          y: {
            beginAtZero: true,
          },
        },
      },
    });
  }

  // Response Time Chart
  const respCtx = document
    .getElementById("responseTimeChart")
    ?.getContext("2d");
  if (respCtx) {
    new Chart(respCtx, {
      type: "line",
      data: {
        labels: [
          "6h ago",
          "5h ago",
          "4h ago",
          "3h ago",
          "2h ago",
          "1h ago",
          "Now",
        ],
        datasets: [
          {
            label: "Response Time (ms)",
            data: [45, 42, 50, 48, 35, 40, 38],
            borderColor: "#10B981",
            tension: 0.4,
            fill: false,
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: true,
            position: "top",
          },
        },
        scales: {
          y: {
            beginAtZero: true,
          },
        },
      },
    });
  }
}
function setupChangelogFilters() {
  const filters = document.querySelectorAll(".changelog-filter");
  const items = document.querySelectorAll(".changelog-item");

  filters.forEach((filter) => {
    filter.addEventListener("click", () => {
      filters.forEach((f) =>
        f.classList.remove("active", "bg-blue-100", "text-blue-600")
      );
      filter.classList.add("active", "bg-blue-100", "text-blue-600");

      const type = filter.textContent.trim().toLowerCase();
      items.forEach((item) => {
        item.style.display =
          type === "all" || item.dataset.type === type ? "block" : "none";
      });
    });
  });
}

function setupHealthMonitor() {
  const refreshBtn = document.getElementById("refreshHealth");
  if (refreshBtn) {
    refreshBtn.addEventListener("click", () => {
      const icon = refreshBtn.querySelector("i");
      icon?.classList.add("fa-spin");

      updateHealthMetrics().finally(() => {
        setTimeout(() => {
          icon?.classList.remove("fa-spin");
        }, 1000);
      });
    });
  }
}

function updateHealthMetrics() {
  console.log("Fetching health metrics...");
  return fetch("/api/health")
    .then((response) => response.json())
    .then((result) => {
      console.log("Received health data:", result);
      if (result.success) {
        const data = result.data;

        // Update System Health Monitor
        console.log("Updating system health with:", data);
        updateSystemHealth(data);

        // Update Integration Status
        console.log("Updating integration status with:", data.integrations);
        updateIntegrationStatus(data.integrations);

        // Update System Insights
        console.log("Updating system insights with:", data);
        updateSystemInsights(data);

        // Update activity feed
        if (data.recent_events) {
          console.log("Updating activity feed with:", data.recent_events);
          updateActivityFeed(data.recent_events);
        }
      }
    })
    .catch((error) => {
      console.error("Error updating health metrics:", error);
      showErrorStatus();
    });
}

function updateSystemInsights(data) {
  console.log("updateSystemInsights called with:", data);

  // Processing Time
  if (data.processing_time) {
    updateElement("processingTime", data.processing_time);
  }

  // Success Rate
  if (data.success_rate) {
    updateElement("successRate", data.success_rate);
  }

  // Uptime
  if (data.uptime) {
    updateElement("uptimeMetric", data.uptime);
  } else if (data.systemUptime) {
    // Try alternate property name
    const uptime = data.systemUptime;
    updateElement("uptimeMetric", `${uptime.days}d ${uptime.hours}h`);
  }

  // Update activity feed
  updateActivityFeed(data.recent_events || []);

  // Update system load heatmap if data is available
  if (data.system_load && data.system_load.length > 0) {
    updateSystemLoadHeatmap(data.system_load);
  }

  // Update the small charts for each metric
  updateMetricCharts(data);
}

function updateMetricCharts(data) {
  // Create simple sparkline charts for the metrics
  // This is a simplified version - in a real app, you'd use a library like Chart.js

  // Processing Time chart
  const procTimeChart = document.getElementById("processingTimeChart");
  if (procTimeChart) {
    procTimeChart.innerHTML = createSparkline(
      [30, 28, 32, 35, 25, Number(data.dbResponse) || 30],
      "blue"
    );
  }

  // Success Rate chart
  const successRateChart = document.getElementById("successRateChart");
  if (successRateChart) {
    const successRateValue =
      parseFloat(String(data.success_rate).replace("%", "")) || 0;
    successRateChart.innerHTML = createSparkline(
      [97, 98, 96, 99, 97, successRateValue],
      "green"
    );
  }

  // Uptime chart
  const uptimeChart = document.getElementById("uptimeChart");
  if (uptimeChart) {
    const uptime = data.systemUptime || { days: 0, hours: 0 };
    const uptimeValue = uptime.days * 24 + uptime.hours;
    uptimeChart.innerHTML = createSparkline(
      [
        uptimeValue - 5,
        uptimeValue - 4,
        uptimeValue - 3,
        uptimeValue - 2,
        uptimeValue - 1,
        uptimeValue,
      ],
      "purple"
    );
  }
}

function createSparkline(values, color) {
  // Simple function to create a basic SVG sparkline
  const width = 100;
  const height = 16;
  const max = Math.max(...values);
  const min = Math.min(...values);
  const range = max - min || 1;

  const points = values
    .map((value, index) => {
      const x = (index / (values.length - 1)) * width;
      const y = height - ((value - min) / range) * height;
      return `${x},${y}`;
    })
    .join(" ");

  return `
        <svg width="${width}" height="${height}" class="sparkline">
            <polyline
                fill="none"
                stroke="${color}"
                stroke-width="1.5"
                points="${points}"
            />
        </svg>
    `;
}

function updateActivityFeed(events) {
  const feed = document.getElementById("activityFeed");
  if (!feed || !events || events.length === 0) return;

  feed.innerHTML = events
    .map(
      (event) => `
                <div class="mb-2 p-2 bg-white dark:bg-gray-800 rounded shadow-sm">
                    <div class="flex items-center">
                        <i class="fas ${getActivityIcon(
                          event.type
                        )} text-${getActivityColor(event.type)}-500 mr-2"></i>
                        <div>
                            <div class="text-sm font-medium">${
                              event.description
                            }</div>
                            <div class="text-xs text-gray-500">${
                              event.timestamp
                            }</div>
                        </div>
                    </div>
                </div>
            `
    )
    .join("");
}

function updateSystemLoadHeatmap(loadData) {
  const container = document.getElementById("heatmapContainer");
  if (!container) return;

  // Clear any existing content
  container.innerHTML = "";

  // Group data by day
  const days = [...new Set(loadData.map((item) => item.day))];
  const hours = [...new Set(loadData.map((item) => item.hour))];

  // Create a grid for the heatmap
  const gridHTML = `
        <div class="grid grid-cols-${hours.length + 1} gap-1">
            <div class="p-1"></div>
            ${hours
              .map(
                (hour) => `<div class="p-1 text-xs text-center">${hour}</div>`
              )
              .join("")}
            ${days
              .map((day) => {
                return `
                    <div class="p-1 text-xs">${day}</div>
                    ${hours
                      .map((hour) => {
                        const cell = loadData.find(
                          (item) => item.day === day && item.hour === hour
                        );
                        const value = cell ? cell.value : 0;
                        const intensity = Math.floor((value / 100) * 255);
                        const color = `rgb(${255 - intensity}, ${
                          255 - intensity
                        }, 255)`;
                        return `<div class="p-1 h-5 rounded" style="background-color: ${color}" title="${day} ${hour}: ${value}%"></div>`;
                      })
                      .join("")}
                `;
              })
              .join("")}
        </div>
    `;

  container.innerHTML = gridHTML;
}

function getActivityIcon(type) {
  const icons = {
    label_generation: "fa-tag",
    inventory_update: "fa-box",
    error: "fa-exclamation-circle",
    success: "fa-check-circle",
    warning: "fa-exclamation-triangle",
    default: "fa-info-circle",
  };
  return icons[type] || icons.default;
}

function getActivityColor(type) {
  const colors = {
    label_generation: "blue",
    inventory_update: "green",
    error: "red",
    success: "green",
    warning: "yellow",
    default: "gray",
  };
  return colors[type] || colors.default;
}

function updateSystemHealth(data) {
  if (!data) return;

  console.log("Updating specific system health metrics");

  // Update Database Connection status
  const dbStatus = document.getElementById("dbStatus");
  const isDbConnected = data.integrations?.database?.status === true;
  if (dbStatus) {
    dbStatus.className = isDbConnected
      ? "px-2 py-1 text-xs rounded-full bg-green-100 text-green-800"
      : "px-2 py-1 text-xs rounded-full bg-red-100 text-red-800";
    dbStatus.innerHTML = isDbConnected
      ? '<i class="fas fa-check-circle mr-1"></i> Operational'
      : '<i class="fas fa-exclamation-circle mr-1"></i> Error';
  }

  // Update API Services status
  const apiStatus = document.getElementById("apiStatus");
  const asanaConnected = data.integrations?.asana?.status === true;
  const driveConnected = data.integrations?.drive?.status === true;
  const allApisConnected = asanaConnected && driveConnected;

  if (apiStatus) {
    apiStatus.className = allApisConnected
      ? "px-2 py-1 text-xs rounded-full bg-green-100 text-green-800"
      : "px-2 py-1 text-xs rounded-full bg-red-100 text-red-800";
    apiStatus.innerHTML = allApisConnected
      ? '<i class="fas fa-check-circle mr-1"></i> All Systems Go'
      : '<i class="fas fa-exclamation-circle mr-1"></i> Error';
  }

  // Update System Resources status
  const resourceStatus = document.getElementById("resourceStatus");
  const cpuHealthy = data.cpu < 80;
  const memoryHealthy = data.memory < 80;
  const resourcesHealthy = cpuHealthy && memoryHealthy;

  if (resourceStatus) {
    resourceStatus.className = resourcesHealthy
      ? "px-2 py-1 text-xs rounded-full bg-green-100 text-green-800"
      : "px-2 py-1 text-xs rounded-full bg-red-100 text-red-800";
    resourceStatus.innerHTML = resourcesHealthy
      ? '<i class="fas fa-check-circle mr-1"></i> Optimal'
      : '<i class="fas fa-exclamation-circle mr-1"></i> Error';
  }

  // Update metrics
  updateElement("dbResponseTime", `${data.dbResponse}ms`);
  updateElement("dbLastSync", data.lastSync);
  updateElement("cpuUsage", `${data.cpu}%`);
  updateElement("memoryUsage", `${data.memory}%`);

  // Update API statuses
  updateStatusIndicator("asanaStatus", asanaConnected);
  updateStatusIndicator("driveStatus", driveConnected);

  // Update progress bars
  updateProgressBar("cpu", data.cpu);
  updateProgressBar("memory", data.memory);
}

function updateIntegrationStatus(integrations) {
  if (!integrations) return;

  console.log("Updating integration status indicators");

  // Asana
  updateStatusIndicator("asanaConnectionStatus", integrations.asana.status);
  updateElement("asanaLastSync", integrations.asana.lastSync);

  // Google Drive
  updateStatusIndicator("driveConnectionStatus", integrations.drive.status);
  updateElement("driveStorage", integrations.drive.storage);

  // Printer
  updateStatusIndicator("printerStatus", integrations.printer.status);
  updateElement("labelCount", integrations.printer.labelsToday);

  // Database
  updateStatusIndicator("dbConnectionStatus", integrations.database.status);
  updateElement("dbConnections", integrations.database.connections);
}

function updateStatusIndicator(elementId, status) {
  const element = document.getElementById(elementId);
  if (element) {
    const statusClass = status ? "text-green-500" : "text-red-500";
    const statusText = status ? "Connected" : "Disconnected";
    element.className = `flex items-center ${statusClass}`;
    element.innerHTML = `
        <i class="fas fa-circle text-xs mr-1"></i>
        ${statusText}
    `;
  }
}

function updateElement(id, value) {
  const element = document.getElementById(id);
  if (element) {
    element.textContent = value;
  }
}

function updateProgressBar(type, value) {
  const bar = document.querySelector(
    `div[style*="width"][class*="bg-${type === "cpu" ? "blue" : "green"}-500"]`
  );
  if (bar) {
    bar.style.width = `${value}%`;
  }
}

function showErrorStatus() {
  ["dbStatus", "apiStatus", "resourceStatus"].forEach((id) => {
    const element = document.getElementById(id);
    if (element) {
      element.className =
        "px-2 py-1 text-xs rounded-full bg-red-100 text-red-800";
      element.innerHTML =
        '<i class="fas fa-exclamation-circle mr-1"></i> Error';
    }
  });
}

function startRealtimeUpdates() {
  // Initial update
  updateHealthMetrics();

  // Update metrics every 30 seconds
  setInterval(updateHealthMetrics, 30000);
}

function initializeInteractiveFeatures() {
  // Initialize socket connection
  const socket = io();

  socket.on("connect", () => {
    console.log("SocketIO connected");
  });

  socket.on("system_event", (data) => {
    updateHealthMetrics();
  });

  // Initial metrics update
  updateHealthMetrics();
}

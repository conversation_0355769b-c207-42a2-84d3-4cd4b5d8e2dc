// Advanced Inventory Management Features for Talaria
// Includes smart search, batch operations, predictive analytics, and workflow automation

class AdvancedInventoryManager {
  constructor() {
    this.searchCache = new Map();
    this.batchOperations = [];
    this.predictiveModel = null;
    this.workflowEngine = new WorkflowEngine();
    this.smartFilters = new SmartFilterManager();
    
    this.init();
  }

  init() {
    this.setupSmartSearch();
    this.setupBatchOperations();
    this.setupPredictiveAnalytics();
    this.setupWorkflowAutomation();
    this.setupKeyboardShortcuts();
  }

  // Smart Search with AI-powered suggestions
  setupSmartSearch() {
    const searchInput = document.getElementById('smart-search');
    if (!searchInput) return;

    // Add smart search container
    const searchContainer = searchInput.parentNode;
    searchContainer.classList.add('smart-search-container');
    
    // Create suggestions dropdown
    const suggestionsDropdown = document.createElement('div');
    suggestionsDropdown.className = 'search-suggestions hidden';
    suggestionsDropdown.id = 'search-suggestions';
    searchContainer.appendChild(suggestionsDropdown);

    // Debounced search with intelligent suggestions
    let searchTimeout;
    searchInput.addEventListener('input', (e) => {
      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(() => {
        this.performSmartSearch(e.target.value);
      }, 300);
    });

    // Handle suggestion selection
    suggestionsDropdown.addEventListener('click', (e) => {
      if (e.target.classList.contains('suggestion-item')) {
        this.applySuggestion(e.target.dataset.suggestion);
      }
    });
  }

  async performSmartSearch(query) {
    if (query.length < 2) {
      this.hideSuggestions();
      return;
    }

    // Check cache first
    if (this.searchCache.has(query)) {
      this.showSuggestions(this.searchCache.get(query));
      return;
    }

    try {
      const response = await fetch('/api/inventory/smart-search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': getCSRFToken()
        },
        body: JSON.stringify({
          query: query,
          context: this.getSearchContext()
        })
      });

      const data = await response.json();
      if (data.success) {
        this.searchCache.set(query, data.suggestions);
        this.showSuggestions(data.suggestions);
      }
    } catch (error) {
      console.error('Smart search error:', error);
    }
  }

  showSuggestions(suggestions) {
    const dropdown = document.getElementById('search-suggestions');
    if (!dropdown || !suggestions.length) return;

    dropdown.innerHTML = suggestions.map(suggestion => `
      <div class="suggestion-item" data-suggestion="${suggestion.value}">
        <div class="suggestion-content">
          <span class="suggestion-text">${suggestion.text}</span>
          <span class="suggestion-type">${suggestion.type}</span>
        </div>
        <div class="suggestion-meta">
          <span class="suggestion-count">${suggestion.count} results</span>
        </div>
      </div>
    `).join('');

    dropdown.classList.remove('hidden');
  }

  // Batch Operations with Progress Tracking
  setupBatchOperations() {
    const batchPanel = this.createBatchOperationsPanel();
    document.body.appendChild(batchPanel);

    // Setup batch operation buttons
    this.setupBatchButtons();
  }

  createBatchOperationsPanel() {
    const panel = document.createElement('div');
    panel.className = 'batch-operations-panel hidden';
    panel.id = 'batch-operations-panel';
    panel.innerHTML = `
      <div class="batch-panel-header">
        <h3>Batch Operations</h3>
        <button class="close-batch-panel">×</button>
      </div>
      <div class="batch-panel-content">
        <div class="selected-items-count">
          <span id="batch-count">0</span> items selected
        </div>
        <div class="batch-actions">
          <button class="batch-btn" data-action="export">
            <i class="fas fa-download"></i> Export Selected
          </button>
          <button class="batch-btn" data-action="modify">
            <i class="fas fa-edit"></i> Bulk Modify
          </button>
          <button class="batch-btn" data-action="move">
            <i class="fas fa-arrows-alt"></i> Move Location
          </button>
          <button class="batch-btn" data-action="archive">
            <i class="fas fa-archive"></i> Archive
          </button>
        </div>
        <div class="batch-progress hidden">
          <div class="progress-bar">
            <div class="progress-fill"></div>
          </div>
          <div class="progress-text">Processing...</div>
        </div>
      </div>
    `;

    return panel;
  }

  setupBatchButtons() {
    const panel = document.getElementById('batch-operations-panel');
    
    panel.addEventListener('click', (e) => {
      if (e.target.classList.contains('batch-btn')) {
        const action = e.target.dataset.action;
        this.executeBatchOperation(action);
      }
    });

    // Monitor selection changes
    document.addEventListener('change', (e) => {
      if (e.target.name === 'inventory-item') {
        this.updateBatchPanel();
      }
    });
  }

  updateBatchPanel() {
    const selectedItems = document.querySelectorAll('input[name="inventory-item"]:checked');
    const panel = document.getElementById('batch-operations-panel');
    const countElement = document.getElementById('batch-count');

    if (selectedItems.length > 0) {
      panel.classList.remove('hidden');
      countElement.textContent = selectedItems.length;
    } else {
      panel.classList.add('hidden');
    }
  }

  async executeBatchOperation(action) {
    const selectedItems = Array.from(document.querySelectorAll('input[name="inventory-item"]:checked'))
      .map(cb => cb.value);

    if (selectedItems.length === 0) return;

    this.showBatchProgress();

    try {
      const response = await fetch(`/api/inventory/batch-${action}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': getCSRFToken()
        },
        body: JSON.stringify({
          wafer_ids: selectedItems,
          action: action
        })
      });

      const data = await response.json();
      
      if (data.success) {
        this.showBatchSuccess(action, selectedItems.length);
        this.refreshInventoryTable();
      } else {
        this.showBatchError(data.message);
      }
    } catch (error) {
      this.showBatchError(error.message);
    } finally {
      this.hideBatchProgress();
    }
  }

  // Predictive Analytics for Inventory Optimization
  setupPredictiveAnalytics() {
    this.createPredictivePanel();
    this.loadPredictiveModel();
  }

  createPredictivePanel() {
    const panel = document.createElement('div');
    panel.className = 'predictive-panel';
    panel.id = 'predictive-panel';
    panel.innerHTML = `
      <div class="panel-header">
        <h3><i class="fas fa-brain"></i> Smart Insights</h3>
        <button class="toggle-panel">−</button>
      </div>
      <div class="panel-content">
        <div class="insight-cards">
          <div class="insight-card" id="capacity-prediction">
            <h4>Capacity Forecast</h4>
            <div class="insight-content">Loading...</div>
          </div>
          <div class="insight-card" id="demand-prediction">
            <h4>Demand Prediction</h4>
            <div class="insight-content">Loading...</div>
          </div>
          <div class="insight-card" id="optimization-suggestions">
            <h4>Optimization Tips</h4>
            <div class="insight-content">Loading...</div>
          </div>
        </div>
      </div>
    `;

    // Add to sidebar or main content area
    const sidebar = document.querySelector('.sidebar') || document.querySelector('.main-content');
    if (sidebar) {
      sidebar.appendChild(panel);
    }
  }

  async loadPredictiveModel() {
    try {
      const response = await fetch('/api/analytics/predictions');
      const data = await response.json();
      
      if (data.success) {
        this.updatePredictiveInsights(data.insights);
      }
    } catch (error) {
      console.error('Failed to load predictive analytics:', error);
    }
  }

  updatePredictiveInsights(insights) {
    // Update capacity prediction
    const capacityCard = document.getElementById('capacity-prediction');
    if (capacityCard && insights.capacity) {
      capacityCard.querySelector('.insight-content').innerHTML = `
        <div class="prediction-value">${insights.capacity.predicted_days} days</div>
        <div class="prediction-label">Until capacity limit</div>
        <div class="confidence">Confidence: ${insights.capacity.confidence}%</div>
      `;
    }

    // Update demand prediction
    const demandCard = document.getElementById('demand-prediction');
    if (demandCard && insights.demand) {
      demandCard.querySelector('.insight-content').innerHTML = `
        <div class="prediction-value">${insights.demand.next_week} wafers</div>
        <div class="prediction-label">Expected next week</div>
        <div class="trend ${insights.demand.trend}">${insights.demand.trend_text}</div>
      `;
    }

    // Update optimization suggestions
    const optimizationCard = document.getElementById('optimization-suggestions');
    if (optimizationCard && insights.optimizations) {
      optimizationCard.querySelector('.insight-content').innerHTML = 
        insights.optimizations.map(opt => `
          <div class="optimization-tip">
            <i class="fas fa-lightbulb"></i>
            <span>${opt.suggestion}</span>
          </div>
        `).join('');
    }
  }

  // Workflow Automation
  setupWorkflowAutomation() {
    this.workflowEngine.registerTrigger('inventory_low', this.handleLowInventory.bind(this));
    this.workflowEngine.registerTrigger('shipment_ready', this.handleShipmentReady.bind(this));
    this.workflowEngine.registerTrigger('quality_check_needed', this.handleQualityCheck.bind(this));
  }

  handleLowInventory(data) {
    // Automatically create reorder suggestions
    this.createReorderSuggestion(data.lot_id, data.current_count, data.threshold);
  }

  handleShipmentReady(data) {
    // Automatically generate shipping labels and notifications
    this.autoGenerateShippingLabels(data.shipment_id);
  }

  // Keyboard Shortcuts for Power Users
  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      // Ctrl/Cmd + K for quick search
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        this.focusSmartSearch();
      }

      // Ctrl/Cmd + A for select all visible
      if ((e.ctrlKey || e.metaKey) && e.key === 'a' && e.target.closest('.inventory-table')) {
        e.preventDefault();
        this.selectAllVisible();
      }

      // Escape to clear selections
      if (e.key === 'Escape') {
        this.clearAllSelections();
      }

      // Ctrl/Cmd + E for export
      if ((e.ctrlKey || e.metaKey) && e.key === 'e') {
        e.preventDefault();
        this.quickExport();
      }
    });
  }

  focusSmartSearch() {
    const searchInput = document.getElementById('smart-search') || document.getElementById('wafer-id');
    if (searchInput) {
      searchInput.focus();
      searchInput.select();
    }
  }

  selectAllVisible() {
    const visibleCheckboxes = document.querySelectorAll('#inventory-table-body input[name="inventory-item"]:not([disabled])');
    visibleCheckboxes.forEach(cb => cb.checked = true);
    this.updateBatchPanel();
  }

  clearAllSelections() {
    const checkboxes = document.querySelectorAll('input[name="inventory-item"]:checked');
    checkboxes.forEach(cb => cb.checked = false);
    this.updateBatchPanel();
  }

  // Utility methods
  getSearchContext() {
    return {
      current_page: window.location.pathname,
      user_role: document.body.dataset.userRole,
      recent_searches: this.getRecentSearches()
    };
  }

  getRecentSearches() {
    return JSON.parse(localStorage.getItem('recent_searches') || '[]').slice(0, 5);
  }

  showBatchProgress() {
    const progressElement = document.querySelector('.batch-progress');
    if (progressElement) {
      progressElement.classList.remove('hidden');
    }
  }

  hideBatchProgress() {
    const progressElement = document.querySelector('.batch-progress');
    if (progressElement) {
      progressElement.classList.add('hidden');
    }
  }
}

// Simple Workflow Engine
class WorkflowEngine {
  constructor() {
    this.triggers = new Map();
    this.rules = [];
  }

  registerTrigger(event, handler) {
    if (!this.triggers.has(event)) {
      this.triggers.set(event, []);
    }
    this.triggers.get(event).push(handler);
  }

  trigger(event, data) {
    const handlers = this.triggers.get(event) || [];
    handlers.forEach(handler => {
      try {
        handler(data);
      } catch (error) {
        console.error(`Workflow handler error for ${event}:`, error);
      }
    });
  }
}

// Smart Filter Manager
class SmartFilterManager {
  constructor() {
    this.activeFilters = new Map();
    this.filterHistory = [];
  }

  addFilter(type, value) {
    this.activeFilters.set(type, value);
    this.filterHistory.push({ type, value, timestamp: Date.now() });
  }

  removeFilter(type) {
    this.activeFilters.delete(type);
  }

  getActiveFilters() {
    return Object.fromEntries(this.activeFilters);
  }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  if (window.location.pathname.includes('inventory')) {
    window.advancedInventoryManager = new AdvancedInventoryManager();
  }
});

// Export for global access
window.AdvancedInventoryManager = AdvancedInventoryManager;

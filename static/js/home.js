// home.js is the main script for the dashboard page that fetches data from the server and updates the UI. It initializes the charts, updates the stats, and handles error states. The script also includes functions for animating counters and progress bars, as well as setting up refresh intervals for data updates.

document.addEventListener("DOMContentLoaded", function () {
  // Make sure we don't initialize multiple times
  if (window.chartsInitialized) {
    console.log("Charts already initialized, skipping...");
    return;
  }
  window.chartsInitialized = true;

  // Global variables for charts
  let sectionChart = null;
  let monthlyChart = null;

  // Cache for dashboard data to prevent data loss during failed API calls
  let cachedData = {
    asana_stats: {
      total_shipments: 0,
      on_time_rate: 0,
      active_shipments: 0,
      shipped_wafers: 0,
    },
    // Basic inventory data
    available_lots: 0,
    total_lots: 0,
    capacity_percentage: 0,
    lot_change: 0,
    available_wafers: 0,
    wafer_percentage: 0,
    shipped_wafers: 0,
    quarterly_target: 0,
    quarterly_progress: 0,
    monthly_change: 0,
    // Chart data
    section_data: null,
    monthly_data: null,
  };

  // State management
  let isLoading = false;
  let lastUpdated = null;
  let retryCount = 0;
  const MAX_RETRIES = 3;
  const RETRY_DELAY = 3000; // 3 seconds

  // Function to get current month labels with years (last 12 months, most recent first)
  function getRecentMonths() {
    const months = [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ];
    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();
    const result = [];

    for (let i = 0; i < 12; i++) {
      const monthIndex = (currentMonth - i + 12) % 12;
      const yearOffset = Math.floor((currentMonth - i) / 12);
      const year = currentYear + yearOffset;
      result.push(`${months[monthIndex]} ${year}`);
    }

    return result.reverse();
  }

  // Function to get theme colors for charts
  function getThemeColors() {
    const isDark = document.documentElement.classList.contains("dark");
    return {
      text: isDark ? "#ffffff" : "#111827",
      grid: isDark ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.1)",
      background: isDark ? "#2d2d2d" : "#ffffff",
    };
  }

  // Stats update function with caching and error handling
  function updateStats(forceRefresh = false) {
    if (isLoading && !forceRefresh) {
      console.log("Already loading data, skipping...");
      return;
    }

    // Show loading state
    isLoading = true;

    // Show skeleton loading only if we don't have cached data or force refresh
    if (!cachedData.section_data || forceRefresh) {
      if (
        window.skeletonLoader &&
        typeof window.skeletonLoader.showSkeletons === "function"
      ) {
        window.skeletonLoader.showSkeletons();
      }

      // Update refresh button to show loading state
      const refreshBtn = document.querySelector(".refresh-btn");
      if (refreshBtn) {
        refreshBtn.innerHTML =
          '<svg class="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> Refreshing...';
        refreshBtn.disabled = true;
      }
    }

    console.log("Fetching dashboard stats...");

    fetch("/api/dashboard/stats", {
      method: "GET",
      headers: {
        "Cache-Control": "no-cache",
        Pragma: "no-cache",
      },
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
      })
      .then((response) => {
        // Reset retry count on success
        retryCount = 0;

        const data = response.success
          ? {
              ...response.stats,
            }
          : cachedData;

        // Update our cache with new data
        cachedData = {
          asana_stats: {
            total_shipments:
              data.total_shipments || cachedData.asana_stats.total_shipments,
            on_time_rate:
              data.on_time_rate || cachedData.asana_stats.on_time_rate,
            active_shipments:
              data.active_shipments || cachedData.asana_stats.active_shipments,
            shipped_wafers:
              data.shipped_wafers || cachedData.asana_stats.shipped_wafers,
          },
          // Basic inventory data
          available_lots: data.available_lots || cachedData.available_lots,
          total_lots: data.total_lots || cachedData.total_lots,
          capacity_percentage:
            data.capacity_percentage || cachedData.capacity_percentage,
          lot_change: data.lot_change || cachedData.lot_change,
          available_wafers:
            data.available_wafers || cachedData.available_wafers,
          wafer_percentage:
            data.wafer_percentage || cachedData.wafer_percentage,
          shipped_wafers: data.shipped_wafers || cachedData.shipped_wafers,
          quarterly_target:
            data.quarterly_target || cachedData.quarterly_target,
          quarterly_progress:
            data.quarterly_progress || cachedData.quarterly_progress,
          monthly_change: data.monthly_change || cachedData.monthly_change,
          // Chart data
          section_data: data.section_data || cachedData.section_data,
          monthly_data: data.monthly_data || cachedData.monthly_data,
          last_updated: data.last_updated || new Date().toLocaleString(),
        };

        // Update the UI with new data
        updateStatsDisplay(cachedData);
        updateChartsWithData(cachedData);
        animateProgressBars();

        // Update last updated time
        lastUpdated = new Date();
        const lastUpdatedEl = document.getElementById("last-updated");
        if (lastUpdatedEl) {
          lastUpdatedEl.textContent =
            cachedData.last_updated || lastUpdated.toLocaleString();
        }
      })
      .catch((error) => {
        console.error("Error updating stats:", error);

        // Increment retry count
        retryCount++;

        // If we've cached data and haven't exceeded retry count, use cached data
        if (cachedData.section_data && retryCount <= MAX_RETRIES) {
          console.log(
            `Using cached data (retry ${retryCount}/${MAX_RETRIES})...`
          );

          // Only show error message, keep using cached data
          const errorMessage = document.getElementById("error-message");
          if (errorMessage) {
            errorMessage.textContent = `Unable to refresh data: ${error.message}. Retrying...`;
            errorMessage.classList.remove("hidden");

            // Hide error after 5 seconds
            setTimeout(() => {
              errorMessage.classList.add("hidden");
            }, 5000);
          }

          // Schedule retry
          setTimeout(() => {
            updateStats(true);
          }, RETRY_DELAY * retryCount);
        } else {
          // If no cached data or max retries exceeded, show error UI
          const errorMessage = document.getElementById("error-message");
          if (errorMessage) {
            errorMessage.textContent = `Failed to load dashboard data: ${error.message}. Please try again later.`;
            errorMessage.classList.remove("hidden");
          }
        }
      })
      .finally(() => {
        // Remove skeleton loaders
        if (
          window.skeletonLoader &&
          typeof window.skeletonLoader.removeSkeletons === "function"
        ) {
          window.skeletonLoader.removeSkeletons();
        }

        // Reset refresh button
        const refreshBtn = document.querySelector(".refresh-btn");
        if (refreshBtn) {
          refreshBtn.innerHTML =
            '<svg class="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg> Refresh';
          refreshBtn.disabled = false;
        }

        isLoading = false;
      });
  }

  // Update stats display elements
  function updateStatsDisplay(data) {
    // Update the stats in the welcome section
    const totalShipments = document.querySelector(
      '[data-stat="total-shipments"]'
    );
    const onTimeRate = document.querySelector('[data-stat="on-time-rate"]');
    const activeShipments = document.querySelector(
      '[data-stat="active-shipments"]'
    );

    if (totalShipments) {
      animateCounter(totalShipments, data.asana_stats?.total_shipments || 0);
    }

    if (onTimeRate) {
      animateCounter(onTimeRate, data.asana_stats?.on_time_rate || 0);
    }

    if (activeShipments) {
      animateCounter(activeShipments, data.asana_stats?.active_shipments || 0);
    }

    // Update inventory stats
    const availableLotsEl = document.querySelector(
      '[data-stat="available-lots"]'
    );
    const availableWafersEl = document.querySelector(
      '[data-stat="available-wafers"]'
    );
    const shippedWafersEl = document.querySelector(
      '[data-stat="shipped-wafers"]'
    );

    if (availableLotsEl) {
      animateCounter(availableLotsEl, data.available_lots || 0);
    }

    if (availableWafersEl) {
      animateCounter(availableWafersEl, data.available_wafers || 0);
    }

    if (shippedWafersEl) {
      animateCounter(shippedWafersEl, data.shipped_wafers || 0);
    }

    // Update dynamic elements with data attributes
    updateDynamicElement(".capacity-percentage", data.capacity_percentage);
    updateDynamicElement(".total-lots", data.total_lots);
    updateDynamicElement(".lot-change", data.lot_change, true);
    updateDynamicElement(".wafer-percentage", data.wafer_percentage);
    updateDynamicElement(".quarterly-target", data.quarterly_target);
    updateDynamicElement(".quarterly-progress", data.quarterly_progress);
    updateDynamicElement(".monthly-change", data.monthly_change, true);

    // Update progress bars
    updateProgressBar("capacity", data.capacity_percentage);
    updateProgressBar("wafer", data.wafer_percentage);
    updateProgressBar("quarterly", data.quarterly_progress);
  }

  // Helper function to update dynamic text elements
  function updateDynamicElement(selector, value, isChangeValue = false) {
    const elements = document.querySelectorAll(selector);
    if (elements.length === 0) return;

    elements.forEach((el) => {
      // For percentage change values, we need to handle the display differently
      if (isChangeValue) {
        const icon = el.querySelector("i");
        if (icon) {
          if (value > 0) {
            icon.className = "fas fa-arrow-up";
            el.className = el.className.replace(
              /text-red-500|text-gray-500/g,
              "text-green-500"
            );
          } else if (value < 0) {
            icon.className = "fas fa-arrow-down";
            el.className = el.className.replace(
              /text-green-500|text-gray-500/g,
              "text-red-500"
            );
            value = Math.abs(value); // Use absolute value for display
          } else {
            icon.className = "";
            el.className = el.className.replace(
              /text-green-500|text-red-500/g,
              "text-gray-500"
            );
            el.textContent = "No change";
            return;
          }
        }
        el.textContent = value + "%";
      } else {
        el.textContent = value;
      }
    });
  }

  // Helper function to update progress bars dynamically
  function updateProgressBar(type, percentage) {
    const progressBar = document.querySelector(
      `.progress-bar-fill.gradient-${
        type === "capacity" ? "blue" : type === "wafer" ? "green" : "amber"
      }`
    );
    if (!progressBar) return;

    // Update the class
    progressBar.className = progressBar.className.replace(
      /capacity-\d+/g,
      `capacity-${percentage}`
    );

    // Ensure the progress is capped at 100%
    const displayPercentage = Math.min(percentage, 100);

    // Update the width directly
    progressBar.style.width = `${displayPercentage}%`;
  }

  // Function to update charts with real data
  function updateChartsWithData(data) {
    const colors = getThemeColors();

    // Check if charts exist, if not, try to initialize them
    if (!sectionChart || !monthlyChart) {
      console.log("Charts not initialized yet, attempting to initialize...");
      initializeCharts();
      // Return early to avoid errors, charts will be updated on next cycle
      return;
    }

    if (sectionChart) {
      if (data.section_data && data.section_data.datasets) {
        sectionChart.data.labels = data.section_data.labels;
        sectionChart.data.datasets[0].data = data.section_data.datasets[0].data;
      } else {
        // Fallback data if no section data available
        sectionChart.data.labels = [
          "Incoming",
          "Planned",
          "In preparation",
          "Packaged",
          "Waiting for Review",
          "Sent",
          "Delivered",
        ];
        sectionChart.data.datasets[0].data = [5, 8, 3, 4, 2, 7, 10];
      }

      if (sectionChart.options.plugins && sectionChart.options.plugins.legend) {
        sectionChart.options.plugins.legend.labels.color = colors.text;
      }

      sectionChart.update();
    }

    if (monthlyChart) {
      if (data.monthly_data && data.monthly_data.datasets) {
        monthlyChart.data.labels = data.monthly_data.labels;

        // Update each dataset
        data.monthly_data.datasets.forEach((dataset, index) => {
          if (monthlyChart.data.datasets[index]) {
            monthlyChart.data.datasets[index].data = dataset.data;
            if (dataset.label) {
              monthlyChart.data.datasets[index].label = dataset.label;
            }
          }
        });
      } else {
        // Fallback data if no monthly data available
        const monthLabels = getRecentMonths();
        monthlyChart.data.labels = monthLabels;

        // Mock data for deliveries
        monthlyChart.data.datasets[0].data = [
          3, 6, 4, 10, 13, 10, 8, 4, 10, 19, 15, 11,
        ];

        // Mock data for target
        if (monthlyChart.data.datasets[1]) {
          monthlyChart.data.datasets[1].data = [
            5, 5, 5, 8, 8, 8, 12, 12, 12, 15, 15, 15,
          ];
        }
      }

      if (monthlyChart.options.plugins && monthlyChart.options.plugins.legend) {
        monthlyChart.options.plugins.legend.labels.color = colors.text;
      }

      if (monthlyChart.options.scales) {
        if (monthlyChart.options.scales.x) {
          monthlyChart.options.scales.x.ticks.color = colors.text;
          monthlyChart.options.scales.x.grid.color = colors.grid;
        }

        if (monthlyChart.options.scales.y) {
          monthlyChart.options.scales.y.ticks.color = colors.text;
          monthlyChart.options.scales.y.grid.color = colors.grid;
        }
      }

      monthlyChart.update();
    }
  }

  // Animate counter function
  function animateCounter(element, targetValue) {
    // Don't animate if target is 0 and we already show 0
    if (targetValue === 0 && parseInt(element.textContent) === 0) {
      return;
    }

    const startValue = parseInt(element.textContent) || 0;
    const duration = 1000;
    const startTime = performance.now();

    function updateCounter(currentTime) {
      const elapsedTime = currentTime - startTime;
      if (elapsedTime < duration) {
        const progress = elapsedTime / duration;
        const easedProgress = easeOutQuad(progress);
        const currentValue = Math.floor(
          startValue + (targetValue - startValue) * easedProgress
        );
        element.textContent = currentValue;
        requestAnimationFrame(updateCounter);
      } else {
        element.textContent = targetValue;
      }
    }

    requestAnimationFrame(updateCounter);
  }

  // Easing function for animations
  function easeOutQuad(t) {
    return t * (2 - t);
  }

  // Initialize charts
  function initializeCharts() {
    console.log("Initializing charts...");

    // Ensure Chart.js is available
    if (typeof Chart === "undefined") {
      console.error("Chart.js is not loaded! Charts cannot be initialized.");
      return;
    }

    // Destroy any existing charts first
    const chartIds = ["sectionChart", "monthlyChart"];
    chartIds.forEach((id) => {
      try {
        const chartInstance = Chart.getChart(id);
        if (chartInstance) {
          console.log(`Destroying existing chart with ID: ${id}`);
          chartInstance.destroy();
        }
      } catch (err) {
        console.warn(`Error checking chart ${id}:`, err);
      }
    });

    // Add CSS to ensure chart containers have proper height
    const style = document.createElement("style");
    style.textContent = `
  .chart-height {
    height: 300px !important;
    position: relative !important;
    width: 100% !important;
  }
  .chart-height canvas {
    display: block !important;
    width: 100% !important;
    height: 100% !important;
  }
  
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
  .animate-spin {
    animation: spin 1s linear infinite;
  }
  
  /* Quick action buttons */
  .btn-quick-action {
    display: flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
    background-color: #f3f4f6;
    color: #4b5563;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
  }
  
  .btn-quick-action:hover {
    background-color: #e5e7eb;
    color: #1f2937;
  }
  
  .dark .btn-quick-action {
    background-color: #374151;
    color: #d1d5db;
  }
  
  .dark .btn-quick-action:hover {
    background-color: #4b5563;
    color: #f9fafb;
  }
`;
    document.head.appendChild(style);

    // Section Distribution Chart
    const sectionCtx = document.getElementById("sectionChart");
    if (sectionCtx) {
      // Ensure canvas has proper dimensions
      const container = sectionCtx.parentElement;
      if (container) {
        container.style.height = "300px";
        container.style.width = "100%";
      }

      // Delay chart creation slightly to ensure DOM is ready
      setTimeout(() => {
        sectionChart = new Chart(sectionCtx, {
          type: "doughnut",
          data: {
            labels: [
              "Incoming",
              "Planned",
              "In preparation",
              "Packaged",
              "Waiting for Review",
              "Sent",
              "Delivered",
            ],
            datasets: [
              {
                data: [1, 2, 3, 4, 5, 6, 7], // Initial data so chart renders
                backgroundColor: [
                  "#4F46E5", // Incoming
                  "#8B5CF6", // Planned
                  "#EC4899", // In preparation
                  "#F59E0B", // Packaged
                  "#10B981", // Waiting for Review
                  "#3B82F6", // Sent
                  "#34D399", // Delivered
                ],
                borderWidth: 0,
              },
            ],
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
              duration: 1200,
              easing: "easeOutQuad",
              onComplete: function (animation) {
                // Make sure we have a valid chart instance before proceeding
                if (!this.chart || !this.chart.ctx) {
                  console.warn(
                    "Chart or context not available in animation callback"
                  );
                  return;
                }

                try {
                  const chartInstance = this.chart;
                  const ctx = chartInstance.ctx;
                  const total = chartInstance.data.datasets[0].data.reduce(
                    (a, b) => a + b,
                    0
                  );

                  // Set styles and draw text
                  ctx.textAlign = "center";
                  ctx.textBaseline = "middle";
                  ctx.fillStyle = getThemeColors().text;

                  // Draw the total value
                  ctx.font = "bold 16px 'Arial'";
                  ctx.fillText(
                    total,
                    chartInstance.width / 2,
                    chartInstance.height / 2
                  );

                  // Draw the "Total" label
                  ctx.font = "12px 'Arial'";
                  ctx.fillText(
                    "Total",
                    chartInstance.width / 2,
                    chartInstance.height / 2 + 20
                  );
                } catch (err) {
                  console.warn("Error in chart animation callback:", err);
                }
              },
            },

            plugins: {
              legend: {
                position: "right",
                labels: {
                  boxWidth: 12,
                  padding: 15,
                  font: {
                    size: 12,
                  },
                  color: getThemeColors().text,
                },
              },
              tooltip: {
                callbacks: {
                  label: function (context) {
                    const label = context.label || "";
                    const value = context.raw || 0;
                    return `${label}: ${value} shipments`;
                  },
                },
              },
            },
            cutout: "70%",
          },
        });
      }, 300); // 300ms delay
    } else {
      console.error("Section chart canvas element not found");
    }
    // Monthly Deliveries Chart
    const monthlyCtx = document.getElementById("monthlyChart");
    if (monthlyCtx) {
      // Ensure canvas has proper dimensions
      const container = monthlyCtx.parentElement;
      if (container) {
        container.style.height = "300px";
      }

      monthlyChart = new Chart(monthlyCtx, {
        type: "bar",
        data: {
          labels: getRecentMonths(),
          datasets: [
            {
              label: "Deliveries",
              data: [3, 6, 4, 10, 13, 10, 8, 4, 10, 19, 15, 11], // Initial data
              backgroundColor: "#4F46E5",
              borderRadius: 4,
              order: 2,
            },
            {
              label: "Target",
              type: "line",
              data: [5, 5, 5, 8, 8, 8, 12, 12, 12, 15, 15, 15], // Target line
              borderColor: "#F59E0B",
              borderWidth: 2,
              pointBackgroundColor: "#F59E0B",
              fill: false,
              tension: 0.4,
              order: 1,
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            x: {
              grid: {
                display: false,
                color: getThemeColors().grid,
              },
              ticks: {
                color: getThemeColors().text,
                maxRotation: 45,
                minRotation: 45,
              },
            },
            y: {
              beginAtZero: true,
              grid: {
                color: getThemeColors().grid,
              },
              ticks: {
                color: getThemeColors().text,
              },
            },
          },
          plugins: {
            legend: {
              position: "top",
              labels: {
                boxWidth: 12,
                padding: 15,
                font: {
                  size: 12,
                },
                color: getThemeColors().text,
              },
            },
            tooltip: {
              mode: "index",
              intersect: false,
            },
          },
          barPercentage: 0.6,
        },
      });
    } else {
      console.error("Monthly chart canvas element not found");
    }
    console.log("Charts initialized successfully");
  }

  const refreshBtn = document.getElementById("refresh-chart");
  if (refreshBtn) {
    refreshBtn.addEventListener("click", function () {
      // Add spinning animation
      this.querySelector("i").classList.add("animate-spin");

      // Force refresh after short delay
      setTimeout(() => {
        updateStats(true); // Force refresh

        // Remove spinning animation after update completes
        setTimeout(() => {
          this.querySelector("i").classList.remove("animate-spin");
        }, 500);
      }, 300);
    });
  }

  // Animate progress bars dynamically
  function animateProgressBars() {
    const progressBars = document.querySelectorAll(".progress-bar-fill");
    progressBars.forEach((bar) => {
      // Find the capacity class (if any)
      const widthClasses = Array.from(bar.classList).find((cls) =>
        cls.startsWith("capacity-")
      );

      // Get the percentage value from the class
      let width = widthClasses
        ? widthClasses.replace("capacity-", "") + "%"
        : "0%";

      // Ensure the value is not NaN or undefined
      if (width === "NaN%" || width === "undefined%") {
        width = "0%";
      }

      // Limit to 100% maximum
      const numericWidth = parseInt(width);
      if (numericWidth > 100) {
        width = "100%";
      }

      // Animate the progress bar
      bar.style.width = "0%";
      setTimeout(() => {
        bar.style.transition = "width 1s ease-out";
        bar.style.width = width;
      }, 300);
    });
  }

  // Insert error message container if doesn't exist
  const dashboardContent = document.querySelector(".dashboard-content");
  if (dashboardContent && !document.getElementById("error-message")) {
    const errorContainer = document.createElement("div");
    errorContainer.id = "error-message";
    errorContainer.className =
      "bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative hidden";
    errorContainer.role = "alert";
    dashboardContent.prepend(errorContainer);
  }

  // Initialize and start data flow
  initializeCharts();
  updateStats();

  // Refresh every 60 seconds instead of every minute to avoid timer drift
  let refreshInterval = setInterval(updateStats, 60000);

  // Reset interval if page loses focus and regains it
  document.addEventListener("visibilitychange", function () {
    if (document.visibilityState === "visible") {
      clearInterval(refreshInterval);
      updateStats();
      refreshInterval = setInterval(updateStats, 60000);
    }
  });
});

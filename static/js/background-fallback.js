/**
 * Background Image Fallback Script
 * 
 * This script checks if the background image for the welcome section loaded correctly
 * and applies a fallback if needed.
 */
document.addEventListener('DOMContentLoaded', function() {
    // Function to check if an image exists and is accessible
    function checkImageExists(imageUrl, callback) {
        const img = new Image();
        img.onload = function() {
            callback(true);
        };
        img.onerror = function() {
            callback(false);
        };
        img.src = imageUrl;
    }

    // Get the welcome section element
    const welcomeSection = document.querySelector('.welcome-section');
    
    if (welcomeSection) {
        // Extract the background image URL from the computed style
        const computedStyle = window.getComputedStyle(welcomeSection);
        const backgroundImage = computedStyle.backgroundImage;
        
        // Check if the background image contains the logo
        if (backgroundImage.includes('logoTalaria.jpeg') || backgroundImage.includes('logotalaria.jpeg')) {
            // Extract the URL from the background-image property
            const urlMatch = backgroundImage.match(/url\(['"]?([^'"]+)['"]?\)/);
            
            if (urlMatch && urlMatch[1]) {
                const imageUrl = urlMatch[1];
                
                // Check if the image exists
                checkImageExists(imageUrl, function(exists) {
                    if (!exists) {
                        console.log('Background image not found, applying fallback');
                        // Apply a fallback background
                        welcomeSection.style.backgroundImage = 'linear-gradient(to right, rgba(59, 130, 246, 0.9), rgba(99, 102, 241, 0.9))';
                    }
                });
            }
        }
    }
});

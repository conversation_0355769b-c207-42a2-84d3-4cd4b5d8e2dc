/**
 * IntelParsing.js - Intelligent parsing for Asana content
 *
 * This file provides functionality to:
 * 1. Parse content copied from Asana tasks
 * 2. Extract structured data from unstructured text
 * 3. Populate form fields automatically
 * 4. Save and load templates for common shipment configurations
 */

// Intelligent parsing and form population functionality
const AsanaParser = {
  // Main entry point - parse content and populate form
  parseAndPopulate(content) {
    try {
      console.log("[DEBUG] Parsing Asana content...");
      const parsedData = this.parseAsanaContent(content);
      console.log("[DEBUG] Parsed data:", parsedData);
      this.populateFormFields(parsedData);
      return true;
    } catch (error) {
      console.error("[DEBUG] Error parsing content:", error);
      return false;
    }
  },

  // Parse Asana content text into structured data
  parseAsanaContent(content) {
    const data = {};

    // Extract key-value pairs using different patterns
    this.extractKeyValuePairs(content, data);

    // Extract wafer IDs with special handling
    data.wafer_ids = this.extractWaferIds(content);

    // Try to determine if this is an Erfurt-related shipment
    data.type = this.determineShipmentType(content);

    // Extract Yes/No fields
    const booleanFields = [
      {
        field: "need_reviewing",
        patterns: ["need reviewing", "reviewing needed", "review required"],
      },
      {
        field: "label_free",
        patterns: ["label free", "label-free shipment", "no label"],
      },
      {
        field: "keep_cassette_closed",
        patterns: [
          "keep cassette closed",
          "closed cassette",
          "don't open cassette",
        ],
      },
      { field: "rib", patterns: ["rib:", "rib ?", "with rib"] },
      {
        field: "heater",
        patterns: ["heater:", "heaters:", "heater ?", "with heater"],
      },
      {
        field: "undercut",
        patterns: ["undercut:", "undercut ?", "with undercut"],
      },
    ];

    booleanFields.forEach(({ field, patterns }) => {
      data[field] = this.extractBooleanValue(content, patterns);
    });

    return data;
  },

  // Extract key-value pairs using various patterns
  extractKeyValuePairs(content, data) {
    // This handles different formats that might appear in Asana tasks

    // Format 1: "Field Name: Value"
    const fieldMappings = [
      { field: "title", patterns: ["title:", "shipment title:", "shipment:"] },
      { field: "priority", patterns: ["priority:"] },
      {
        field: "contact_person",
        patterns: ["contact person:", "contact:", "person:"],
      },
      { field: "email", patterns: ["email:", "email (delivery contact):"] },
      {
        field: "address",
        patterns: ["address:", "shipping address:", "ship to:"],
      },
      {
        field: "telephone",
        patterns: ["telephone:", "phone:", "tel:", "telephone number:"],
      },
      {
        field: "destination_label",
        patterns: ["destination:", "destination label:"],
      },
      {
        field: "label_title",
        patterns: ["label title:", "ligentec label title:"],
      },
      {
        field: "number_of_wafers",
        patterns: ["number of wafers:", "wafer count:", "wafers:"],
      },
      {
        field: "parcel_size",
        patterns: ["parcel size:", "package size:", "package:"],
      },

      // Erfurt fields
      { field: "xfab_po", patterns: ["xfab po:", "xfab purchase order:"] },
      { field: "xfab_device_id", patterns: ["xfab device id:"] },
      { field: "project_id", patterns: ["project id:", "project:"] },

      // Eiger fields
      { field: "eiger_number", patterns: ["eiger number:", "eiger#:"] },
      { field: "tapeout", patterns: ["tapeout:", "tapeout (eiger):"] },
      { field: "vendor_lot", patterns: ["vendor lot:"] },
      {
        field: "customer_lot",
        patterns: ["customer lot:", "customer lot (eiger):"],
      },
      {
        field: "tox_target_sin",
        patterns: ["tox target:", "tox target nm:", "tox:"],
      },
      {
        field: "sin_tube_position",
        patterns: ["sin tube position:", "tube position:"],
      },
      { field: "mask", patterns: ["mask:"] },
      {
        field: "wafer_choice",
        patterns: ["wafer choice:", "wafer choice type:"],
      },
    ];

    // Look for each field in the content
    fieldMappings.forEach(({ field, patterns }) => {
      patterns.forEach((pattern) => {
        const regex = new RegExp(`${pattern}\\s*([^\\n]+)`, "i");
        const match = content.match(regex);
        if (match && match[1]) {
          const value = match[1].trim();
          if (value && (!data[field] || value.length > data[field].length)) {
            data[field] = value;
          }
        }
      });
    });

    // Format 2: Look for sections with fields
    const sections = [
      { name: "Shipment Information", prefix: "" },
      { name: "Erfurt Details", prefix: "xfab_" },
      { name: "Eiger Details", prefix: "" },
      { name: "Carrier Information", prefix: "" },
    ];

    sections.forEach((section) => {
      const sectionRegex = new RegExp(
        `${section.name}[:\\-]*\\s*([\\s\\S]*?)(?=\\n\\s*\\n|$)`,
        "i"
      );
      const sectionMatch = content.match(sectionRegex);

      if (sectionMatch && sectionMatch[1]) {
        const sectionContent = sectionMatch[1];

        // Extract key-value pairs from the section
        const keyValueRegex = /([A-Za-z0-9 _]+):\s*([^\n]+)/g;
        let kvMatch;

        while ((kvMatch = keyValueRegex.exec(sectionContent)) !== null) {
          const key = kvMatch[1].trim().toLowerCase().replace(/\s+/g, "_");
          const value = kvMatch[2].trim();

          if (value && value !== "N/A") {
            // Create the field name with the section prefix
            const fieldName = section.prefix + key;
            data[fieldName] = value;
          }
        }
      }
    });

    // Special handling for certain fields
    this.processSpecialFields(data);

    return data;
  },

  // Process fields that need special handling
  processSpecialFields(data) {
    // Handle priority field
    if (data.priority) {
      if (/high/i.test(data.priority)) {
        data.priority = "high";
      } else if (/medium/i.test(data.priority)) {
        data.priority = "medium";
      } else if (/low/i.test(data.priority)) {
        data.priority = "low";
      }
    }

    // Handle wafer choice
    if (data.wafer_choice) {
      if (/not random|specific/i.test(data.wafer_choice)) {
        data.wafer_choice = "not_random";
      } else {
        data.wafer_choice = "random";
      }
    }

    // Handle parcel size
    if (data.parcel_size) {
      if (/40x40x35|5 kg|1 cassette/i.test(data.parcel_size)) {
        data.parcel_size = "40x40x35";
      } else if (/40x80x35|8 kg|2 cassette/i.test(data.parcel_size)) {
        data.parcel_size = "40x20x35";
      }
    }

    // Handle mask type
    if (data.mask) {
      if (/e-?beam/i.test(data.mask)) {
        data.mask = "e-beam";
      } else if (/laser/i.test(data.mask)) {
        data.mask = "laser";
      }
    }
  },

  // Extract wafer IDs from content
  extractWaferIds(content) {
    const waferIds = [];

    // Look for wafer IDs section
    const waferSectionRegex =
      /wafer\s+ids?[:\-]*\s*([^]*?)(?=\n\s*\n|\n[A-Za-z]|\n*$)/i;
    const waferSectionMatch = content.match(waferSectionRegex);

    if (waferSectionMatch && waferSectionMatch[1]) {
      // Extract IDs (typical format is alphanumeric with some special characters)
      const idsText = waferSectionMatch[1].trim();

      // Split by common separators
      const potentialIds = idsText.split(/[\s,;]+/);

      potentialIds.forEach((id) => {
        const cleanId = id.trim();
        if (
          cleanId &&
          cleanId !== "N/A" &&
          /^[A-Za-z0-9\-_]+$/i.test(cleanId)
        ) {
          waferIds.push(cleanId);
        }
      });
    }

    // If we couldn't find the section, try a more general pattern
    if (waferIds.length === 0) {
      const generalWaferPattern = /\b([A-Z0-9]{2,}[A-Z][A-Z0-9]{2,})\b/g;
      let match;

      while ((match = generalWaferPattern.exec(content)) !== null) {
        waferIds.push(match[1]);
      }
    }

    return waferIds;
  },

  // Determine if content contains Erfurt/critical shipment info
  determineShipmentType(content) {
    const erfurtPatterns = [
      /xfab/i,
      /erfurt/i,
      /purchase order/i,
      /device id/i,
    ];

    for (const pattern of erfurtPatterns) {
      if (pattern.test(content)) {
        return "critical";
      }
    }

    return "standard";
  },

  // Extract boolean values (yes/no, true/false)
  extractBooleanValue(content, patterns) {
    for (const pattern of patterns) {
      const regex = new RegExp(`${pattern}\\s*([^\\n]+)`, "i");
      const match = content.match(regex);

      if (match && match[1]) {
        const value = match[1].trim().toLowerCase();
        return /yes|true|1|on/i.test(value);
      }
    }

    // Check for just the presence of the term with positive indicators
    for (const pattern of patterns) {
      const positiveRegex = new RegExp(
        `${pattern}.*?(yes|true|✓|✔|checked)`,
        "i"
      );
      if (positiveRegex.test(content)) {
        return true;
      }
    }

    return false;
  },

  // Populate form fields with extracted data
  populateFormFields(data) {
    console.log("[DEBUG] Populating form with data:", data);

    // Helper function to set field value
    const setFieldValue = (fieldId, value) => {
      if (value === undefined || value === null || value === "") {
        return; // Skip empty values
      }

      const field =
        document.getElementById(fieldId) ||
        document.querySelector(`[name="${fieldId}"]`);

      if (!field) {
        console.warn(`[DEBUG] Field ${fieldId} not found`);
        return;
      }

      if (field.type === "checkbox") {
        field.checked = value === true || value === "true";
      } else if (field.type === "select-one") {
        // Find matching option (case-insensitive)
        const option = Array.from(field.options).find(
          (opt) => opt.value.toLowerCase() === String(value).toLowerCase()
        );

        if (option) {
          field.value = option.value;
        } else {
          console.warn(`[DEBUG] No matching option for ${fieldId}: ${value}`);
        }
      } else {
        field.value = value;
      }

      // Highlight the field to show it was updated
      field.classList.add("bg-yellow-50");
      setTimeout(() => {
        field.classList.remove("bg-yellow-50");
      }, 2000);
    };

    // Map all fields
    Object.entries(data).forEach(([field, value]) => {
      setFieldValue(field, value);
    });

    // Special handling for wafer IDs array
    if (data.wafer_ids && data.wafer_ids.length > 0) {
      setFieldValue("wafer_ids", data.wafer_ids.join(", "));

      // Update wafer count if it's not already set
      if (!data.number_of_wafers) {
        setFieldValue("number_of_wafers", data.wafer_ids.length);
      }
    }

    // Trigger any form change events
    document.dispatchEvent(
      new CustomEvent("formDataPopulated", { detail: data })
    );
  },
};

// Template management functionality
const ShipmentTemplates = {
  // Save current form as template
  saveTemplate() {
    try {
      const templateName = prompt("Enter a name for this template:");
      if (!templateName) return;

      const form = document.getElementById("shipment-form");
      const formData = new FormData(form);

      // Convert FormData to object
      const templateData = {};
      formData.forEach((value, key) => {
        if (key === "wafer_ids") {
          // Don't save specific wafer IDs in templates
          return;
        }

        // Handle checkboxes
        if (value === "on") {
          templateData[key] = true;
        } else {
          templateData[key] = value;
        }
      });

      // Get existing templates
      const existingTemplates = this.getTemplates();
      existingTemplates[templateName] = templateData;

      // Save to localStorage
      localStorage.setItem(
        "shipmentTemplates",
        JSON.stringify(existingTemplates)
      );

      this.updateTemplateDropdown();
      return true;
    } catch (error) {
      console.error("[DEBUG] Error saving template:", error);
      return false;
    }
  },

  // Load template into form
  loadTemplate(templateName) {
    try {
      const templates = this.getTemplates();
      const templateData = templates[templateName];

      if (!templateData) {
        console.error("[DEBUG] Template not found:", templateName);
        return false;
      }

      // Populate form with template data
      AsanaParser.populateFormFields(templateData);
      return true;
    } catch (error) {
      console.error("[DEBUG] Error loading template:", error);
      return false;
    }
  },

  // Delete a template
  deleteTemplate(templateName) {
    try {
      const templates = this.getTemplates();
      if (!templates[templateName]) return false;

      delete templates[templateName];
      localStorage.setItem("shipmentTemplates", JSON.stringify(templates));

      this.updateTemplateDropdown();
      return true;
    } catch (error) {
      console.error("[DEBUG] Error deleting template:", error);
      return false;
    }
  },

  // Get all saved templates
  getTemplates() {
    try {
      const templatesJson = localStorage.getItem("shipmentTemplates");
      return templatesJson ? JSON.parse(templatesJson) : {};
    } catch (error) {
      console.error("[DEBUG] Error getting templates:", error);
      return {};
    }
  },

  // Update the template dropdown in the UI
  updateTemplateDropdown() {
    const templateSelect = document.getElementById("template-select");
    if (!templateSelect) return;

    const templates = this.getTemplates();

    // Clear current options
    templateSelect.innerHTML = '<option value="">Select Template</option>';

    // Add options for each template
    Object.keys(templates).forEach((name) => {
      const option = document.createElement("option");
      option.value = name;
      option.textContent = name;
      templateSelect.appendChild(option);
    });
  },

  // Initialize template functionality
  init() {
    // Make sure we have the template UI elements
    this.ensureTemplateUI();
    this.updateTemplateDropdown();

    // Add event listeners
    const templateSelect = document.getElementById("template-select");
    if (templateSelect) {
      templateSelect.addEventListener("change", (e) => {
        if (e.target.value) {
          this.loadTemplate(e.target.value);
        }
      });
    }

    const saveTemplateBtn = document.getElementById("save-template-btn");
    if (saveTemplateBtn) {
      saveTemplateBtn.addEventListener("click", () => this.saveTemplate());
    }

    const deleteTemplateBtn = document.getElementById("delete-template-btn");
    if (deleteTemplateBtn) {
      deleteTemplateBtn.addEventListener("click", () => {
        const templateSelect = document.getElementById("template-select");
        if (templateSelect.value) {
          if (
            confirm(
              `Are you sure you want to delete the template "${templateSelect.value}"?`
            )
          ) {
            this.deleteTemplate(templateSelect.value);
          }
        }
      });
    }
  },

  // Ensure the template UI elements exist
  ensureTemplateUI() {
    // Check if template controls already exist
    if (document.getElementById("template-controls")) return;

    // Create template controls
    const templateControls = document.createElement("div");
    templateControls.id = "template-controls";
    templateControls.className =
      "bg-white dark:bg-gray-800 rounded-lg shadow p-4 mb-6";
    templateControls.innerHTML = `
            <h2 class="text-lg font-semibold mb-4">Templates</h2>
            <div class="flex flex-wrap items-end gap-4">
                <div class="flex-grow">
                    <label class="block text-sm font-medium mb-2">Saved Templates</label>
                    <select id="template-select" class="w-full border rounded-md p-2">
                        <option value="">Select Template</option>
                    </select>
                </div>
                <div class="flex gap-2">
                    <button id="save-template-btn" type="button" 
                            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                        <i class="fas fa-save"></i> Save
                    </button>
                    <button id="delete-template-btn" type="button" 
                            class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            </div>
        `;

    // Add to the page
    const shipmentForm = document.getElementById("shipment-form");
    if (shipmentForm && shipmentForm.parentNode) {
      shipmentForm.parentNode.insertBefore(templateControls, shipmentForm);
    }
  },
};

// Asana content paste functionality
const AsanaPaster = {
  // Initialize paste area
  init() {
    this.ensurePasteArea();

    // Add event listeners to the paste area
    const pasteArea = document.getElementById("asana-paste-area");
    const parseButton = document.getElementById("parse-asana-button");
    const clearButton = document.getElementById("clear-paste-btn");

    if (pasteArea) {
      // Add paste shortcut (Ctrl+V / Cmd+V in the textarea)
      pasteArea.addEventListener("paste", () => {
        // Give time for the paste to complete
        setTimeout(() => {
          if (pasteArea.value.trim().length > 0) {
            // Enable the parse button
            if (parseButton) parseButton.disabled = false;
          }
        }, 100);
      });

      // Add input handler to enable/disable parse button
      pasteArea.addEventListener("input", () => {
        if (parseButton) {
          parseButton.disabled = pasteArea.value.trim().length === 0;
        }
      });
    }

    if (parseButton) {
      parseButton.addEventListener("click", () => {
        if (!pasteArea || !pasteArea.value) {
          Swal.fire({
            icon: "error",
            title: "No Content",
            text: "Please paste Asana task content first",
          });
          return;
        }

        const content = pasteArea.value;
        const success = AsanaParser.parseAndPopulate(content);

        if (success) {
          // Show success message
          Swal.fire({
            icon: "success",
            title: "Success!",
            text: "Form populated from Asana content",
            timer: 2000,
          });
        } else {
          Swal.fire({
            icon: "warning",
            title: "Limited Data Found",
            text: "Some fields could not be found in the pasted content. Please check and fill in any missing information.",
          });
        }
      });
    }

    if (clearButton) {
      clearButton.addEventListener("click", () => {
        if (pasteArea) {
          pasteArea.value = "";
          if (parseButton) parseButton.disabled = true;
        }
      });
    }
  },

  // Ensure paste area exists in the DOM
  ensurePasteArea() {
    // Check if paste area already exists
    if (document.getElementById("asana-paste-area")) return;

    // Create paste area
    const pasteArea = document.createElement("div");
    pasteArea.id = "asana-paste-section";
    pasteArea.className =
      "bg-white dark:bg-gray-800 rounded-lg shadow p-4 mb-6";
    pasteArea.innerHTML = `
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold">Paste Asana Content</h2>
                <div class="flex items-center text-sm text-gray-500">
                    <i class="fas fa-info-circle mr-2"></i>
                    <span>Copy content from Asana and paste it below</span>
                </div>
            </div>
            <div class="space-y-4">
                <textarea id="asana-paste-area" rows="5" 
                          class="w-full border rounded-md p-2 font-mono text-sm resize-vertical"
                          placeholder="Paste content from Asana task here..."></textarea>
                <div class="flex justify-end gap-2">
                    <button id="clear-paste-btn" type="button"
                            class="px-3 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                        <i class="fas fa-times mr-1"></i> Clear
                    </button>
                    <button id="parse-asana-button" type="button" 
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700" disabled>
                        <i class="fas fa-magic mr-1"></i> Parse Content
                    </button>
                </div>
            </div>
        `;

    // Add to the page before the form
    const shipmentForm = document.getElementById("shipment-form");
    if (shipmentForm && shipmentForm.parentNode) {
      shipmentForm.parentNode.insertBefore(pasteArea, shipmentForm);
    }
  },
};

// Initialize everything when document is ready
document.addEventListener("DOMContentLoaded", function () {
  // Initialize template management
  ShipmentTemplates.init();

  // Initialize paste functionality
  AsanaPaster.init();

  // Add functionality to make the existing "Upload" button toggle the paste area
  const uploadBtn = document.querySelector("button.bg-blue-600");
  if (uploadBtn) {
    uploadBtn.addEventListener("click", function (e) {
      e.preventDefault();

      // Toggle the paste area visibility
      const pasteSection = document.getElementById("asana-paste-section");
      if (pasteSection) {
        pasteSection.classList.toggle("hidden");

        // Scroll to the paste area if we're showing it
        if (!pasteSection.classList.contains("hidden")) {
          pasteSection.scrollIntoView({ behavior: "smooth" });

          // Focus the textarea
          const pasteArea = document.getElementById("asana-paste-area");
          if (pasteArea) {
            pasteArea.focus();
          }
        }
      }
    });
  }

  // Add keyboard shortcut to toggle the paste area (Alt+P)
  document.addEventListener("keydown", function (e) {
    if (e.altKey && e.key === "p") {
      e.preventDefault();
      const pasteSection = document.getElementById("asana-paste-section");
      if (pasteSection) {
        pasteSection.classList.toggle("hidden");
        if (!pasteSection.classList.contains("hidden")) {
          document.getElementById("asana-paste-area")?.focus();
        }
      }
    }
  });
});

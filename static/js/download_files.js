// download_files.js

// Utility function to get CSRF token
function getCsrfToken() {
  const token = document.querySelector('meta[name="csrf-token"]')?.content;
  if (!token) throw new Error("CSRF token not found");
  return token;
}

// Utility function for making API calls
async function apiCall(url, method, data = null) {
  const options = {
    method: method,
    headers: {
      "Content-Type": "application/json",
      "X-CSRFToken": getCsrfToken(),
      Accept: "application/json",
    },
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  const response = await fetch(url, options);
  if (!response.ok) {
    throw new Error(`API call failed: ${response.statusText}`);
  }
  return response.json();
}

// Print Label handler
async function handlePrint(printerIp, copies) {
  try {
    const result = await Swal.fire({
      title: "Confirm Print",
      html: `
                    <div class="text-left">
                        <p>Printer IP: ${printerIp}</p>
                        <p>Copies: ${copies}</p>
                    </div>
                `,
      icon: "question",
      showCancelButton: true,
      confirmButtonText: "Print",
      cancelButtonText: "Cancel",
    });

    if (!result.isConfirmed) return;

    const response = await apiCall("/print_to_honeywell", "POST", {
      printer_ip: printerIp,
      copy_number: parseInt(copies),
    });

    if (response.success) {
      Swal.fire({
        icon: "success",
        title: "Success",
        text: `Printed ${copies} copies successfully`,
      });
    } else {
      throw new Error(response.message || "Print failed");
    }
  } catch (error) {
    console.error("Print error:", error);
    Swal.fire({
      icon: "error",
      title: "Print Failed",
      text: error.message || "Failed to print label",
    });
  }
}

// Initialize event listeners
document.addEventListener("DOMContentLoaded", function () {
  // Print button handler
  const printButton = document.getElementById("printButton");
  if (printButton) {
    printButton.addEventListener("click", async () => {
      const printerIp = document.getElementById("printer-ip").value;
      const copies = document.getElementById("copies").value;
      await handlePrint(printerIp, copies);
    });
  }

  // Test printer connection handler
  const testConnectionButton = document.getElementById("testConnectionButton");
  if (testConnectionButton) {
    testConnectionButton.addEventListener("click", async () => {
      const printerIp = document.getElementById("printer-ip").value;
      try {
        const response = await apiCall("/test_printer_connection", "POST", {
          printer_ip: printerIp,
        });

        Swal.fire({
          icon: response.success ? "success" : "error",
          title: response.success ? "Connected" : "Connection Failed",
          text: response.message,
        });
      } catch (error) {
        console.error("Connection test error:", error);
        Swal.fire({
          icon: "error",
          title: "Connection Failed",
          text: error.message || "Failed to test printer connection",
        });
      }
    });
  }

  // Address selection handler - only initialize if packing slip section exists
  const addressButtons = document.querySelectorAll(".address-btn");
  const downloadPackingSlipBtn = document.getElementById(
    "download-packing-slip"
  );
  let selectedOrigin = "france"; // Default to France

  // Only set up address buttons if they exist (for non-Substrate-wafer labels)
  if (addressButtons.length > 0 && downloadPackingSlipBtn) {
    addressButtons.forEach((button) => {
      button.addEventListener("click", function () {
        // Remove active class from all buttons
        addressButtons.forEach((btn) => {
          btn.classList.remove("active");
          btn.classList.remove(
            "bg-indigo-600",
            "text-white",
            "hover:bg-indigo-700"
          );
          btn.classList.add(
            "bg-white",
            "text-gray-700",
            "hover:bg-gray-50",
            "border-gray-300"
          );
        });

        // Add active class to clicked button
        this.classList.add("active");
        this.classList.remove(
          "bg-white",
          "text-gray-700",
          "hover:bg-gray-50",
          "border-gray-300"
        );
        this.classList.add(
          "bg-indigo-600",
          "text-white",
          "hover:bg-indigo-700"
        );

        // Set the selected origin
        if (this.id === "france-address") {
          selectedOrigin = "france";
        } else {
          selectedOrigin = "switzerland";
        }

        // Update download link with query parameter
        if (downloadPackingSlipBtn) {
          // Get the base URL for the download_packing_slip route
          const baseUrl = "/download_packing_slip";
          downloadPackingSlipBtn.href = `${baseUrl}?origin=${selectedOrigin}`;
        }
      });
    });
  }

  // Send Email handler
  const sendEmailBtn = document.getElementById("sendEmailBtn");
  if (sendEmailBtn) {
    sendEmailBtn.addEventListener("click", async () => {
      try {
        // Get task GID from sessionStorage if available
        let taskGid = sessionStorage.getItem("current_task_gid");
        let asanaLink = taskGid;

        // Show input dialog for Asana link if not in session
        if (!asanaLink) {
          const { value: inputLink, isConfirmed } = await Swal.fire({
            title: "Send Email Notification",
            html: `
              <div class="text-left">
                <label class="block text-sm font-medium text-gray-700 mb-2">Asana Task Link</label>
                <input id="swal-input-asana-email" class="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500" placeholder="Paste your Asana task link here">
              </div>
            `,
            showCancelButton: true,
            confirmButtonText: "Send Email",
            confirmButtonColor: "#3b82f6",
            cancelButtonColor: "#6b7280",
            preConfirm: () => {
              const input = document.getElementById("swal-input-asana-email");
              if (!input.value.trim()) {
                Swal.showValidationMessage("Please enter an Asana task link");
                return false;
              }
              return input.value;
            },
          });

          if (!isConfirmed || !inputLink) return;
          asanaLink = inputLink;

          // Extract the task GID from various Asana URL formats
          // Check if asanaLink is just a number (direct GID input)
          if (/^\d+$/.test(asanaLink.trim())) {
            taskGid = asanaLink.trim();
          } else {
            // Try all possible Asana URL patterns
            const taskGidMatch =
              asanaLink.match(/asana\.com\/\d+\/\d+\/(\d+)(?:\/[a-z])?/) ||
              asanaLink.match(
                /app\.asana\.com\/\d+\/\d+\/\d+\/(\d+)(?:\/[a-z])?/
              ) ||
              asanaLink.match(/\/task\/(\d+)/) ||
              asanaLink.match(/\/(\d+)\?/) ||
              asanaLink.match(/\/(\d+)$/) ||
              asanaLink.match(/\/project\/\d+\/task\/(\d+)/) ||
              asanaLink.match(/app\.asana\.com\/\d+\/\d+\/\d+\/\d+\/(\d+)/);
            taskGid = taskGidMatch ? taskGidMatch[1] : null;
          }

          if (!taskGid) {
            throw new Error(
              "Invalid Asana task link. Please provide a valid link."
            );
          }
        }

        // Check if this is an Eiger project by making a preliminary call
        const checkResponse = await apiCall(
          `/api/get_task_info?task_gid=${taskGid}`,
          "GET"
        );

        if (!checkResponse.success) {
          throw new Error(
            checkResponse.message || "Failed to get task information"
          );
        }

        // Check if it's an Eiger project by looking at Lot project field
        const lotProject = checkResponse.task_info["Lot project"] || "";
        const isEigerProject = lotProject.toLowerCase().includes("eiger");

        // If not an Eiger project, show a confirmation dialog first
        if (!isEigerProject) {
          const { isConfirmed } = await Swal.fire({
            icon: "warning",
            title: "Non-Eiger Shipment",
            html: `
              <div class="text-left">
                <p class="font-semibold mb-2">This does not appear to be an Eiger shipment.</p>
                <p class="mb-4">The Lot project field does not contain 'Eiger'.</p>
                <p>Do you want to send the email notification anyway?</p>
              </div>
            `,
            showCancelButton: true,
            confirmButtonText: "Yes, Send Anyway",
            cancelButtonText: "Cancel",
            confirmButtonColor: "#f59e0b",
            cancelButtonColor: "#6b7280",
          });

          if (!isConfirmed) {
            return; // User canceled the operation
          }
        }

        Swal.fire({
          title: "Sending notification...",
          text: "Please wait...",
          allowOutsideClick: false,
          didOpen: () => {
            Swal.showLoading();
          },
        });

        const response = await apiCall("/api/send_notification", "POST", {
          task_gid: taskGid,
        });

        if (response.success) {
          // Check if it's not an Eiger project but sent anyway
          if (response.warning && !response.is_eiger) {
            await Swal.fire({
              icon: "warning",
              title: "Non-Eiger Shipment",
              html: `
                <div class="text-left">
                  <p class="mb-4">Warning: ${response.warning}</p>
                  <p>The email notification was sent successfully, but please verify this was intentional.</p>
                </div>
              `,
              confirmButtonColor: "#f59e0b",
            });
          } else {
            Swal.fire({
              icon: "success",
              title: "Success",
              text: "Email notification sent successfully!",
            });
          }
        } else if (
          response.message &&
          response.message.includes("tracking number")
        ) {
          await Swal.fire({
            icon: "warning",
            title: "Tracking Number Required",
            text: "Please ensure the UPS Tracking Number is filled in Asana before sending the notification.",
            confirmButtonColor: "#f59e0b",
          });
        } else {
          throw new Error(response.message || "Failed to send notification");
        }
      } catch (error) {
        console.error("Email error:", error);
        Swal.fire({
          icon: "error",
          title: "Error",
          text: error.message || "Failed to send email notification",
        });
      }
    });
  }

  // Test Email handler
  const testEmailBtn = document.getElementById("testEmailBtn");
  if (testEmailBtn) {
    testEmailBtn.addEventListener("click", async () => {
      try {
        // Get task GID from session or prompt for Asana task link to test Eiger verification
        const { value: asanaLink, isConfirmed } = await Swal.fire({
          title: "Send Test Email",
          html: `
            <div class="text-left">
              <label class="block text-sm font-medium text-gray-700 mb-2">Asana Task Link (Optional)</label>
              <input id="swal-input-asana-test" class="w-full p-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500" placeholder="Paste your Asana task link here to test Eiger verification">
              <p class="mt-2 text-xs text-gray-500">If provided, the system will verify if this is an Eiger shipment before sending the test email.</p>
            </div>
          `,
          showCancelButton: true,
          confirmButtonText: "Send Test Email",
          confirmButtonColor: "#3b82f6",
          cancelButtonColor: "#6b7280",
          customClass: {
            popup: "rounded-lg",
          },
        });

        if (!isConfirmed) return;

        // Extract task GID if Asana link was provided
        let taskGid = null;
        if (asanaLink && typeof asanaLink === "string") {
          // Try all possible Asana URL patterns
          const taskGidMatch =
            asanaLink.match(/asana\.com\/\d+\/\d+\/(\d+)(?:\/[a-z])?/) ||
            asanaLink.match(
              /app\.asana\.com\/\d+\/\d+\/\d+\/(\d+)(?:\/[a-z])?/
            ) ||
            asanaLink.match(/\/task\/(\d+)/) ||
            asanaLink.match(/\/(\d+)\?/) ||
            asanaLink.match(/\/(\d+)$/) ||
            asanaLink.match(/\/project\/\d+\/task\/(\d+)/) ||
            asanaLink.match(/app\.asana\.com\/\d+\/\d+\/\d+\/\d+\/(\d+)/);
          taskGid = taskGidMatch ? taskGidMatch[1] : null;

          // Check if asanaLink is just a number (direct GID input)
          if (!taskGid && /^\d+$/.test(asanaLink.trim())) {
            taskGid = asanaLink.trim();
          }
        }

        Swal.fire({
          title: "Sending Test Email",
          html: `
            <div class="flex flex-col items-center">
              <div class="test-email-spinner mb-4"></div>
              <div class="text-sm text-gray-500">Sending test email to verify email functionality...</div>
            </div>
          `,
          showConfirmButton: false,
          allowOutsideClick: false,
          allowEscapeKey: false,
          didOpen: () => {
            const style = document.createElement("style");
            style.textContent = `
              .test-email-spinner {
                width: 48px;
                height: 48px;
                border: 5px solid #dbeafe;
                border-top: 5px solid #3b82f6;
                border-radius: 50%;
                animation: spin 1s linear infinite;
              }
              @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
              }
            `;
            Swal.getPopup().appendChild(style);
          },
        });

        // Add a small delay to show the loading animation
        await new Promise((resolve) => setTimeout(resolve, 800));

        // Check if it's an Eiger project
        if (taskGid) {
          const checkResponse = await apiCall(
            `/api/get_task_info?task_gid=${taskGid}`,
            "GET"
          );

          if (checkResponse.success) {
            // Check if it's an Eiger project by looking at Lot project field
            const lotProject = checkResponse.task_info["Lot project"] || "";
            const isEigerProject = lotProject.toLowerCase().includes("eiger");

            // If not an Eiger project, show a confirmation dialog first
            if (!isEigerProject) {
              const { isConfirmed: proceedAnyway } = await Swal.fire({
                icon: "warning",
                title: "Non-Eiger Shipment",
                html: `
                  <div class="text-left">
                    <p class="font-semibold mb-2">This does not appear to be an Eiger shipment.</p>
                    <p class="mb-4">The Lot project field does not contain 'Eiger'.</p>
                    <p>Do you want to send the test email anyway?</p>
                    <p class="mt-4 text-xs text-gray-500">Note: Test emails are only sent to admin recipients.</p>
                  </div>
                `,
                showCancelButton: true,
                confirmButtonText: "Yes, Send Anyway",
                cancelButtonText: "Cancel",
                confirmButtonColor: "#f59e0b",
                cancelButtonColor: "#6b7280",
              });

              if (!proceedAnyway) {
                return; // User canceled the operation
              }
            }
          }
        }

        // Use apiCall with data parameter to include the task_gid
        const response = await apiCall("/api/test_email", "POST", {
          task_gid: taskGid,
        });

        if (response.warning && !response.is_eiger) {
          await Swal.fire({
            icon: "warning",
            title: "Non-Eiger Test Email Sent",
            html: `
              <div class="text-left">
                <p class="mb-4">Warning: ${response.warning}</p>
                <p>The test email was sent successfully to admin recipients only.</p>
                <p class="mt-4 text-xs text-gray-500">Remember: This was just a test and only went to admin emails.</p>
              </div>
            `,
            confirmButtonColor: "#f59e0b",
          });
        } else {
          Swal.fire({
            icon: "success",
            title: "Success",
            text: "Test email sent successfully!",
          });
        }
      } catch (error) {
        console.error("Test email error:", error);
        Swal.fire({
          icon: "error",
          title: "Error",
          text: error.message || "Failed to send test email",
        });
      }
    });
  }
});

// Generate Eiger CSV script handler
document.addEventListener("DOMContentLoaded", function () {
  const eigerCsvBtn = document.getElementById("eigerCsvBtn");

  async function generateEigerCsv() {
    try {
      // Get task GID from sessionStorage if available
      let asanaLink = sessionStorage.getItem("current_task_gid");

      // Show input dialog for Asana link if not in session
      if (!asanaLink) {
        const { value: inputLink } = await Swal.fire({
          title: "Enter Asana Task Link",
          input: "text",
          inputLabel: "Asana Link",
          inputPlaceholder: "Paste your Asana task link here",
          showCancelButton: true,
          inputValidator: (value) => {
            if (!value) {
              return "Please enter an Asana task link";
            }
          },
        });

        if (!inputLink) return;
        asanaLink = inputLink;
      }

      // If it's just a GID, convert to full URL
      if (asanaLink && !asanaLink.includes("https://")) {
        asanaLink = `https://app.asana.com/0/0/${asanaLink}`;
      }

      // Show loading state
      Swal.fire({
        title: "Generating CSV...",
        text: "Please wait...",
        allowOutsideClick: false,
        didOpen: () => {
          Swal.showLoading();
        },
      });

      const response = await fetch("/generate_eiger_csv", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRFToken": document.querySelector('meta[name="csrf-token"]')
            .content,
        },
        body: JSON.stringify({
          asana_link: asanaLink,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to generate CSV");
      }

      // Create blob and download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;

      // Get the filename from the Content-Disposition header if available
      let serverFilename = "";

      // Extract the filename from the Content-Disposition header
      const contentDisposition = response.headers.get("Content-Disposition");
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
        if (filenameMatch && filenameMatch[1]) {
          serverFilename = filenameMatch[1];
          console.log("Using server-provided filename:", serverFilename);
        }
      }

      // If we got a server filename, use it
      if (serverFilename) {
        a.download = serverFilename;
      } else {
        // Fall back to client-side filename generation
        console.log("No server filename found, generating locally");

        // Get the task GID from the session or input field
        let taskGid =
          document.querySelector('[name="task_gid"]')?.value ||
          sessionStorage.getItem("current_task_gid");

        // If we have an Asana link in the input field, extract the task GID
        if (asanaLink && !taskGid) {
          // Check if asanaLink is just a number (direct GID input)
          if (/^\d+$/.test(asanaLink.trim())) {
            taskGid = asanaLink.trim();
          } else {
            // Try all possible Asana URL patterns
            const taskGidMatch =
              asanaLink.match(/asana\.com\/\d+\/\d+\/(\d+)(?:\/[a-z])?/) ||
              asanaLink.match(
                /app\.asana\.com\/\d+\/\d+\/\d+\/(\d+)(?:\/[a-z])?/
              ) ||
              asanaLink.match(/\/task\/(\d+)/) ||
              asanaLink.match(/\/(\d+)\?/) ||
              asanaLink.match(/\/(\d+)$/) ||
              asanaLink.match(/\/project\/\d+\/task\/(\d+)/) ||
              asanaLink.match(/app\.asana\.com\/\d+\/\d+\/\d+\/\d+\/(\d+)/);
            taskGid = taskGidMatch ? taskGidMatch[1] : null;
          }
        }

        // Create a timestamp with date and time (YYYY-MM-DD_HH-MM-SS)
        const now = new Date();
        const dateStr = now.toISOString().slice(0, 10);
        const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, "-");
        const timestamp = `${dateStr}_${timeStr}`;

        // Default filename component
        let taskName = "";

        // If we have a task GID, try to get the task name
        if (taskGid) {
          try {
            // Fetch task info to get the task name
            const taskInfoResponse = await fetch(
              `/api/get_task_info?task_gid=${taskGid}`
            );

            if (taskInfoResponse.ok) {
              const taskInfoData = await taskInfoResponse.json();
              if (
                taskInfoData.success &&
                taskInfoData.task_info &&
                taskInfoData.task_info.name
              ) {
                // Clean the task name for use in a filename (remove special characters)
                taskName = taskInfoData.task_info.name
                  .replace(/[^a-zA-Z0-9]/g, "_") // Replace special chars with underscore
                  .replace(/_+/g, "_") // Replace multiple underscores with a single one
                  .substring(0, 50); // Limit length to avoid very long filenames
              }
            }
          } catch (error) {
            console.error("Error fetching task name:", error);
            // Continue with default name if there's an error
          }
        }

        // Always prepend "Eiger_" to the filename and include date and hour separately
        // Split timestamp into date and hour parts
        const timestampParts = timestamp.split("_");
        const date = timestampParts[0];
        const hour = timestampParts[1];

        a.download = taskName
          ? `Eiger_${taskName}_${date}_${hour}.csv`
          : `Eiger_${date}_${hour}.csv`;
      }

      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      await Swal.fire({
        icon: "success",
        title: "Success",
        text: "CSV file generated successfully!",
      });
    } catch (error) {
      console.error("CSV generation error:", error);
      await Swal.fire({
        icon: "error",
        title: "Error",
        text: error.message || "Failed to generate CSV file",
      });
    }
  }

  if (eigerCsvBtn) {
    eigerCsvBtn.addEventListener("click", generateEigerCsv);
  }
});

/**
 * Breadcrumb Navigation System
 * Manages dynamic breadcrumb generation and navigation
 */

class BreadcrumbManager {
    constructor() {
        this.breadcrumbContainer = document.querySelector('.breadcrumb-nav ol');
        this.currentPath = [];
        this.init();
    }

    init() {
        this.updateBreadcrumbFromURL();
        this.setupNavigationListeners();
    }

    /**
     * Update breadcrumb based on current URL
     */
    updateBreadcrumbFromURL() {
        const path = window.location.pathname;
        const searchParams = new URLSearchParams(window.location.search);
        
        // Clear existing breadcrumb items (except home)
        this.clearBreadcrumb();
        
        // Generate breadcrumb based on current route
        if (path.includes('/inventory')) {
            this.addBreadcrumbItem('Inventory Management', '/inventory_management', 'fas fa-boxes');
        } else if (path.includes('/label-packing-slip') || path.includes('/select_lot')) {
            this.addBreadcrumbItem('Generate Labels', '/label-packing-slip', 'fas fa-tag');
            
            // Add specific lot if available
            const lot = searchParams.get('lot') || sessionStorage.getItem('current_lot');
            if (lot) {
                this.addBreadcrumbItem(`Lot: ${lot}`, null, 'fas fa-cube');
            }
        } else if (path.includes('/wafer_details')) {
            this.addBreadcrumbItem('Generate Labels', '/label-packing-slip', 'fas fa-tag');
            const lot = searchParams.get('lot') || sessionStorage.getItem('current_lot');
            if (lot) {
                this.addBreadcrumbItem(`Wafer Details - ${lot}`, null, 'fas fa-microchip');
            }
        } else if (path.includes('/shipment')) {
            this.addBreadcrumbItem('Shipments Task', '/shipment/dashboard', 'fas fa-truck');
        } else if (path.includes('/rfq')) {
            this.addBreadcrumbItem('RFQ Automation', '/rfq/automation', 'fas fa-envelope-open-text');
        } else if (path.includes('/label-generator')) {
            this.addBreadcrumbItem('Manual Label Generator', '/label-generator/', 'fas fa-tags');
        } else if (path.includes('/support')) {
            this.addBreadcrumbItem('Support', '/support', 'fas fa-headset');
        } else if (path.includes('/chat')) {
            this.addBreadcrumbItem('Chat', '/chatbot', 'fas fa-robot');
        } else if (path.includes('/preferences')) {
            this.addBreadcrumbItem('Preferences', '/preferences', 'fas fa-sliders-h');
        } else if (path.includes('/settings')) {
            this.addBreadcrumbItem('Settings', '/settings', 'fas fa-cog');
        } else if (path.includes('/users')) {
            this.addBreadcrumbItem('User Management', '/users/manage', 'fas fa-users');
        }
    }

    /**
     * Clear breadcrumb items except home
     */
    clearBreadcrumb() {
        const items = this.breadcrumbContainer.querySelectorAll('li:not(:first-child)');
        items.forEach(item => item.remove());
    }

    /**
     * Add a breadcrumb item
     */
    addBreadcrumbItem(text, url = null, icon = null) {
        const li = document.createElement('li');
        li.className = 'flex items-center';
        
        // Add separator
        const separator = document.createElement('span');
        separator.innerHTML = '<i class="fas fa-chevron-right mx-2 text-gray-400"></i>';
        li.appendChild(separator);
        
        if (url) {
            const link = document.createElement('a');
            link.href = url;
            link.className = 'hover:text-blue-600 transition-colors flex items-center';
            if (icon) {
                link.innerHTML = `<i class="${icon} mr-1"></i>${text}`;
            } else {
                link.textContent = text;
            }
            li.appendChild(link);
        } else {
            const span = document.createElement('span');
            span.className = 'text-gray-900 font-medium flex items-center';
            if (icon) {
                span.innerHTML = `<i class="${icon} mr-1"></i>${text}`;
            } else {
                span.textContent = text;
            }
            li.appendChild(span);
        }
        
        this.breadcrumbContainer.appendChild(li);
    }

    /**
     * Setup navigation listeners to update breadcrumb
     */
    setupNavigationListeners() {
        // Listen for navigation events
        window.addEventListener('popstate', () => {
            this.updateBreadcrumbFromURL();
        });

        // Listen for custom breadcrumb events
        document.addEventListener('breadcrumb:update', (event) => {
            if (event.detail) {
                this.addBreadcrumbItem(event.detail.text, event.detail.url, event.detail.icon);
            }
        });

        // Listen for custom breadcrumb clear events
        document.addEventListener('breadcrumb:clear', () => {
            this.clearBreadcrumb();
        });
    }

    /**
     * Manually add breadcrumb item (for dynamic content)
     */
    static addItem(text, url = null, icon = null) {
        const event = new CustomEvent('breadcrumb:update', {
            detail: { text, url, icon }
        });
        document.dispatchEvent(event);
    }

    /**
     * Clear dynamic breadcrumb items
     */
    static clear() {
        const event = new CustomEvent('breadcrumb:clear');
        document.dispatchEvent(event);
    }
}

// Initialize breadcrumb manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (document.querySelector('.breadcrumb-nav')) {
        window.breadcrumbManager = new BreadcrumbManager();
    }
});

// Export for use in other scripts
window.BreadcrumbManager = BreadcrumbManager;

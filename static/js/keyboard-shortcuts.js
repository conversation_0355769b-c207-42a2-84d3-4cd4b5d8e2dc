/**
 * Keyboard Shortcuts System
 * Manages global keyboard shortcuts for power users
 */

class KeyboardShortcuts {
    constructor() {
        this.shortcuts = new Map();
        this.modal = document.getElementById('shortcuts-modal');
        this.isModalOpen = false;
        this.init();
    }

    init() {
        this.registerDefaultShortcuts();
        this.setupEventListeners();
        this.setupModal();
    }

    /**
     * Register default keyboard shortcuts
     */
    registerDefaultShortcuts() {
        // Navigation shortcuts
        this.register('Alt+KeyH', () => this.navigate('/dashboard'), 'Navigate to Dashboard');
        this.register('Alt+KeyI', () => this.navigate('/inventory_management'), 'Navigate to Inventory');
        this.register('Alt+KeyL', () => this.navigate('/label-packing-slip'), 'Navigate to Generate Labels');
        this.register('Alt+KeyS', () => this.navigate('/shipment/dashboard'), 'Navigate to Shipments');
        this.register('Alt+KeyR', () => this.navigate('/rfq/automation'), 'Navigate to RFQ Automation');
        this.register('Alt+KeyC', () => this.navigate('/chatbot'), 'Navigate to Chat');

        // Action shortcuts
        this.register('Ctrl+KeyK', () => this.focusSearch(), 'Focus Search');
        this.register('Ctrl+KeyS', (e) => this.saveCurrentForm(e), 'Save Current Form');
        this.register('Ctrl+KeyB', () => this.toggleSidebar(), 'Toggle Sidebar');
        this.register('Escape', () => this.handleEscape(), 'Close Modal/Cancel');
        
        // Help shortcut
        this.register('Shift+Slash', () => this.showShortcutsModal(), 'Show Keyboard Shortcuts');

        // Quick actions
        this.register('Alt+KeyN', () => this.quickNewAction(), 'Quick New Action');
        this.register('Alt+KeyF', () => this.quickFilter(), 'Quick Filter');
        this.register('Alt+KeyE', () => this.quickExport(), 'Quick Export');

        // Form navigation
        this.register('Tab', (e) => this.handleTabNavigation(e), 'Navigate Form Fields');
        this.register('Enter', (e) => this.handleEnterKey(e), 'Submit/Confirm');
    }

    /**
     * Register a keyboard shortcut
     */
    register(keyCombo, callback, description = '') {
        this.shortcuts.set(keyCombo, { callback, description });
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        document.addEventListener('keydown', (e) => {
            // Don't trigger shortcuts when typing in input fields (except specific cases)
            if (this.isTypingInInput(e) && !this.isGlobalShortcut(e)) {
                return;
            }

            const keyCombo = this.getKeyCombo(e);
            const shortcut = this.shortcuts.get(keyCombo);

            if (shortcut) {
                e.preventDefault();
                shortcut.callback(e);
            }
        });
    }

    /**
     * Setup shortcuts modal
     */
    setupModal() {
        const closeButton = document.getElementById('close-shortcuts');
        if (closeButton) {
            closeButton.addEventListener('click', () => this.hideShortcutsModal());
        }

        // Close modal on outside click
        this.modal?.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.hideShortcutsModal();
            }
        });
    }

    /**
     * Get key combination string
     */
    getKeyCombo(e) {
        const parts = [];
        
        if (e.ctrlKey) parts.push('Ctrl');
        if (e.altKey) parts.push('Alt');
        if (e.shiftKey) parts.push('Shift');
        if (e.metaKey) parts.push('Meta');
        
        // Handle special keys
        if (e.key === 'Escape') parts.push('Escape');
        else if (e.key === 'Enter') parts.push('Enter');
        else if (e.key === 'Tab') parts.push('Tab');
        else if (e.key === '/') parts.push('Slash');
        else if (e.code) parts.push(e.code);
        
        return parts.join('+');
    }

    /**
     * Check if user is typing in an input field
     */
    isTypingInInput(e) {
        const target = e.target;
        const tagName = target.tagName.toLowerCase();
        const inputTypes = ['input', 'textarea', 'select'];
        const isContentEditable = target.contentEditable === 'true';
        
        return inputTypes.includes(tagName) || isContentEditable;
    }

    /**
     * Check if this is a global shortcut that should work even in input fields
     */
    isGlobalShortcut(e) {
        const keyCombo = this.getKeyCombo(e);
        const globalShortcuts = ['Ctrl+KeyS', 'Escape', 'Shift+Slash', 'Ctrl+KeyK'];
        return globalShortcuts.includes(keyCombo);
    }

    /**
     * Navigate to a URL
     */
    navigate(url) {
        window.location.href = url;
    }

    /**
     * Focus search functionality
     */
    focusSearch() {
        // Look for search inputs in the page
        const searchInputs = document.querySelectorAll('input[type="search"], input[placeholder*="search" i], #search-input');
        if (searchInputs.length > 0) {
            searchInputs[0].focus();
            searchInputs[0].select();
        } else {
            // If no search input found, show a quick search modal or create one
            this.showQuickSearch();
        }
    }

    /**
     * Save current form
     */
    saveCurrentForm(e) {
        e.preventDefault();
        
        // Find the active form
        const forms = document.querySelectorAll('form');
        const activeForm = Array.from(forms).find(form => 
            form.contains(document.activeElement) || 
            form.querySelector('input:focus, textarea:focus, select:focus')
        );

        if (activeForm) {
            // Trigger form submission
            const submitButton = activeForm.querySelector('button[type="submit"], input[type="submit"]');
            if (submitButton) {
                submitButton.click();
            } else {
                activeForm.submit();
            }
            
            // Show feedback
            this.showToast('Form saved!', 'success');
        }
    }

    /**
     * Toggle sidebar
     */
    toggleSidebar() {
        const sidebarToggle = document.getElementById('sidebar-toggle');
        if (sidebarToggle) {
            sidebarToggle.click();
        }
    }

    /**
     * Handle escape key
     */
    handleEscape() {
        // Close any open modals
        if (this.isModalOpen) {
            this.hideShortcutsModal();
            return;
        }

        // Close other modals
        const openModals = document.querySelectorAll('.modal:not(.hidden), [id*="modal"]:not(.hidden)');
        openModals.forEach(modal => {
            if (!modal.classList.contains('hidden')) {
                modal.classList.add('hidden');
            }
        });

        // Clear active focus
        if (document.activeElement && document.activeElement.blur) {
            document.activeElement.blur();
        }
    }

    /**
     * Show shortcuts modal
     */
    showShortcutsModal() {
        if (this.modal) {
            this.modal.classList.remove('hidden');
            this.isModalOpen = true;
        }
    }

    /**
     * Hide shortcuts modal
     */
    hideShortcutsModal() {
        if (this.modal) {
            this.modal.classList.add('hidden');
            this.isModalOpen = false;
        }
    }

    /**
     * Quick new action based on current page
     */
    quickNewAction() {
        const path = window.location.pathname;
        
        if (path.includes('/inventory')) {
            // Trigger add inventory action
            const addButton = document.querySelector('[data-action="add"], .btn-add, #add-inventory');
            if (addButton) addButton.click();
        } else if (path.includes('/label')) {
            // Start new label generation
            this.navigate('/label-packing-slip');
        } else if (path.includes('/rfq')) {
            // Clear RFQ form
            const clearButton = document.querySelector('[data-action="clear"], .btn-clear');
            if (clearButton) clearButton.click();
        }
    }

    /**
     * Quick filter toggle
     */
    quickFilter() {
        const filterButton = document.querySelector('[data-action="filter"], .btn-filter, #filter-toggle');
        if (filterButton) {
            filterButton.click();
        }
    }

    /**
     * Quick export action
     */
    quickExport() {
        const exportButton = document.querySelector('[data-action="export"], .btn-export, #export-data');
        if (exportButton) {
            exportButton.click();
        }
    }

    /**
     * Handle tab navigation
     */
    handleTabNavigation(e) {
        // Enhanced tab navigation for forms
        // This is handled by default browser behavior, but we can enhance it
    }

    /**
     * Handle enter key
     */
    handleEnterKey(e) {
        // If in a form, try to submit
        if (e.target.form && !e.target.matches('textarea')) {
            const submitButton = e.target.form.querySelector('button[type="submit"], input[type="submit"]');
            if (submitButton && !e.target.matches('button')) {
                e.preventDefault();
                submitButton.click();
            }
        }
    }

    /**
     * Show quick search modal
     */
    showQuickSearch() {
        // Implementation for quick search modal
        this.showToast('Quick search: Ctrl+K', 'info');
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'info') {
        // Use SweetAlert2 for consistent notifications
        if (window.Swal) {
            const icon = type === 'success' ? 'success' : type === 'error' ? 'error' : 'info';
            Swal.fire({
                toast: true,
                position: 'top-end',
                icon: icon,
                title: message,
                showConfirmButton: false,
                timer: 2000,
                timerProgressBar: true
            });
        }
    }
}

// Initialize keyboard shortcuts when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.keyboardShortcuts = new KeyboardShortcuts();
});

// Export for use in other scripts
window.KeyboardShortcuts = KeyboardShortcuts;

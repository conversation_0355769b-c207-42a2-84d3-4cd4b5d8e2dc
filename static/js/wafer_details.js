// Main application logic
const WaferManager = {
  // State
  elements: {
    form: null,
    validateButton: null,
    nextButton: null,
    selectAllCheckbox: null,
    waferCheckboxes: null,
    slotIdTextarea: null,
    scribeIdTextarea: null,
    waferPairsInput: null,
  },

  // Initialization
  init() {
    this.initializeElements();
    this.attachEventListeners();
    this.elements.nextButton.disabled = true; // Disable next button initially
    this.updateButtonStyles();
  },

  initializeElements() {
    this.elements = {
      form: document.getElementById("initialForm"),
      validateButton: document.getElementById("validateButton"),
      nextButton: document.getElementById("nextButton"),
      selectAllCheckbox: document.getElementById("select-all"),
      waferCheckboxes: document.querySelectorAll(".wafer-checkbox"),
      slotIdTextarea: document.getElementById("auto-filled-slot-id"),
      scribeIdTextarea: document.getElementById("auto-filled-scribe-id"),
      waferPairsInput: document.getElementById("wafer-pairs-input"),
    };
  },

  updateButtonStyles() {
    // Apply disabled styles to the next button initially
    if (this.elements.nextButton.disabled) {
      this.elements.nextButton.classList.add(
        "opacity-50",
        "cursor-not-allowed"
      );
    }
  },

  attachEventListeners() {
    this.elements.validateButton.addEventListener("click", () =>
      this.validateSelection()
    );
    this.elements.form.addEventListener("submit", (e) =>
      this.handleFormSubmit(e)
    );
    this.elements.selectAllCheckbox.addEventListener("change", () =>
      this.handleSelectAll()
    );

    // Add listeners to individual checkboxes to update the "Select All" state
    this.elements.waferCheckboxes.forEach((checkbox) => {
      checkbox.addEventListener("change", () => this.updateSelectAllState());
    });
  },

  updateSelectAllState() {
    const checkboxes = Array.from(this.elements.waferCheckboxes);
    const allChecked = checkboxes.every((cb) => cb.checked);
    const anyChecked = checkboxes.some((cb) => cb.checked);

    this.elements.selectAllCheckbox.checked = allChecked;
    this.elements.selectAllCheckbox.indeterminate = anyChecked && !allChecked;
  },

  handleSelectAll() {
    this.elements.waferCheckboxes.forEach((checkbox) => {
      checkbox.checked = this.elements.selectAllCheckbox.checked;
    });
  },

  validateSelection() {
    const selectedCheckboxes = document.querySelectorAll(
      ".wafer-checkbox:checked"
    );

    if (selectedCheckboxes.length === 0) {
      this.showAlert(
        "error",
        "No Wafers Selected",
        "Please select at least one wafer before validating."
      );
      return false;
    }

    const { slotIds, scribeIds, waferPairs } =
      this.collectSelectedData(selectedCheckboxes);
    this.updateTextareas(slotIds, scribeIds, waferPairs);

    // Enable next button and update styling
    this.elements.nextButton.disabled = false;
    this.elements.nextButton.classList.remove(
      "opacity-50",
      "cursor-not-allowed"
    );

    this.showAlert(
      "success",
      "Selection Validated",
      "Your wafer selection has been validated successfully."
    );
    return true;
  },

  collectSelectedData(checkboxes) {
    const slotIds = [];
    const scribeIds = [];
    const waferPairs = [];

    checkboxes.forEach((checkbox) => {
      const slotId = checkbox.getAttribute("data-slot-id");
      const scribeId = checkbox.getAttribute("data-scribe-id");
      slotIds.push(slotId);
      scribeIds.push(scribeId);
      waferPairs.push(`${slotId},${scribeId}`);
    });

    return { slotIds, scribeIds, waferPairs };
  },

  updateTextareas(slotIds, scribeIds, waferPairs) {
    this.elements.slotIdTextarea.value = slotIds.join(", ");
    this.elements.scribeIdTextarea.value = scribeIds.join(", ");
    this.elements.waferPairsInput.value = waferPairs.join(";");
  },

  async handleFormSubmit(e) {
    e.preventDefault();

    if (
      !this.elements.slotIdTextarea.value ||
      !this.elements.scribeIdTextarea.value
    ) {
      this.showAlert(
        "error",
        "Validation Required",
        "Please validate your selection before proceeding."
      );
      return;
    }

    const formData = new FormData(this.elements.form);
    const csrfToken = document.querySelector('input[name="csrf_token"]').value;

    try {
      const response = await fetch(this.elements.form.action, {
        method: "POST",
        body: formData,
        headers: {
          "X-Requested-With": "XMLHttpRequest",
          "X-CSRFToken": csrfToken,
        },
        credentials: "same-origin",
      });

      const contentType = response.headers.get("content-type");
      if (contentType && contentType.indexOf("application/json") !== -1) {
        const result = await response.json();

        if (result.status === "success") {
          window.location.href = result.redirect;
        } else {
          throw new Error(result.message || "Unknown error occurred");
        }
      } else {
        // Handle non-JSON response (like redirects)
        const text = await response.text();
        if (response.ok && response.url.includes("print_labels")) {
          window.location.href = "/print_labels";
        } else {
          throw new Error("Invalid response format from server");
        }
      }
    } catch (error) {
      console.error("Error:", error);
      this.showAlert(
        "error",
        "Submission Error",
        error.message ||
          "An error occurred while submitting the form. Please try again."
      );
    }
  },

  showAlert(icon, title, text) {
    Swal.fire({
      icon,
      title,
      text,
      confirmButtonColor: "#4F46E5", // Indigo color matching your UI
      confirmButtonText: "OK",
    });
  },
};

// Initialize when DOM is ready
document.addEventListener("DOMContentLoaded", () => WaferManager.init());

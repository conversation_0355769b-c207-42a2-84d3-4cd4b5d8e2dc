//base.js

document.addEventListener("DOMContentLoaded", function () {
  // Theme initialization and toggle
  const html = document.documentElement;
  const body = document.body;
  const themeToggle =
    document.getElementById("themeToggle") ||
    document.getElementById("loginThemeToggle");
  const savedTheme =
    localStorage.getItem("theme") ||
    (window.matchMedia("(prefers-color-scheme: dark)").matches
      ? "dark"
      : "light");

  function setTheme(theme) {
    // Apply theme to the HTML element for Tailwind's dark: variant
    html.classList.toggle("dark", theme === "dark");

    // Apply theme to body for our custom CSS variables
    body.classList.toggle("dark", theme === "dark");

    // Store theme preference
    localStorage.setItem("theme", theme);
  }

  // Set initial theme
  setTheme(savedTheme);

  // Theme toggle handler
  themeToggle.addEventListener("click", () => {
    const newTheme = html.classList.contains("dark") ? "light" : "dark";
    setTheme(newTheme);
  });

  // Sidebar functionality
  const sidebar = document.querySelector(".sidebar");
  const toggleButton = document.getElementById("sidebar-toggle");
  const collapseButton = document.getElementById("collapse-btn");
  const mainContent = document.getElementById("main-content");
  const sidebarBackdrop = document.getElementById("sidebar-backdrop");
  const sidebarTexts = document.querySelectorAll(".sidebar-text");
  let isCollapsed = localStorage.getItem("sidebar-collapsed") === "true";

  // Function to toggle sidebar on mobile
  function toggleSidebar() {
    if (window.innerWidth <= 768) {
      // Mobile behavior
      sidebar.classList.toggle("expanded");
      sidebarBackdrop.classList.toggle("hidden");
      document.body.classList.toggle("overflow-hidden");
    } else {
      // On desktop, use collapse button instead
      collapseButton.click();
    }
  }

  // Function to collapse/expand sidebar
  function collapseSidebar() {
    isCollapsed = !isCollapsed;

    sidebar.classList.toggle("collapsed", isCollapsed);

    // Adjust main content margin - responsive approach
    if (isCollapsed) {
      mainContent.classList.remove("md:ml-64");
      mainContent.classList.add("md:ml-16");
      sidebarTexts.forEach((text) => (text.style.display = "none"));
    } else {
      mainContent.classList.add("md:ml-64");
      mainContent.classList.remove("md:ml-16");
      sidebarTexts.forEach((text) => (text.style.display = "block"));
    }

    // Save state
    localStorage.setItem("sidebar-collapsed", isCollapsed);
  }

  // Apply initial collapsed state
  if (isCollapsed) {
    sidebar.classList.add("collapsed");
    mainContent.classList.remove("md:ml-64");
    mainContent.classList.add("md:ml-16");
    sidebarTexts.forEach((text) => (text.style.display = "none"));
  }

  // Event listeners
  if (toggleButton) {
    toggleButton.addEventListener("click", toggleSidebar);
  }
  if (collapseButton) {
    collapseButton.addEventListener("click", collapseSidebar);
  }
  if (sidebarBackdrop) {
    sidebarBackdrop.addEventListener("click", toggleSidebar);
  }

  // Handle window resize
  window.addEventListener("resize", () => {
    const isMobile = window.innerWidth <= 768;

    if (!isMobile && sidebar && sidebar.classList.contains("expanded")) {
      sidebar.classList.remove("expanded");
      sidebarBackdrop.classList.add("hidden");
      document.body.classList.remove("overflow-hidden");
    }
  });

  // Active link highlighting
  const currentPath = window.location.pathname;
  document.querySelectorAll(".nav-link").forEach((link) => {
    if (link.getAttribute("href") === currentPath) {
      link.classList.add("active");
    }
  });

  // Initialize notifications if any
  const notificationBadge = document.querySelector(".notification-badge");
  if (notificationBadge) {
    // Example: Set notification count
    const count = 0; // Replace with actual notification count
    notificationBadge.style.display = count > 0 ? "block" : "none";
  }

  // User dropdown menu functionality
  const userMenuButton = document.getElementById("user-menu-button");
  const userDropdown = document.getElementById("user-dropdown");

  if (userMenuButton && userDropdown) {
    // Toggle dropdown when clicking the user menu button
    userMenuButton.addEventListener("click", (e) => {
      e.stopPropagation();
      userDropdown.classList.toggle("hidden");
    });

    // Close dropdown when clicking outside
    document.addEventListener("click", (e) => {
      if (
        !userMenuButton.contains(e.target) &&
        !userDropdown.contains(e.target)
      ) {
        userDropdown.classList.add("hidden");
      }
    });

    // Close dropdown when pressing Escape key
    document.addEventListener("keydown", (e) => {
      if (e.key === "Escape") {
        userDropdown.classList.add("hidden");
      }
    });
  }
  
  // Make tables responsive
  document.querySelectorAll('table').forEach(table => {
    // Skip tables that are already inside a responsive container
    if (!table.parentElement.classList.contains('table-responsive')) {
      // Create a responsive wrapper
      const wrapper = document.createElement('div');
      wrapper.className = 'table-responsive';
      
      // Replace the table with the wrapper containing the table
      table.parentNode.insertBefore(wrapper, table);
      wrapper.appendChild(table);
    }
  });
  
  // Mobile menu dropdown functionality
  const mobileMenuToggle = document.getElementById("mobile-menu-toggle");
  const mobileMenuDropdown = document.getElementById("mobile-menu-dropdown");
  
  if (mobileMenuToggle && mobileMenuDropdown) {
    // Toggle dropdown when clicking the mobile menu button
    mobileMenuToggle.addEventListener("click", (e) => {
      e.stopPropagation();
      mobileMenuDropdown.classList.toggle("hidden");
    });
    
    // Close dropdown when clicking outside
    document.addEventListener("click", (e) => {
      if (
        !mobileMenuToggle.contains(e.target) &&
        !mobileMenuDropdown.contains(e.target)
      ) {
        mobileMenuDropdown.classList.add("hidden");
      }
    });
  }
  
  // Mobile warning for complex UIs, only show on small screens and only on select pages
  const mobileWarning = document.getElementById("mobile-warning");
  if (mobileWarning) {
    const complexUIPages = ["inventory_management", "shipment.shipment_dashboard"];
    const currentPath = window.location.pathname;
    
    // Check if this is a complex UI page
    const isComplexUI = complexUIPages.some(pagePath => currentPath.includes(pagePath));
    
    // Only show warning on mobile for complex UI pages
    if (isComplexUI && window.innerWidth < 768) {
      mobileWarning.style.display = "block";
    }
  }
});

function createNavigationButtons(
  prevUrl,
  nextUrl,
  prevText = "Previous",
  nextText = "Next"
) {
  const container = document.createElement("div");
  container.className = "flex justify-between mt-8";

  if (prevUrl && prevUrl !== "None") {
    const prevButton = document.createElement("a");
    prevButton.href = prevUrl;
    prevButton.className =
      "px-6 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg shadow-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition duration-150 ease-in-out";
    prevButton.textContent = `← ${prevText}`;
    container.appendChild(prevButton);
  }

  if (nextUrl && nextUrl !== "None") {
    const nextButton = document.createElement("a");
    nextButton.href = nextUrl;
    nextButton.className =
      "px-6 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg shadow-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition duration-150 ease-in-out";
    nextButton.textContent = `${nextText} →`;
    container.appendChild(nextButton);
  }

  return container;
}

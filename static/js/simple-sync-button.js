/**
 * Simple Sync Wafers Implementation
 *
 * This is a completely self-contained implementation that bypasses the issues
 * with the current sync functionality. It uses a more reliable approach with
 * form submission instead of JSON for better Flask compatibility.
 */

function createSimpleSyncButton() {
  // Create a new button to replace the existing one
  const newButton = document.createElement("button");
  newButton.innerHTML = `
    <i class="fas fa-sync-alt mr-2"></i>
    Sync Wafers (New)
  `;
  newButton.className =
    "px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 ml-2";
  newButton.onclick = simpleSyncWafers;

  // Find the existing sync button
  const existingButton = document.querySelector(
    "button:has(.fa-sync-alt), button:has(.fas.fa-sync)"
  );
  if (existingButton) {
    // Insert the new button after the existing one
    existingButton.parentNode.insertBefore(
      newButton,
      existingButton.nextSibling
    );
    // Optionally hide the original button
    // existingButton.style.display = 'none';
  } else {
    // If we can't find the existing button, add it to the button container
    const buttonContainer = document.querySelector(
      ".flex.justify-end, .button-container"
    );
    if (buttonContainer) {
      buttonContainer.appendChild(newButton);
    } else {
      // As a last resort, add it to the top of the page
      document.body.insertBefore(newButton, document.body.firstChild);
    }
  }

  console.log("Simple Sync Button created");
  return newButton;
}

function simpleSyncWafers() {
  // Show confirmation dialog
  Swal.fire({
    title: "Sync Wafers to Inventory",
    text: "This will add all missing wafers from the wafers table to the inventory. Continue?",
    icon: "question",
    showCancelButton: true,
    confirmButtonText: "Yes, sync wafers",
    cancelButtonText: "Cancel",
    confirmButtonColor: "#4F46E5",
  }).then((result) => {
    if (result.isConfirmed) {
      performSimpleSync();
    }
  });
}

function performSimpleSync() {
  // Show loading overlay
  const loadingOverlay = document.createElement("div");
  loadingOverlay.className =
    "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50";
  loadingOverlay.innerHTML = `
    <div class="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
      <div class="flex items-center justify-center mb-4">
        <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-indigo-600"></div>
      </div>
      <p class="text-center text-gray-800 mb-1">Syncing wafers to inventory...</p>
      <p class="text-center text-gray-500 text-sm">This may take a moment</p>
    </div>
  `;
  document.body.appendChild(loadingOverlay);

  // Create a form to submit (better for Flask compatibility)
  const form = document.createElement("form");
  form.method = "POST";
  form.action = "/api/inventory/sync-wafers";
  form.style.display = "none";

  // Add CSRF token if available
  const csrfToken = getCSRFTokenFromPage();
  if (csrfToken) {
    const csrfInput = document.createElement("input");
    csrfInput.type = "hidden";
    csrfInput.name = "csrf_token";
    csrfInput.value = csrfToken;
    form.appendChild(csrfInput);
  }

  // Create an iframe to receive the response
  const iframe = document.createElement("iframe");
  iframe.name = "sync-response-frame";
  iframe.style.display = "none";
  document.body.appendChild(iframe);

  // Set form target to iframe
  form.target = "sync-response-frame";
  document.body.appendChild(form);

  // Listen for iframe load
  iframe.onload = function () {
    try {
      // Check if we got a response
      const frameContent =
        iframe.contentDocument || iframe.contentWindow.document;
      const responseText = frameContent.body.innerText;

      // Check if response contains JSON
      let responseData = null;
      try {
        responseData = JSON.parse(responseText);
      } catch (e) {
        // Not JSON, probably HTML
        console.log(
          "Response was not JSON:",
          responseText.substring(0, 200) + "..."
        );
      }

      // Remove loading overlay
      loadingOverlay.remove();

      // Handle response
      if (responseData && responseData.success) {
        // Success response
        Swal.fire({
          title: "Sync Complete",
          html: `
            <div class="text-left">
              <p>Successfully synced wafers to inventory.</p>
              <p class="mt-2"><strong>Added:</strong> ${
                responseData.added || 0
              } wafers</p>
              <p><strong>Total in database:</strong> ${
                responseData.total_wafers || 0
              }</p>
            </div>
          `,
          icon: "success",
          confirmButtonColor: "#4F46E5",
        }).then(() => {
          // Refresh inventory table if available
          if (typeof searchInventory === "function") {
            searchInventory();
          } else {
            window.location.reload();
          }
        });
      } else if (responseData) {
        // Error with details
        Swal.fire({
          title: "Sync Error",
          text: responseData.message || "Failed to sync wafers.",
          icon: "error",
          confirmButtonColor: "#EF4444",
        });
      } else {
        // No JSON response - likely an error page
        handleNonJsonResponse(responseText);
      }
    } catch (error) {
      // Handle any errors during response processing
      console.error("Error processing sync response:", error);
      loadingOverlay.remove();
      Swal.fire({
        title: "Sync Error",
        text: "Failed to process server response. Try refreshing the page.",
        icon: "error",
        confirmButtonColor: "#EF4444",
      });
    } finally {
      // Clean up
      setTimeout(() => {
        iframe.remove();
        form.remove();
      }, 500);
    }
  };

  // Submit the form
  form.submit();

  // Timeout fallback in case the iframe never loads
  setTimeout(() => {
    if (document.body.contains(loadingOverlay)) {
      loadingOverlay.remove();
      Swal.fire({
        title: "Sync Timeout",
        text: "The server is taking too long to respond. The sync may still be processing.",
        icon: "warning",
        confirmButtonColor: "#F59E0B",
      });
    }
  }, 30000); // 30 second timeout
}

function handleNonJsonResponse(responseText) {
  // Check if it's a login page
  if (
    responseText.includes("login") ||
    responseText.includes("sign in") ||
    responseText.includes("username") ||
    responseText.includes("password")
  ) {
    Swal.fire({
      title: "Session Expired",
      text: "Your session has expired. Please refresh the page and log in again.",
      icon: "warning",
      confirmButtonText: "Refresh Page",
      confirmButtonColor: "#F59E0B",
    }).then((result) => {
      if (result.isConfirmed) {
        window.location.reload();
      }
    });
  } else if (
    responseText.includes("error") ||
    responseText.includes("exception")
  ) {
    // Looks like an error page
    Swal.fire({
      title: "Server Error",
      text: "The server encountered an error. Please try again later.",
      icon: "error",
      confirmButtonColor: "#EF4444",
    });
  } else {
    // Unknown response
    Swal.fire({
      title: "Unexpected Response",
      text: "The server returned an unexpected response. The sync may or may not have completed.",
      icon: "warning",
      confirmButtonColor: "#F59E0B",
    });
  }
}

function getCSRFTokenFromPage() {
  // Try to get CSRF token from meta tag
  const metaToken = document.querySelector('meta[name="csrf-token"]');
  if (metaToken) {
    return metaToken.getAttribute("content");
  }

  // Try to get from hidden input
  const inputToken = document.querySelector('input[name="csrf_token"]');
  if (inputToken) {
    return inputToken.value;
  }

  // Try from cookie
  const cookies = document.cookie.split(";");
  for (const cookie of cookies) {
    const [name, value] = cookie.trim().split("=");
    if (name === "csrf_token") {
      return value;
    }
  }

  return null;
}

// Create the button when the page loads
document.addEventListener("DOMContentLoaded", createSimpleSyncButton);

// If the page is already loaded, create the button now
if (
  document.readyState === "complete" ||
  document.readyState === "interactive"
) {
  createSimpleSyncButton();
}

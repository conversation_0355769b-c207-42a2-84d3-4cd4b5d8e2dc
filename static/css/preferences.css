/**
 * Modern Preferences Page Styles
 * Enhanced UI for Talaria Dashboard preferences
 */

/* Quick Settings Bar */
.quick-settings-bar {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dark .quick-settings-bar {
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    border-color: #4b5563;
}

/* Theme Quick Buttons */
.theme-quick-btn {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    border: 2px solid #e5e7eb;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    cursor: pointer;
}

.theme-quick-btn:hover {
    border-color: #3b82f6;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.theme-quick-btn.active {
    border-color: #3b82f6;
    background: #3b82f6;
    color: white;
}

/* Font Size Buttons */
.font-size-btn {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    border: 1px solid #d1d5db;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    transition: all 0.2s ease;
    cursor: pointer;
}

.font-size-btn[data-size="small"] { font-size: 12px; }
.font-size-btn[data-size="medium"] { font-size: 14px; }
.font-size-btn[data-size="large"] { font-size: 16px; }

.font-size-btn:hover {
    border-color: #10b981;
    background: #ecfdf5;
}

.font-size-btn.active {
    border-color: #10b981;
    background: #10b981;
    color: white;
}

/* Preferences Container */
.preferences-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 2rem;
}

.dark .preferences-container {
    background: #1f2937;
}

/* Tab Navigation */
.tab-navigation {
    border-bottom: 1px solid #e5e7eb;
    background: #f9fafb;
}

.dark .tab-navigation {
    border-bottom-color: #374151;
    background: #111827;
}

.preference-tab {
    padding: 1rem 1.5rem;
    border: none;
    background: none;
    color: #6b7280;
    font-weight: 500;
    transition: all 0.2s ease;
    border-bottom: 3px solid transparent;
    cursor: pointer;
}

.preference-tab:hover {
    color: #3b82f6;
    background: rgba(59, 130, 246, 0.05);
}

.preference-tab.active {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
    background: rgba(59, 130, 246, 0.05);
}

/* Tab Content */
.tab-content-container {
    padding: 2rem;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.tab-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
}

.dark .tab-title {
    color: #f9fafb;
}

/* Profile Section */
.profile-picture-section {
    text-align: center;
}

.profile-picture-container {
    position: relative;
    display: inline-block;
}

.profile-image {
    width: 160px;
    height: 160px;
    border-radius: 50%;
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin-bottom: 1rem;
    border: 4px solid white;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.profile-initial {
    color: #3b82f6;
    font-size: 4rem;
    font-weight: 700;
}

.profile-change-btn {
    position: absolute;
    bottom: 8px;
    right: 8px;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: #3b82f6;
    color: white;
    border: 3px solid white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.profile-change-btn:hover {
    background: #2563eb;
    transform: scale(1.05);
}

.profile-picture-info {
    margin-top: 1rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.profile-picture-info p {
    margin: 0.25rem 0;
}

/* Profile Info Cards */
.profile-info-card {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.2s ease;
}

.profile-info-card:hover {
    border-color: #3b82f6;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

.dark .profile-info-card {
    background: #374151;
    border-color: #4b5563;
}

.profile-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
    margin-bottom: 0.5rem;
}

.profile-value {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #1f2937;
}

.dark .profile-value {
    color: #f9fafb;
}

/* Role Badges */
.role-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.role-admin {
    background: #fef3c7;
    color: #92400e;
}

.role-colleague {
    background: #dbeafe;
    color: #1e40af;
}

.role-viewer {
    background: #f3e8ff;
    color: #7c3aed;
}

/* Profile Actions */
.profile-actions {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
}

.dark .profile-actions {
    border-top-color: #374151;
}

/* Preference Sections */
.preference-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
}

.dark .preference-section {
    background: #374151;
    border-color: #4b5563;
}

.section-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

.dark .section-title {
    color: #f9fafb;
}

/* Theme Options */
.theme-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.theme-option {
    cursor: pointer;
}

.theme-card {
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    transition: all 0.2s ease;
    background: white;
}

.theme-card:hover {
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.theme-option input:checked + .theme-card {
    border-color: #3b82f6;
    background: #eff6ff;
}

.theme-preview {
    width: 60px;
    height: 40px;
    border-radius: 4px;
    margin: 0 auto 0.5rem;
    border: 1px solid #d1d5db;
}

.light-preview {
    background: linear-gradient(135deg, #ffffff 0%, #f3f4f6 100%);
}

.dark-preview {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}

.system-preview {
    background: linear-gradient(135deg, #ffffff 0%, #1f2937 100%);
}

.theme-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-weight: 500;
}

/* Color Options */
.color-options {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.color-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.color-swatch {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 3px solid transparent;
    transition: all 0.2s ease;
}

.color-option:hover .color-swatch {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.color-option input:checked + .color-swatch {
    border-color: white;
    box-shadow: 0 0 0 2px currentColor;
}

/* Form Elements */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

.dark .form-label {
    color: #d1d5db;
}

.form-select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.form-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dark .form-select {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
}

/* Font Size Slider */
.font-size-slider {
    position: relative;
}

.slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #e5e7eb;
    outline: none;
    -webkit-appearance: none;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #3b82f6;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #3b82f6;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: #6b7280;
}

/* Buttons */
.btn-secondary {
    padding: 0.5rem 1rem;
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
}

.btn-secondary:hover {
    background: #e5e7eb;
    border-color: #9ca3af;
}

.btn-primary {
    padding: 0.75rem 1.5rem;
    background: #3b82f6;
    color: white;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    transition: all 0.2s ease;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
}

.btn-primary:hover {
    background: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-primary-large {
    padding: 1rem 2rem;
    background: #3b82f6;
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1.125rem;
    transition: all 0.2s ease;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    box-shadow: 0 4px 14px rgba(59, 130, 246, 0.3);
}

.btn-primary-large:hover {
    background: #2563eb;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

/* Save Section */
.save-section {
    text-align: center;
    padding: 2rem;
    background: #f8fafc;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.dark .save-section {
    background: #1f2937;
    border-color: #374151;
}

/* Toggle Switches */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggle-switch input:checked + .toggle-slider {
    background-color: #3b82f6;
}

.toggle-switch input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.toggle-switch:hover .toggle-slider {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Dark mode toggle switches */
.dark .toggle-slider {
    background-color: #4b5563;
}

.dark .toggle-switch input:checked + .toggle-slider {
    background-color: #3b82f6;
}

/* Notification Items */
.notification-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.notification-item:hover {
    border-color: #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.dark .notification-item {
    background: #374151;
    border-color: #4b5563;
}

.notification-info {
    flex: 1;
    margin-right: 1rem;
}

.notification-title {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.dark .notification-title {
    color: #f9fafb;
}

.notification-desc {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0;
}

.dark .notification-desc {
    color: #9ca3af;
}

/* Warning styles for unsaved changes */
.btn-warning {
    background: #f59e0b !important;
    color: white !important;
}

.btn-warning:hover {
    background: #d97706 !important;
}

/* Loading states */
.btn-primary:disabled,
.btn-primary-large:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Enhanced form elements */
.form-select:hover {
    border-color: #9ca3af;
}

.form-select:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Improved accessibility */
.preference-tab:focus,
.toggle-switch:focus-within,
.theme-option:focus-within,
.color-option:focus-within {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
    border-radius: 4px;
}

/* Animation for tab switching */
.tab-content {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.tab-content.active {
    opacity: 1;
    transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
    .quick-settings-bar .flex {
        flex-direction: column;
        gap: 1rem;
    }

    .tab-navigation nav {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .preference-tab {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }

    .tab-content-container {
        padding: 1rem;
    }

    .theme-options {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }

    .color-options {
        justify-content: center;
    }

    .notification-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .notification-info {
        margin-right: 0;
    }

    .profile-image {
        width: 120px;
        height: 120px;
    }

    .profile-initial {
        font-size: 3rem;
    }

    .profile-change-btn {
        width: 40px;
        height: 40px;
    }
}

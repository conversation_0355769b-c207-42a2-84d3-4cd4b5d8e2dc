/* Round Favicon Styles */

/* This is for in-page favicon references, not the actual browser tab */
.favicon-round {
  border-radius: 50%;
  overflow: hidden;
  display: inline-block;
}

.favicon-round img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* For browser tab favicon, this has limited effect due to browser security */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  head link[rel="icon"] {
    border-radius: 50% !important;
    -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%);
    mask-image: radial-gradient(circle, white 100%, black 100%);
  }
}

/* Modern UI/UX Enhancements for Talaria Dashboard */
/* Includes glassmorphism, micro-interactions, and advanced animations */

/* CSS Custom Properties for Dynamic Theming */
:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  --danger-gradient: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
  
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  
  --animation-fast: 0.2s ease-out;
  --animation-normal: 0.3s ease-out;
  --animation-slow: 0.5s ease-out;
  
  --border-radius-sm: 8px;
  --border-radius-md: 12px;
  --border-radius-lg: 16px;
  --border-radius-xl: 24px;
}

/* Dark mode variables */
[data-theme="dark"] {
  --glass-bg: rgba(0, 0, 0, 0.2);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.5);
}

/* Glassmorphism Components */
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--glass-shadow);
  transition: all var(--animation-normal);
}

.glass-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.5);
}

/* Enhanced Dashboard Cards */
.dashboard-card {
  @apply glass-card;
  position: relative;
  overflow: hidden;
  padding: 1.5rem;
  margin-bottom: 1rem;
}

.dashboard-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity var(--animation-normal);
}

.dashboard-card:hover::before {
  opacity: 1;
}

/* Animated Counters */
.metric-counter {
  font-size: 2.5rem;
  font-weight: 700;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all var(--animation-normal);
}

.metric-counter.updating {
  animation: counterPulse 0.6s ease-in-out;
}

@keyframes counterPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* Change Indicators */
.change-indicator {
  position: absolute;
  top: -10px;
  right: 10px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  animation: slideInDown 0.3s ease-out;
  z-index: 10;
}

.change-indicator.positive {
  background: var(--success-gradient);
  color: white;
}

.change-indicator.negative {
  background: var(--danger-gradient);
  color: white;
}

.change-indicator.fade-out {
  animation: fadeOutUp 0.3s ease-in forwards;
}

@keyframes slideInDown {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeOutUp {
  to {
    transform: translateY(-20px);
    opacity: 0;
  }
}

/* Enhanced Progress Bars */
.progress-bar {
  height: 8px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.progress-bar-fill {
  height: 100%;
  border-radius: 4px;
  transition: width var(--animation-slow), background var(--animation-normal);
  position: relative;
  overflow: hidden;
}

.progress-bar-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 2s infinite;
}

.progress-bar-fill.updating::after {
  animation: shimmer 0.8s ease-in-out;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Smart Search Enhancements */
.smart-search-container {
  position: relative;
}

.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius-md);
  box-shadow: var(--glass-shadow);
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
  margin-top: 4px;
}

.suggestion-item {
  padding: 12px 16px;
  cursor: pointer;
  transition: all var(--animation-fast);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.suggestion-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(4px);
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.suggestion-text {
  font-weight: 500;
  color: #1f2937;
}

.suggestion-type {
  font-size: 0.75rem;
  padding: 2px 8px;
  background: var(--primary-gradient);
  color: white;
  border-radius: 12px;
}

.suggestion-meta {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 4px;
}

/* Batch Operations Panel */
.batch-operations-panel {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 320px;
  background: var(--glass-bg);
  backdrop-filter: blur(15px);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--glass-shadow);
  z-index: 1000;
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.batch-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--glass-border);
}

.batch-panel-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.close-batch-panel {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6b7280;
  transition: color var(--animation-fast);
}

.close-batch-panel:hover {
  color: #ef4444;
}

.batch-panel-content {
  padding: 16px;
}

.selected-items-count {
  text-align: center;
  margin-bottom: 16px;
  font-weight: 500;
  color: #4f46e5;
}

.batch-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-bottom: 16px;
}

.batch-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 12px;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius-md);
  color: #374151;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all var(--animation-fast);
}

.batch-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.batch-btn i {
  font-size: 0.875rem;
}

/* Predictive Analytics Panel */
.predictive-panel {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius-lg);
  margin: 20px 0;
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid var(--glass-border);
}

.panel-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.toggle-panel {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  transition: transform var(--animation-fast);
}

.toggle-panel:hover {
  transform: scale(1.1);
}

.insight-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  padding: 20px;
}

.insight-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius-md);
  padding: 16px;
  transition: all var(--animation-normal);
}

.insight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.insight-card h4 {
  margin: 0 0 12px 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.prediction-value {
  font-size: 2rem;
  font-weight: 700;
  background: var(--success-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 4px;
}

.prediction-label {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 8px;
}

.confidence {
  font-size: 0.75rem;
  color: #10b981;
  font-weight: 500;
}

.trend {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 12px;
}

.trend.up {
  background: #dcfce7;
  color: #166534;
}

.trend.down {
  background: #fee2e2;
  color: #991b1b;
}

.optimization-tip {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  font-size: 0.875rem;
  color: #374151;
}

.optimization-tip i {
  color: #f59e0b;
}

/* Connection Status Indicator */
.connection-status {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  z-index: 1000;
  transition: all var(--animation-normal);
}

.connection-status.connected {
  background: var(--success-gradient);
  color: white;
}

.connection-status.disconnected {
  background: var(--danger-gradient);
  color: white;
  animation: pulse 2s infinite;
}

.connection-status.error {
  background: var(--warning-gradient);
  color: white;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Real-time Notification Styles */
.real-time-notification {
  backdrop-filter: blur(10px) !important;
  background: var(--glass-bg) !important;
  border: 1px solid var(--glass-border) !important;
}

/* Enhanced Table Styles */
.inventory-table {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  border: 1px solid var(--glass-border);
}

.inventory-table th {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  position: sticky;
  top: 0;
  z-index: 10;
}

.inventory-table tr {
  transition: all var(--animation-fast);
}

.inventory-table tr:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: scale(1.01);
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
  .batch-operations-panel {
    width: calc(100% - 40px);
    left: 20px;
    right: 20px;
  }
  
  .insight-cards {
    grid-template-columns: 1fr;
  }
  
  .batch-actions {
    grid-template-columns: 1fr;
  }
}

/* Loading States */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus Styles */
.glass-card:focus-within {
  outline: 2px solid #4f46e5;
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .glass-card {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid #000;
  }
  
  [data-theme="dark"] .glass-card {
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid #fff;
  }
}

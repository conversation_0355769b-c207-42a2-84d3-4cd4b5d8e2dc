/*Base template styles*/
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.table-container {
  max-height: calc(100vh - 400px);
  overflow-y: auto;
}

.sticky-header th {
  position: sticky;
  top: 0;
  z-index: 10;
  background: #f9fafb;
}

.dark .sticky-header th {
  background: #374151;
}

.sort-indicator {
  display: inline-block;
  margin-left: 0.5rem;
  width: 0.5rem;
}

th.cursor-pointer:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.dark th.cursor-pointer:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

th.cursor-pointer {
  position: relative;
  padding-right: 2rem;
}

.sort-indicator::after {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
}

.sort-indicator.asc::after {
  content: "↑";
}

.sort-indicator.desc::after {
  content: "↓";

  .search-fab {
    transition: transform 0.2s ease;
  }

  .search-fab:hover {
    transform: scale(1.1);
  }

  .date-picker-wrapper {
    position: relative;
  }

  .flatpickr-calendar {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border-radius: 0.5rem;
  }

  .date-picker-wrapper .flatpickr-input {
    background-color: transparent !important;
  }
}

.date-picker-wrapper {
  position: relative;
}

.date-picker-wrapper input[type="date"] {
  min-width: 150px;
}

.date-picker-wrapper input[type="date"]::-webkit-calendar-picker-indicator {
  position: absolute;
  right: 8px;
  cursor: pointer;
}

/* For better grid layout */
.grid {
  display: grid;
  gap: 1rem;
}

/* Ensure the search box stays contained */
.bg-white {
  overflow: visible;
}

/* Better spacing for the date picker containers */
.space-y-2 > * + * {
  margin-top: 0.5rem;
}

/* Ensure date inputs have consistent height */
input[type="date"] {
  height: 38px;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* CSS for inventoryModification modal function only */

/* Base modal styles */
#inventory-modal {
  z-index: 9999;
}

#inventory-modal .bg-white {
  background-color: white;
}

.dark #inventory-modal .bg-white {
  background-color: #1f2937;
}

/* Sticky header and footer */
.sticky-header {
  position: sticky;
  top: 0;
  z-index: 20;
  background-color: white;
  border-bottom: 1px solid #e5e7eb;
}

.sticky-footer {
  position: sticky;
  bottom: 0;
  z-index: 20;
  background-color: white;
  border-top: 1px solid #e5e7eb;
}

.dark .sticky-header,
.dark .sticky-footer {
  background-color: #1f2937;
  border-color: #374151;
}

/* Wafer ID text styling */
.wafer-id {
  font-weight: 500;
  font-size: 0.875rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 0.5rem;
}

/* Slot input styling */
.slot-input {
  width: 100%;
  text-align: center;
  padding: 0.25rem;
  border-radius: 0.25rem;
  border: 1px solid #d1d5db;
  background-color: #f9fafb;
}

.dark .slot-input {
  background-color: #374151;
  border-color: #4b5563;
  color: #f3f4f6;
}

/* Form controls */
.form-field {
  margin-bottom: 0.75rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
  color: #4b5563;
}

.dark .form-label {
  color: #d1d5db;
}

/* Section headers */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.section-title {
  font-size: 1rem;
  font-weight: 500;
  color: #111827;
}

.dark .section-title {
  color: #f9fafb;
}

/* Make slot pagination more obvious */
.pagination-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.75rem;
}

.page-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 0.25rem;
  background-color: #f3f4f6;
  color: #4b5563;
  border: none;
  cursor: pointer;
}

.page-button:hover:not(:disabled) {
  background-color: #e5e7eb;
}

.page-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.dark .page-button {
  background-color: #374151;
  color: #d1d5db;
}

.dark .page-button:hover:not(:disabled) {
  background-color: #4b5563;
}

/* Fix for button styling */
.px-3.py-2.text-sm.bg-blue-600.text-white.rounded-md.hover\:bg-blue-700 {
  background-color: #2563eb;
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  border: none;
  cursor: pointer;
}

.px-3.py-2.text-sm.bg-blue-600.text-white.rounded-md.hover\:bg-blue-700:hover {
  background-color: #1d4ed8;
}

/* Update Items button */
.px-3.sm\:px-4.py-2.text-sm.bg-yellow-600.text-white.rounded-md.hover\:bg-yellow-700 {
  background-color: #d97706;
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  border: none;
  cursor: pointer;
}

.px-3.sm\:px-4.py-2.text-sm.bg-yellow-600.text-white.rounded-md.hover\:bg-yellow-700:hover {
  background-color: #b45309;
}

/* Cancel button */
.px-3.sm\:px-4.py-2.text-sm.bg-gray-100.text-gray-700.rounded-md.hover\:bg-gray-200 {
  background-color: #f3f4f6;
  color: #374151;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  border: none;
  cursor: pointer;
}

.px-3.sm\:px-4.py-2.text-sm.bg-gray-100.text-gray-700.rounded-md.hover\:bg-gray-200:hover {
  background-color: #e5e7eb;
}

/* Basic scrolling behavior */
#inventory-modal .overflow-y-auto {
  overflow-y: auto;
  max-height: calc(90vh - 120px); /* Allows space for header and footer */
}

/* Slot cards appearance */
.slot-card {
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  padding: 0.75rem;
  background-color: white;
  transition: all 0.15s ease-in-out;
}

.slot-card:hover {
  border-color: #d1d5db;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dark .slot-card {
  background-color: #374151;
  border-color: #4b5563;
}

.dark .slot-card:hover {
  border-color: #6b7280;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Current slot value */
.slot-card .text-red-500 {
  color: #ef4444;
  font-weight: 500;
}

/* Changed slot highlight */
.changed-slot {
  background-color: #d1fae5 !important;
  border-color: #34d399 !important;
  color: #047857 !important;
}

/* Pagination styling */
#prev-slots-btn:disabled,
#next-slots-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Grid layout for different screen sizes */
.slot-page {
  margin-bottom: 1rem;
}

@media (max-width: 640px) {
  .slot-page .grid {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .slot-page .grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .slot-page .grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1025px) and (max-width: 1280px) {
  .slot-page .grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1281px) {
  .slot-page .grid {
    grid-template-columns: repeat(5, 1fr);
  }
}

/* Ensure modal overlay works properly */
.fixed.inset-0 {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

/* Form section styling */
.bg-gray-50 {
  background-color: #f9fafb;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
}

.dark .bg-gray-50 {
  background-color: #1f2937;
}

/* Bulk Modification Styles */
.bulk-modification-section {
  transition: all 0.3s ease;
}

.bulk-group {
  transition: all 0.2s ease;
}

.bulk-group:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.bulk-group-checkbox:checked + label {
  color: #1d4ed8;
  font-weight: 600;
}

.bulk-groups-container {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.bulk-groups-container::-webkit-scrollbar {
  width: 6px;
}

.bulk-groups-container::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.bulk-groups-container::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.bulk-groups-container::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Group details animation */
#group-details-0,
#group-details-1,
#group-details-2,
#group-details-3,
#group-details-4 {
  transition: all 0.3s ease;
}

/* Bulk action buttons */
#select-all-groups,
#apply-bulk-changes {
  transition: all 0.2s ease;
}

#select-all-groups:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

#apply-bulk-changes:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

#apply-bulk-changes:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Wafer ID tags in group details */
.bulk-group span {
  transition: all 0.2s ease;
}

.bulk-group span:hover {
  transform: scale(1.05);
  background-color: #dbeafe;
  border-color: #3b82f6;
}

/* Success animation for bulk section */
.bulk-modification-section.success {
  animation: bulkSuccess 2s ease-in-out;
}

@keyframes bulkSuccess {
  0% { background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%); }
  50% { background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%); }
  100% { background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%); }
}

/* New Tabbed Modal Styles */
.tab-btn {
  transition: all 0.2s ease;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}

.tab-btn:hover {
  color: #374151 !important;
  border-color: #d1d5db !important;
}

.tab-btn.active {
  color: #2563eb !important;
  border-color: #2563eb !important;
}

.tab-panel {
  transition: opacity 0.2s ease;
}

.tab-panel.hidden {
  display: none;
}

.tab-panel.active {
  display: block;
}

/* Modal fixed height layout */
.modal-fixed-height {
  height: 85vh;
  display: flex;
  flex-direction: column;
}

.modal-header {
  flex-shrink: 0;
  border-bottom: 1px solid #e5e7eb;
}

.modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

.modal-footer {
  flex-shrink: 0;
  border-top: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.dark .modal-header,
.dark .modal-footer {
  border-color: #374151;
}

.dark .modal-footer {
  background-color: #374151;
}

/* Individual slots grid improvements */
.slots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 0.75rem;
  max-height: 16rem;
  overflow-y: auto;
  padding: 1rem;
  background-color: #f9fafb;
  border-radius: 0.5rem;
}

.dark .slots-grid {
  background-color: #374151;
}

.slot-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

.slot-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.dark .slot-item {
  background-color: #1f2937;
  border-color: #4b5563;
}

.dark .slot-item:hover {
  border-color: #60a5fa;
}

.slot-wafer-id {
  font-weight: 500;
  color: #374151;
  min-width: 0;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 0.875rem;
}

.dark .slot-wafer-id {
  color: #d1d5db;
}

.slot-input-field {
  width: 4rem;
  padding: 0.25rem 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  text-align: center;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.slot-input-field:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dark .slot-input-field {
  background-color: #374151;
  border-color: #4b5563;
  color: #f3f4f6;
}

/* Slot management buttons */
.slot-management-buttons {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.slot-btn {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.375rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.slot-btn-primary {
  background-color: #dbeafe;
  color: #1e40af;
}

.slot-btn-primary:hover {
  background-color: #bfdbfe;
}

.slot-btn-secondary {
  background-color: #f3f4f6;
  color: #374151;
}

.slot-btn-secondary:hover {
  background-color: #e5e7eb;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .modal-fixed-height {
    height: 95vh;
  }

  .slots-grid {
    grid-template-columns: 1fr;
    max-height: 12rem;
  }

  .slot-management-buttons {
    flex-direction: column;
  }
}

/* Focus improvements */
.tab-btn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

input:focus,
select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

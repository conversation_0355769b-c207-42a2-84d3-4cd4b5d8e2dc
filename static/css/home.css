/* Styles for the home.css page */

:root {
  /* Light theme */
  --bg-primary-light: #ffffff;
  --bg-secondary-light: #f3f4f6;
  --text-primary-light: #111827;
  --text-secondary-light: #4b5563;
  --border-light: #e5e7eb;
  --card-shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);

  /* Dark theme */
  --bg-primary-dark: #1a1a1a;
  --bg-secondary-dark: #2d2d2d;
  --text-primary-dark: #ffffff;
  --text-secondary-dark: #9ca3af;
  --border-dark: #4b5563;
  --card-shadow-dark: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* Dark mode transitions */
.dark-transition {
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Dark mode styles */
.dark body {
  background-color: var(--bg-primary-dark);
  color: var(--text-primary-dark);
}

.dark .dashboard-card {
  background-color: var(--bg-secondary-dark);
  color: var(--text-primary-dark);
}

.dark .section-title {
  color: var(--text-primary-dark);
}

.dark .stat-card {
  background-color: var(--bg-secondary-dark);
  border-color: var(--border-dark);
}

.dark .text-primary {
  color: var(--text-primary-dark);
}

.dark .text-secondary {
  color: var(--text-secondary-dark);
}

/* Section title styles */
.section-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary-light);
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--border-light);
}

/* Dashboard Card */
.dashboard-card {
  background-color: #ffffff;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  padding: 1.5rem;
  height: 100%;
  overflow-x: auto;
}

@media (max-width: 768px) {
  .dashboard-card {
    padding: 1rem;
  }
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Stat Card */
.stat-card {
  padding: 1.5rem;
  border-radius: 0.75rem;
  background-color: #ffffff;
  border: 1px solid var(--border-light);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Chart container */
.chart-container {
  position: relative;
  height: 100%;
  width: 100%;
  transition: all 0.3s ease;
}

/* Dark mode adjustments */
.dark .dashboard-card {
  background-color: var(--bg-secondary-dark);
}

.dark .stat-card {
  background-color: var(--bg-secondary-dark);
  border-color: var(--border-dark);
}

/* Typewriter effect styles */
.typewriter {
  overflow: hidden;
  border-right: 3px solid #fff;
  white-space: nowrap;
  margin: 0;
  animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@media (max-width: 768px) {
  .typewriter {
    white-space: normal;
    border-right: none;
    animation: none;
  }
}

.typewriter-subtitle {
  opacity: 0;
  animation: fadeIn 1s ease-out 3.5s forwards;
}

@media (max-width: 768px) {
  .typewriter-subtitle {
    opacity: 1;
    animation: none;
  }
}

@keyframes typing {
  from {
    width: 0;
  }

  to {
    width: 100%;
  }
}

@keyframes blink-caret {
  from,
  to {
    border-color: transparent;
  }

  50% {
    border-color: #fff;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 0.9;
  }
}

/* Welcome section styles */
.welcome-section {
  position: relative;
  /* Add a fallback background color in case the image fails to load */
  background-color: #3b82f6;
  background-image: url("../img/logoTalaria.jpeg"),
    linear-gradient(to right, rgba(59, 130, 246, 0.8), rgba(99, 102, 241, 0.8));
  background-size: cover;
  background-position: center;
  background-blend-mode: overlay;
  border-radius: 0.75rem;
  overflow: hidden;
}

.welcome-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.2));
}

/* Smooth transitions */
.dashboard-card,
.stat-card,
.chart-container {
  transition: all 0.3s ease;
}

/* Animation for stats */
@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-value {
  animation: countUp 0.5s ease-out forwards;
}

/* Dashboard Grid - Mobile Responsive */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 1rem;
}

/* Mobile-first approach */
@media (max-width: 640px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .col-span-4,
  .col-span-8,
  .col-span-12 {
    grid-column: span 1;
  }
}

/* Tablet */
@media (min-width: 641px) and (max-width: 1024px) {
  .dashboard-grid {
    grid-template-columns: repeat(6, 1fr);
    gap: 0.875rem;
  }

  .col-span-4 {
    grid-column: span 3;
  }

  .col-span-8 {
    grid-column: span 6;
  }

  .col-span-12 {
    grid-column: span 6;
  }
}

/* Desktop */
@media (min-width: 1025px) {
  .col-span-4 {
    grid-column: span 4;
  }

  .col-span-8 {
    grid-column: span 8;
  }

  .col-span-12 {
    grid-column: span 12;
  }
}

/* Welcome section content */
.welcome-content {
  position: relative;
  z-index: 10;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 2rem;
}

/* Welcome stats section - Mobile Responsive */
.welcome-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 1.5rem;
}

/* Glass card for welcome section stats */
.glass-card {
  background-color: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  flex: 1;
  min-width: 100px;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .welcome-stats {
    gap: 0.5rem;
    margin-top: 1rem;
  }

  .glass-card {
    padding: 0.5rem 0.75rem;
  }
}

@media (min-width: 768px) {
  .welcome-stats {
    gap: 1.5rem;
    margin-top: 2rem;
  }

  .glass-card {
    padding: 0.75rem 1.5rem;
  }
}

/* Pulse animation for active cards */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}

/* Progress bar styles */
.progress-bar {
  height: 0.5rem;
  background-color: #e5e7eb;
  border-radius: 9999px;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  border-radius: 9999px;
}

/* Capacity percentages */
.capacity-45 {
  width: 45%;
}

.capacity-64 {
  width: 64%;
}

.capacity-78 {
  width: 78%;
}

/* Gradient backgrounds */
.gradient-blue {
  background: linear-gradient(to right, #3b82f6, #6366f1);
}

.gradient-green {
  background: linear-gradient(to right, #10b981, #059669);
}

.gradient-amber {
  background: linear-gradient(to right, #f59e0b, #d97706);
}

/* Metric icon styling */
.metric-icon {
  padding: 1rem;
  border-radius: 9999px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Chart height */
.chart-height {
  height: 20rem;
}

@media (max-width: 768px) {
  .chart-height {
    height: 15rem;
  }
}

/* Footer Styles */
.dashboard-footer {
  background-color: var(--bg-primary-light);
  border-radius: 0.75rem;
  box-shadow: var(--card-shadow-light);
  padding: 2rem;
  margin-top: 1rem;
  transition: all 0.3s ease;
}

@media (max-width: 768px) {
  .dashboard-footer {
    padding: 1rem;
    overflow-x: hidden;
  }
}

.dark .dashboard-footer {
  background-color: var(--bg-secondary-dark);
  box-shadow: var(--card-shadow-dark);
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
}

@media (max-width: 1024px) {
  .footer-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (max-width: 640px) {
  .footer-grid {
    grid-template-columns: 1fr;
    gap: 1.25rem;
  }

  .footer-section {
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(229, 231, 235, 0.2);
  }

  .footer-section:last-child {
    border-bottom: none;
  }

  .dark .footer-section {
    border-bottom-color: rgba(75, 85, 99, 0.2);
  }
}

.footer-section {
  display: flex;
  flex-direction: column;
}

/* Updated footer logo styles */
.footer-logo-container {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.logo-iridescent {
  position: relative;
  display: inline-block;
  margin-right: 0.5rem;
  border-radius: 0.25rem;
  overflow: hidden; /* Ensure the effect stays within bounds */
  width: 40px; /* Added fixed width */
  height: 40px; /* Added fixed height */
}

.footer-logo {
  height: 40px; /* Modified from 2.5rem to 40px */
  width: 40px; /* Modified from 2.5rem to 40px */
  object-fit: cover; /* Make sure image fills the square */
  border-radius: 0.25rem;
  position: relative;
  z-index: 1;
  mix-blend-mode: multiply; /* This will help blend with the effect */
}

/* Modified iridescent effect to overlay the logo */
.logo-iridescent::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(148, 0, 211, 0.6) 0%,
    rgba(75, 0, 130, 0.6) 15%,
    rgba(138, 43, 226, 0.6) 30%,
    rgba(255, 20, 147, 0.6) 45%,
    rgba(50, 205, 50, 0.6) 60%,
    rgba(255, 215, 0, 0.6) 75%,
    rgba(148, 0, 211, 0.6) 100%
  );
  mix-blend-mode: overlay; /* Changed from color-dodge for better blending */
  border-radius: 0.25rem;
  pointer-events: none;
  animation: iridescent-shift 6s linear infinite;
  filter: brightness(1.2) contrast(1.1);
  z-index: 2; /* Changed from 1 to 2 to overlay the logo */
}

.footer-title {
  margin-left: 0.5rem;
  position: relative;
  z-index: 1;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary-light);
}

/* Animation for the iridescent effect - unchanged */
@keyframes iridescent-shift {
  0% {
    filter: hue-rotate(0deg);
    opacity: 0.7;
  }
  50% {
    filter: hue-rotate(180deg);
    opacity: 0.9;
  }
  100% {
    filter: hue-rotate(360deg);
    opacity: 0.7;
  }
}

.footer-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary-light);
}

.dark .footer-title {
  color: var(--text-primary-dark);
}

.footer-description {
  font-size: 0.875rem;
  color: var(--text-secondary-light);
  margin-top: 0.5rem;
  line-height: 1.5;
}

.dark .footer-description {
  color: var(--text-secondary-dark);
}

.footer-heading {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary-light);
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-light);
}

.dark .footer-heading {
  color: var(--text-primary-dark);
  border-bottom-color: var(--border-dark);
}

.footer-links,
.footer-contact {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.footer-links li,
.footer-contact li {
  margin-bottom: 0.75rem;
  font-size: 0.875rem;
  color: var(--text-secondary-light);
}

.dark .footer-links li,
.dark .footer-contact li {
  color: var(--text-secondary-dark);
}

.footer-link {
  color: var(--text-secondary-light);
  text-decoration: none;
  transition: color 0.2s ease;
  display: flex;
  align-items: center;
}

@media (max-width: 768px) {
  .footer-link {
    padding: 0.75rem 0;
  }
}

.dark .footer-link {
  color: var(--text-secondary-dark);
}

.footer-link:hover {
  color: #3b82f6;
}

.footer-stats {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.footer-stat-item {
  display: flex;
  align-items: center;
}

.stat-icon {
  height: 2rem;
  width: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  margin-right: 0.75rem;
  color: white;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-label {
  font-size: 0.75rem;
  color: var(--text-secondary-light);
}

.dark .stat-label {
  color: var(--text-secondary-dark);
}

.stat-value {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary-light);
}

.dark .stat-value {
  color: var(--text-primary-dark);
}

.footer-bottom {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dark .footer-bottom {
  border-top-color: var(--border-dark);
}

.copyright {
  font-size: 0.875rem;
  color: var(--text-secondary-light);
}

.dark .copyright {
  color: var(--text-secondary-dark);
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-link {
  height: 2rem;
  width: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: var(--bg-secondary-light);
  color: var(--text-secondary-light);
  transition: all 0.2s ease;
}

.dark .social-link {
  background-color: var(--bg-primary-dark);
  color: var(--text-secondary-dark);
}

.social-link:hover {
  background-color: #3b82f6;
  color: white;
  transform: translateY(-2px);
}

/* Chart container styles */
.chart-height {
  height: 300px;
  position: relative;
  width: 100%;
}

/* Ensure charts are visible */
.chart-height canvas {
  display: block;
  width: 100% !important;
  height: 100% !important;
}

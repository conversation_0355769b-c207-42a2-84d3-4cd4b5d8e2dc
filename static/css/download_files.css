/* Utility Classes */
.flash-messages {
  margin-bottom: 1rem;
}

.alert {
  padding: 1rem;
  margin-bottom: 1rem;
  border-radius: 0.375rem;
}

.alert-info {
  background-color: #eff6ff;
  border: 1px solid #bfdbfe;
  color: #1e40af;
}

/* Button Transitions */
button {
  transition: all 0.2s ease-in-out;
}

/* Print Dialog Customization */
.print-dialog {
  max-width: 500px;
  margin: 0 auto;
}

/* Loading Spinner */
.spinner {
  border: 3px solid #f3f3f3;
  border-radius: 50%;
  border-top: 3px solid #3498db;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Responsive Adjustments */
@media (max-width: 640px) {
  .grid-cols-1 {
    grid-template-columns: 1fr;
  }

  .fixed.bottom-0 {
    position: static;
    margin-top: 2rem;
  }
}

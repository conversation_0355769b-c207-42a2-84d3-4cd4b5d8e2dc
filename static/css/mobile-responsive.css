/* Mobile-First Responsive Design for Talaria Dashboard */
/* Progressive Web App optimizations and touch-friendly interfaces */

/* Mobile-First Base Styles */
:root {
  --mobile-header-height: 60px;
  --mobile-nav-height: 70px;
  --mobile-padding: 16px;
  --mobile-margin: 12px;
  --touch-target-size: 44px;
  --mobile-font-size: 16px;
  --mobile-line-height: 1.5;
}

/* Base Mobile Layout */
@media (max-width: 768px) {
  body {
    font-size: var(--mobile-font-size);
    line-height: var(--mobile-line-height);
    padding-bottom: var(--mobile-nav-height);
  }

  /* Mobile Header */
  .mobile-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--mobile-header-height);
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--glass-border);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--mobile-padding);
  }

  .mobile-header .logo {
    height: 32px;
    width: auto;
  }

  .mobile-header .header-actions {
    display: flex;
    gap: 8px;
  }

  .mobile-header .header-btn {
    width: var(--touch-target-size);
    height: var(--touch-target-size);
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #374151;
    transition: all 0.2s ease;
  }

  .mobile-header .header-btn:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.2);
  }

  /* Mobile Navigation */
  .mobile-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: var(--mobile-nav-height);
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border-top: 1px solid var(--glass-border);
    z-index: 1000;
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 8px 0;
  }

  .mobile-nav .nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-width: var(--touch-target-size);
    min-height: var(--touch-target-size);
    text-decoration: none;
    color: #6b7280;
    transition: all 0.2s ease;
    border-radius: 12px;
    padding: 4px 8px;
  }

  .mobile-nav .nav-item.active {
    color: #4f46e5;
    background: rgba(79, 70, 229, 0.1);
  }

  .mobile-nav .nav-item:active {
    transform: scale(0.95);
  }

  .mobile-nav .nav-icon {
    font-size: 20px;
    margin-bottom: 2px;
  }

  .mobile-nav .nav-label {
    font-size: 10px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  /* Main Content Adjustments */
  .main-content {
    margin-top: var(--mobile-header-height);
    padding: var(--mobile-padding);
    min-height: calc(100vh - var(--mobile-header-height) - var(--mobile-nav-height));
  }

  /* Mobile Dashboard Cards */
  .dashboard-card {
    margin-bottom: var(--mobile-margin);
    padding: var(--mobile-padding);
    border-radius: 16px;
  }

  .dashboard-card h3 {
    font-size: 1rem;
    margin-bottom: 12px;
  }

  .metric-counter {
    font-size: 2rem;
  }

  /* Mobile Tables */
  .mobile-table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border-radius: 12px;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
  }

  .mobile-table {
    width: 100%;
    min-width: 600px; /* Ensure horizontal scroll for complex tables */
  }

  .mobile-table th,
  .mobile-table td {
    padding: 12px 8px;
    font-size: 14px;
  }

  /* Mobile Forms */
  .mobile-form {
    padding: var(--mobile-padding);
  }

  .mobile-form .form-group {
    margin-bottom: var(--mobile-margin);
  }

  .mobile-form label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    font-size: 14px;
  }

  .mobile-form input,
  .mobile-form select,
  .mobile-form textarea {
    width: 100%;
    min-height: var(--touch-target-size);
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    border-radius: 12px;
    font-size: var(--mobile-font-size);
    background: rgba(255, 255, 255, 0.9);
    transition: all 0.2s ease;
  }

  .mobile-form input:focus,
  .mobile-form select:focus,
  .mobile-form textarea:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
  }

  /* Mobile Buttons */
  .mobile-btn {
    min-height: var(--touch-target-size);
    padding: 12px 24px;
    border-radius: 12px;
    font-size: var(--mobile-font-size);
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  .mobile-btn:active {
    transform: scale(0.98);
  }

  .mobile-btn-primary {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
  }

  .mobile-btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: #374151;
    border: 1px solid #d1d5db;
  }

  /* Mobile Modals */
  .mobile-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding: 0;
  }

  .mobile-modal-content {
    background: white;
    border-radius: 20px 20px 0 0;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    animation: slideUpModal 0.3s ease-out;
  }

  @keyframes slideUpModal {
    from {
      transform: translateY(100%);
    }
    to {
      transform: translateY(0);
    }
  }

  .mobile-modal-header {
    position: sticky;
    top: 0;
    background: white;
    padding: 20px var(--mobile-padding) 16px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .mobile-modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
  }

  .mobile-modal-close {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #f3f4f6;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
  }

  .mobile-modal-body {
    padding: var(--mobile-padding);
  }

  /* Mobile Search */
  .mobile-search {
    position: relative;
    margin-bottom: var(--mobile-margin);
  }

  .mobile-search input {
    width: 100%;
    height: var(--touch-target-size);
    padding: 12px 16px 12px 48px;
    border: 1px solid #d1d5db;
    border-radius: 22px;
    font-size: var(--mobile-font-size);
    background: rgba(255, 255, 255, 0.9);
  }

  .mobile-search .search-icon {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
    font-size: 18px;
  }

  /* Mobile Filters */
  .mobile-filters {
    display: flex;
    gap: 8px;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    padding: 8px 0;
    margin-bottom: var(--mobile-margin);
  }

  .mobile-filter-chip {
    flex-shrink: 0;
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid #d1d5db;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    text-decoration: none;
    transition: all 0.2s ease;
  }

  .mobile-filter-chip.active {
    background: #4f46e5;
    color: white;
    border-color: #4f46e5;
  }

  /* Mobile Cards Grid */
  .mobile-cards-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--mobile-margin);
  }

  .mobile-card {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    padding: var(--mobile-padding);
    transition: all 0.2s ease;
  }

  .mobile-card:active {
    transform: scale(0.98);
  }

  /* Mobile Tabs */
  .mobile-tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 4px;
    margin-bottom: var(--mobile-margin);
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .mobile-tab {
    flex: 1;
    min-width: 80px;
    padding: 12px 16px;
    text-align: center;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #6b7280;
    text-decoration: none;
    transition: all 0.2s ease;
  }

  .mobile-tab.active {
    background: white;
    color: #4f46e5;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  /* Mobile Floating Action Button */
  .mobile-fab {
    position: fixed;
    bottom: calc(var(--mobile-nav-height) + 16px);
    right: 16px;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    border: none;
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    z-index: 999;
    transition: all 0.2s ease;
  }

  .mobile-fab:active {
    transform: scale(0.95);
  }

  /* Mobile Pull-to-Refresh */
  .mobile-pull-refresh {
    position: relative;
    overflow: hidden;
  }

  .mobile-pull-indicator {
    position: absolute;
    top: -60px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
  }

  .mobile-pull-indicator.active {
    top: 20px;
  }

  /* Mobile Swipe Actions */
  .mobile-swipe-item {
    position: relative;
    overflow: hidden;
    background: white;
    border-radius: 12px;
    margin-bottom: 8px;
  }

  .mobile-swipe-content {
    position: relative;
    z-index: 2;
    background: inherit;
    transition: transform 0.3s ease;
  }

  .mobile-swipe-actions {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    z-index: 1;
  }

  .mobile-swipe-action {
    height: 100%;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 500;
    min-width: 80px;
  }

  .mobile-swipe-action.edit {
    background: #f59e0b;
  }

  .mobile-swipe-action.delete {
    background: #ef4444;
  }

  /* Mobile Loading States */
  .mobile-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #6b7280;
  }

  .mobile-loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #e5e7eb;
    border-top: 3px solid #4f46e5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Mobile Empty States */
  .mobile-empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
  }

  .mobile-empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  .mobile-empty-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: #374151;
  }

  .mobile-empty-description {
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 24px;
  }
}

/* Tablet Adjustments */
@media (min-width: 769px) and (max-width: 1024px) {
  .mobile-cards-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .mobile-nav {
    display: none;
  }

  .main-content {
    margin-top: 0;
    padding-bottom: 0;
  }
}

/* Touch Optimizations */
@media (hover: none) and (pointer: coarse) {
  /* Remove hover effects on touch devices */
  .dashboard-card:hover,
  .mobile-card:hover {
    transform: none;
  }

  /* Increase touch targets */
  button, a, input, select {
    min-height: var(--touch-target-size);
  }
}

/* High DPI Display Optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .mobile-header .logo {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Landscape Orientation */
@media (max-width: 768px) and (orientation: landscape) {
  .mobile-header {
    height: 50px;
  }

  .mobile-nav {
    height: 60px;
  }

  .main-content {
    margin-top: 50px;
  }

  body {
    padding-bottom: 60px;
  }
}

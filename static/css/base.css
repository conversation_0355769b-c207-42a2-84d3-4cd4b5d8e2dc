/*base.css*/

/* Theme Variables */
:root {
  --bg-primary-light: #ffffff;
  --bg-secondary-light: #f3f4f6;
  --text-primary-light: #111827;
  --text-secondary-light: #4b5563;
  --border-light: #e5e7eb;

  --bg-primary-dark: #1a1a1a;
  --bg-secondary-dark: #2d2d2d;
  --text-primary-dark: #ffffff;
  --text-secondary-dark: #9ca3af;
  --border-dark: #4b5563;
}

/* Base Styles */
body {
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  background-repeat: no-repeat;
  min-height: 100vh;
  transition: background-color 0.3s ease;
}

/* Dark Mode Styles */
body.dark {
  background-color: var(--bg-primary-dark);
  color: var(--text-primary-dark);
}

/* Dashboard Card Styles */
.dashboard-card {
  background-color: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Sidebar Core Styles - Mobile Responsive */
.sidebar {
  width: 16rem;
  height: calc(100vh - 4rem);
  display: flex;
  flex-direction: column;
  background-color: white;
  transition: all 0.3s ease;
  overflow: hidden;
  z-index: 40;
}

.sidebar.collapsed {
  width: 4.5rem;
}

.sidebar-nav-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.sidebar-bottom {
  padding: 1rem;
  border-top: 1px solid var(--border-light);
  background: inherit;
}

/* Mobile sidebar adjustments */
@media (max-width: 640px) {
  .sidebar {
    width: 14rem;
  }

  .sidebar.collapsed {
    width: 3.5rem;
  }

  .sidebar-nav-container {
    padding: 0.75rem;
  }

  .sidebar-bottom {
    padding: 0.75rem;
  }
}

/* Navigation Links */
.nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  margin-bottom: 0.25rem;
  border-radius: 0.375rem;
  color: var(--text-secondary-light);
  transition: all 0.2s ease;
}

.nav-link:hover {
  background-color: var(--bg-secondary-light);
  color: var(--text-primary-light);
}

.nav-link.active {
  background-color: #ebf5ff;
  color: #3b82f6;
}

.nav-link i {
  width: 1.5rem;
  margin-right: 0.75rem;
}

/* Section Headers */
.section-header {
  padding: 0 1rem;
  margin: 1.5rem 0 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: #6b7280;
}

/* Dark Mode Adjustments */
.dark .sidebar {
  background-color: var(--bg-secondary-dark);
}

.dark .sidebar-bottom {
  border-color: var(--border-dark);
}

.dark .section-header {
  color: var(--text-secondary-dark);
}

.dark .nav-link {
  color: var(--text-secondary-dark);
}

.dark .nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-primary-dark);
}

.dark .nav-link.active {
  background-color: rgba(59, 130, 246, 0.2);
  color: #60a5fa;
}

/* Additional dark mode text enhancements */
.dark h1,
.dark h2,
.dark h3,
.dark h4,
.dark h5,
.dark h6 {
  color: var(--text-primary-dark);
}

.dark p,
.dark span,
.dark div {
  color: var(--text-secondary-dark);
}

/* Sidebar Collapse Button */
.collapse-btn {
  position: absolute;
  right: -12px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  background-color: #3b82f6;
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 20;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.collapse-btn:hover {
  background-color: #2563eb;
}

.sidebar.collapsed .collapse-btn i {
  transform: rotate(180deg);
}

/* Mobile Adjustments */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: -16rem;
    top: 4rem;
    bottom: 0;
    z-index: 40;
    transition: left 0.3s ease;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .sidebar.expanded {
    left: 0;
  }

  /* Adjust main content when sidebar is expanded */
  .sidebar.expanded + #main-content {
    margin-left: 0;
  }

  /* Make sidebar backdrop visible when sidebar is expanded */
  .sidebar.expanded ~ #sidebar-backdrop {
    display: block;
  }

  /* Improve sidebar text readability on small screens */
  .sidebar-text {
    font-size: 0.875rem;
  }

  /* Adjust nav links for better touch targets */
  .nav-link {
    padding: 0.875rem 1rem;
    margin-bottom: 0.375rem;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .sidebar {
    width: 85%;
    max-width: 16rem;
    left: -100%;
  }

  .sidebar.collapsed {
    width: 3.5rem;
  }

  /* Improve touch targets further */
  .nav-link {
    padding: 1rem;
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-5px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.5;
  }

  50% {
    opacity: 0.2;
  }
}

/* Iridescent Wafer Effect for Talaria Logo */
.logo-iridescent {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.logo-iridescent:hover {
  transform: scale(1.05);
}

.logo-iridescent img {
  position: relative;
  display: block;
  width: 100%;
  height: 100%;
  object-fit: contain;
  object-position: center;
}

.logo-iridescent::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(148, 0, 211, 0.6) 0%,
    /* violet */ rgba(75, 0, 130, 0.6) 15%,
    /* indigo */ rgba(138, 43, 226, 0.6) 30%,
    /* purple */ rgba(255, 20, 147, 0.6) 45%,
    /* pink */ rgba(50, 205, 50, 0.6) 60%,
    /* green */ rgba(255, 215, 0, 0.6) 75%,
    /* gold */ rgba(148, 0, 211, 0.6) 100% /* back to violet */
  );
  mix-blend-mode: color-dodge;
  border-radius: 50%;
  pointer-events: none;
  animation: iridescent-shift 6s linear infinite;
  filter: brightness(1.2) contrast(1.1);
}

/* Animation for the iridescent effect */
@keyframes iridescent-shift {
  0% {
    filter: hue-rotate(0deg);
    opacity: 0.7;
  }

  50% {
    filter: hue-rotate(180deg);
    opacity: 0.9;
  }

  100% {
    filter: hue-rotate(360deg);
    opacity: 0.7;
  }
}

/* Specific styling for the sidebar profile logo */
.sidebar .logo-iridescent::after {
  clip-path: circle(50% at center);
  mix-blend-mode: overlay;
}

/* Specific styling for the login page logo */
.bg-white .logo-iridescent::after {
  clip-path: circle(40% at center);
  mix-blend-mode: screen;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
}

/* Quick Action Buttons */
.btn-quick-action {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background-color: #f3f4f6;
  color: #4b5563;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.btn-quick-action:hover {
  background-color: #e5e7eb;
  color: #1f2937;
}

.dark .btn-quick-action {
  background-color: #374151;
  color: #d1d5db;
}

.dark .btn-quick-action:hover {
  background-color: #4b5563;
  color: #f9fafb;
}

/* Status Badge */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge.active {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.status-badge.pending {
  background-color: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.status-badge.inactive {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/* Notification Badge */
.notification-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  height: 8px;
  width: 8px;
  background-color: #ef4444;
  border-radius: 50%;
  border: 2px solid white;
}

.dark .notification-badge {
  border-color: var(--bg-secondary-dark);
}

/* User Avatar */
.user-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #edf2f7;
  color: #3b82f6;
  border-radius: 9999px;
  font-weight: 600;
}

.dark .user-avatar {
  background-color: #2d3748;
  color: #60a5fa;
}

/* User Dropdown Menu */
#user-dropdown {
  transform-origin: top right;
  transition: transform 0.2s ease-out, opacity 0.2s ease-out;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

#user-dropdown.hidden {
  display: none;
  transform: scale(0.95);
  opacity: 0;
}

#user-dropdown:not(.hidden) {
  display: block;
  transform: scale(1);
  opacity: 1;
}

.dark #user-dropdown {
  background-color: var(--bg-secondary-dark);
  border-color: var(--border-dark);
}

.dark #user-dropdown a {
  color: var(--text-primary-dark);
}

.dark #user-dropdown a:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Logo-specific CSS fix */
.login-logo-container {
  position: relative;
  width: 128px;
  height: 128px;
  margin: 0 auto 1.5rem;
  border-radius: 50%;
  overflow: hidden;
  background: linear-gradient(to right, #e6f0ff, #eee6ff);
  padding: 0.25rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.login-logo-pulse {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    to right,
    rgba(96, 165, 250, 0.2),
    rgba(99, 102, 241, 0.2)
  );
  border-radius: 50%;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.login-logo-image {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.login-logo-image:hover {
  transform: scale(1.05);
}

.login-logo-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 50%;
  padding: 0.5rem;
  background-color: rgba(255, 255, 255, 0.9);
  /*backdrop-filter: blur(4px);*/
}

.login-logo-image::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(148, 0, 211, 0.6) 0%,
    rgba(75, 0, 130, 0.6) 15%,
    rgba(138, 43, 226, 0.6) 30%,
    rgba(255, 20, 147, 0.6) 45%,
    rgba(50, 205, 50, 0.6) 60%,
    rgba(255, 215, 0, 0.6) 75%,
    rgba(148, 0, 211, 0.6) 100%
  );
  mix-blend-mode: color-dodge;
  border-radius: 50%;
  pointer-events: none;
  animation: iridescent-shift 6s linear infinite;
  filter: brightness(1.2) contrast(1.1);
}

.login-logo-shadow {
  position: absolute;
  inset: 0;
  border-radius: 50%;
  box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.5;
  }
  50% {
    opacity: 0.2;
  }
}

@keyframes iridescent-shift {
  0% {
    filter: hue-rotate(0deg);
    opacity: 0.7;
  }
  50% {
    filter: hue-rotate(180deg);
    opacity: 0.9;
  }
  100% {
    filter: hue-rotate(360deg);
    opacity: 0.7;
  }
}

/* 
 * select_lot.css
 * Custom styles for the Label & Packing Slip Generation interface
 */

/* ===== Variables ===== */
:root {
  --primary: #4f46e5;
  --primary-hover: #4338ca;
  --primary-active: #3730a3;
  --primary-focus: rgba(79, 70, 229, 0.25);

  --success: #10b981;
  --success-hover: #059669;
  --success-focus: rgba(16, 185, 129, 0.25);

  --info: #3b82f6;
  --info-hover: #2563eb;
  --info-focus: rgba(59, 130, 246, 0.25);

  --warning: #f59e0b;
  --danger: #ef4444;

  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  --transition-fast: 200ms;
  --transition-normal: 300ms;
  --transition-slow: 500ms;

  --radius-sm: 0.25rem;
  --radius: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;
}

/* ===== Typography ===== */
.gradient-text {
  background: linear-gradient(135deg, #4f46e5, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.help-text {
  font-size: 0.875rem;
  color: var(--gray-500);
}

.dark .help-text {
  color: var(--gray-400);
}

/* ===== Card Styles ===== */
.card-hover {
  transition: transform var(--transition-fast) ease,
    box-shadow var(--transition-fast) ease;
  will-change: transform, box-shadow;
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* ===== Tab Navigation ===== */
.tab-container {
  position: relative;
  border-bottom: 1px solid var(--gray-200);
  background-color: var(--gray-50);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
  overflow: hidden;
}

.dark .tab-container {
  border-bottom: 1px solid var(--gray-700);
  background-color: var(--gray-800);
}

.tabs-nav {
  display: flex;
  position: relative;
}

.tab-button {
  padding: 1rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  color: var(--gray-600);
  background-color: transparent;
  border: none;
  cursor: pointer;
  transition: color var(--transition-fast) ease;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1;
}

.tab-button:hover {
  color: var(--primary);
}

.tab-button.active {
  color: var(--primary);
  font-weight: 600;
}

.dark .tab-button {
  color: var(--gray-400);
}

.dark .tab-button:hover,
.dark .tab-button.active {
  color: var(--primary);
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  height: 3px;
  background-color: var(--primary);
  transition: all var(--transition-normal) ease;
}

/* ===== Tab Content ===== */
.tab-content {
  display: none;
  opacity: 0;
  transform: translateY(8px);
  transition: opacity var(--transition-normal) ease,
    transform var(--transition-normal) ease;
}

.tab-content.active {
  display: block;
  opacity: 1;
  transform: translateY(0);
}

/* ===== Form Elements ===== */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--gray-700);
}

.dark .form-label {
  color: var(--gray-300);
}

.form-select,
.form-input,
.form-textarea {
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  line-height: 1.5;
  color: var(--gray-800);
  background-color: white;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius);
  transition: border-color var(--transition-fast) ease-in-out,
    box-shadow var(--transition-fast) ease-in-out;
}

.form-textarea {
  min-height: 6rem;
  resize: vertical;
}

.form-select:focus,
.form-input:focus,
.form-textarea:focus {
  border-color: var(--primary);
  outline: 0;
  box-shadow: 0 0 0 3px var(--primary-focus);
}

.dark .form-select,
.dark .form-input,
.dark .form-textarea {
  color: var(--gray-100);
  background-color: var(--gray-800);
  border-color: var(--gray-700);
}

.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.input-with-icon i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  z-index: 1;
  pointer-events: none; /* So the icon doesn't interfere with clicking the input */
}

.input-with-icon .form-input {
  padding-left: 40px;
  width: 100%;
}

/* Make sure the placeholder text is properly aligned */
.input-with-icon .form-input::placeholder {
  color: #9ca3af;
  opacity: 1; /* Firefox */
}

/* For Safari/Chrome */
::-webkit-input-placeholder {
  line-height: normal !important;
}

/* For Firefox */
::-moz-placeholder {
  line-height: normal !important;
}

/* For IE/Edge */
:-ms-input-placeholder {
  line-height: normal !important;
}

.date-range {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

/* ===== Buttons ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.25rem;
  text-align: center;
  border-radius: var(--radius);
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast) ease;
}

.btn-primary {
  background-color: var(--primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
}

.btn-primary:focus {
  box-shadow: 0 0 0 3px var(--primary-focus);
}

.btn-success {
  background-color: var(--success);
  color: white;
}

.btn-success:hover {
  background-color: var(--success-hover);
}

.btn-success:focus {
  box-shadow: 0 0 0 3px var(--success-focus);
}

.btn-info {
  background-color: var(--info);
  color: white;
}

.btn-info:hover {
  background-color: var(--info-hover);
}

.btn-info:focus {
  box-shadow: 0 0 0 3px var(--info-focus);
}

/* ===== Responsive Design ===== */
@media (max-width: 640px) {
  .tab-button {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }

  .btn {
    padding: 0.625rem 1.25rem;
  }

  .date-range {
    grid-template-columns: 1fr;
  }
}

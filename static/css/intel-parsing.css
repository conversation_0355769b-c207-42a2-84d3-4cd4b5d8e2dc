/* 
 * intel-parsing.css
 * Styles for the Asana content parser and template management UI
 */

/* Paste area styles */
#asana-paste-section {
  transition: all 0.3s ease;
  margin-bottom: 1.5rem;
  border-radius: 0.5rem;
}

#asana-paste-section.hidden {
  max-height: 0;
  opacity: 0;
  overflow: hidden;
  margin: 0;
  padding: 0;
  pointer-events: none;
}

#asana-paste-area {
  width: 100%;
  padding: 0.75rem;
  border-radius: 0.375rem;
  border: 1px solid #e5e7eb;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
    "Liberation Mono", "Courier New", monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  resize: vertical;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

#asana-paste-area:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

/* Template selection styles */
#template-controls {
  margin-bottom: 1.5rem;
  border-radius: 0.5rem;
}

#template-select {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  border: 1px solid #e5e7eb;
  background-color: #fff;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

#template-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

/* Field highlight animation for updated fields */
@keyframes highlight-field {
  0% {
    background-color: rgba(254, 240, 138, 0.5);
  }
  100% {
    background-color: transparent;
  }
}

.bg-yellow-50 {
  animation: highlight-field 2s ease;
}

/* Button styles */
.parser-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.25rem;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.parser-btn i {
  margin-right: 0.25rem;
}

.parser-btn-primary {
  background-color: #2563eb;
  color: white;
}

.parser-btn-primary:hover {
  background-color: #1d4ed8;
}

.parser-btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.parser-btn-secondary {
  background-color: transparent;
  border: 1px solid #e5e7eb;
  color: #4b5563;
}

.parser-btn-secondary:hover {
  background-color: #f9fafb;
}

.parser-btn-success {
  background-color: #10b981;
  color: white;
}

.parser-btn-success:hover {
  background-color: #059669;
}

.parser-btn-danger {
  background-color: #ef4444;
  color: white;
}

.parser-btn-danger:hover {
  background-color: #dc2626;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  #asana-paste-area {
    background-color: #1f2937;
    border-color: #4b5563;
    color: #e5e7eb;
  }

  #asana-paste-area:focus {
    border-color: #60a5fa;
    box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.3);
  }

  #template-select {
    background-color: #1f2937;
    border-color: #4b5563;
    color: #e5e7eb;
  }

  #template-select:focus {
    border-color: #60a5fa;
    box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.3);
  }

  @keyframes highlight-field-dark {
    0% {
      background-color: rgba(161, 98, 7, 0.3);
    }
    100% {
      background-color: transparent;
    }
  }

  .dark .bg-yellow-50 {
    animation: highlight-field-dark 2s ease;
  }

  .parser-btn-secondary {
    border-color: #4b5563;
    color: #9ca3af;
  }

  .parser-btn-secondary:hover {
    background-color: #374151;
  }
}

/* Helper text */
.helper-text {
  font-size: 0.875rem;
  color: #6b7280;
}

.helper-text i {
  margin-right: 0.25rem;
}

/* Section spacing */
.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

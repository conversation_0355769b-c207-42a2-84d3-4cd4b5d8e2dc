/* Base Styles */
:root {
  --header-height: 64px;
  --search-height: 76px;
  --pagination-height: 56px;
  --table-padding: 32px;
}

/* Animation Styles */
.highlight-match {
  animation: highlightFade 2s ease-out;
}

@keyframes highlightFade {
  from {
    background-color: rgba(59, 130, 246, 0.1);
  }

  to {
    background-color: transparent;
  }
}

/* Enhanced Scrollbar Styles */
.custom-scrollbar {
  overflow-x: auto;
  -ms-overflow-style: -ms-autohiding-scrollbar;
}

.custom-scrollbar::-webkit-scrollbar,
.overflow-x-auto::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track,
.overflow-x-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb,
.overflow-x-auto::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover,
.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Table Layout Styles */
#table-wrapper {
  transition: max-height 0.5s ease, opacity 0.3s ease, margin 0.3s ease,
    padding 0.3s ease;
  max-height: 1000px;
  opacity: 1;
  overflow-y: auto;
  display: block;
  position: relative;
  z-index: 1;
}

/* Table Layout Styles */
#table-wrapper.collapsed {
  max-height: 0 !important;
  opacity: 0 !important;
  overflow: hidden !important;
  pointer-events: none !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  visibility: collapse !important;
  position: absolute !important;
  z-index: -1 !important;
}

#toggle-table {
  position: relative;
  z-index: 5;
}

/* Table Container Styles */
.overflow-x-auto {
  transition: max-height 0.5s ease;
}

/* Enhanced Table Header Styles */
thead {
  position: sticky;
  top: 0;
  z-index: 10;
  background: inherit;
}

thead::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: -1px;
  height: 1px;
  background: rgba(0, 0, 0, 0.1);
}

/* Table Cell Styles */
td {
  white-space: nowrap;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Selection Styles */
.selected-row {
  background-color: rgba(59, 130, 246, 0.1);
}

/* Status Badge Styles */
.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

/* Toggle Button Styles */
#toggle-icon {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: inline-block;
}

#toggle-icon.rotated {
  transform: rotate(180deg);
}

/* Form Field Styles */
input[data-prefilled="true"] {
  background-color: rgba(243, 244, 246, 0.5);
}

.form-loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Loading State Styles */
.loading-overlay {
  position: absolute;
  inset: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 20;
}

/* Sort Indicator Styles */
.sort-indicator {
  display: inline-block;
  margin-left: 0.5rem;
  transition: transform 0.2s ease;
}

.sort-indicator.asc {
  transform: rotate(180deg);
}

/* Pagination Styles */
.pagination-btn {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  border: 1px solid #e5e7eb;
  background-color: #ffffff;
  cursor: pointer;
}

.pagination-btn:hover {
  background-color: #f3f4f6;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-number {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  cursor: pointer;
}

.page-number:hover {
  background-color: #f3f4f6;
}

.page-number.current {
  background-color: #2563eb;
  color: #ffffff;
}

.page-number.current:hover {
  background-color: #1d4ed8;
}

/* Animation for smooth collapsing/expanding */
@keyframes collapse {
  from {
    max-height: 1000px;
    opacity: 1;
  }

  to {
    max-height: 0;
    opacity: 0;
  }
}

@keyframes expand {
  from {
    max-height: 0;
    opacity: 0;
  }

  to {
    max-height: 1000px;
    opacity: 1;
  }
}

/* Dark Mode Styles */
@media (prefers-color-scheme: dark) {
  thead,
  th {
    background: #1f2937;
  }

  .loading-overlay {
    background-color: rgba(31, 41, 55, 0.8);
  }

  .pagination-btn {
    background-color: #374151;
    border-color: #4b5563;
    color: #e5e7eb;
  }

  .pagination-btn:hover {
    background-color: #4b5563;
  }

  .page-number {
    color: #e5e7eb;
  }

  .page-number:hover {
    background-color: #374151;
  }
}

/* 
 * This CSS will hide the search bar, filters button, and status filters
 * Add this to your stylesheet or as a <style> tag in the head of your document
 */

/* Hide search bar and its container */
input[placeholder="Search wafers..."],
#table-search,
.relative.flex-1,
.container-fluid .row:first-child {
  display: none !important;
}

/* Hide filters button */
button:has(.fa-filter),
.filters-button,
button.px-4.py-2.text-sm.font-medium:has(i.fas.fa-filter),
button[title="Filters"] {
  display: none !important;
}

/* Hide status filter buttons */
button[data-quick-filter],
.flex.gap-2.mb-4,
#filter-row,
div:has(> button[data-quick-filter]) {
  display: none !important;
}

/* Hide any other related containers */
.px-6.py-3.bg-gray-50.dark\:bg-gray-700\/50,
#filter-row,
.bg-gray-50.dark\:bg-gray-700\/50.border-t.border-gray-200.dark\:border-gray-700 {
  display: none !important;
}

/* Hide the parent container of Available Wafers, In Transit, Received buttons */
.flex.items-center.gap-4 + .flex.items-center.gap-2,
div:has(> button[data-quick-filter="available"]),
div:has(> button[data-quick-filter="in_transit"]),
div:has(> button[data-quick-filter="received"]) {
  display: none !important;
}

/* Remove spacing that might be left behind */
.container.mx-auto.px-4.py-6 > div:empty {
  margin: 0 !important;
  padding: 0 !important;
  height: 0 !important;
}

/* Basic responsive utilities for Talaria Dashboard 
 * Add these selectively to your components for better mobile experience
 * Without breaking existing functionality
 */

/* Responsive table container - apply to div wrapping tables */
.table-responsive {
  display: block;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* Responsive images */
.img-fluid {
  max-width: 100%;
  height: auto;
}

/* Responsive containers */
.container-sm {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 576px) {
  .container-sm {
    max-width: 540px;
  }
}

@media (min-width: 768px) {
  .container-sm {
    max-width: 720px;
  }
}

@media (min-width: 992px) {
  .container-sm {
    max-width: 960px;
  }
}

/* Display utilities for responsive layouts */
.d-none {
  display: none !important;
}

.d-block {
  display: block !important;
}

@media (min-width: 576px) {
  .d-sm-none {
    display: none !important;
  }
  .d-sm-block {
    display: block !important;
  }
}

@media (min-width: 768px) {
  .d-md-none {
    display: none !important;
  }
  .d-md-block {
    display: block !important;
  }
}

/* Improved touch targets for mobile */
@media (max-width: 768px) {
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Text truncation for tables and labels */
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Spacing utilities for mobile */
@media (max-width: 768px) {
  .m-0-mobile {
    margin: 0 !important;
  }
  .mt-2-mobile {
    margin-top: 0.5rem !important;
  }
  .mb-2-mobile {
    margin-bottom: 0.5rem !important;
  }
  .p-2-mobile {
    padding: 0.5rem !important;
  }
}

/* Card adjustments for mobile */
@media (max-width: 768px) {
  .card-mobile {
    margin-bottom: 1rem;
    border-radius: 0.375rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .card-header-mobile {
    padding: 0.75rem 1rem;
    background-color: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
  }

  .card-body-mobile {
    padding: 1rem;
  }

  /* Stack buttons on mobile */
  .btn-stack-mobile {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .btn-stack-mobile > button,
  .btn-stack-mobile > a {
    width: 100%;
    text-align: center;
    justify-content: center;
  }
}

/* Make images not overflow on mobile */
img {
  max-width: 100%;
  height: auto;
}

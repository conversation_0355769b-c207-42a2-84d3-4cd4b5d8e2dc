[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
flask-sqlalchemy = "*"
flask-login = "*"
python-dotenv = "*"
flask = "*"
flask-wtf = "*"
google-auth-oauthlib = "*"
google-auth = "*"
google-api-python-client = "*"
pandas = "*"
plotly = "*"
pillow = "*"
qrcode = "*"
reportlab = "*"
asana = "*"
requests = "*"
python-dateutil = "*"
typing = "*"
abacusai = "*"

[dev-packages]

[requires]
python_version = "3.13"

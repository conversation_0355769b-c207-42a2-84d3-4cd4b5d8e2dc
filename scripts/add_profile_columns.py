#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to add profile_image and remember_token columns to the users table
"""

import os
import sys
import psycopg2
import psycopg2.extras

# Add the parent directory to the path so we can import from the project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_config import get_db_connection
from core.auth.user_manager import get_auth_db_connection


def add_profile_columns():
    """Add profile_image and remember_token columns to the users table"""
    conn = None
    cursor = None
    try:
        # Connect to the database
        conn = get_auth_db_connection()
        cursor = conn.cursor()
        
        # Check if the columns exist
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'users' AND column_name IN ('profile_image', 'remember_token')
        """)
        existing_columns = [row['column_name'] for row in cursor.fetchall()]
        
        # Add profile_image column if it doesn't exist
        if 'profile_image' not in existing_columns:
            print("Adding profile_image column...")
            cursor.execute("""
                ALTER TABLE users ADD COLUMN profile_image VARCHAR(255)
            """)
            print("profile_image column added successfully")
        else:
            print("profile_image column already exists")
        
        # Add remember_token column if it doesn't exist
        if 'remember_token' not in existing_columns:
            print("Adding remember_token column...")
            cursor.execute("""
                ALTER TABLE users ADD COLUMN remember_token VARCHAR(255)
            """)
            print("remember_token column added successfully")
        else:
            print("remember_token column already exists")
        
        # Commit the changes
        conn.commit()
        print("Database update completed successfully")
        
    except Exception as e:
        print(f"Error updating database: {e}")
        if conn:
            conn.rollback()
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()


if __name__ == "__main__":
    add_profile_columns()

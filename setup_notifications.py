#!/usr/bin/env python3
"""
Setup script for the bell notification system.
This script will create the notifications table and insert sample data.
"""

import os
import sys

import psycopg2
from psycopg2.extras import RealDictCursor


def get_database_config():
    """Get database configuration from environment variables."""
    # Try to load from database/.env file
    env_file = os.path.join(os.path.dirname(__file__), "database", ".env")
    if os.path.exists(env_file):
        with open(env_file, "r") as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith("#") and "=" in line:
                    key, value = line.split("=", 1)
                    os.environ[key] = value

    return {
        "host": os.getenv("AUTH_DB_HOST", "localhost"),
        "port": os.getenv("AUTH_DB_PORT", "5432"),
        "database": os.getenv("AUTH_DB_NAME", "shipment_fr"),
        "user": os.getenv("AUTH_DB_USER", "postgres"),
        "password": os.getenv("AUTH_DB_PASSWORD", ""),
    }


def create_notifications_table(cursor):
    """Create the notifications table and related objects."""

    # Read the SQL file
    sql_file = os.path.join(
        os.path.dirname(__file__), "database", "create_notifications_table.sql"
    )

    if not os.path.exists(sql_file):
        print(f"Error: SQL file not found at {sql_file}")
        return False

    with open(sql_file, "r") as f:
        sql_content = f.read()

    try:
        # Execute the SQL
        cursor.execute(sql_content)
        print("✓ Notifications table and related objects created successfully")
        return True
    except Exception as e:
        print(f"✗ Error creating notifications table: {str(e)}")
        return False


def verify_setup(cursor):
    """Verify that the setup was successful."""
    try:
        # Check if table exists
        cursor.execute(
            """
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'notifications'
            );
        """
        )

        result = cursor.fetchone()
        table_exists = result[0] if result else False

        if table_exists:
            # Check if sample data exists
            cursor.execute("SELECT COUNT(*) FROM notifications;")
            result = cursor.fetchone()
            count = result[0] if result else 0
            print(f"✓ Notifications table exists with {count} sample notifications")

            # Check if view exists
            cursor.execute(
                """
                SELECT EXISTS (
                    SELECT FROM information_schema.views 
                    WHERE table_schema = 'public' 
                    AND table_name = 'notification_summary'
                );
            """
            )

            result = cursor.fetchone()
            view_exists = result[0] if result else False
            if view_exists:
                print("✓ Notification summary view created successfully")
            else:
                print("✗ Notification summary view not found")
                return False

            # Check if extra_data column exists (not metadata)
            cursor.execute(
                """
                SELECT EXISTS (
                    SELECT FROM information_schema.columns
                    WHERE table_schema = 'public'
                    AND table_name = 'notifications'
                    AND column_name = 'extra_data'
                );
            """
            )

            result = cursor.fetchone()
            extra_data_exists = result[0] if result else False
            if extra_data_exists:
                print(
                    "✓ extra_data column exists (metadata column renamed successfully)"
                )
            else:
                print("✗ extra_data column not found")
                return False

            return True
        else:
            print("✗ Notifications table was not created")
            return False

    except Exception as e:
        print(f"✗ Error verifying setup: {str(e)}")
        return False


def main():
    """Main setup function."""
    print("🔔 Setting up Bell Notification System...")
    print("=" * 50)

    # Get database configuration
    db_config = get_database_config()
    print(
        f"Connecting to database: {db_config['host']}:{db_config['port']}/{db_config['database']}"
    )

    try:
        # Connect to database
        conn = psycopg2.connect(**db_config, cursor_factory=RealDictCursor)
        cursor = conn.cursor()

        print("✓ Connected to database successfully")

        # Create notifications table
        if create_notifications_table(cursor):
            # Commit the changes
            conn.commit()
            print("✓ Changes committed to database")

            # Verify setup
            if verify_setup(cursor):
                print("\n🎉 Bell notification system setup completed successfully!")
                print("\nNext steps:")
                print("1. Run your Flask application")
                print("2. Log in to see the bell notification icon")
                print("3. Send an email through the app to see notifications in action")
                print("4. Click the bell icon to view your notifications")
            else:
                print("\n⚠️  Setup completed but verification failed")
                return 1
        else:
            print("\n❌ Setup failed")
            return 1

    except psycopg2.Error as e:
        print(f"✗ Database error: {str(e)}")
        print("\nPlease check your database configuration in database/.env")
        return 1
    except Exception as e:
        print(f"✗ Unexpected error: {str(e)}")
        return 1
    finally:
        if "conn" in locals():
            conn.close()

    return 0


if __name__ == "__main__":
    sys.exit(main())

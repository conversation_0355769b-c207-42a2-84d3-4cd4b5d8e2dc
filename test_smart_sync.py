#!/usr/bin/env python3
"""
Test Smart Sync Features
Quick test to verify the Smart Sync implementation works
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all Smart Sync modules can be imported"""
    print("🧪 Testing Smart Sync imports...")
    
    try:
        from core.services.icarium_monitor_service import IcariumMonitorService
        print("✅ IcariumMonitorService imported successfully")
    except Exception as e:
        print(f"❌ Failed to import IcariumMonitorService: {e}")
        return False
    
    try:
        from core.services.smart_sync_engine import SmartSyncEngine
        print("✅ SmartSyncEngine imported successfully")
    except Exception as e:
        print(f"❌ Failed to import SmartSyncEngine: {e}")
        return False
    
    try:
        from core.services.scheduler_service import get_scheduler_service
        print("✅ SchedulerService imported successfully")
    except Exception as e:
        print(f"❌ Failed to import SchedulerService: {e}")
        return False
    
    try:
        from routes.icarium_integration_routes import icarium_bp
        print("✅ Icarium routes imported successfully")
    except Exception as e:
        print(f"❌ Failed to import Icarium routes: {e}")
        return False
    
    return True

def test_smart_sync_engine():
    """Test the Smart Sync Engine functionality"""
    print("\n🧠 Testing Smart Sync Engine...")
    
    try:
        from core.services.smart_sync_engine import SmartSyncEngine
        
        engine = SmartSyncEngine()
        print("✅ SmartSyncEngine instance created")
        
        # Test with no data (should return empty recommendations)
        result = engine.generate_smart_recommendations(hours_back=24)
        
        if result['success']:
            print(f"✅ Smart recommendations generated: {len(result['recommendations'])} recommendations")
            print(f"   - Total wafers: {result['total_wafers']}")
            print(f"   - Lots affected: {result['lots_affected']}")
            print(f"   - Locations affected: {result['locations_affected']}")
            
            if result['recommendations']:
                print("   - Sample recommendation:")
                rec = result['recommendations'][0]
                print(f"     * {rec['title']}: {rec['description']}")
                print(f"     * Priority: {rec['priority_score']:.2f}")
                print(f"     * Impact: {rec['impact']}")
        else:
            print(f"⚠️ Smart recommendations returned error: {result.get('error')}")
        
        return True
        
    except Exception as e:
        print(f"❌ SmartSyncEngine test failed: {e}")
        return False

def test_icarium_monitor():
    """Test the Icarium Monitor Service"""
    print("\n📡 Testing Icarium Monitor Service...")
    
    try:
        from core.services.icarium_monitor_service import IcariumMonitorService
        
        monitor = IcariumMonitorService()
        print("✅ IcariumMonitorService instance created")
        
        # Test check new wafers (should work even with no data)
        result = monitor.check_new_wafers(hours_back=24)
        
        if result['success']:
            print(f"✅ New wafer check completed")
            print(f"   - Summary: {result['summary']}")
            print(f"   - Unsynced wafers: {len(result.get('unsynced_wafers', []))}")
        else:
            print(f"⚠️ New wafer check returned error: {result.get('error')}")
        
        # Test sync recommendations
        rec_result = monitor.get_sync_recommendations()
        
        if rec_result['success']:
            print(f"✅ Sync recommendations generated: {len(rec_result['recommendations'])} recommendations")
        else:
            print(f"⚠️ Sync recommendations returned error: {rec_result.get('error')}")
        
        return True
        
    except Exception as e:
        print(f"❌ IcariumMonitorService test failed: {e}")
        return False

def test_scheduler_service():
    """Test the Scheduler Service"""
    print("\n⏰ Testing Scheduler Service...")
    
    try:
        from core.services.scheduler_service import get_scheduler_service
        
        scheduler = get_scheduler_service()
        print("✅ SchedulerService instance created")
        
        # Test getting task status
        tasks = scheduler.get_task_status()
        print(f"✅ Scheduler tasks loaded: {len(tasks)} tasks")
        
        for task in tasks:
            print(f"   - {task['name']}: {task['description']}")
            print(f"     * Schedule: {task['schedule_type']} at {task['schedule_time']}")
            print(f"     * Enabled: {task['enabled']}")
            print(f"     * Run count: {task['run_count']}")
        
        return True
        
    except Exception as e:
        print(f"❌ SchedulerService test failed: {e}")
        return False

def test_file_structure():
    """Test that all required files exist"""
    print("\n📁 Testing file structure...")
    
    required_files = [
        'core/services/icarium_monitor_service.py',
        'core/services/smart_sync_engine.py', 
        'core/services/scheduler_service.py',
        'routes/icarium_integration_routes.py',
        'static/js/icarium-sync-widget.js',
        'static/js/smart-sync-dashboard.js',
        'templates/select_lot.html',
        'templates/home.html'
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - MISSING")
            all_exist = False
    
    return all_exist

def main():
    """Run all tests"""
    print("🚀 Smart Sync Feature Test Suite")
    print("=" * 50)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Module Imports", test_imports),
        ("Smart Sync Engine", test_smart_sync_engine),
        ("Icarium Monitor", test_icarium_monitor),
        ("Scheduler Service", test_scheduler_service)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Smart Sync features are ready to use.")
        print("\n📋 Next steps:")
        print("1. Start the app: python3 app.py")
        print("2. Go to Inventory → Smart Sync tab")
        print("3. Check the dashboard for the Icarium Sync Widget")
        print("4. Test the API endpoints:")
        print("   - /api/icarium/smart-sync-recommendations")
        print("   - /api/icarium/check-new-wafers")
        print("   - /api/icarium/scheduler/status")
    else:
        print(f"\n⚠️ {total - passed} tests failed. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

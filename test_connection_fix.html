<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connection Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button { margin: 5px; padding: 10px 15px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Talaria Dashboard Connection Test</h1>
    
    <div>
        <button onclick="testNotificationAPI()">Test Notification API</button>
        <button onclick="testSmartSyncAPI()">Test Smart Sync API</button>
        <button onclick="testAuthCheck()">Test Auth Check</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>
    
    <div id="results"></div>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testNotificationAPI() {
            addResult('Testing notification API...', 'warning');
            
            try {
                const response = await fetch('/api/notifications/unread-count', {
                    method: 'GET',
                    credentials: 'same-origin',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Notification API Success: ${JSON.stringify(data)}`, 'success');
                } else if (response.status === 401 || response.status === 403) {
                    addResult(`🔒 Authentication required (${response.status})`, 'warning');
                } else {
                    addResult(`❌ Notification API Error: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Notification API Exception: ${error.message}`, 'error');
            }
        }

        async function testSmartSyncAPI() {
            addResult('Testing smart sync API...', 'warning');
            
            try {
                const response = await fetch('/api/icarium/smart-sync-recommendations?hours=24', {
                    method: 'GET',
                    credentials: 'same-origin',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Smart Sync API Success: Found ${data.recommendations?.length || 0} recommendations`, 'success');
                } else if (response.status === 401 || response.status === 403) {
                    addResult(`🔒 Authentication required (${response.status})`, 'warning');
                } else {
                    addResult(`❌ Smart Sync API Error: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Smart Sync API Exception: ${error.message}`, 'error');
            }
        }

        async function testAuthCheck() {
            addResult('Testing authentication status...', 'warning');
            
            try {
                const response = await fetch('/api/auth/check', {
                    method: 'GET',
                    credentials: 'same-origin',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.authenticated) {
                        addResult(`✅ User authenticated: ${data.user?.email || 'Unknown'} (${data.user?.role || 'Unknown role'})`, 'success');
                    } else {
                        addResult(`🔒 User not authenticated`, 'warning');
                    }
                } else {
                    addResult(`❌ Auth Check Error: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Auth Check Exception: ${error.message}`, 'error');
            }
        }

        // Auto-run tests on page load
        window.addEventListener('load', function() {
            addResult('🚀 Starting connection tests...', 'warning');
            setTimeout(() => {
                testAuthCheck();
                setTimeout(() => testNotificationAPI(), 1000);
                setTimeout(() => testSmartSyncAPI(), 2000);
            }, 500);
        });
    </script>
</body>
</html>

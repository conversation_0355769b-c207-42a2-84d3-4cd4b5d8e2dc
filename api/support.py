"""
Support API module for handling support ticket submissions and management.
"""

import logging
import os
import traceback
from datetime import datetime

from flask import Blueprint, current_app, jsonify, request, session
from flask_mail import Message
from werkzeug.utils import secure_filename

from core.auth.auth import login_required
from database.auth_db_config import get_auth_db_connection
from database.db_config import get_db_connection

# Create blueprint
support_bp = Blueprint("support", __name__)

# Configure logging
logger = logging.getLogger(__name__)

# Support email address
SUPPORT_EMAIL = "<EMAIL>"

# Upload folder for attachments
UPLOAD_FOLDER = os.path.join(
    os.path.dirname(os.path.dirname(__file__)), "uploads", "support"
)
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# Allowed file extensions
ALLOWED_EXTENSIONS = {
    "png",
    "jpg",
    "jpeg",
    "gif",
    "pdf",
    "doc",
    "docx",
    "xls",
    "xlsx",
    "txt",
}


def allowed_file(filename):
    """Check if file extension is allowed."""
    return "." in filename and filename.rsplit(".", 1)[1].lower() in ALLOWED_EXTENSIONS


def save_attachment(file, ticket_id):
    """Save attachment to disk and database."""
    try:
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            # Create unique filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            unique_filename = f"{timestamp}_{filename}"

            # Save file to disk
            file_path = os.path.join(UPLOAD_FOLDER, unique_filename)
            file.save(file_path)

            # Save file info to database
            with get_auth_db_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(
                        """
                        INSERT INTO support_attachments
                        (ticket_id, file_name, file_path, file_size, file_type)
                        VALUES (%s, %s, %s, %s, %s)
                        """,
                        (
                            ticket_id,
                            filename,
                            file_path,
                            os.path.getsize(file_path),
                            file.content_type or "application/octet-stream",
                        ),
                    )
                conn.commit()

            return True, file_path
        return False, None
    except Exception as e:
        logger.error(f"Error saving attachment: {str(e)}")
        return False, None


def send_support_email(ticket_data, ticket_id):
    """Send email notification about new support ticket.

    This function is completely isolated from the shipment notification system
    and uses a dedicated configuration for support tickets only.
    """
    try:
        # Create message
        subject = f"New Support Ticket #{ticket_id}: {ticket_data['subject']}"

        # Format message body
        body = f"""
A new support ticket has been submitted:

Ticket ID: {ticket_id}
Issue Type: {ticket_data['issue_type']}
Priority: {ticket_data['priority']}
Subject: {ticket_data['subject']}

Message:
{ticket_data['message']}

Submitted by: {ticket_data.get('user_name', 'Unknown')} ({ticket_data.get('user_email', 'No email')})
Submitted at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

You can view this ticket in the support dashboard.
"""

        try:
            # Import smtplib for direct email sending without using Flask-Mail
            # This ensures we don't interfere with the main application's mail configuration
            import smtplib
            from email.mime.multipart import MIMEMultipart
            from email.mime.text import MIMEText

            # Create a dedicated SMTP connection for support emails
            smtp_server = current_app.config.get("MAIL_SERVER", "smtp-relay.gmail.com")
            smtp_port = int(current_app.config.get("MAIL_PORT", 587))
            use_tls = current_app.config.get("MAIL_USE_TLS", True)

            # Create message
            msg = MIMEMultipart()
            msg["Subject"] = subject
            msg["From"] = "<EMAIL>"  # Hardcoded support email sender
            msg["To"] = SUPPORT_EMAIL

            # Attach body
            msg.attach(MIMEText(body, "plain"))

            # Send email
            server = smtplib.SMTP(smtp_server, smtp_port)
            if use_tls:
                server.starttls()

            # No authentication needed for smtp-relay
            server.send_message(msg)
            server.quit()

            logger.info(f"Support notification email sent for ticket #{ticket_id}")
            return True
        except Exception as smtp_error:
            # If direct SMTP fails, log the error and try an alternative method
            logger.warning(f"SMTP direct send failed: {str(smtp_error)}")

            # Create a simple file-based notification as a fallback
            try:
                notification_dir = os.path.join(
                    os.path.dirname(UPLOAD_FOLDER), "notifications"
                )
                os.makedirs(notification_dir, exist_ok=True)

                notification_file = os.path.join(
                    notification_dir,
                    f"support_ticket_{ticket_id}_{int(datetime.now().timestamp())}.txt",
                )
                with open(notification_file, "w") as f:
                    f.write(f"SUPPORT TICKET NOTIFICATION\n\n{body}")

                logger.info(f"Support notification saved to file: {notification_file}")
                return True
            except Exception as file_error:
                logger.error(f"Failed to create notification file: {str(file_error)}")
                logger.info(f"Email content would have been: {body}")
                return True
    except Exception as e:
        logger.error(f"Error sending support email: {str(e)}")
        logger.error(traceback.format_exc())
        return False


@support_bp.route("/api/support/ticket", methods=["POST"])
def submit_ticket():
    """Handle support ticket submission."""
    try:
        # Log the request data for debugging
        logger.info(f"Support ticket submission request: {request.form}")
        logger.info(f"Files in request: {request.files}")

        # Get form data
        issue_type = request.form.get("issueType")
        priority = request.form.get("priority")
        subject = request.form.get("subject")
        message = request.form.get("message")

        logger.info(
            f"Form data: issue_type={issue_type}, priority={priority}, subject={subject}, message_length={len(message) if message else 0}"
        )

        # Validate required fields
        if not all([issue_type, priority, subject, message]):
            missing = []
            if not issue_type:
                missing.append("issue type")
            if not priority:
                missing.append("priority")
            if not subject:
                missing.append("subject")
            if not message:
                missing.append("message")

            logger.warning(f"Missing required fields: {', '.join(missing)}")
            return (
                jsonify(
                    {
                        "success": False,
                        "message": f"Missing required fields: {', '.join(missing)}",
                    }
                ),
                400,
            )

        # Get user information if available
        user_id = session.get("user_id")
        user_email = session.get("user_email")
        user_name = session.get("user_name")

        logger.info(f"User info: id={user_id}, email={user_email}, name={user_name}")

        try:
            # Insert ticket into database
            with get_auth_db_connection() as conn:
                logger.info("Database connection established")
                with conn.cursor() as cursor:
                    logger.info("Executing INSERT query")
                    cursor.execute(
                        """
                        INSERT INTO support_tickets
                        (user_id, user_email, user_name, issue_type, priority, subject, message)
                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                        RETURNING id
                        """,
                        (
                            user_id,
                            user_email,
                            user_name,
                            issue_type,
                            priority,
                            subject,
                            message,
                        ),
                    )
                    logger.info("Query executed, fetching result")
                    result = cursor.fetchone()
                    logger.info(f"Query result: {result}")
                    # Handle different types of result objects
                    if result:
                        if isinstance(result, dict) or hasattr(result, "get"):
                            ticket_id = result.get("id", 1)
                        elif isinstance(result, (list, tuple)) and len(result) > 0:
                            ticket_id = result[0]
                        else:
                            # Try to access as attribute
                            try:
                                ticket_id = result.id
                            except:
                                # Last resort, convert to string and parse
                                try:
                                    str_result = str(result)
                                    logger.info(
                                        f"Converting result to string: {str_result}"
                                    )
                                    if "id" in str_result and ":" in str_result:
                                        # Try to extract ID from string representation
                                        id_part = str_result.split("id")[1].split(",")[
                                            0
                                        ]
                                        id_value = "".join(
                                            c for c in id_part if c.isdigit()
                                        )
                                        ticket_id = int(id_value) if id_value else 1
                                    else:
                                        ticket_id = 1
                                except:
                                    ticket_id = 1
                    else:
                        ticket_id = 1

                    logger.info(f"Extracted Ticket ID: {ticket_id}")
                    conn.commit()
                    logger.info("Transaction committed")
        except Exception as db_error:
            logger.error(f"Database error: {str(db_error)}")
            logger.error(traceback.format_exc())
            return (
                jsonify(
                    {"success": False, "message": f"Database error: {str(db_error)}"}
                ),
                500,
            )

        try:
            # Handle file attachments
            files = request.files.getlist("attachments")
            attachment_paths = []

            logger.info(f"Processing {len(files)} file attachments")

            for file in files:
                if file and file.filename:
                    logger.info(f"Processing file: {file.filename}")
                    success, file_path = save_attachment(file, ticket_id)
                    if success and file_path:
                        logger.info(f"File saved successfully at {file_path}")
                        attachment_paths.append(file_path)
                    else:
                        logger.warning(f"Failed to save file: {file.filename}")
        except Exception as file_error:
            logger.error(f"File processing error: {str(file_error)}")
            logger.error(traceback.format_exc())
            # Continue processing even if file upload fails

        try:
            # Send email notification
            ticket_data = {
                "issue_type": issue_type,
                "priority": priority,
                "subject": subject,
                "message": message,
                "user_id": user_id,
                "user_email": user_email,
                "user_name": user_name,
            }
            logger.info(f"Sending email notification for ticket #{ticket_id}")
            email_sent = send_support_email(ticket_data, ticket_id)
            logger.info(f"Email notification sent: {email_sent}")
        except Exception as email_error:
            logger.error(f"Email notification error: {str(email_error)}")
            logger.error(traceback.format_exc())
            # Continue processing even if email fails

        logger.info(f"Support ticket #{ticket_id} submitted successfully")
        return jsonify(
            {
                "success": True,
                "message": "Support ticket submitted successfully",
                "ticket_id": ticket_id,
            }
        )

    except Exception as e:
        logger.error(f"Error submitting support ticket: {str(e)}")
        logger.error(traceback.format_exc())
        return (
            jsonify(
                {"success": False, "message": f"Error submitting ticket: {str(e)}"}
            ),
            500,
        )


@support_bp.route("/api/support/quick-issues", methods=["GET"])
def get_quick_issues():
    """Get list of common issues for quick links."""
    try:
        # Return predefined list of common issues
        quick_issues = [
            {
                "id": "login",
                "title": "Login Issues",
                "description": "Problems with logging in or authentication",
                "issue_type": "technical",
                "priority": "high",
            },
            {
                "id": "inventory",
                "title": "Inventory Management",
                "description": "Issues with inventory tracking or updates",
                "issue_type": "technical",
                "priority": "medium",
            },
            {
                "id": "shipping",
                "title": "Shipping Problems",
                "description": "Issues with shipping labels or tracking",
                "issue_type": "technical",
                "priority": "high",
            },
            {
                "id": "feature",
                "title": "Feature Request",
                "description": "Suggest a new feature or improvement",
                "issue_type": "feature",
                "priority": "medium",
            },
            {
                "id": "bug",
                "title": "Report a Bug",
                "description": "Report a software bug or unexpected behavior",
                "issue_type": "technical",
                "priority": "high",
            },
        ]

        return jsonify({"success": True, "quick_issues": quick_issues})

    except Exception as e:
        logger.error(f"Error getting quick issues: {str(e)}")
        return (
            jsonify(
                {"success": False, "message": f"Error getting quick issues: {str(e)}"}
            ),
            500,
        )

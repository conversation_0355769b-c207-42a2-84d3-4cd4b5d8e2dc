# api.py
from flask import Blueprint, jsonify, request, abort, session
import pandas as pd  # If you need to access the Excel data
import functools
import os

api_blueprint = Blueprint("api", __name__)

# Use a static API Key that will remain consistent
API_KEY = "b051547fdc8b2ddd138eb0a7f4dc0a9e"  # Your fixed API Key


# API_KEY = secrets.token_hex(16)  # Generate a 32-character hexadecimal API key
# print(f"Your API Key: {API_KEY}")


def require_api_key(func):
    @functools.wraps(func)
    def decorated_function(*args, **kwargs):
        api_key = request.headers.get("X-API-Key")
        if api_key != API_KEY:
            abort(401, description="Unauthorized: Invalid API Key")
        return func(*args, **kwargs)

    return decorated_function


@api_blueprint.route("/current_user", methods=["GET"])
def get_current_user():
    try:
        if "user_email" in session:
            user_data = {
                "email": session.get("user_email"),
                "role": session.get("user_role"),
                "name": session.get("user_name"),
                "id": session.get("user_id")
            }
            return jsonify({"success": True, "user": user_data})
        else:
            return jsonify({"success": False, "message": "User not authenticated"}), 401
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500


@api_blueprint.route("/wafers", methods=["GET"])
@require_api_key
def get_wafers():
    try:
        # Verify the Excel file exists and is loaded correctly
        excel_file_path = "/Users/<USER>/Documents/Wafer Inventory LGT FR.xlsx"
        if not os.path.exists(excel_file_path):
            raise FileNotFoundError(f"Excel file not found: {excel_file_path}")

        # Read Excel file into a DataFrame
        df = pd.read_excel(excel_file_path)

        if not isinstance(df, pd.DataFrame):
            raise ValueError("Loaded data is not a valid DataFrame")

        # Convert the DataFrame to a dictionary of records
        wafer_data = df.to_dict("records")
        return jsonify({"status": "success", "data": wafer_data}), 200

    except FileNotFoundError as fnf_error:
        return jsonify({"status": "error", "message": str(fnf_error)}), 500
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

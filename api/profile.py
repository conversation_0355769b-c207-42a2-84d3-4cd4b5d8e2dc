"""
Profile API Module
This module provides API endpoints for user profile management.
"""

import os
import uuid
import secrets
from flask import Blueprint, request, jsonify, session, current_app
from werkzeug.utils import secure_filename
from core.auth.auth import login_required
from core.auth.user_manager import User<PERSON>anager

# Create a Blueprint for profile API
profile_bp = Blueprint("profile_api", __name__, url_prefix="/api/profile")

# Configure upload folder
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static", "uploads", "profile_images")
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# Allowed file extensions
ALLOWED_EXTENSIONS = {"png", "jpg", "jpeg", "gif"}


def allowed_file(filename):
    """Check if the file extension is allowed"""
    return "." in filename and filename.rsplit(".", 1)[1].lower() in ALLOWED_EXTENSIONS


@profile_bp.route("/upload-image", methods=["POST"])
@login_required
def upload_profile_image():
    """Upload a profile image for the current user"""
    try:
        # Check if the post request has the file part
        if "profile_image" not in request.files:
            return jsonify({"success": False, "message": "No file part"}), 400
            
        file = request.files["profile_image"]
        
        # If user does not select file, browser also
        # submit an empty part without filename
        if file.filename == "":
            return jsonify({"success": False, "message": "No selected file"}), 400
            
        if file and allowed_file(file.filename):
            # Generate a secure filename with UUID to avoid collisions
            filename = secure_filename(file.filename)
            file_extension = filename.rsplit(".", 1)[1].lower()
            unique_filename = f"{uuid.uuid4().hex}.{file_extension}"
            
            # Save the file
            file_path = os.path.join(UPLOAD_FOLDER, unique_filename)
            file.save(file_path)
            
            # Update the user's profile image in the database
            user_id = session.get("user_id")
            if UserManager.update_profile_image(user_id, unique_filename):
                # Update the session with the new profile image
                return jsonify({
                    "success": True, 
                    "message": "Profile image updated successfully",
                    "filename": unique_filename
                })
            else:
                # If database update fails, delete the uploaded file
                os.remove(file_path)
                return jsonify({"success": False, "message": "Failed to update profile image in database"}), 500
        else:
            return jsonify({
                "success": False, 
                "message": f"Invalid file type. Allowed types: {', '.join(ALLOWED_EXTENSIONS)}"
            }), 400
            
    except Exception as e:
        current_app.logger.error(f"Error uploading profile image: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500


@profile_bp.route("/remember", methods=["POST"])
@login_required
def set_remember_me():
    """Enable or disable remember me for the current user"""
    try:
        data = request.json
        enabled = data.get("enabled", False)
        user_id = session.get("user_id")
        
        if enabled:
            # Generate a secure token
            token = secrets.token_hex(32)
            
            # Save the token in the database
            if UserManager.set_remember_token(user_id, token):
                # Set a cookie with the token
                response = jsonify({"success": True, "message": "Remember me enabled"})
                # Cookie expires in 30 days
                response.set_cookie(
                    "remember_token", 
                    token, 
                    max_age=30*24*60*60, 
                    httponly=True, 
                    secure=request.is_secure
                )
                return response
            else:
                return jsonify({"success": False, "message": "Failed to enable remember me"}), 500
        else:
            # Clear the token in the database
            if UserManager.set_remember_token(user_id, None):
                # Clear the cookie
                response = jsonify({"success": True, "message": "Remember me disabled"})
                response.delete_cookie("remember_token")
                return response
            else:
                return jsonify({"success": False, "message": "Failed to disable remember me"}), 500
                
    except Exception as e:
        current_app.logger.error(f"Error setting remember me: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500


@profile_bp.route("/remember-status", methods=["GET"])
@login_required
def get_remember_status():
    """Check if remember me is enabled for the current user"""
    try:
        user_id = session.get("user_id")
        user = UserManager.get_user_by_id(user_id)
        
        if user and user.get("remember_token"):
            return jsonify({"remembered": True})
        else:
            return jsonify({"remembered": False})
            
    except Exception as e:
        current_app.logger.error(f"Error checking remember status: {str(e)}")
        return jsonify({"remembered": False, "error": str(e)}), 500

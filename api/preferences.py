"""
Preferences API Module
This module provides API endpoints for user preferences management.
"""

import json
from flask import Blueprint, request, jsonify, session, current_app
from core.auth.auth import login_required
from core.auth.user_manager import get_auth_db_connection

# Create a Blueprint for preferences API
preferences_bp = Blueprint("preferences_api", __name__, url_prefix="/api/preferences")

# Default preferences
DEFAULT_PREFERENCES = {
    "theme": "system",
    "fontSize": "medium",
    "fontFamily": "inter",
    "emailNotifications": True,
    "browserNotifications": True,
    "defaultView": "grid",
    "language": "en",
    "dateFormat": "MM/DD/YYYY",
    "timeFormat": "12h",
    "dashboardLayout": "default",
}


@preferences_bp.route("", methods=["GET"])
@login_required
def get_preferences():
    """Get preferences for the current user"""
    try:
        user_id = session.get("user_id")
        if not user_id:
            return jsonify({"success": False, "message": "User not authenticated"}), 401

        conn = get_auth_db_connection()
        cursor = conn.cursor()

        try:
            # Check if preferences column exists
            cursor.execute(
                """
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'users' AND column_name = 'preferences'
            """
            )
            column_exists = cursor.fetchone() is not None

            if not column_exists:
                # Add preferences column if it doesn't exist
                cursor.execute(
                    """
                    ALTER TABLE users ADD COLUMN preferences JSONB DEFAULT '{}'::jsonb
                """
                )
                conn.commit()

            # Get user preferences
            cursor.execute("SELECT preferences FROM users WHERE id = %s", (user_id,))
            result = cursor.fetchone()

            if result and result["preferences"]:
                # Merge with default preferences to ensure all keys exist
                user_prefs = result["preferences"]
                if isinstance(user_prefs, str):
                    user_prefs = json.loads(user_prefs)

                # Combine default preferences with user preferences
                preferences = {**DEFAULT_PREFERENCES, **user_prefs}
            else:
                preferences = DEFAULT_PREFERENCES

            return jsonify(preferences)
        finally:
            cursor.close()
            conn.close()
    except Exception as e:
        current_app.logger.error(f"Error getting preferences: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500


@preferences_bp.route("", methods=["POST"])
@login_required
def save_preferences():
    """Save preferences for the current user"""
    try:
        user_id = session.get("user_id")
        if not user_id:
            return jsonify({"success": False, "message": "User not authenticated"}), 401

        # Get preferences from request
        preferences = request.json
        if not preferences:
            return (
                jsonify({"success": False, "message": "No preferences provided"}),
                400,
            )

        conn = get_auth_db_connection()
        cursor = conn.cursor()

        try:
            # Check if preferences column exists
            cursor.execute(
                """
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'users' AND column_name = 'preferences'
            """
            )
            column_exists = cursor.fetchone() is not None

            if not column_exists:
                # Add preferences column if it doesn't exist
                cursor.execute(
                    """
                    ALTER TABLE users ADD COLUMN preferences JSONB DEFAULT '{}'::jsonb
                """
                )
                conn.commit()

            # Update user preferences
            cursor.execute(
                "UPDATE users SET preferences = %s::jsonb WHERE id = %s",
                (json.dumps(preferences), user_id),
            )
            conn.commit()

            return jsonify(
                {"success": True, "message": "Preferences saved successfully"}
            )
        finally:
            cursor.close()
            conn.close()
    except Exception as e:
        current_app.logger.error(f"Error saving preferences: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500


@preferences_bp.route("/reset", methods=["POST"])
@login_required
def reset_preferences():
    """Reset preferences to default for the current user"""
    try:
        user_id = session.get("user_id")
        if not user_id:
            return jsonify({"success": False, "message": "User not authenticated"}), 401

        conn = get_auth_db_connection()
        cursor = conn.cursor()

        try:
            # Update user preferences to default
            cursor.execute(
                "UPDATE users SET preferences = %s::jsonb WHERE id = %s",
                (json.dumps(DEFAULT_PREFERENCES), user_id),
            )
            conn.commit()

            return jsonify(
                {
                    "success": True,
                    "message": "Preferences reset successfully",
                    "preferences": DEFAULT_PREFERENCES,
                }
            )
        finally:
            cursor.close()
            conn.close()
    except Exception as e:
        current_app.logger.error(f"Error resetting preferences: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500

"""
Notifications API module for handling bell notification system.
"""

import logging

from flask import Blueprint, jsonify, request, session

from core.auth.auth import login_required
from core.models.notification_models import EmailNotificationData, NotificationCreate
from core.services.notification_service import notification_service

# Create blueprint
notifications_bp = Blueprint("notifications", __name__)

# Configure logging
logger = logging.getLogger(__name__)


@notifications_bp.route("/api/notifications", methods=["GET"])
@login_required
def get_notifications():
    """Get notifications for the current user."""
    try:
        user_id = session.get("user_id")
        if not user_id:
            return jsonify({"success": False, "message": "User not authenticated"}), 401

        # Get query parameters
        limit = request.args.get("limit", 50, type=int)
        unread_only = request.args.get("unread_only", "false").lower() == "true"

        # Get notifications
        notifications = notification_service.get_user_notifications(
            user_id=user_id, limit=limit, unread_only=unread_only
        )

        # Get unread count
        unread_count = notification_service.get_unread_count(user_id)

        return jsonify(
            {
                "success": True,
                "notifications": notifications,
                "unread_count": unread_count,
                "total_count": len(notifications),
            }
        )

    except Exception as e:
        logger.error(f"Error getting notifications: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500


@notifications_bp.route("/api/notifications/unread-count", methods=["GET"])
@login_required
def get_unread_count():
    """Get unread notification count for the current user."""
    try:
        user_id = session.get("user_id")
        if not user_id:
            return jsonify({"success": False, "message": "User not authenticated"}), 401

        unread_count = notification_service.get_unread_count(user_id)

        return jsonify({"success": True, "unread_count": unread_count})

    except Exception as e:
        logger.error(f"Error getting unread count: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500


@notifications_bp.route(
    "/api/notifications/<int:notification_id>/read", methods=["POST"]
)
@login_required
def mark_notification_read(notification_id):
    """Mark a specific notification as read."""
    try:
        user_id = session.get("user_id")
        if not user_id:
            return jsonify({"success": False, "message": "User not authenticated"}), 401

        success = notification_service.mark_as_read(notification_id, user_id)

        if success:
            return jsonify({"success": True, "message": "Notification marked as read"})
        else:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "Notification not found or already read",
                    }
                ),
                404,
            )

    except Exception as e:
        logger.error(f"Error marking notification as read: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500


@notifications_bp.route("/api/notifications/mark-all-read", methods=["POST"])
@login_required
def mark_all_notifications_read():
    """Mark all notifications as read for the current user."""
    try:
        user_id = session.get("user_id")
        if not user_id:
            return jsonify({"success": False, "message": "User not authenticated"}), 401

        success = notification_service.mark_all_as_read(user_id)

        if success:
            return jsonify(
                {"success": True, "message": "All notifications marked as read"}
            )
        else:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "Failed to mark notifications as read",
                    }
                ),
                500,
            )

    except Exception as e:
        logger.error(f"Error marking all notifications as read: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500


@notifications_bp.route("/api/notifications/<int:notification_id>", methods=["DELETE"])
@login_required
def delete_notification(notification_id):
    """Delete a specific notification."""
    try:
        user_id = session.get("user_id")
        if not user_id:
            return jsonify({"success": False, "message": "User not authenticated"}), 401

        success = notification_service.delete_notification(notification_id, user_id)

        if success:
            return jsonify({"success": True, "message": "Notification deleted"})
        else:
            return jsonify({"success": False, "message": "Notification not found"}), 404

    except Exception as e:
        logger.error(f"Error deleting notification: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500


@notifications_bp.route("/api/notifications", methods=["POST"])
@login_required
def create_notification():
    """Create a new notification (admin only or system use)."""
    try:
        user_id = session.get("user_id")
        if not user_id:
            return jsonify({"success": False, "message": "User not authenticated"}), 401

        # Check if user has permission to create notifications
        user_role = session.get("user_role", "")
        if user_role not in ["admin", "modify"]:
            return (
                jsonify({"success": False, "message": "Insufficient permissions"}),
                403,
            )

        data = request.get_json()
        if not data:
            return jsonify({"success": False, "message": "No data provided"}), 400

        # Create notification data
        notification_data = NotificationCreate(
            user_id=data.get("user_id", user_id),
            title=data.get("title", ""),
            message=data.get("message", ""),
            notification_type=data.get("notification_type", "info"),
            is_email_related=data.get("is_email_related", False),
            email_subject=data.get("email_subject"),
            email_sender=data.get("email_sender"),
            email_recipients=data.get("email_recipients"),
            related_entity_type=data.get("related_entity_type"),
            related_entity_id=data.get("related_entity_id"),
            extra_data=data.get("extra_data"),
        )

        notification_id = notification_service.create_notification(notification_data)

        if notification_id:
            return jsonify(
                {
                    "success": True,
                    "message": "Notification created successfully",
                    "notification_id": notification_id,
                }
            )
        else:
            return (
                jsonify({"success": False, "message": "Failed to create notification"}),
                500,
            )

    except Exception as e:
        logger.error(f"Error creating notification: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500


@notifications_bp.route("/api/notifications/email", methods=["POST"])
@login_required
def create_email_notification():
    """Create a notification when an email is sent."""
    try:
        user_id = session.get("user_id")
        if not user_id:
            return jsonify({"success": False, "message": "User not authenticated"}), 401

        data = request.get_json()
        if not data:
            return jsonify({"success": False, "message": "No data provided"}), 400

        # Create email notification data
        email_data = EmailNotificationData(
            subject=data.get("subject", ""),
            sender=data.get("sender", ""),
            recipients=data.get("recipients", []),
            cc=data.get("cc", []),
            body=data.get("body", ""),
            related_entity_type=data.get("related_entity_type"),
            related_entity_id=data.get("related_entity_id"),
        )

        notification_id = notification_service.create_email_notification(
            user_id, email_data
        )

        if notification_id:
            return jsonify(
                {
                    "success": True,
                    "message": "Email notification created successfully",
                    "notification_id": notification_id,
                }
            )
        else:
            return (
                jsonify(
                    {"success": False, "message": "Failed to create email notification"}
                ),
                500,
            )

    except Exception as e:
        logger.error(f"Error creating email notification: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500


@notifications_bp.route("/api/notifications/cleanup", methods=["POST"])
@login_required
def cleanup_expired_notifications():
    """Clean up expired notifications (admin only)."""
    try:
        user_role = session.get("user_role", "")
        if user_role not in ["admin"]:
            return jsonify({"success": False, "message": "Admin access required"}), 403

        deleted_count = notification_service.cleanup_expired_notifications()

        return jsonify(
            {
                "success": True,
                "message": f"Cleaned up {deleted_count} expired notifications",
                "deleted_count": deleted_count,
            }
        )

    except Exception as e:
        logger.error(f"Error cleaning up notifications: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500

#!/usr/bin/env python3
"""
Deployment script for Talaria Dashboard.
This script helps you deploy the new structure to replace the existing project.
"""

import os
import sys
import shutil
import datetime


# Define paths
SOURCE_DIR = os.path.dirname(os.path.abspath(__file__))
ORIGINAL_DIR = os.path.join(os.path.dirname(SOURCE_DIR), "Talaria_Dashboard")


def backup_original():
    """Create a backup of the original project."""
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"{ORIGINAL_DIR}_backup_{timestamp}"
    print(f"Creating backup of original project at: {backup_dir}")
    shutil.copytree(ORIGINAL_DIR, backup_dir)
    print("Backup completed successfully.")
    return backup_dir


def deploy_new_structure():
    """Deploy the new structure to replace the original project."""
    # First, remove everything except .git and any user files you want to keep
    for item in os.listdir(ORIGINAL_DIR):
        item_path = os.path.join(ORIGINAL_DIR, item)
        if item == ".git":
            print(f"Keeping {item}")
            continue
        if item.endswith(".env"):
            print(f"Keeping {item}")
            continue
        if os.path.isdir(item_path):
            shutil.rmtree(item_path)
            print(f"Removed directory: {item}")
        else:
            os.remove(item_path)
            print(f"Removed file: {item}")
    # Now copy everything from the new structure
    for item in os.listdir(SOURCE_DIR):
        item_path = os.path.join(SOURCE_DIR, item)
        target_path = os.path.join(ORIGINAL_DIR, item)
        if item == ".git" or item == "deploy.py":
            print(f"Skipping {item}")
            continue
        if os.path.isdir(item_path):
            shutil.copytree(item_path, target_path)
            print(f"Copied directory: {item}")
        else:
            shutil.copy2(item_path, target_path)
            print(f"Copied file: {item}")
    print("\nDeployment completed successfully.")
    print(f"The new structure has replaced the original project at: {ORIGINAL_DIR}")


def main():
    """Main function to execute the deployment."""
    print("Talaria Dashboard Deployment Script")
    print("===================================")
    print("\nThis script will:")
    print("1. Create a backup of your original project")
    print("2. Replace the original project with the new structure")
    print("\nWARNING: This will modify your original project directory!")
    confirm = input("\nDo you want to continue? (yes/no): ")
    if confirm.lower() != "yes":
        print("Deployment cancelled.")
        sys.exit(0)
    # Create backup
    backup_dir = backup_original()
    # Deploy new structure
    deploy_new_structure()
    print("\nImportant Next Steps:")
    print("1. Test the application thoroughly")
    print("2. If you encounter any issues, you can restore from the backup at:")
    print(f"   {backup_dir}")
    print("3. Update any deployment scripts to use the new structure")


if __name__ == "__main__":
    main()

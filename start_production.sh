#!/bin/bash

# This script starts the application in production mode

# Activate virtual environment
source ./talaria_env/bin/activate

# Set environment variables from .env file if available
if [ -f .env ]; then
    eval "$(grep -v '^#' .env | sed 's/^/export /')"
fi

# Set production mode explicitly
export FLASK_ENV=production

# Start the application with gunicorn
echo "Starting in production mode..."
echo "Using gunicorn with 4 workers on port ${PORT:-8000}"
./talaria_env/bin/gunicorn --workers 4 --bind 0.0.0.0:${PORT:-8000} app:app